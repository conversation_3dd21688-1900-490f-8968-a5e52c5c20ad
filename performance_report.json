{"export_timestamp": 1755235067.248857, "export_datetime": "2025-08-15T13:17:47.248858", "system_performance": {"monitoring_period": {"start": "2025-08-15T13:17:45.450697", "end": "2025-08-15T13:17:45.450697", "duration_minutes": 0.0}, "cpu_stats": {"current_percent": 50.7, "avg_percent": 50.7, "max_percent": 50.7, "min_percent": 50.7}, "memory_stats": {"current_percent": 71.2, "avg_percent": 71.2, "max_percent": 71.2, "min_percent": 71.2, "current_available_gb": 9.229949951171875}, "disk_stats": {"current_percent": 10.5, "avg_percent": 10.5, "max_percent": 10.5, "min_percent": 10.5}, "process_count": 942, "network_io": {"bytes_sent": 1888078010.0, "bytes_recv": 3320237089.0, "packets_sent": 3033367.0, "packets_recv": 3664209.0}}, "operation_performance": {"数据获取": {"operation_name": "数据获取", "total_executions": 1, "success_rate": 1.0, "duration_stats": {"min_ms": 760.3111267089844, "max_ms": 760.3111267089844, "avg_ms": 760.3111267089844, "p95_ms": 760.3111267089844}, "memory_stats": {"min_mb": 21.96875, "max_mb": 21.96875, "avg_mb": 21.96875}, "cpu_stats": {"min_percent": 0.0, "max_percent": 0.0, "avg_percent": 0.0}, "recent_executions": [{"timestamp": 1755235064.4952939, "duration_ms": 760.3111267089844, "success": true, "error": null}]}, "数据标准化": {"operation_name": "数据标准化", "total_executions": 1, "success_rate": 1.0, "duration_stats": {"min_ms": 1096.1780548095703, "max_ms": 1096.1780548095703, "avg_ms": 1096.1780548095703, "p95_ms": 1096.1780548095703}, "memory_stats": {"min_mb": 5.828125, "max_mb": 5.828125, "avg_mb": 5.828125}, "cpu_stats": {"min_percent": 0.0, "max_percent": 0.0, "avg_percent": 0.0}, "recent_executions": [{"timestamp": 1755235065.255847, "duration_ms": 1096.1780548095703, "success": true, "error": null}]}, "约束计算": {"operation_name": "约束计算", "total_executions": 1, "success_rate": 1.0, "duration_stats": {"min_ms": 5.172967910766602, "max_ms": 5.172967910766602, "avg_ms": 5.172967910766602, "p95_ms": 5.172967910766602}, "memory_stats": {"min_mb": 0.0625, "max_mb": 0.0625, "avg_mb": 0.0625}, "cpu_stats": {"min_percent": 0.0, "max_percent": 0.0, "avg_percent": 0.0}, "recent_executions": [{"timestamp": 1755235066.352392, "duration_ms": 5.172967910766602, "success": true, "error": null}]}, "数据平衡": {"operation_name": "数据平衡", "total_executions": 1, "success_rate": 1.0, "duration_stats": {"min_ms": 881.3538551330566, "max_ms": 881.3538551330566, "avg_ms": 881.3538551330566, "p95_ms": 881.3538551330566}, "memory_stats": {"min_mb": 1.546875, "max_mb": 1.546875, "avg_mb": 1.546875}, "cpu_stats": {"min_percent": 0.0, "max_percent": 0.0, "avg_percent": 0.0}, "recent_executions": [{"timestamp": 1755235066.3576262, "duration_ms": 881.3538551330566, "success": true, "error": null}]}, "清单构建": {"operation_name": "清单构建", "total_executions": 1, "success_rate": 1.0, "duration_stats": {"min_ms": 8.955955505371094, "max_ms": 8.955955505371094, "avg_ms": 8.955955505371094, "p95_ms": 8.955955505371094}, "memory_stats": {"min_mb": 1.03125, "max_mb": 1.03125, "avg_mb": 1.03125}, "cpu_stats": {"min_percent": 0.0, "max_percent": 0.0, "avg_percent": 0.0}, "recent_executions": [{"timestamp": 1755235067.239192, "duration_ms": 8.955955505371094, "success": true, "error": null}]}, "结果验证": {"operation_name": "结果验证", "total_executions": 1, "success_rate": 1.0, "duration_stats": {"min_ms": 0.3910064697265625, "max_ms": 0.3910064697265625, "avg_ms": 0.3910064697265625, "p95_ms": 0.3910064697265625}, "memory_stats": {"min_mb": 0.0, "max_mb": 0.0, "avg_mb": 0.0}, "cpu_stats": {"min_percent": 0.0, "max_percent": 0.0, "avg_percent": 0.0}, "recent_executions": [{"timestamp": 1755235067.248379, "duration_ms": 0.3910064697265625, "success": true, "error": null}]}}, "performance_thresholds": {"slow_operation_threshold_ms": 1000.0, "memory_warning_threshold_percent": 80.0, "cpu_warning_threshold_percent": 90.0}}