from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional
import pandas as pd
from enum import Enum, auto
from pydantic import BaseModel, validator, Field

# ===================================================================
# 1. 基础值对象 (Foundational Value Objects)
# ===================================================================
class Region(BaseModel):
    """Represents a geographical area, such as a country, province or a city."""
    
    name: str
    level: str  # e.g., 'country', 'province', 'city'

    @validator('level')
    def level_must_be_valid(cls, v):
        allowed_levels = ['province', 'city', 'country']
        if v not in allowed_levels:
            raise ValueError(f"无效的地区级别: {v}。只允许 {allowed_levels}")
        return v
    
    class Config:
        frozen = True

class Industry(BaseModel):
    """Represents a standardized industry category."""
    
    name: str
    code: Optional[str] = None
    
    class Config:
        frozen = True

class StandardizedDataType(Enum):
    """标准化数据的类型枚举"""
    ENERGY = 1
    PRODUCT = 2
    ECONOMIC = 3
    EMISSION = 4
    ELECTRICITY = 5

# ===================================================================
# 2. 统一的标准化数据VO (Unified Standardized Data VO)
# ===================================================================
class StandardizedData(BaseModel):
    """统一的标准化数据值对象"""

    data_type: StandardizedDataType
    year: int
    province: str
    city: str
    source_table: str
    attributes: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        frozen = True
        arbitrary_types_allowed = True # Allow pandas DataFrame

# ===================================================================
# 3. 流程与输出值对象 (Process & Output Value Objects)
# ===================================================================
class RawData(BaseModel):
    """原始数据值对象 - 直接从数据库读取"""
    
    table_name: str                    # 数据库表名
    data: pd.DataFrame                 # 原始数据
    metadata: Dict[str, Any] = Field(default_factory=dict) # FIX
    source_name: str                   # 数据源名称
    
    def is_empty(self) -> bool:
        """检查数据是否为空"""
        return self.data.empty
    
    class Config:
        frozen = True
        arbitrary_types_allowed = True # Allow pandas DataFrame


class CityEnergyMatrix(BaseModel):
    """城市能源矩阵值对象 - 数据下沉后的中间结果"""
    
    year: int                          # 年份
    province: Region                   # 省份
    cities: List[Region]               # 城市列表
    industries: List[Industry]         # 行业列表
    energy_types: List[str]            # 能源品种列表
    matrix_data: pd.DataFrame          # 城市-行业-能源品种矩阵
    allocation_method: str             # 分配方法（如用电比例）
    source_data: Dict[str, Any]       # 源数据信息
    validation_status: str             # 验证状态
    
    class Config:
        frozen = True
        arbitrary_types_allowed = True # Allow pandas DataFrame

    def get_energy_consumption(self, city: Region, industry: Industry) -> float:
        """获取指定城市和行业的能源消费量"""
        try:
            # The matrix index is likely still a string, so we need to use the name
            value = self.matrix_data.loc[city.name, industry.name]
            if pd.isna(value):
                return 0.0
            # 安全地转换为float
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                return float(value) if value.replace('.', '').replace('-', '').isdigit() else 0.0
            else:
                return 0.0
        except (KeyError, IndexError, ValueError):
            return 0.0

class ConstraintData(BaseModel):
    """约束数据值对象"""
    
    year: int
    province: Region
    constraints: Dict[str, pd.Series] = Field(default_factory=dict) # FIX
    # 添加设计文档中要求的属性
    row_constraints: Optional[pd.Series] = None
    column_constraints: Optional[pd.Series] = None
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        frozen = True
        arbitrary_types_allowed = True # Allow pandas DataFrame

class EnergyConsumptionInventory(BaseModel):
    """能源消费清单值对象 - 最终输出"""
    
    inventory_df: pd.DataFrame
    metadata: Dict[str, Any]
    
    class Config:
        frozen = True
        arbitrary_types_allowed = True


class CarbonEmissionInventory(BaseModel):
    """碳排放清单"""
    
    inventory_df: pd.DataFrame
    metadata: Dict[str, Any]
    
    class Config:
        frozen = True
        arbitrary_types_allowed = True


class DataQualityReport(BaseModel):
    """数据质量报告"""
    
    is_valid: bool
    error_messages: List[str] = Field(default_factory=list) # FIX
    warnings: List[str] = Field(default_factory=list) # FIX
    statistics: Dict[str, Any] = Field(default_factory=dict) # FIX
    
    class Config:
        frozen = True

# 保留原有的基础值对象，用于向后兼容
class TabularData(BaseModel):
    """
    代表从数据源获取的原始数据，通常是类似表格的格式。
    内部使用 pandas DataFrame，因为它功能强大且灵活。
    """
    
    source_name: str
    data: pd.DataFrame

    def is_empty(self) -> bool:
        return self.data.empty
    
    class Config:
        frozen = True
        arbitrary_types_allowed = True # Allow pandas DataFrame

class QualityCheckedData(BaseModel):
    """质量已检数据值对象"""
    
    original_data: RawData
    is_valid: bool
    validation_errors: List[str] = Field(default_factory=list) # FIX
    validation_warnings: List[str] = Field(default_factory=list) # FIX
    
    class Config:
        frozen = True
        arbitrary_types_allowed = True
