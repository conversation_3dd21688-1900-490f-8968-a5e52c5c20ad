from dataclasses import dataclass, field
from datetime import datetime
import uuid
from typing import Optional

from .job_status import JobStatus

@dataclass
class CalculationJob:
    """
    代表一个有状态的、生命周期长的计算任务流程。
    这是整个计算流程的聚合根。
    """
    id: uuid.UUID = field(default_factory=uuid.uuid4)
    status: JobStatus = JobStatus.STARTED
    failure_reason: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

    def advance_state(self, new_status: JobStatus, message: Optional[str] = None):
        """将任务推进到下一个状态，并可选地记录一条消息。"""
        self.status = new_status
        self.updated_at = datetime.utcnow()
        if message:
            # 如果是失败状态，则记录到 failure_reason
            if new_status == JobStatus.FAILED:
                self.failure_reason = message
            # 可以在这里添加逻辑来处理其他状态的消息，如果需要的话

    def fail(self, reason: str):
        """将任务标记为失败。"""
        self.status = JobStatus.FAILED
        self.failure_reason = reason
        self.updated_at = datetime.utcnow()

    def complete(self):
        """将任务标记为成功完成。"""
        self.status = JobStatus.COMPLETED
        self.updated_at = datetime.utcnow()
