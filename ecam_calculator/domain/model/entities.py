from enum import Enum, auto

class JobStatus(Enum):
    """核算任务的状态"""
    PENDING = auto()
    RAW_DATA_FETCHING = auto()
    RAW_DATA_FETCHED = auto()
    RAW_DATA_CHECKED = auto()  # 新增
    DATA_STANDARDIZING = auto()
    DATA_STANDARDIZED = auto()
    CONSTRAINT_CALCULATING = auto()
    CONSTRAINT_CALCULATED = auto()
    BALANCING = auto()
    BALANCED = auto()
    INVENTORY_CONSTRUCTING = auto()
    INVENTORY_CONSTRUCTED = auto()
    COMPLETED = auto()
    FAILED = auto()  # 新增

class CalculationJob:
    """核算任务实体"""
    # ... ( existing code ... )
    def advance_state(self, new_state: JobStatus):
        """推进任务到下一个状态"""
        # 可以在这里添加状态转换的验证逻辑
        self.status = new_state
        self.history.append(f"状态更新为: {new_state.name}")

    def fail(self, reason: str):
        """将任务标记为失败"""
        self.status = JobStatus.FAILED
        self.history.append(f"任务失败: {reason}")
        self.result_summary = f"失败: {reason}"
