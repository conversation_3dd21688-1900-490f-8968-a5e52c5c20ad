from abc import ABC, abstractmethod
import uuid
from typing import Optional, List

from ..model.calculation_job import CalculationJob

class JobRepository(ABC):
    """
    用于计算任务持久化的抽象基类。
    它定义了基础设施层需要遵守的契约。
    """

    @abstractmethod
    def save(self, job: CalculationJob) -> None:
        """保存或更新一个计算任务。"""
        raise NotImplementedError

    @abstractmethod
    def find_by_id(self, job_id: uuid.UUID) -> Optional[CalculationJob]:
        """通过ID查找一个计算任务。"""
        raise NotImplementedError

    @abstractmethod
    def find_all(self) -> List[CalculationJob]:
        """查找所有的计算任务。"""
        raise NotImplementedError
