"""
建模预测服务
负责ARDL模型构建和基于电力数据的预测
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional
from statsmodels.tsa.ardl import ARDL
from statsmodels.tsa.ardl import ardl_select_order
from ecam_calculator.infrastructure.config_reader import get_config_reader


class ModelingPredictionService:
    """
    建模预测服务：负责ARDL模型构建和基于电力数据的预测。
    """
    
    def __init__(self, config_reader=None):
        self.config_reader = config_reader or get_config_reader()
        self.logger = logging.getLogger(__name__)
        self.logger.info("建模预测服务初始化完成")
    
    def build_and_predict(self, converted_data: Dict[str, pd.DataFrame], 
                        electricity_data: pd.DataFrame,
                        calculation_context: Any) -> Dict[str, Any]:
        """
        对转换后的能源数据进行建模预测处理。

        Args:
            converted_data (Dict[str, pd.DataFrame]): 转换后的能源数据字典
            electricity_data (pd.DataFrame): 电力消费数据，包含历史数据和最新数据
            calculation_context (Any): 计算上下文

        Returns:
            Dict[str, Any]: 预测结果字典
        """
        self.logger.info("开始建模预测处理")
        
        # 1. 构建ARDL模型
        ardl_models = self.build_ardl_models(
            converted_data.get('converted_temporal_energy', pd.DataFrame()),
            electricity_data
        )
        
        # 2. 基于电力数据进行预测
        prediction_results = self.predict_with_latest_electricity(
            ardl_models, electricity_data
        )
        
        return {
            'ardl_models': ardl_models,
            'prediction_results': prediction_results
        }
    
    def build_ardl_models(self, energy_data: pd.DataFrame, 
                         electricity_data: pd.DataFrame) -> Dict[str, Any]:
        """
        为各地区的各行业建立ARDL模型
        """
        self.logger.info("开始构建ARDL模型")
        
        if energy_data.empty or electricity_data.empty:
            self.logger.warning("输入数据为空，跳过ARDL模型构建")
            return {}
        
        # 1. 数据预处理和分组
        grouped_data = self._prepare_modeling_data(energy_data, electricity_data)
        
        ardl_models = {}
        
        # 2. 为每个地区-行业组合构建模型
        for (area, industry), group_data in grouped_data.items():
            self.logger.info(f"为 {area}-{industry} 构建ARDL模型")
            
            try:
                # 3. 自动阶数选择
                optimal_order = self._select_optimal_order(group_data)
                
                # 4. 模型训练
                model = self._train_ardl_model(group_data, optimal_order)
                
                # 5. 存储模型
                ardl_models[f"{area}_{industry}"] = {
                    'model': model,
                    'optimal_order': optimal_order,
                    'area': area,
                    'industry': industry,
                    'r_squared': model.rsquared,
                    'aic': model.aic,
                    'bic': model.bic
                }
                
            except Exception as e:
                self.logger.error(f"为 {area}-{industry} 构建ARDL模型失败: {e}")
                continue
        
        self.logger.info(f"ARDL模型构建完成，共构建 {len(ardl_models)} 个模型")
        return ardl_models
    
    def predict_with_latest_electricity(self, ardl_models: Dict[str, Any], 
                                      latest_electricity_data: pd.DataFrame) -> pd.DataFrame:
        """
        使用ARDL模型和电力数据的最新数据进行预测
        """
        self.logger.info("开始基于电力数据进行预测")
        
        if not ardl_models or latest_electricity_data.empty:
            self.logger.warning("ARDL模型为空或电力数据为空，跳过预测")
            return pd.DataFrame()
        
        # 1. 识别最新电力数据
        latest_data = self._identify_latest_electricity_data(latest_electricity_data)
        
        if latest_data.empty:
            self.logger.warning("未找到电力数据的最新数据")
            return pd.DataFrame()
        
        prediction_results = []
        
        # 2. 为每个地区-行业组合进行预测
        for (area, industry), electricity_group in latest_data.groupby(['standard_area', 'standard_industry']):
            model_key = f"{area}_{industry}"
            
            if model_key not in ardl_models:
                self.logger.warning(f"未找到 {area}-{industry} 的ARDL模型，跳过预测")
                continue
            
            try:
                # 3. 使用ARDL模型进行预测
                predictions = self._apply_ardl_prediction(
                    ardl_models[model_key], 
                    electricity_group
                )
                
                prediction_results.append(predictions)
                
            except Exception as e:
                self.logger.error(f"为 {area}-{industry} 进行预测失败: {e}")
                continue
        
        # 4. 整合所有预测结果
        if prediction_results:
            final_predictions = pd.concat(prediction_results, ignore_index=True)
            self.logger.info(f"基于电力数据的预测完成，生成 {len(final_predictions)} 条预测结果")
            return final_predictions
        else:
            self.logger.warning("未生成任何预测结果")
            return pd.DataFrame()
    
    def _prepare_modeling_data(self, energy_data: pd.DataFrame, 
                              electricity_data: pd.DataFrame) -> Dict[tuple, pd.DataFrame]:
        """
        准备建模数据，按地区和行业分组
        """
        self.logger.info("准备建模数据")
        
        # 合并能源消费数据和电力消费数据
        # 假设能源数据包含标准煤当量，电力数据包含电力消费量
        merged_data = pd.merge(
            energy_data,
            electricity_data,
            on=['year', 'month', 'standard_area', 'standard_industry'],
            how='inner'
        )
        
        if merged_data.empty:
            self.logger.warning("合并后的数据为空")
            return {}
        
        # 按地区和行业分组
        grouped_data = {}
        for (area, industry), group in merged_data.groupby(['standard_area', 'standard_industry']):
            # 检查数据量是否足够
            min_observations = 12  # 默认最小观测值数量
            if len(group) >= min_observations:
                grouped_data[(area, industry)] = group.sort_values(['year', 'month'])
            else:
                self.logger.warning(f"{area}-{industry} 数据量不足，跳过建模")
        
        self.logger.info(f"准备建模数据完成，共 {len(grouped_data)} 个地区-行业组合")
        return grouped_data
    
    def _select_optimal_order(self, data: pd.DataFrame) -> Dict[str, int]:
        """
        自动选择ARDL模型的最优阶数
        """
        # 准备数据
        y = data['standard_coal_equivalent']  # 能源消费总量（标准煤当量）
        x = data[['value']]  # 电力消费量
        
        # 使用statsmodels的自动阶数选择
        try:
            max_lags = 3
            max_order = 2
            trend = 'c'
            selected_order = ardl_select_order(
                endog=y,
                exog=x,
                maxlag=max_lags,
                maxorder=max_order,
                trend=trend
            )
            
            # 提取最优阶数
            optimal_order = {
                'lags': selected_order.bic_lags,
                'order': selected_order.bic_order
            }
            
            return optimal_order
            
        except Exception as e:
            self.logger.warning(f"自动阶数选择失败，使用默认参数: {e}")
            return {'lags': 1, 'order': 1}
    
    def _train_ardl_model(self, data: pd.DataFrame, optimal_order: Dict[str, int]) -> Any:
        """
        训练ARDL模型
        """
        y = data['standard_coal_equivalent']
        x = data[['value']]
        
        # 创建ARDL模型
        model = ARDL(
            endog=y,
            lags=optimal_order['lags'],
            exog=x,
            order=optimal_order['order'],
            trend='c'
        )
        
        # 拟合模型
        fitted_model = model.fit()
        
        return fitted_model
    
    def _identify_latest_electricity_data(self, electricity_data: pd.DataFrame) -> pd.DataFrame:
        """
        识别电力数据中的最新数据（月度能源消费数据之后的部分）
        """
        # 按地区和行业分组，找到每个组合的最新时间点
        latest_data = []
        
        for (area, industry), group in electricity_data.groupby(['standard_area', 'standard_industry']):
            # 按时间排序，获取最新的数据
            group_sorted = group.sort_values(['year', 'month'])
            
            # 找到能源消费数据的最后时间点（这里需要从上下文获取）
            # 假设能源消费数据的最新时间是2020年12月
            energy_end_year = 2020
            energy_end_month = 12
            
            # 获取电力数据中2021年1月之后的数据
            latest_group = group_sorted[
                (group_sorted['year'] > energy_end_year) | 
                ((group_sorted['year'] == energy_end_year) & (group_sorted['month'] > energy_end_month))
            ]
            
            if not latest_group.empty:
                latest_data.append(latest_group)
        
        if latest_data:
            return pd.concat(latest_data, ignore_index=True)
        else:
            self.logger.warning("未找到电力数据的最新数据")
            return pd.DataFrame()
    
    def _apply_ardl_prediction(self, model_info: Dict[str, Any], 
                              electricity_data: pd.DataFrame) -> pd.DataFrame:
        """
        使用ARDL模型进行预测
        """
        model = model_info['model']
        area = model_info['area']
        industry = model_info['industry']
        
        # 准备预测用的外生变量（电力消费量）
        exog_oos = electricity_data[['value']].values
        
        # 进行预测
        predictions = model.predict(
            start=len(model.fittedvalues),
            end=len(model.fittedvalues) + len(electricity_data) - 1,
            exog_oos=exog_oos
        )
        
        # 构建预测结果DataFrame
        prediction_df = pd.DataFrame({
            'year': electricity_data['year'],
            'month': electricity_data['month'],
            'standard_area': area,
            'standard_industry': industry,
            'predicted_energy_consumption': predictions,
            'electricity_consumption': electricity_data['value'],
            'prediction_method': 'ardl',
            'model_id': f"{area}_{industry}"
        })
        
        return prediction_df
