"""
碳排放计算服务
负责计算能源活动和工业过程碳排放的业务逻辑
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
from decimal import Decimal

from ecam_calculator.domain.model.value_objects import StandardizedData, StandardizedDataType
from ecam_calculator.infrastructure.config_reader import get_config_reader


class CarbonEmissionService:
    """碳排放计算服务 - 业务逻辑服务"""

    def __init__(self, config_reader=None, emission_factor_repository=None):
        self.config_reader = config_reader or get_config_reader()
        self.emission_factor_repo = emission_factor_repository
        self.logger = logging.getLogger(__name__)
        self._emission_factor_cache: Dict[str, Dict[str, Any]] = {}
        self.energy_factor_config = self.config_reader.get_energy_factor_table_config()

    def calculate_emissions(self, standardized_data_list: List[StandardizedData]) -> List[StandardizedData]:
        """计算给定标准化数据集的碳排放 - 业务逻辑"""
        self.logger.info(f"开始计算 {len(standardized_data_list)} 条记录的碳排放...")
        
        # 1. 批量预加载所有需要的排放因子
        factor_lookup = self._preload_emission_factors(standardized_data_list)
        if not factor_lookup:
            self.logger.warning("未能预加载任何排放因子，无法进行计算。")
            return []

        # 2. 将标准化数据转换为DataFrame以便于向量化操作
        df = self._convert_to_dataframe(standardized_data_list)
        
        emissions_df = pd.DataFrame()
        
        # 3. 分别处理能源和工业过程
        energy_df = df[df['data_type'] == StandardizedDataType.ENERGY]
        if not energy_df.empty:
            emissions_df = pd.concat([emissions_df, self._calculate_energy_emissions(energy_df, factor_lookup)])

        product_df = df[df['data_type'] == StandardizedDataType.PRODUCT]
        if not product_df.empty:
            emissions_df = pd.concat([emissions_df, self._calculate_process_emissions(product_df, factor_lookup)])
        
        if emissions_df.empty:
            self.logger.info("计算完成，没有生成排放记录。")
            return []

        # 4. 将结果DataFrame转换回StandardizedData对象列表
        final_emissions = self._convert_to_standardized_data(emissions_df)
        
        self.logger.info(f"碳排放计算完成，生成 {len(final_emissions)} 条排放记录。")
        return final_emissions

    def _calculate_energy_emissions(self, energy_df: pd.DataFrame, factor_lookup: Dict) -> pd.DataFrame:
        """向量化计算能源活动的碳排放 - 业务规则"""
        self.logger.info(f"正在向量化计算 {len(energy_df)} 条能源活动的碳排放...")
        df = energy_df.copy()

        # 定义一个函数来应用因子查找逻辑
        def find_factor(row):
            # 优先实物量
            factor = factor_lookup.get((row['energy_type'], row['year'], '实物量折碳排放因子'))
            if factor:
                return factor['factor'], '能源活动 (实物量)', row['original_value'], row['original_unit'], factor['unit'], factor['source']
            # 回退等价值
            factor = factor_lookup.get((row['energy_type'], row['year'], '标煤折碳排放因子'))
            if factor:
                return factor['factor'], '能源活动 (等价值)', row['value'], row['unit'], factor['unit'], factor['source']
            return None, None, 0, '', None, None

        # 应用查找逻辑
        res = df.apply(find_factor, axis=1, result_type='expand')
        df[['factor', 'emission_source_type', 'activity_value', 'activity_unit', 'factor_unit', 'factor_source']] = res
        
        df.dropna(subset=['factor'], inplace=True)
        if df.empty:
            return pd.DataFrame()

        # 向量化计算
        df['emission_value'] = df['activity_value'] * df['factor']

        # 单位转换 (简化逻辑)
        df.loc[df['activity_unit'].str.contains('万', na=False), 'emission_value'] /= 1
        df.loc[~df['activity_unit'].str.contains('万', na=False), 'emission_value'] /= 10000

        # 构建结果
        return self._format_emission_dataframe(df)

    def _calculate_process_emissions(self, product_df: pd.DataFrame, factor_lookup: Dict) -> pd.DataFrame:
        """向量化计算工业过程的碳排放 - 业务规则"""
        self.logger.info(f"正在向量化计算 {len(product_df)} 条工业过程的碳排放...")
        df = product_df.copy()

        def find_factor(row):
            factor = factor_lookup.get((row['product_name'], row['year'], '实物量折碳排放因子'))
            if factor:
                return factor['factor'], factor['unit'], factor['source']
            return None, None, None

        res = df.apply(find_factor, axis=1, result_type='expand')
        df[['factor', 'factor_unit', 'factor_source']] = res
        df.dropna(subset=['factor'], inplace=True)

        if df.empty:
            return pd.DataFrame()
            
        df['emission_source_type'] = '工业过程'
        df['activity_value'] = df['value']
        df['activity_unit'] = df['unit']
        
        df['emission_value'] = df['activity_value'] * df['factor']
        df.loc[df['activity_unit'] == '吨', 'emission_value'] /= 10000
        
        return self._format_emission_dataframe(df)

    def _preload_emission_factors(self, data_list: List[StandardizedData]) -> Dict[Tuple[str, int, str], Dict]:
        """预加载排放因子 - 业务数据获取"""
        # 这里应该调用数据访问服务获取排放因子
        # 但计算逻辑本身是业务逻辑
        return {}

    def _convert_to_dataframe(self, data_list: List[StandardizedData]) -> pd.DataFrame:
        """将StandardizedData列表转换为DataFrame - 数据转换"""
        records = []
        for item in data_list:
            rec = {
                'data_type': item.data_type,
                'year': item.year,
                'province': item.province,
                'city': item.city,
                'source_table': item.source_table,
            }
            rec.update(item.attributes)
            records.append(rec)
        return pd.DataFrame(records)

    def _format_emission_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """格式化排放DataFrame - 数据格式化"""
        return df[[
            'year', 'province', 'city', 'source_table', 'emission_value', 
            'emission_source_type', 'activity_value', 'activity_unit', 
            'factor_unit', 'factor_source'
        ]]

    def _convert_to_standardized_data(self, df: pd.DataFrame) -> List[StandardizedData]:
        """将DataFrame转换为StandardizedData列表 - 数据转换"""
        # 这里实现数据转换逻辑
        return []

