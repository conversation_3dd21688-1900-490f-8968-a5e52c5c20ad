"""
数据质量检查服务
负责对原始数据进行质量检查的业务逻辑
"""

import logging
from typing import List

from ecam_calculator.domain.model.value_objects import RawData, DataQualityReport


class QualityCheckService:
    """数据质量检查服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def check_quality(self, raw_data_list: List[RawData]) -> DataQualityReport:
        self.logger.info(f"开始对 {len(raw_data_list)} 个原始数据集进行质量检查...")
        
        error_messages = []
        warnings = []
        
        KEY_COLUMNS_BY_SOURCE = {
            'default': ['year', 'area', 'indicator', 'record'],
            'fct_y_prd_output': ['year', 'area', 'product_name', 'record'],
            'ecam_in_m_pro_ind_ele_off': ['year', 'month', 'area', 'industry', 'electricity'],
            '年度工业产品产量': ['year', 'area', 'product_name', 'record'],
            '年度GDP': ['year', 'area', 'indicator', 'record'],
            '年度能耗强度': ['year', 'area', 'indicator', 'record'],
            '能源因子': ['year', 'area', 'factor_type', 'value'],
            '月度分行业用电量': ['month', 'area', 'industry', 'electricity'],
            '年度省级分行业分品种能源消费量': ['year', 'area', 'industry', 'energy_type', 'value'],
            '年度地市工业能源消费量': ['year', 'area', 'industry', 'energy_type', 'value']
        }

        for i, raw_data in enumerate(raw_data_list):
            source_name = raw_data.source_name
            df = raw_data.data
            
            if df.empty:
                warnings.append(f"数据集 '{source_name}' (序号 {i}) 为空。")
                continue

            key_columns = KEY_COLUMNS_BY_SOURCE.get(source_name, KEY_COLUMNS_BY_SOURCE['default'])
            missing_columns = [col for col in key_columns if col not in df.columns]
            if missing_columns:
                error_messages.append(
                    f"数据集 '{source_name}' (序号 {i}) 缺失关键列: {', '.join(missing_columns)}."
                )
                continue

            for col in key_columns:
                if df[col].isnull().any():
                    null_count = df[col].isnull().sum()
                    total_count = len(df)
                    warnings.append(
                        f"数据集 '{source_name}' (序号 {i}) 的关键列 '{col}' 中存在 {null_count}/{total_count} 个空值。"
                    )
        
        is_valid = not error_messages
        
        report = DataQualityReport(
            is_valid=is_valid,
            error_messages=error_messages,
            warnings=warnings,
            statistics={'total_datasets_checked': len(raw_data_list)}
        )
        
        if is_valid:
            self.logger.info("数据质量检查通过。")
        else:
            self.logger.warning(f"数据质量检查未通过。错误: {error_messages} 警告: {warnings}")
            
        return report
