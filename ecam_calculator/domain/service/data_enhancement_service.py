"""
数据增强服务
负责通过空间降尺度和时间降尺度将省级年度数据增强为地市级月度数据
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import pandas as pd
import logging

from ecam_calculator.infrastructure.config_reader import get_config_reader


class DataEnhancementService(ABC):
    """
    数据增强服务接口：负责数据增强处理，包括空间降尺度和时间降尺度。
    """
    
    @abstractmethod
    def enhance_data(self, standardized_data: Dict[str, pd.DataFrame], 
                    calculation_context: Any) -> Dict[str, pd.DataFrame]:
        """
        对标准化后的数据进行增强处理。

        Args:
            standardized_data (Dict[str, pd.DataFrame]): 标准化后的数据字典，包含：
                - 'emission_factors': 排放因子数据
                - 'energy_consumption': 省级能源消费数据
                - 'energy_consumption2': 地市工业能源消费数据  
                - 'electricity': 地市用电量数据
                - 'gdp_data': GDP数据
                - 'energy_intensity': 能耗强度数据
                - 'product_output': 工业产品产量数据
            calculation_context: 计算上下文对象

        Returns:
            Dict[str, pd.DataFrame]: 增强后的数据字典，包含：
                - 'spatial_downscaled_energy': 空间降尺度后的地市能源数据
                - 'temporal_downscaled_energy': 时间降尺度后的地市月度能源数据
        """
        raise NotImplementedError
    
    @abstractmethod
    def spatial_downscaling(self, provincial_data: pd.DataFrame, 
                           city_data: pd.DataFrame, 
                           city_electricity: pd.DataFrame) -> pd.DataFrame:
        """
        执行空间降尺度处理。

        Args:
            provincial_data (pd.DataFrame): 省级能源消费数据
            city_data (pd.DataFrame): 地市工业能源消费数据
            city_electricity (pd.DataFrame): 地市年度分行业用电量数据

        Returns:
            pd.DataFrame: 空间降尺度后的地市能源消费数据
        """
        raise NotImplementedError
    
    @abstractmethod
    def temporal_downscaling(self, spatial_enhanced_data: pd.DataFrame, 
                            monthly_electricity: pd.DataFrame) -> pd.DataFrame:
        """
        执行时间降尺度处理。

        Args:
            spatial_enhanced_data (pd.DataFrame): 空间降尺度后的地市能源数据
            monthly_electricity (pd.DataFrame): 地市月度用电量数据

        Returns:
            pd.DataFrame: 时间降尺度后的地市月度能源消费数据
        """
        raise NotImplementedError


class DataEnhancementServiceImpl(DataEnhancementService):
    """
    数据增强服务实现
    """
    
    def __init__(self, config_reader=None):
        self.logger = logging.getLogger(__name__)
        self.config_reader = config_reader or get_config_reader()
        
        # 加载数据增强配置
        self.spatial_config = self._load_spatial_config()
        self.temporal_config = self._load_temporal_config()
        
        self.logger.info("数据增强服务初始化完成")
    
    def _prepare_electricity_data(self, electricity_data: pd.DataFrame) -> pd.DataFrame:
        """
        准备电力数据，确保列名正确，并优化内存使用
        """
        if electricity_data.empty:
            return electricity_data
        
        prepared_data = electricity_data.copy()
        
        # 如果电力数据有month列但没有year列，从month列提取year
        if 'month' in prepared_data.columns and 'year' not in prepared_data.columns:
            prepared_data['year'] = pd.to_datetime(prepared_data['month']).dt.year
        
        # 如果电力数据有electricity列但没有value列，重命名
        if 'electricity' in prepared_data.columns and 'value' not in prepared_data.columns:
            prepared_data['value'] = prepared_data['electricity']
        
        # 优化内存使用：删除不需要的列，处理标准化列名
        columns_to_keep = ['year', 'value']
        
        # 处理area列（可能是area或standard_area）
        if 'standard_area' in prepared_data.columns:
            columns_to_keep.append('standard_area')
        elif 'area' in prepared_data.columns:
            columns_to_keep.append('area')
            
        # 处理industry列（可能是industry或standard_industry）
        if 'standard_industry' in prepared_data.columns:
            columns_to_keep.append('standard_industry')
        elif 'industry' in prepared_data.columns:
            columns_to_keep.append('industry')
        
        existing_columns = [col for col in columns_to_keep if col in prepared_data.columns]
        prepared_data = prepared_data[existing_columns]
        
        # 使用更节省内存的数据类型
        if 'year' in prepared_data.columns:
            prepared_data['year'] = prepared_data['year'].astype('int16')
        if 'value' in prepared_data.columns:
            prepared_data['value'] = pd.to_numeric(prepared_data['value'], errors='coerce').astype('float32')
        
        return prepared_data
    
    def enhance_data(self, standardized_data: Dict[str, pd.DataFrame], 
                    calculation_context: Any) -> Dict[str, pd.DataFrame]:
        """
        执行完整的数据增强流程
        """
        self.logger.info("开始执行数据增强处理")
        
        try:
            # 检查配置是否启用
            if not self.is_spatial_downscaling_enabled() and not self.is_temporal_downscaling_enabled():
                self.logger.warning("数据增强功能未启用，跳过处理")
                return {
                    'spatial_downscaled_energy': pd.DataFrame(),
                    'temporal_downscaled_energy': pd.DataFrame()
                }
            
            # 获取必要的数据
            provincial_data = standardized_data.get('energy_consumption', pd.DataFrame())
            city_data = standardized_data.get('energy_consumption2', pd.DataFrame())
            electricity_data = standardized_data.get('electricity', pd.DataFrame())
            
            # 准备电力数据
            electricity_data = self._prepare_electricity_data(electricity_data)
            
            spatial_result = pd.DataFrame()
            temporal_result = pd.DataFrame()
            
            # 步骤1: 空间降尺度（如果启用）
            if self.is_spatial_downscaling_enabled():
                spatial_result = self.spatial_downscaling(
                    provincial_data=provincial_data,
                    city_data=city_data,
                    city_electricity=electricity_data
                )
            else:
                self.logger.info("空间降尺度未启用，跳过")
            
            # 步骤2: 时间降尺度（如果启用）
            if self.is_temporal_downscaling_enabled():
                temporal_result = self.temporal_downscaling(
                    spatial_enhanced_data=spatial_result,
                    monthly_electricity=electricity_data
                )
            else:
                self.logger.info("时间降尺度未启用，跳过")
            
            enhanced_results = {
                'spatial_downscaled_energy': spatial_result,
                'temporal_downscaled_energy': temporal_result
            }
            
            # 记录结果统计
            spatial_count = len(spatial_result) if not spatial_result.empty else 0
            temporal_count = len(temporal_result) if not temporal_result.empty else 0
            
            self.logger.info(f"数据增强完成 - 空间降尺度: {spatial_count} 条记录, "
                           f"时间降尺度: {temporal_count} 条记录")
            
            return enhanced_results
            
        except Exception as e:
            self.logger.error(f"数据增强处理失败: {e}")
            raise
    
    def spatial_downscaling(self, provincial_data: pd.DataFrame, 
                           city_data: pd.DataFrame, 
                           city_electricity: pd.DataFrame) -> pd.DataFrame:
        """
        执行空间降尺度处理
        """
        self.logger.info("开始执行空间降尺度处理")
        
        if provincial_data.empty and city_data.empty:
            self.logger.warning("省级和地市能源数据都为空，跳过空间降尺度")
            return pd.DataFrame()
        
        try:
            # 1. 分析地市数据缺失情况
            missing_analysis = self._analyze_missing_city_data(city_data, city_electricity)
            
            # 2. 计算地市用电量权重
            city_weights = self._calculate_city_electricity_weights(city_electricity)
            
            # 3. 将省级数据按权重分配到地市
            provincial_downscaled = self._allocate_provincial_to_cities(
                provincial_data, city_weights, missing_analysis
            )
            
            # 4. 合并地市直接数据和省级下沉数据
            enhanced_data = self._merge_city_and_provincial_data(
                city_data, provincial_downscaled
            )
            
            self.logger.info(f"空间降尺度处理完成，生成 {len(enhanced_data)} 条记录")
            return enhanced_data
            
        except Exception as e:
            self.logger.error(f"空间降尺度处理失败: {e}")
            raise
    
    def temporal_downscaling(self, spatial_enhanced_data: pd.DataFrame, 
                            monthly_electricity: pd.DataFrame) -> pd.DataFrame:
        """
        执行时间降尺度处理
        """
        self.logger.info("开始执行时间降尺度处理")
        
        if spatial_enhanced_data.empty:
            self.logger.warning("空间降尺度数据为空，跳过时间降尺度")
            return pd.DataFrame()
        
        try:
            # 1. 准备年度能源消费数据和月度用电量指示器
            prepared_data = self._prepare_temporal_data(spatial_enhanced_data, monthly_electricity)
            
            if prepared_data.empty:
                self.logger.warning("时间降尺度数据准备失败")
                return pd.DataFrame()
            
            # 2. 使用Denton方法进行时间分解
            decomposed_data = self._apply_denton_decomposition(prepared_data)
            
            # 3. 约束调整确保月度数据总和等于年度数据
            adjusted_data = self._apply_temporal_constraints(decomposed_data, spatial_enhanced_data)
            
            # 4. 质量验证分解结果
            validated_data = self._validate_temporal_decomposition(adjusted_data, spatial_enhanced_data)
            
            self.logger.info(f"时间降尺度处理完成，生成 {len(validated_data)} 条记录")
            return validated_data
            
        except Exception as e:
            self.logger.error(f"时间降尺度处理失败: {e}")
            raise


    def _analyze_missing_city_data(self, city_data: pd.DataFrame, 
                                  city_electricity: pd.DataFrame) -> Dict[str, Any]:
        """
        分析地市数据缺失情况
        
        Args:
            city_data: 地市能源消费数据
            city_electricity: 地市用电量数据
            
        Returns:
            缺失分析结果字典
        """
        self.logger.info("分析地市数据缺失情况")
        
        missing_analysis = {
            'missing_cities': [],
            'missing_years': [],
            'missing_industries': [],
            'missing_energy_types': []
        }
        
        if city_electricity.empty:
            self.logger.warning("地市用电量数据为空")
            return missing_analysis
        
        # 获取用电量数据中的城市、年份、行业信息
        if 'area' in city_electricity.columns:
            available_cities = city_electricity['area'].unique().tolist()
        else:
            available_cities = []
            
        if 'year' in city_electricity.columns:
            available_years = city_electricity['year'].unique().tolist()
        else:
            available_years = []
            
        # 分析缺失情况
        if city_data.empty:
            missing_analysis['missing_cities'] = available_cities
            missing_analysis['missing_years'] = available_years
        else:
            # 比较城市数据与用电量数据，找出缺失的城市
            if 'area' in city_data.columns:
                city_data_cities = set(city_data['area'].unique())
                electricity_cities = set(available_cities)
                missing_analysis['missing_cities'] = list(electricity_cities - city_data_cities)
            
            if 'year' in city_data.columns:
                city_data_years = set(city_data['year'].unique())
                electricity_years = set(available_years)
                missing_analysis['missing_years'] = list(electricity_years - city_data_years)
        
        self.logger.info(f"缺失分析完成 - 缺失城市: {len(missing_analysis['missing_cities'])}, "
                        f"缺失年份: {len(missing_analysis['missing_years'])}")
        
        return missing_analysis
    
    def _calculate_city_electricity_weights(self, city_electricity: pd.DataFrame) -> pd.DataFrame:
        """
        计算地市用电量权重
        
        Args:
            city_electricity: 地市用电量数据
            
        Returns:
            包含权重的DataFrame
        """
        self.logger.info("计算地市用电量权重")
        
        if city_electricity.empty:
            self.logger.warning("地市用电量数据为空，无法计算权重")
            return pd.DataFrame()
        
        # 按年份和行业分组计算权重
        weight_columns = ['year']
        if 'industry' in city_electricity.columns:
            weight_columns.append('industry')
        
        # 计算每个城市在总用电量中的占比
        weights_df = city_electricity.copy()
        
        if len(weight_columns) > 0:
            # 按分组计算总用电量
            total_by_group = city_electricity.groupby(weight_columns)['value'].sum().reset_index()
            total_by_group.rename(columns={'value': 'total_value'}, inplace=True)
            
            # 合并总用电量数据
            weights_df = weights_df.merge(total_by_group, on=weight_columns, how='left')
            
            # 计算权重
            weights_df['allocation_weight'] = weights_df['value'] / weights_df['total_value']
            weights_df['allocation_weight'] = weights_df['allocation_weight'].fillna(0)
        else:
            # 如果没有分组列，直接按城市计算权重
            total_electricity = city_electricity['value'].sum()
            weights_df['allocation_weight'] = weights_df['value'] / total_electricity
        
        self.logger.info(f"权重计算完成，处理了 {len(weights_df)} 条记录")
        return weights_df
    
    def _allocate_provincial_to_cities(self, provincial_data: pd.DataFrame, 
                                     city_weights: pd.DataFrame, 
                                     missing_analysis: Dict[str, Any]) -> pd.DataFrame:
        """
        将省级数据按权重分配到地市 - 优化版本使用向量化操作
        
        Args:
            provincial_data: 省级能源消费数据
            city_weights: 地市用电量权重
            missing_analysis: 缺失分析结果
            
        Returns:
            省级下沉到地市的数据
        """
        self.logger.info("将省级数据按权重分配到地市")
        
        if provincial_data.empty or city_weights.empty:
            self.logger.warning("省级数据或城市权重为空，跳过分配")
            return pd.DataFrame()
        
        # 获取电力数据支持的年份范围
        available_years = set(city_weights['year'].unique()) if 'year' in city_weights.columns else set()
        self.logger.info(f"电力数据支持的年份: {sorted(available_years)}")
        
        # 过滤省级数据，只处理有电力数据支持的年份
        if available_years:
            provincial_data = provincial_data[provincial_data['year'].isin(available_years)]
            self.logger.info(f"过滤后省级数据: {len(provincial_data)} 条记录")
        
        if provincial_data.empty:
            self.logger.warning("过滤后省级数据为空")
            return pd.DataFrame()
        
        try:
            # 准备合并键 - 使用标准化列名
            merge_keys = ['year']
            
            # 检查省级数据的行业列（可能是standard_item）
            provincial_industry_col = None
            if 'standard_item' in provincial_data.columns:
                provincial_industry_col = 'standard_item'
            elif 'industry' in provincial_data.columns:
                provincial_industry_col = 'industry'
                
            # 检查权重数据的行业列
            weight_industry_col = None
            if 'standard_industry' in city_weights.columns:
                weight_industry_col = 'standard_industry'
            elif 'industry' in city_weights.columns:
                weight_industry_col = 'industry'
                
            # 如果两个都有行业列，需要统一列名后再合并
            # 这里不直接添加到merge_keys，而是在合并前重命名
            
            # 使用分块处理避免内存爆炸
            chunk_size = 100  # 每次处理100条省级记录
            allocated_chunks = []
            
            for i in range(0, len(provincial_data), chunk_size):
                chunk_provincial = provincial_data.iloc[i:i+chunk_size]
                
                # 获取这个chunk需要的权重数据
                chunk_years = set(chunk_provincial['year'].unique())
                
                # 获取行业数据（使用正确的列名）
                chunk_industries = set()
                if provincial_industry_col and provincial_industry_col in chunk_provincial.columns:
                    chunk_industries = set(chunk_provincial[provincial_industry_col].unique())
                
                # 筛选相关的权重数据
                weight_filter = city_weights['year'].isin(chunk_years)
                if chunk_industries and weight_industry_col and weight_industry_col in city_weights.columns:
                    weight_filter = weight_filter & city_weights[weight_industry_col].isin(chunk_industries)
                
                chunk_weights = city_weights[weight_filter]
                
                if chunk_weights.empty:
                    continue
                
                # 准备合并数据 - 统一列名
                chunk_provincial_for_merge = chunk_provincial.copy()
                chunk_weights_for_merge = chunk_weights.copy()
                
                # 如果行业列名不同，重命名以统一
                if provincial_industry_col and weight_industry_col and provincial_industry_col != weight_industry_col:
                    chunk_provincial_for_merge = chunk_provincial_for_merge.rename(columns={provincial_industry_col: 'industry'})
                    chunk_weights_for_merge = chunk_weights_for_merge.rename(columns={weight_industry_col: 'industry'})
                
                # 合并chunk数据
                merge_keys_final = ['year']
                if 'industry' in chunk_provincial_for_merge.columns and 'industry' in chunk_weights_for_merge.columns:
                    merge_keys_final.append('industry')
                    
                merged_chunk = chunk_provincial_for_merge.merge(
                    chunk_weights_for_merge,
                    on=merge_keys_final,
                    how='inner',
                    suffixes=('_provincial', '_weight')
                )
                
                if merged_chunk.empty:
                    continue
                
                # 计算分配值
                merged_chunk['allocated_value'] = merged_chunk['value'] * merged_chunk['allocation_weight']
                
                # 选择需要的列 - 使用正确的列名
                result_columns = ['year', 'allocated_value', 'allocation_weight']
                
                # 添加area列（从权重数据中获取）
                if 'area' in merged_chunk.columns:
                    result_columns.append('area')
                elif 'standard_area' in merged_chunk.columns:
                    result_columns.append('standard_area')
                
                # 添加energy_type列（从省级数据中获取）
                if 'energy_type' in merged_chunk.columns:
                    result_columns.append('energy_type')
                elif 'standard_energy_type' in merged_chunk.columns:
                    result_columns.append('standard_energy_type')
                
                # 添加industry列 - 使用标准化后的行业名称
                if 'industry' in merged_chunk.columns:
                    result_columns.append('industry')
                elif 'standard_item' in merged_chunk.columns:
                    result_columns.append('standard_item')
                
                chunk_result = merged_chunk[result_columns].copy()
                chunk_result = chunk_result.rename(columns={'allocated_value': 'value'})
                
                # 统一列名：将标准化列名重命名为标准列名
                if 'standard_item' in chunk_result.columns:
                    chunk_result = chunk_result.rename(columns={'standard_item': 'industry'})
                if 'standard_area' in chunk_result.columns:
                    chunk_result = chunk_result.rename(columns={'standard_area': 'area'})
                if 'standard_energy_type' in chunk_result.columns:
                    chunk_result = chunk_result.rename(columns={'standard_energy_type': 'energy_type'})
                
                chunk_result['data_source'] = 'provincial_downscaled'
                
                allocated_chunks.append(chunk_result)
                
                # 强制垃圾回收
                del merged_chunk, chunk_result
                import gc
                gc.collect()
            
            if allocated_chunks:
                allocated_df = pd.concat(allocated_chunks, ignore_index=True)
            else:
                allocated_df = pd.DataFrame()
            
            self.logger.info(f"分块处理完成，生成 {len(allocated_df)} 条记录")
            
            return allocated_df
            
        except Exception as e:
            self.logger.error(f"省级数据分配失败: {e}")
            # 如果向量化操作失败，回退到原始方法但限制数据量
            self.logger.warning("回退到原始分配方法（限制数据量）")
            return self._allocate_provincial_to_cities_fallback(provincial_data, city_weights, missing_analysis)
    
    def _allocate_provincial_to_cities_fallback(self, provincial_data: pd.DataFrame, 
                                              city_weights: pd.DataFrame, 
                                              missing_analysis: Dict[str, Any]) -> pd.DataFrame:
        """
        回退方法：限制数据量的原始分配方法
        """
        self.logger.info("使用回退方法进行省级数据分配")
        
        # 限制数据量以避免内存问题
        max_provincial_records = 100  # 最多处理100条省级记录
        max_weight_records = 1000    # 最多处理1000条权重记录
        
        if len(provincial_data) > max_provincial_records:
            self.logger.warning(f"省级数据量过大({len(provincial_data)})，限制为{max_provincial_records}条")
            provincial_data = provincial_data.head(max_provincial_records)
        
        if len(city_weights) > max_weight_records:
            self.logger.warning(f"权重数据量过大({len(city_weights)})，限制为{max_weight_records}条")
            city_weights = city_weights.head(max_weight_records)
        
        allocated_data = []
        
        # 遍历省级数据
        for _, provincial_row in provincial_data.iterrows():
            province_year = provincial_row.get('year')
            province_energy_type = provincial_row.get('energy_type', provincial_row.get('standard_energy_type'))
            province_value = provincial_row.get('value', 0)
            
            # 筛选对应年份和行业的城市权重
            weight_filter = city_weights['year'] == province_year
            if 'industry' in city_weights.columns and 'industry' in provincial_row:
                weight_filter = weight_filter & (city_weights['industry'] == provincial_row['industry'])
            
            relevant_weights = city_weights[weight_filter]
            
            if relevant_weights.empty:
                continue
            
            # 按权重分配省级数据到各个城市
            for _, weight_row in relevant_weights.iterrows():
                # 获取城市名称，优先使用标准化列名
                city_name = weight_row.get('standard_area', weight_row.get('area'))
                allocation_weight = weight_row.get('allocation_weight', 0)
                allocated_value = province_value * allocation_weight
                
                # 获取行业信息
                province_industry = provincial_row.get('industry', provincial_row.get('standard_item'))
                
                # 创建分配后的数据记录
                allocated_record = {
                    'year': province_year,
                    'area': city_name,
                    'energy_type': province_energy_type,
                    'industry': province_industry,
                    'value': allocated_value,
                    'data_source': 'provincial_downscaled',
                    'allocation_weight': allocation_weight
                }
                
                allocated_data.append(allocated_record)
        
        allocated_df = pd.DataFrame(allocated_data)
        self.logger.info(f"回退方法分配完成，生成 {len(allocated_df)} 条记录")
        
        return allocated_df
    
    def _merge_city_and_provincial_data(self, city_data: pd.DataFrame, 
                                      provincial_downscaled: pd.DataFrame) -> pd.DataFrame:
        """
        合并地市直接数据和省级下沉数据
        
        Args:
            city_data: 地市直接数据
            provincial_downscaled: 省级下沉数据
            
        Returns:
            合并后的增强数据
        """
        self.logger.info("合并地市直接数据和省级下沉数据")
        
        if city_data.empty and provincial_downscaled.empty:
            return pd.DataFrame()
        
        if city_data.empty:
            return provincial_downscaled
        
        if provincial_downscaled.empty:
            return city_data
        
        # 为数据添加来源标识
        city_data_copy = city_data.copy()
        city_data_copy['data_source'] = 'city_direct'
        city_data_copy['allocation_weight'] = 1.0
        
        # 确保所有数据都使用统一的列名
        # 将标准化列名重命名为标准列名
        if 'standard_item' in city_data_copy.columns:
            city_data_copy = city_data_copy.rename(columns={'standard_item': 'industry'})
        if 'standard_area' in city_data_copy.columns:
            city_data_copy = city_data_copy.rename(columns={'standard_area': 'area'})
        if 'standard_energy_type' in city_data_copy.columns:
            city_data_copy = city_data_copy.rename(columns={'standard_energy_type': 'energy_type'})
        
        # 合并数据
        merged_data = pd.concat([city_data_copy, provincial_downscaled], ignore_index=True)
        
        # 去重处理：如果同一城市、年份、行业、能源类型有多条记录，
        # 优先保留地市直接数据
        if not merged_data.empty:
            # 定义优先级：city_direct > provincial_downscaled
            merged_data['priority'] = merged_data['data_source'].map({
                'city_direct': 1,
                'provincial_downscaled': 2
            })
            
            # 按关键字段分组，保留优先级最高的记录
            key_columns = ['year', 'area', 'energy_type']
            if 'industry' in merged_data.columns:
                key_columns.append('industry')
            
            merged_data = merged_data.sort_values('priority').drop_duplicates(
                subset=key_columns, keep='first'
            ).drop(columns=['priority'])
        
        self.logger.info(f"数据合并完成，最终生成 {len(merged_data)} 条记录")
        
        return merged_data
    
    def _prepare_temporal_data(self, spatial_enhanced_data: pd.DataFrame, 
                              monthly_electricity: pd.DataFrame) -> pd.DataFrame:
        """
        准备时间降尺度所需的数据 - 优化版本，减少重复警告
        
        Args:
            spatial_enhanced_data: 空间降尺度后的年度数据
            monthly_electricity: 月度用电量指示器数据
            
        Returns:
            准备好的时间降尺度数据
        """
        self.logger.info("准备时间降尺度数据")
        
        if spatial_enhanced_data.empty:
            return pd.DataFrame()
        
        prepared_data = []
        
        # 获取有电力数据支持的城市列表，避免重复警告
        available_cities = set(monthly_electricity['area'].unique()) if not monthly_electricity.empty else set()
        missing_cities = set()
        
        # 按城市、行业、能源类型分组处理 - 使用标准化列名
        group_columns = []
        
        # 处理area列
        if 'area' in spatial_enhanced_data.columns:
            group_columns.append('area')
        elif 'standard_area' in spatial_enhanced_data.columns:
            group_columns.append('standard_area')
            
        # 处理energy_type列
        if 'energy_type' in spatial_enhanced_data.columns:
            group_columns.append('energy_type')
        elif 'standard_energy_type' in spatial_enhanced_data.columns:
            group_columns.append('standard_energy_type')
            
        # 处理industry列
        if 'industry' in spatial_enhanced_data.columns:
            group_columns.append('industry')
        elif 'standard_item' in spatial_enhanced_data.columns:
            group_columns.append('standard_item')
        elif 'standard_industry' in spatial_enhanced_data.columns:
            group_columns.append('standard_industry')
        
        for group_key, group_data in spatial_enhanced_data.groupby(group_columns):
            if isinstance(group_key, tuple):
                area, energy_type = group_key[0], group_key[1]
                industry = group_key[2] if len(group_key) > 2 else None
            else:
                area, energy_type = group_key, None
                industry = None
            
            # 检查城市是否有电力数据支持
            if area not in available_cities:
                if area not in missing_cities:
                    missing_cities.add(area)
                    self.logger.warning(f"城市 {area} 没有月度用电量数据，跳过时间降尺度")
                continue
            
            # 获取该组的年度数据
            annual_values = group_data.groupby('year')['value'].sum()
            
            # 获取对应的月度用电量指示器
            monthly_indicator = self._get_monthly_indicator(
                monthly_electricity, area, industry
            )
            
            if monthly_indicator.empty:
                self.logger.debug(f"城市 {area} 行业 {industry} 的月度用电量指示器为空")
                continue
            
            # 为每个年度数据准备分解
            for year, annual_value in annual_values.items():
                # 确保year是整数类型，避免类型比较错误
                year_int = int(year) if pd.notna(year) else None
                if year_int is None:
                    continue
                    
                year_monthly_indicator = monthly_indicator[
                    monthly_indicator['year'] == year_int
                ]
                
                if year_monthly_indicator.empty:
                    self.logger.debug(f"未找到 {year} 年城市 {area} 的月度用电量数据")
                    continue
                
                # 创建分解准备数据
                prepared_record = {
                    'area': area,
                    'energy_type': energy_type,
                    'year': year,
                    'annual_value': annual_value,
                    'monthly_indicator': year_monthly_indicator,
                    'industry': industry
                }
                
                prepared_data.append(prepared_record)
        
        self.logger.info(f"时间降尺度数据准备完成，处理了 {len(prepared_data)} 个分组")
        if missing_cities:
            self.logger.info(f"跳过了 {len(missing_cities)} 个没有电力数据的城市")
        
        return pd.DataFrame(prepared_data) if prepared_data else pd.DataFrame()
    
    def _get_monthly_indicator(self, monthly_electricity: pd.DataFrame, 
                              area: str, industry: Optional[str] = None) -> pd.DataFrame:
        """
        获取指定城市和行业的月度用电量指示器
        
        Args:
            monthly_electricity: 月度用电量数据
            area: 城市名称
            industry: 行业名称（可选）
            
        Returns:
            月度用电量指示器数据
        """
        if monthly_electricity.empty:
            return pd.DataFrame()
        
        # 筛选城市数据 - 处理标准化列名
        if 'standard_area' in monthly_electricity.columns:
            indicator = monthly_electricity[monthly_electricity['standard_area'] == area]
        elif 'area' in monthly_electricity.columns:
            indicator = monthly_electricity[monthly_electricity['area'] == area]
        else:
            return pd.DataFrame()
        
        # 如果有行业信息，直接使用标准化行业名称匹配
        if industry and 'standard_industry' in monthly_electricity.columns:
            # 直接使用标准化行业名称匹配
            indicator = indicator[indicator['standard_industry'] == industry]
        elif industry and 'industry' in monthly_electricity.columns:
            # 如果没有标准化行业列，使用原始行业名称匹配
            indicator = indicator[indicator['industry'] == industry]
        
        # 确保年份字段是整数类型
        if 'year' in indicator.columns:
            indicator = indicator.copy()
            indicator['year'] = indicator['year'].astype('int16')
        
        return indicator
    
    def _map_industry_to_standard(self, original_industry: str) -> Optional[str]:
        """
        将原始行业名称映射到标准化行业名称
        
        Args:
            original_industry: 原始行业名称
            
        Returns:
            标准化行业名称，如果找不到映射则返回None
        """
        # 简单的映射规则 - 可以根据实际数据调整
        industry_mapping = {
            # 制造业相关
            '酒、饮料及精制茶制造业': '工业',
            '采矿业': '工业',
            '金属制品业': '工业',
            '非金属矿物制品业': '工业',
            '食品制造业': '工业',
            '黑色金属冶炼和压延加工业': '工业',
            '专用设备制造业': '工业',
            '交通运输设备制造业': '工业',
            '仪器仪表制造业': '工业',
            '全部工业企业': '工业',
            '其他制造业': '工业',
            '农副食品加工业': '工业',
            '制造业': '工业',
            '化学原料及化学制品制造业': '工业',
            '化学纤维制造业': '工业',
            '医药制造业': '工业',
            '印刷业和记录媒介复制业': '工业',
            '塑料制品业': '工业',
            '家具制造业': '工业',
            '工艺品及其他制造业': '工业',
            '废弃资源综合利用业': '工业',
            '开采专业及辅助性活动': '工业',
            '文教、工美、体育和娱乐用品制造业': '工业',
            '有色金属冶炼及压延加工业': '工业',
            '有色金属矿采选业': '工业',
            '木材加工和木、竹、藤、棕、草制品业': '工业',
            '橡胶制品业': '工业',
            '水的生产和供应业': '工业',
            '汽车制造业': '工业',
            '烟草制品业': '工业',
            '煤炭开采和洗选业': '工业',
            '燃气生产和供应业': '工业',
            '电力、热力生产和供应业': '工业',
            '电气机械和器材制造业': '工业',
            '皮革、毛皮、羽毛及其制品和制鞋业': '工业',
            '石油、煤炭及其他燃料加工业': '工业',
            '石油加工、炼焦和核燃料加工业': '工业',
            '石油和天然气开采业': '工业',
            '纺织业': '工业',
            '纺织服装、服饰业': '工业',
            '计算机、通信和其他电子设备制造业': '工业',
            '轻工业': '工业',
            '通用设备制造业': '工业',
            '造纸和纸制品业': '工业',
            '酒、饮料和精制茶制造业': '工业',
            '重工业': '工业',
            '金属制品、机械和设备修理业': '工业',
            '铁路、船舶、航空航天和其他运输设备制造业': '工业',
            '非金属矿采选业': '工业',
            '黑色金属矿采选业': '工业',
            '化学原料及化学制品制造': '工业',
            '化学原料和化学制品制造业': '工业',
            '印刷业和记录媒介的复制': '工业',
            '印刷和记录媒介复制业': '工业',
            '橡胶和塑料制品业': '工业',
            '电力、热力、燃气及水生产和供应业': '工业',
            '电力、热力的生产和供应': '工业',
            '电力、煤气及水的生产等': '工业',
            '电气机械及器材制造业': '工业',
            '石油加工、炼焦及核燃料加工业': '工业',
            '石油加工炼焦及核燃料': '工业',
            '纺织服装、鞋、帽制造业': '工业',
            '通信设备、计算机及其他': '工业',
            '造纸及纸制品业': '工业',
            '有色金属冶炼及压延': '工业',
            '木材加工及木、竹、藤等': '工业',
            '黑色金属冶炼及压延': '工业',
            '黑色金属冶炼及压延加工业': '工业',
            '饮料制造业': '工业',
            '文教体育用品制造业': '工业',
            
            # 建筑业
            '建筑业': '建筑业',
            
            # 能源行业
            '能源行业': '能源行业',
        }
        
        return industry_mapping.get(original_industry)
    
    def _apply_denton_decomposition(self, prepared_data: pd.DataFrame) -> pd.DataFrame:
        """
        使用Denton方法进行时间分解
        
        Args:
            prepared_data: 准备好的时间降尺度数据
            
        Returns:
            分解后的月度数据
        """
        self.logger.info("应用Denton方法进行时间分解")
        
        if prepared_data.empty:
            return pd.DataFrame()
        
        decomposed_records = []
        
        for _, row in prepared_data.iterrows():
            area = row['area']
            energy_type = row['energy_type']
            year = row['year']
            annual_value = row['annual_value']
            monthly_indicator = row['monthly_indicator']
            industry = row.get('industry')
            
            try:
                # 使用简化的Denton方法进行分解
                monthly_values = self._simple_denton_decomposition(
                    annual_value, monthly_indicator
                )
                
                # 创建月度数据记录
                for month, monthly_value in monthly_values.items():
                    record = {
                        'year': year,
                        'month': month,
                        'area': area,
                        'energy_type': energy_type,
                        'value': monthly_value,
                        'data_source': 'temporal_decomposed',
                        'decomposition_method': 'denton',
                        'allocation_weight': monthly_value / annual_value if annual_value > 0 else 0
                    }
                    
                    if industry:
                        record['industry'] = industry
                    
                    decomposed_records.append(record)
                    
            except Exception as e:
                self.logger.error(f"Denton分解失败 - 城市: {area}, 年份: {year}, 错误: {e}")
                continue
        
        decomposed_df = pd.DataFrame(decomposed_records)
        self.logger.info(f"Denton分解完成，生成 {len(decomposed_df)} 条月度记录")
        
        return decomposed_df
    
    def _simple_denton_decomposition(self, annual_value: float, 
                                   monthly_indicator: pd.DataFrame) -> Dict[int, float]:
        """
        简化的Denton分解方法
        
        Args:
            annual_value: 年度总值
            monthly_indicator: 月度指示器数据
            
        Returns:
            月度分解值字典
        """
        if monthly_indicator.empty or pd.to_numeric(annual_value, errors='coerce') <= 0:
            return {}
        
        # 获取月度指示器值，确保是数值类型
        monthly_indicator_values = pd.to_numeric(monthly_indicator['value'], errors='coerce').values
        
        # 计算月度指示器的总和
        total_indicator = float(pd.to_numeric(monthly_indicator['value'], errors='coerce').sum())
        
        if total_indicator <= 0:
            # 如果指示器总和为0，平均分配
            monthly_count = len(monthly_indicator_values)
            return {i+1: annual_value / monthly_count for i in range(monthly_count)}
        
        # 按指示器比例分配年度值
        monthly_values = {}
        # 确保annual_value是数值类型
        annual_value_numeric = pd.to_numeric(annual_value, errors='coerce')
        
        for i, indicator_value in enumerate(monthly_indicator_values):
            month = i + 1
            monthly_value = annual_value_numeric * (indicator_value / total_indicator)
            monthly_values[month] = monthly_value
        
        return monthly_values
    
    def _apply_temporal_constraints(self, decomposed_data: pd.DataFrame, 
                                  spatial_enhanced_data: pd.DataFrame) -> pd.DataFrame:
        """
        应用时间约束，确保月度数据总和等于年度数据
        
        Args:
            decomposed_data: 分解后的月度数据
            spatial_enhanced_data: 原始年度数据
            
        Returns:
            约束调整后的月度数据
        """
        self.logger.info("应用时间约束调整")
        
        if decomposed_data.empty:
            return decomposed_data
        
        adjusted_data = decomposed_data.copy()
        
        # 按城市、年份、行业、能源类型分组进行约束调整
        group_columns = ['area', 'year', 'energy_type']
        if 'industry' in adjusted_data.columns:
            group_columns.append('industry')
        
        for group_key, group_data in adjusted_data.groupby(group_columns):
            if isinstance(group_key, tuple):
                area, year, energy_type = group_key[0], group_key[1], group_key[2]
                industry = group_key[3] if len(group_key) > 3 else None
            else:
                area, year, energy_type = group_key, None, None
                industry = None
            
            # 获取该组的月度总和
            monthly_sum = group_data['value'].sum()
            
            # 获取对应的年度值
            year_int = int(year) if pd.notna(year) else None
            if year_int is None:
                continue
                
            annual_filter = (spatial_enhanced_data['area'] == area) & \
                           (spatial_enhanced_data['year'] == year_int) & \
                           (spatial_enhanced_data['energy_type'] == energy_type)
            
            if industry and 'industry' in spatial_enhanced_data.columns:
                annual_filter = annual_filter & (spatial_enhanced_data['industry'] == industry)
            
            annual_value = spatial_enhanced_data[annual_filter]['value'].sum()
            
            if annual_value > 0 and monthly_sum > 0:
                # 计算调整因子
                adjustment_factor = annual_value / monthly_sum
                
                # 应用调整因子
                group_indices = group_data.index
                adjusted_data.loc[group_indices, 'value'] *= adjustment_factor
        
        self.logger.info("时间约束调整完成")
        return adjusted_data
    
    def _validate_temporal_decomposition(self, adjusted_data: pd.DataFrame, 
                                       spatial_enhanced_data: pd.DataFrame) -> pd.DataFrame:
        """
        验证时间分解结果的质量
        
        Args:
            adjusted_data: 调整后的月度数据
            spatial_enhanced_data: 原始年度数据
            
        Returns:
            验证后的月度数据
        """
        self.logger.info("验证时间分解结果质量")
        
        if adjusted_data.empty:
            return adjusted_data
        
        # 进行基本的质量检查
        validation_results = []
        
        # 检查月度数据总和是否等于年度数据
        group_columns = ['area', 'year', 'energy_type']
        if 'industry' in adjusted_data.columns:
            group_columns.append('industry')
        
        for group_key, group_data in adjusted_data.groupby(group_columns):
            monthly_sum = group_data['value'].sum()
            
            # 获取对应的年度值进行比较
            if isinstance(group_key, tuple):
                area, year, energy_type = group_key[0], group_key[1], group_key[2]
                industry = group_key[3] if len(group_key) > 3 else None
            else:
                area, year, energy_type = group_key, None, None
                industry = None
            
            annual_filter = (spatial_enhanced_data['area'] == area) & \
                           (spatial_enhanced_data['year'] == year) & \
                           (spatial_enhanced_data['energy_type'] == energy_type)
            
            if industry and 'industry' in spatial_enhanced_data.columns:
                annual_filter = annual_filter & (spatial_enhanced_data['industry'] == industry)
            
            annual_value = spatial_enhanced_data[annual_filter]['value'].sum()
            
            # 计算误差
            if annual_value > 0:
                error_rate = abs(monthly_sum - annual_value) / annual_value
                validation_results.append({
                    'group': group_key,
                    'annual_value': annual_value,
                    'monthly_sum': monthly_sum,
                    'error_rate': error_rate,
                    'is_valid': error_rate < 0.01  # 1%误差容忍度
                })
        
        # 记录验证结果
        valid_count = sum(1 for result in validation_results if result['is_valid'])
        total_count = len(validation_results)
        
        self.logger.info(f"时间分解质量验证完成 - 有效: {valid_count}/{total_count}")
        
        if validation_results:
            avg_error = sum(result['error_rate'] for result in validation_results) / len(validation_results)
            self.logger.info(f"平均误差率: {avg_error:.4f}")
        
        return adjusted_data
    
    def _load_spatial_config(self) -> Dict[str, Any]:
        """加载空间降尺度配置"""
        try:
            config = self.config_reader._config  # 直接访问内部配置
            spatial_config = config.get('data_enhancement', {}).get('spatial_downscaling', {})
            
            # 设置默认值
            default_spatial_config = {
                'enabled': True,
                'weight_method': 'electricity_proportion',
                'merge_strategy': 'city_priority',
                'validation_rules': ['total_consistency', 'industry_consistency'],
                'min_weight_threshold': 0.01,
                'max_allocation_error': 0.05
            }
            
            # 合并配置
            for key, value in default_spatial_config.items():
                if key not in spatial_config:
                    spatial_config[key] = value
            
            self.logger.info(f"空间降尺度配置加载完成: {spatial_config}")
            return spatial_config
            
        except Exception as e:
            self.logger.warning(f"加载空间降尺度配置失败，使用默认配置: {e}")
            return {
                'enabled': True,
                'weight_method': 'electricity_proportion',
                'merge_strategy': 'city_priority',
                'validation_rules': ['total_consistency'],
                'min_weight_threshold': 0.01,
                'max_allocation_error': 0.05
            }
    
    def _load_temporal_config(self) -> Dict[str, Any]:
        """加载时间降尺度配置"""
        try:
            config = self.config_reader._config  # 直接访问内部配置
            temporal_config = config.get('data_enhancement', {}).get('temporal_downscaling', {})
            
            # 设置默认值
            default_temporal_config = {
                'enabled': True,
                'method': 'denton',
                'indicator_source': 'monthly_electricity',
                'validation_rules': ['annual_sum_consistency', 'seasonal_pattern_check'],
                'error_tolerance': 0.01,
                'min_monthly_value': 0.001
            }
            
            # 合并配置
            for key, value in default_temporal_config.items():
                if key not in temporal_config:
                    temporal_config[key] = value
            
            self.logger.info(f"时间降尺度配置加载完成: {temporal_config}")
            return temporal_config
            
        except Exception as e:
            self.logger.warning(f"加载时间降尺度配置失败，使用默认配置: {e}")
            return {
                'enabled': True,
                'method': 'denton',
                'indicator_source': 'monthly_electricity',
                'validation_rules': ['annual_sum_consistency'],
                'error_tolerance': 0.01,
                'min_monthly_value': 0.001
            }
    
    def is_spatial_downscaling_enabled(self) -> bool:
        """检查空间降尺度是否启用"""
        return self.spatial_config.get('enabled', True)
    
    def is_temporal_downscaling_enabled(self) -> bool:
        """检查时间降尺度是否启用"""
        return self.temporal_config.get('enabled', True)
    
    def get_spatial_weight_method(self) -> str:
        """获取空间降尺度权重计算方法"""
        return self.spatial_config.get('weight_method', 'electricity_proportion')
    
    def get_temporal_method(self) -> str:
        """获取时间降尺度分解方法"""
        return self.temporal_config.get('method', 'denton')


def get_data_enhancement_service() -> DataEnhancementService:
    """获取数据增强服务实例"""
    return DataEnhancementServiceImpl()
