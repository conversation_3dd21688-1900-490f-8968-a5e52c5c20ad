"""
标准化编排器
负责管理三种核心功能的执行顺序和依赖关系
"""

from typing import Dict, List
import pandas as pd
import logging
from ecam_calculator.domain.service.standardization_functions import Column<PERSON><PERSON><PERSON><PERSON><PERSON>, CrossColumnMapper
from ecam_calculator.domain.model.value_objects import RawData
from ecam_calculator.domain.service.standardization_errors import ConfigurationError

class StandardizationOrchestrator:
    """标准化编排器"""
    
    def __init__(self, config_reader):
        self.config_reader = config_reader
        self.logger = logging.getLogger(__name__)
        self.column_standardizers = self._load_column_standardizers()
        self.cross_column_mappers = self._load_cross_column_mappers()
    
    def _load_column_standardizers(self) -> Dict[str, List[ColumnStandardizer]]:
        """加载列标准化器"""
        try:
            # 验证配置完整性
            if not self.config_reader.validate_standardization_config():
                raise ConfigurationError("标准化配置不完整或格式错误")
            
            mappings = self.config_reader.get_standardization_mappings()
            column_configs = mappings.get('column_standardizations', {})
            
            standardizers = {}
            for category in ['regions', 'industries', 'energy_types']:
                configs = column_configs.get(category, [])
                standardizers[category] = [
                    ColumnStandardizer(config) for config in configs
                ]
            
            self.logger.info(f"加载了 {sum(len(s) for s in standardizers.values())} 个列标准化器")
            return standardizers
            
        except Exception as e:
            if isinstance(e, ConfigurationError):
                raise
            raise ConfigurationError(f"加载列标准化器失败: {e}")
    
    def _load_cross_column_mappers(self) -> List[CrossColumnMapper]:
        """加载跨列映射器"""
        try:
            # 验证配置完整性
            if not self.config_reader.validate_standardization_config():
                raise ConfigurationError("标准化配置不完整或格式错误")
            
            mappings = self.config_reader.get_standardization_mappings()
            cross_configs = mappings.get('cross_column_mappings', {})
            
            mappers = []
            for mapping_name, config in cross_configs.items():
                mappers.append(CrossColumnMapper(config))
            
            self.logger.info(f"加载了 {len(mappers)} 个跨列映射器")
            return mappers
            
        except Exception as e:
            if isinstance(e, ConfigurationError):
                raise
            raise ConfigurationError(f"加载跨列映射器失败: {e}")
    
    def standardize(self, raw_data: RawData) -> pd.DataFrame:
        """执行标准化流程（兼容旧接口）"""
        df = raw_data.data.copy()
        return self.standardize_dataframe(df, raw_data.table_name)
    
    def standardize_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """执行标准化流程（直接处理DataFrame）"""
        if df.empty:
            self.logger.warning(f"表 '{table_name}' 为空，跳过标准化")
            return df
        
        df = df.copy()
        self.logger.info(f"开始标准化表: {table_name}")
        
        # 特殊处理：电力数据需要先创建year列
        if table_name == 'ecam_in_m_pro_ind_ele_off' and 'month' in df.columns and 'year' not in df.columns:
            df['year'] = pd.to_datetime(df['month']).dt.year
        
        try:
            # 步骤1：列内标准化（独立执行，无依赖）
            for category, standardizers in self.column_standardizers.items():
                self.logger.debug(f"执行 {category} 标准化")
                for standardizer in standardizers:
                    df = standardizer.apply(df, table_name)
            
            # 步骤2：跨列映射（依赖列内标准化结果）
            for mapper in self.cross_column_mappers:
                self.logger.debug(f"执行跨列映射: {mapper.source_column} -> {mapper.target_column}")
                df = mapper.apply(df, table_name)
            
            self.logger.info(f"标准化完成，输出 {len(df)} 行数据")
            return df
            
        except Exception as e:
            self.logger.error(f"标准化过程发生错误: {e}")
            # 返回原始数据，不中断流程
            return df
