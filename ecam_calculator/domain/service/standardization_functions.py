"""
标准化功能实现
基于三种核心功能的标准化架构
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any
import pandas as pd
import logging

class StandardizationFunction(ABC):
    """标准化功能基类"""
    
    @abstractmethod
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """应用标准化功能"""
        pass

class ColumnStandardizer(StandardizationFunction):
    """功能1+2：列内映射 + 层次关系"""
    
    def __init__(self, config: Dict):
        self.name = config.get('name', '')
        self.parent = config.get('parent')
        self.column_mappings = config.get('column_mappings', {})
        self.logger = logging.getLogger(__name__)
    
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """应用列标准化功能"""
        table_config = self.column_mappings.get(table_name, {})
        column = table_config.get('column', '')
        keywords = table_config.get('keywords', [])
        
        if column in df.columns:
            # 功能1：列内映射 - 只对匹配的行进行标准化
            mask = df[column].apply(lambda x: self._should_map(x, keywords))
            if mask.any():
                df.loc[mask, f'standard_{column}'] = self.name
                
                # 功能2：层次关系 - 只对匹配的行设置层次关系
                if self.parent:
                    df.loc[mask, f'macro_{column}'] = self.parent
                else:
                    df.loc[mask, f'macro_{column}'] = self.name
            
            self.logger.debug(f"列标准化完成: {column} -> {self.name}")
        
        return df
    
    def _should_map(self, value: Any, keywords: List[str]) -> bool:
        """判断是否应该映射到当前标准化名称"""
        if pd.isna(value):
            return False
        
        value_str = str(value)
        
        # 精确匹配
        if value_str in keywords:
            return True
        
        # 模糊匹配
        for keyword in keywords:
            if keyword in value_str:
                return True
        
        return False
    
    def _map_to_standard(self, value: Any, keywords: List[str]) -> str:
        """将原始值映射到标准化名称"""
        if pd.isna(value):
            return str(value)
        
        value_str = str(value)
        
        # 精确匹配
        if value_str in keywords:
            return self.name
        
        # 模糊匹配
        for keyword in keywords:
            if keyword in value_str:
                return self.name
        
        return value_str

class CrossColumnMapper(StandardizationFunction):
    """功能3：跨列信息映射"""
    
    def __init__(self, config: Dict):
        self.source_column = config.get('source_column', '')
        self.target_column = config.get('target_column', '')
        self.mappings = config.get('mappings', {})
        self.logger = logging.getLogger(__name__)
    
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """应用跨列映射功能"""
        if self.source_column in df.columns:
            # 基于源列推导目标列
            df[self.target_column] = df[self.source_column].apply(
                lambda x: self.mappings.get(x, '未知')
            )
            
            self.logger.debug(f"跨列映射完成: {self.source_column} -> {self.target_column}")
        
        return df
