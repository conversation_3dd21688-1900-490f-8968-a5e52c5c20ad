"""
标准化错误类型定义
遵循项目现有错误处理模式，不引入新的外部依赖
"""

from typing import Dict, Any, List, Optional


class StandardizationError(Exception):
    """标准化错误基类"""
    
    def __init__(self, message: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.context = context or {}
        self.message = message
    
    def __str__(self):
        return f"标准化错误: {self.message}"


class DataConversionError(StandardizationError):
    """数据转换错误"""
    
    def __init__(self, message: str, row_data: Optional[Dict[str, Any]] = None, table_name: Optional[str] = None):
        context = {
            'row_data': row_data,
            'table_name': table_name
        }
        super().__init__(message, context)


class ConfigurationError(StandardizationError):
    """配置错误"""
    
    def __init__(self, message: str, config_path: Optional[str] = None, config_data: Optional[Dict[str, Any]] = None):
        context = {
            'config_path': config_path,
            'config_data': config_data
        }
        super().__init__(message, context)


class DataQualityError(StandardizationError):
    """数据质量错误"""
    
    def __init__(self, message: str, data_source: Optional[str] = None, quality_issues: Optional[List[str]] = None):
        context = {
            'data_source': data_source,
            'quality_issues': quality_issues or []
        }
        super().__init__(message, context)


class StandardizationBatchError(StandardizationError):
    """批量标准化错误"""
    
    def __init__(self, message: str, failed_sources: Optional[List[tuple]] = None):
        context = {
            'failed_sources': failed_sources or []
        }
        super().__init__(message, context)
