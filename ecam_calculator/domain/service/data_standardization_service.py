"""
简化的数据标准化服务
直接处理DataFrame，不做任何包装
"""

import logging
from typing import List, Dict, Any, Optional
import pandas as pd

from ecam_calculator.domain.model.value_objects import StandardizedData, StandardizedDataType
from ecam_calculator.infrastructure.config_reader import get_config_reader
from ecam_calculator.domain.service.standardization_orchestrator import StandardizationOrchestrator


class DataStandardizationService:
    """简化的数据标准化服务 - 直接处理DataFrame"""

    def __init__(self, config_reader=None):
        self.logger = logging.getLogger(__name__)
        self.config_reader = config_reader or get_config_reader()
        
        # 初始化标准化编排器
        self.orchestrator = StandardizationOrchestrator(self.config_reader)
        
        self.logger.info("简化数据标准化服务初始化完成")

    def standardize_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """标准化单个DataFrame，并执行过滤和聚合"""
        if df.empty:
            self.logger.warning(f"表 '{table_name}' 为空，跳过标准化")
            return df
        
        try:
            # 特殊处理：能源因子表
            if table_name == 'ecam_in_energy_factor':
                return self._standardize_energy_factor_table(df)
            
            # 步骤1: 使用编排器执行标准化
            standardized_df = self.orchestrator.standardize_dataframe(df, table_name)
            self.logger.info(f"表 '{table_name}' 标准化完成，输出 {len(standardized_df)} 行数据")
            
            # 步骤2: 过滤成功映射的记录
            filtered_df = self._filter_successfully_mapped_records(standardized_df, table_name)
            self.logger.info(f"表 '{table_name}' 过滤后，输出 {len(filtered_df)} 行数据")
            
            # 步骤3: 聚合数据
            aggregated_df = self._aggregate_data(filtered_df, table_name)
            self.logger.info(f"表 '{table_name}' 聚合后，输出 {len(aggregated_df)} 行数据")
            
            return aggregated_df
            
        except Exception as e:
            self.logger.error(f"表 '{table_name}' 标准化失败: {e}")
            return df  # 返回原始数据，不中断流程
    
    def standardize_all(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """标准化所有DataFrame"""
        self.logger.info(f"开始标准化 {len(data_dict)} 个数据源")
        
        standardized_results = {}
        failed_sources = []
        
        for source_name, df in data_dict.items():
            try:
                # 确定表名（从source_name映射或直接使用）
                table_name = self._get_table_name_from_source(source_name)
                
                standardized_df = self.standardize_dataframe(df, table_name)
                standardized_results[source_name] = standardized_df
                self.logger.info(f"数据源 '{source_name}' 标准化完成，产出 {len(standardized_df)} 行数据")
                
            except Exception as e:
                failed_sources.append((source_name, str(e)))
                self.logger.error(f"数据源 '{source_name}' 标准化失败: {e}")
                standardized_results[source_name] = pd.DataFrame()
        
        # 如果有失败的数据源，记录但不中断
        if failed_sources:
            self.logger.warning(f"标准化过程中 {len(failed_sources)} 个数据源失败: {failed_sources}")
        
        return standardized_results
    
    def convert_to_standardized_objects(self, standardized_df: pd.DataFrame, table_name: str) -> List[StandardizedData]:
        """将标准化后的DataFrame转换为StandardizedData对象列表"""
        if standardized_df.empty:
            return []
        
        standardized_list = []
        
        for index, row in standardized_df.iterrows():
            try:
                # 处理province字段，确保不为NaN
                province_value = row.get('standard_province', row.get('province', ''))
                if pd.isna(province_value):
                    province_value = ''
                
                # 处理city字段，确保不为NaN
                city_value = row.get('standard_area', '')
                if pd.isna(city_value):
                    city_value = ''
                
                # 验证必要字段
                if 'year' not in row or pd.isna(row['year']):
                    self.logger.warning(f"年份字段缺失或为空，行索引: {index}")
                    continue
                
                standardized_data = StandardizedData(
                    data_type=self._determine_data_type(table_name),
                    year=int(row['year']),
                    province=province_value,
                    city=city_value,
                    source_table=table_name,
                    attributes=self._extract_attributes(row, table_name)
                )
                standardized_list.append(standardized_data)
                
            except Exception as e:
                self.logger.error(f"转换标准化数据失败，行 {index}: {e}")
                continue
        
        return standardized_list
    
    def _get_table_name_from_source(self, source_name: str) -> str:
        """从数据源名称获取表名"""
        # 简单的映射规则
        mapping = {
            '排放因子': 'ecam_in_energy_factor',
            '能源消费': 'ecam_in_y_pro_ind_ene_off',
            '用电量': 'ecam_in_m_pro_ind_ele_off',
            'GDP数据': 'fct_y_gdp',
            '能耗强度': 'fct_y_all_ene_intsty',
            '工业产品产量': 'fct_y_prd_output'
        }
        return mapping.get(source_name, source_name)
    
    def _determine_data_type(self, table_name: str) -> StandardizedDataType:
        """确定数据类型"""
        table_config = self.config_reader.get_table_config(table_name)
        if table_config:
            table_type = table_config.get('table_type', '')
            if table_type == 'energy':
                return StandardizedDataType.ENERGY
            elif table_type == 'product':
                return StandardizedDataType.PRODUCT
            elif table_type == 'economic':
                return StandardizedDataType.ECONOMIC
            elif table_type == 'electricity':
                return StandardizedDataType.ENERGY
        return StandardizedDataType.ENERGY  # 默认
    
    def _extract_attributes(self, row: pd.Series, table_name: str) -> Dict[str, Any]:
        """提取属性"""
        attributes = {}
        
        # 基础属性
        attributes['value'] = row.get('record', row.get('consumption', row.get('value', 0)))
        attributes['unit'] = row.get('unit', '')
        
        # 标准化后的属性
        if 'standard_item' in row:
            attributes['standard_industry'] = row['standard_item']
        if 'macro_item' in row:
            attributes['macro_industry'] = row['macro_item']
        if 'standard_energy_type' in row:
            attributes['standard_energy_type'] = row['standard_energy_type']
        if 'macro_energy_type' in row:
            attributes['macro_energy_type'] = row['macro_energy_type']
        if 'industry' in row:
            attributes['industry'] = row['industry']
        
        # 原始属性
        attributes['original_value'] = row.get('record', row.get('consumption', row.get('value', 0)))
        attributes['original_unit'] = row.get('unit', '')
        
        # 根据表类型添加特定属性
        table_config = self.config_reader.get_table_config(table_name)
        if table_config:
            table_type = table_config.get('table_type', '')
            if table_type == 'product':
                attributes['product_name'] = row.get('product_name', '')
                attributes['indicator_category'] = 'industrial_product'
            elif table_type == 'economic':
                attributes['indicator'] = row.get('indicator', '')
                attributes['indicator_category'] = row.get('indicator_category', '')
            else:
                attributes['indicator_category'] = 'energy_consumption'
        
        return attributes

    def _filter_successfully_mapped_records(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """
        过滤成功映射了地区、行业、能源品种的记录
        
        Args:
            df: 标准化后的DataFrame
            table_name: 表名
            
        Returns:
            过滤后的DataFrame
        """
        if df.empty:
            return df
        
        # 根据表名确定需要检查的标准化列
        required_columns = self._get_required_standardized_columns(table_name)
        
        # 检查所有必需的标准化列都不为空
        mask = pd.Series([True] * len(df), index=df.index)
        for col in required_columns:
            if col in df.columns:
                # 检查列不为空且不为NaN
                mask = mask & (df[col].notna()) & (df[col] != '') & (df[col] != 'NaN')
            else:
                self.logger.warning(f"表 '{table_name}' 缺少必需的标准化列: {col}")
                return pd.DataFrame()  # 返回空DataFrame
        
        filtered_df = df[mask].copy()
        
        # 记录过滤统计
        total_records = len(df)
        filtered_records = len(filtered_df)
        self.logger.info(f"表 '{table_name}' 过滤统计: 总计 {total_records} 行，成功映射 {filtered_records} 行，过滤率 {filtered_records/total_records*100:.1f}%")
        
        return filtered_df

    def _get_required_standardized_columns(self, table_name: str) -> list:
        """
        根据表名获取必需的标准化列
        
        Args:
            table_name: 表名
            
        Returns:
            必需的标准化列列表
        """
        # 根据表名确定必需的标准化列
        if table_name == 'ecam_in_y_pro_ind_ene_off':
            return ['standard_province', 'standard_item', 'standard_energy_type']
        elif table_name == 'ecam_in_y_pro_ind_ene2_off':
            return ['standard_area', 'standard_industry', 'standard_energy_type']
        elif table_name == 'ecam_in_m_pro_ind_ele_off':
            return ['standard_area', 'standard_industry']
        elif table_name == 'fct_y_prd_output':
            return ['industry']  # 产品产量表通过跨列映射得到industry
        else:
            # 其他表可能不需要特定的标准化列
            return []

    def _aggregate_data(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """
        基于标准化后的维度聚合数据
        
        Args:
            df: 过滤后的DataFrame
            table_name: 表名
            
        Returns:
            聚合后的DataFrame
        """
        if df.empty:
            return df
        
        # 根据表名确定聚合维度和值列
        aggregation_config = self._get_aggregation_config(table_name)
        
        if not aggregation_config:
            self.logger.warning(f"表 '{table_name}' 没有配置聚合规则，返回原始数据")
            return df
        
        group_columns = aggregation_config['group_columns']
        value_columns = aggregation_config['value_columns']
        
        # 检查必需的列是否存在
        missing_columns = [col for col in group_columns + value_columns if col not in df.columns]
        if missing_columns:
            self.logger.warning(f"表 '{table_name}' 缺少聚合必需的列: {missing_columns}")
            return df
        
        try:
            # 进一步过滤聚合键列，确保没有空值
            group_mask = True
            for col in group_columns:
                group_mask = group_mask & (df[col].notna()) & (df[col] != '') & (df[col] != 'NaN')
            
            final_df = df[group_mask]
            
            if len(final_df) == 0:
                self.logger.warning(f"表 '{table_name}' 聚合键列过滤后无数据")
                return pd.DataFrame()
            
            # 执行聚合
            aggregated_df = final_df.groupby(group_columns, as_index=False)[value_columns].sum()
            
            # 记录聚合统计
            original_records = len(df)
            aggregated_records = len(aggregated_df)
            self.logger.info(f"表 '{table_name}' 聚合统计: 原始 {original_records} 行，聚合后 {aggregated_records} 行，聚合率 {aggregated_records/original_records*100:.1f}%")
            
            return aggregated_df
            
        except Exception as e:
            self.logger.error(f"表 '{table_name}' 聚合失败: {e}")
            return df

    def _get_aggregation_config(self, table_name: str) -> Optional[Dict[str, Any]]:
        """
        根据表名获取聚合配置
        
        Args:
            table_name: 表名
            
        Returns:
            聚合配置字典
        """
        # 根据表名确定聚合配置
        if table_name == 'ecam_in_y_pro_ind_ene_off':
            return {
                'group_columns': ['year', 'standard_province', 'standard_item', 'standard_energy_type'],
                'value_columns': ['value']
            }
        elif table_name == 'ecam_in_y_pro_ind_ene2_off':
            return {
                'group_columns': ['year', 'standard_area', 'standard_industry', 'standard_energy_type'],
                'value_columns': ['value']
            }
        elif table_name == 'ecam_in_m_pro_ind_ele_off':
            return {
                'group_columns': ['month', 'year', 'standard_area', 'standard_industry'],
                'value_columns': ['electricity']
            }
        elif table_name == 'fct_y_prd_output':
            return {
                'group_columns': ['year', 'area', 'industry'],
                'value_columns': ['record']
            }
        else:
            # 其他表可能不需要聚合
            return None

    def _standardize_energy_factor_table(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        特殊处理能源因子表
        提取无空间无时间状态的能源因子，作为DataFrame传递给能源转换服务
        
        Args:
            df: 原始能源因子表DataFrame
            
        Returns:
            标准化后的能源因子DataFrame
        """
        if df.empty:
            self.logger.warning("能源因子表为空，跳过标准化")
            return df
        
        try:
            # 1. 数据清洗和验证
            cleaned_df = df.copy()
            
            # 处理年份列（从datetime转换为年份整数）
            if 'year' in cleaned_df.columns:
                cleaned_df['year'] = pd.to_datetime(cleaned_df['year']).dt.year
            
            # 确保必需的列存在
            required_columns = ['year', 'energy_type', 'factor', 'unit', 'method', 'source', 'province', 'industry']
            missing_columns = [col for col in required_columns if col not in cleaned_df.columns]
            if missing_columns:
                self.logger.warning(f"能源因子表缺少必需列: {missing_columns}")
                return pd.DataFrame()
            
            # 2. 数据验证
            # 检查factor列是否为数值
            cleaned_df['factor'] = pd.to_numeric(cleaned_df['factor'], errors='coerce')
            invalid_factors = cleaned_df['factor'].isna().sum()
            if invalid_factors > 0:
                self.logger.warning(f"发现 {invalid_factors} 个无效的因子值")
                cleaned_df = cleaned_df.dropna(subset=['factor'])
            
            # 3. 提取无空间无时间状态的能源因子
            # 选择全国通用、最新的因子数据
            national_factors = cleaned_df[
                (cleaned_df['province'] == '全国') & 
                (cleaned_df['industry'] == '通用')
            ].copy()
            
            if national_factors.empty:
                self.logger.warning("未找到全国通用的能源因子数据")
                return pd.DataFrame()
            
            # 4. 按能源品种分组，选择最新的因子
            latest_factors = national_factors.sort_values(['energy_type', 'year']).groupby('energy_type').tail(1)
            
            # 5. 数据统计和日志
            total_records = len(df)
            national_records = len(national_factors)
            final_records = len(latest_factors)
            
            self.logger.info(f"能源因子表标准化: 原始 {total_records} 行，全国通用 {national_records} 行，最终 {final_records} 行")
            
            # 6. 统计信息
            energy_type_count = latest_factors['energy_type'].nunique()
            method_count = latest_factors['method'].nunique()
            
            self.logger.info(f"最终能源因子统计: {energy_type_count} 个能源品种，{method_count} 种方法")
            
            # 7. 返回标准化后的DataFrame
            # 只去掉时间和空间维度，保留其他属性列
            result_columns = ['energy_type', 'factor', 'unit', 'method', 'source', 'industry']
            result_df = latest_factors[result_columns].copy()
            
            # 确保数据类型正确
            result_df['factor'] = result_df['factor'].astype(float)
            
            self.logger.info(f"能源因子表标准化完成，输出 {len(result_df)} 行无时间无空间状态的能源因子")
            return result_df
            
        except Exception as e:
            self.logger.error(f"能源因子表标准化失败: {e}")
            return df  # 返回原始数据，不中断流程
