"""
能源转换服务
负责将标准化后的能源数据转换为标准煤当量并加总
"""

import pandas as pd
import logging
from typing import List, Dict, Any, Optional
from ecam_calculator.domain.model.value_objects import StandardizedData
from ecam_calculator.infrastructure.config_reader import get_config_reader


class EnergyConversionService:
    """能源转换服务 - 业务逻辑服务"""
    
    def __init__(self, config_reader=None):
        self.config_reader = config_reader or get_config_reader()
        self.logger = logging.getLogger(__name__)
        self.logger.info("能源转换服务初始化完成")
    
    def convert_to_standard_coal(self, energy_data: pd.DataFrame, conversion_factors: pd.DataFrame) -> pd.DataFrame:
        """将标准化后的能源数据转换为标准煤当量"""
        if energy_data.empty:
            self.logger.warning("输入的能源数据为空，跳过转换")
            return energy_data
        
        if conversion_factors.empty:
            self.logger.warning("转换因子数据为空，使用默认转换")
            return self._apply_default_conversion(energy_data)
        
        # 处理省级能源消费数据和地市工业能源消费数据
        # 通过检查数据源特征来识别数据类型
        if 'item' in energy_data.columns:
            # 这是省级能源消费数据（ecam_in_y_pro_ind_ene_off）
            filtered_data = energy_data.copy()
            self.logger.info(f"处理省级能源消费数据，原始记录数: {len(filtered_data)}")
        elif 'industry' in energy_data.columns:
            # 这是地市工业能源消费数据（ecam_in_y_pro_ind_ene2_off）
            filtered_data = energy_data.copy()
            self.logger.info(f"处理地市工业能源消费数据，原始记录数: {len(filtered_data)}")
        else:
            # 其他数据源，暂时跳过
            self.logger.warning("检测到未知数据源，跳过转换")
            return energy_data

        # 1. 确定能源类型列名（处理标准化后的列名）
        energy_type_column = 'standard_energy_type' if 'standard_energy_type' in filtered_data.columns else 'energy_type'
        
        # 2. 创建转换因子查找字典
        factor_lookup = self._create_factor_lookup(conversion_factors)
        
        # 3. 应用转换
        result_df = filtered_data.copy()
        
        # 确保value列是数值类型
        result_df['value'] = pd.to_numeric(result_df['value'], errors='coerce')
        
        # 使用向量化操作进行转换
        result_df['conversion_factor_used'] = result_df[energy_type_column].map(
            lambda x: factor_lookup.get(x, 1.0)
        )
        
        # 计算标准煤当量（万吨标准煤）
        result_df['standard_coal_equivalent'] = result_df['value'] * result_df['conversion_factor_used']
        
        # 添加转换信息
        result_df['conversion_method'] = '标准化因子转换'
        result_df['conversion_source'] = '标准化因子表'
        result_df['standard_unit'] = '万吨标准煤'
        
        self.logger.info(f"能源转换完成，处理了 {len(result_df)} 行数据")
        self.logger.info(f"标准煤当量总和: {result_df['standard_coal_equivalent'].sum():.2f} 万吨标准煤")
        
        return result_df
    
    def _create_factor_lookup(self, conversion_factors: pd.DataFrame) -> Dict[str, float]:
        """创建转换因子查找字典"""
        factor_lookup = {}
        
        # 确定能源类型列名
        energy_type_column = 'standard_energy_type' if 'standard_energy_type' in conversion_factors.columns else 'energy_type'
        
        # 只处理折标系数的记录，过滤掉co2因子
        valid_factors = conversion_factors[conversion_factors['factor'] == '折标系数'].copy()
        
        if valid_factors.empty:
            self.logger.warning("没有找到有效的折标系数数据")
            return factor_lookup
        
        for _, row in valid_factors.iterrows():
            energy_type = row[energy_type_column]
            factor_value = row.get('value', 1.0)
            
            if pd.notna(energy_type) and pd.notna(factor_value):
                try:
                    # 转换因子值
                    factor_lookup[energy_type] = float(factor_value)
                except (ValueError, TypeError):
                    self.logger.warning(f"跳过无效的转换因子值: {energy_type} = {factor_value}")
                    continue
        
        self.logger.info(f"创建了 {len(factor_lookup)} 个转换因子映射")
        return factor_lookup
    
    def _apply_default_conversion(self, energy_data: pd.DataFrame) -> pd.DataFrame:
        """应用默认转换（当没有转换因子时）"""
        result_df = energy_data.copy()
        
        # 使用默认转换系数
        default_factors = {
            '原煤': 0.7143,
            '焦炭': 0.9714,
            '电力': 0.1229,
            '天然气': 1.2143,
            '汽油': 1.4714,
            '柴油': 1.4571
        }
        
        energy_type_column = 'standard_energy_type' if 'standard_energy_type' in result_df.columns else 'energy_type'
        
        result_df['conversion_factor_used'] = result_df[energy_type_column].map(
            lambda x: default_factors.get(x, 1.0)
        )
        
        result_df['standard_coal_equivalent'] = result_df['value'] * result_df['conversion_factor_used']
        result_df['conversion_method'] = '默认转换'
        result_df['conversion_source'] = '默认系数'
        result_df['standard_unit'] = '万吨标准煤'
        
        self.logger.warning("使用默认转换系数")
        return result_df
    
    def aggregate_by_region_industry(self, converted_data: pd.DataFrame) -> pd.DataFrame:
        """按地区和行业聚合标准煤当量"""
        if converted_data.empty:
            return converted_data
        
        # 确定聚合列
        group_columns = []
        
        if 'standard_province' in converted_data.columns:
            group_columns.append('standard_province')
        elif 'province' in converted_data.columns:
            group_columns.append('province')
        
        if 'standard_item' in converted_data.columns:
            group_columns.append('standard_item')
        elif 'item' in converted_data.columns:
            group_columns.append('item')
        elif 'standard_area' in converted_data.columns: # For municipal data
            group_columns.append('standard_area')
        elif 'area' in converted_data.columns: # For municipal data
            group_columns.append('area')
        
        if 'standard_industry' in converted_data.columns: # For municipal data
            group_columns.append('standard_industry')
        elif 'industry' in converted_data.columns: # For municipal data
            group_columns.append('industry')
        
        if 'year' in converted_data.columns:
            group_columns.append('year')
        
        if not group_columns:
            self.logger.warning("未找到合适的聚合列，返回原始数据")
            return converted_data
        
        # 执行聚合 - 只聚合标准煤当量，不聚合原始value列（因为不同能源类型单位不同）
        aggregated = converted_data.groupby(group_columns).agg({
            'standard_coal_equivalent': 'sum'
        }).reset_index()
        
        self.logger.info(f"聚合完成，从 {len(converted_data)} 行聚合到 {len(aggregated)} 行")
        self.logger.info(f"聚合后标准煤当量总和: {aggregated['standard_coal_equivalent'].sum():.2f} 万吨标准煤")
        
        return aggregated
    
    def validate_conversion_results(self, converted_data: pd.DataFrame) -> List[str]:
        """验证转换结果的有效性"""
        errors = []
        
        if converted_data.empty:
            errors.append("转换结果为空")
            return errors
        
        # 检查必需列
        required_columns = ['standard_coal_equivalent', 'conversion_factor_used']
        for col in required_columns:
            if col not in converted_data.columns:
                errors.append(f"缺少必需列: {col}")
        
        # 检查数值有效性
        if 'standard_coal_equivalent' in converted_data.columns:
            if (converted_data['standard_coal_equivalent'] < 0).any():
                errors.append("存在负的标准煤当量值")
            
            if (converted_data['standard_coal_equivalent'] == 0).all():
                errors.append("所有标准煤当量值都为0")
        
        # 检查转换因子有效性
        if 'conversion_factor_used' in converted_data.columns:
            if (converted_data['conversion_factor_used'] <= 0).any():
                errors.append("存在无效的转换因子（<=0）")
        
        return errors
    
    def get_conversion_summary(self, converted_data: pd.DataFrame) -> Dict[str, Any]:
        """获取转换结果摘要"""
        if converted_data.empty:
            return {"error": "数据为空"}
        
        # 获取能源类型列表
        energy_type_column = 'standard_energy_type' if 'standard_energy_type' in converted_data.columns else 'energy_type'
        energy_types = []
        if energy_type_column in converted_data.columns:
            energy_types = list(converted_data[energy_type_column].unique())
        
        summary = {
            "total_records": len(converted_data),
            "total_standard_coal": float(converted_data['standard_coal_equivalent'].sum()),
            "unit": "万吨标准煤",
            "energy_types": energy_types,
            "conversion_method": converted_data['conversion_method'].iloc[0] if 'conversion_method' in converted_data.columns else "未知"
        }
        
        return summary


