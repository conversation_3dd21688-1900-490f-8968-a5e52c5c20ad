from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, Tuple

from ecam_calculator.domain.model.value_objects import (
    CityEnergyMatrix,
    ConstraintData
)
from ecam_calculator.infrastructure.config_reader import get_config_reader


class BalancingService(ABC):
    """
    数据平衡服务接口。
    主要职责是执行IPF（迭代比例拟合）算法，平衡城市能源矩阵。
    """

    @abstractmethod
    def build_city_energy_matrix(
        self, 
        energy_data: list,
        electricity_data: list,
        year: int,
        province: str
    ) -> CityEnergyMatrix:
        """
        构建城市能源矩阵。
        
        Args:
            energy_data: 标准化后的能源数据列表
            electricity_data: 标准化后的用电量数据列表
            year: 统计年份
            province: 省份名称
            
        Returns:
            城市能源矩阵值对象
        """
        pass

    @abstractmethod
    def balance_matrix(
        self, 
                matrix_to_balance: pd.DataFrame, 
                row_constraints: pd.Series, 
                column_constraints: pd.Series,
        max_iterations: int = 1000,
        tolerance: float = 1e-6
    ) -> pd.DataFrame:
        """
        使用IPF算法平衡矩阵。
        
        Args:
            matrix_to_balance: 待平衡的矩阵
            row_constraints: 行约束（省级行业总量）
            column_constraints: 列约束（城市总能耗）
            max_iterations: 最大迭代次数
            tolerance: 收敛容差
            
        Returns:
            平衡后的矩阵
        """
        pass

    @abstractmethod
    def validate_balanced_matrix(
        self,
        balanced_matrix: pd.DataFrame,
        row_constraints: pd.Series,
        column_constraints: pd.Series
    ) -> Dict[str, Any]:
        """
        验证平衡后的矩阵是否满足约束条件。
        
        Args:
            balanced_matrix: 平衡后的矩阵
            row_constraints: 行约束
            column_constraints: 列约束
            
        Returns:
            验证结果字典
        """
        pass


class BalancingServiceImpl(BalancingService):
    """数据平衡服务实现"""
    
    def __init__(self):
        self.config_reader = get_config_reader()
    
    def build_city_energy_matrix(
        self, 
        energy_data: list,
        electricity_data: list,
        year: int,
        province: str
    ) -> CityEnergyMatrix:
        """构建城市能源矩阵"""
        
        # 过滤指定年份和省份的数据
        filtered_energy = [
            data for data in energy_data 
            if data.year == year and data.province == province
        ]
        
        filtered_electricity = [
            data for data in electricity_data 
            if data.year == year
        ]
        
        # 构建城市-行业-能源品种矩阵
        matrix_data = self._build_matrix_data(filtered_energy, filtered_electricity)
        
        # 创建城市能源矩阵值对象
        city_matrix = CityEnergyMatrix(
            year=year,
            matrix_data=matrix_data,
            allocation_method='用电比例分配',
            source_data={
                'energy_data_count': len(filtered_energy),
                'electricity_data_count': len(filtered_electricity),
                'province': province
            },
            validation_status='待验证'
        )
        
        return city_matrix
    
    def balance_matrix(
        self, 
        matrix_to_balance: pd.DataFrame, 
        row_constraints: pd.Series, 
        column_constraints: pd.Series,
        max_iterations: int = 1000,
        tolerance: float = 1e-6
    ) -> pd.DataFrame:
        """使用IPF算法平衡矩阵"""
        
        # 复制矩阵以避免修改原始数据
        balanced_matrix = matrix_to_balance.copy()
        
        # 确保矩阵索引和约束索引一致
        balanced_matrix = self._align_matrix_with_constraints(
            balanced_matrix, row_constraints, column_constraints
        )
        
        # 执行IPF平衡
        for iteration in range(max_iterations):
            # 保存上一次迭代的结果
            previous_matrix = balanced_matrix.copy()
            
            # 行平衡（按行约束调整）
            balanced_matrix = self._apply_row_constraints(
                balanced_matrix, row_constraints
            )
            
            # 列平衡（按列约束调整）
            balanced_matrix = self._apply_column_constraints(
                balanced_matrix, column_constraints
            )
            
            # 检查收敛性
            if self._check_convergence(previous_matrix, balanced_matrix, tolerance):
                print(f"IPF算法在第 {iteration + 1} 次迭代后收敛")
                break
        
        return balanced_matrix
    
    def validate_balanced_matrix(
        self,
        balanced_matrix: pd.DataFrame,
        row_constraints: pd.Series,
        column_constraints: pd.Series,
        tolerance: float = 1e-6
    ) -> Dict[str, Any]:
        """验证平衡后的矩阵是否满足约束条件"""
        
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'row_errors': {},
            'column_errors': {},
            'total_error': 0.0
        }
        
        # 验证行约束
        for row_name in row_constraints.index:
            if row_name in balanced_matrix.index:
                row_sum = balanced_matrix.loc[row_name].sum()
                expected_sum = row_constraints[row_name]
                error = abs(row_sum - expected_sum)
                validation_results['row_errors'][row_name] = {
                    'expected': expected_sum,
                    'actual': row_sum,
                    'error': error,
                    'relative_error': error / expected_sum if expected_sum > 0 else float('inf')
                }
                
                if error > tolerance:
                    validation_results['is_valid'] = False
                    validation_results['errors'].append(
                        f"行约束 {row_name} 不满足: 期望 {expected_sum}, 实际 {row_sum}"
                    )
        
        # 验证列约束
        for col_name in column_constraints.index:
            if col_name in balanced_matrix.columns:
                col_sum = balanced_matrix[col_name].sum()
                expected_sum = column_constraints[col_name]
                error = abs(col_sum - expected_sum)
                validation_results['column_errors'][col_name] = {
                    'expected': expected_sum,
                    'actual': col_sum,
                    'error': error,
                    'relative_error': error / expected_sum if expected_sum > 0 else float('inf')
                }
                
                if error > tolerance:
                    validation_results['is_valid'] = False
                    validation_results['errors'].append(
                        f"列约束 {col_name} 不满足: 期望 {expected_sum}, 实际 {col_sum}"
                    )
        
        # 计算总误差
        total_error = sum(
            abs(validation_results['row_errors'][row]['error']) 
            for row in validation_results['row_errors']
        ) + sum(
            abs(validation_results['column_errors'][col]['error']) 
            for col in validation_results['column_errors']
        )
        validation_results['total_error'] = float(total_error)
        
        return validation_results
    
    def _build_matrix_data(
        self, 
        energy_data: list, 
        electricity_data: list
    ) -> pd.DataFrame:
        """构建矩阵数据"""
        
        # 收集所有城市和行业
        cities = set()
        industries = set()
        energy_types = set()
        
        for data in energy_data:
            if data.city:
                cities.add(data.city)
            if data.standard_industry:
                industries.add(data.standard_industry)
            if data.energy_type:
                energy_types.add(data.energy_type)
        
        for data in electricity_data:
            if data.city:
                cities.add(data.city)
            if data.standard_industry:
                industries.add(data.standard_industry)
        
        # 创建多级索引
        index = pd.MultiIndex.from_product(
            [list(cities), list(industries), list(energy_types)],
            names=['city', 'industry', 'energy_type']
        )
        
        # 创建空的DataFrame
        matrix_data = pd.DataFrame(index=index, columns=['consumption'])
        matrix_data['consumption'] = 0.0
        
        # 填充能源数据
        for data in energy_data:
            if data.city and data.standard_industry and data.energy_type:
                matrix_data.loc[
                    (data.city, data.standard_industry, data.energy_type), 
                    'consumption'
                ] = data.standard_value
        
        # 填充用电量数据（作为权重）
        for data in electricity_data:
            if data.city and data.standard_industry:
                # 找到对应的能源类型（电力）
                for energy_type in energy_types:
                    if '电力' in energy_type or '电' in energy_type:
                        matrix_data.loc[
                            (data.city, data.standard_industry, energy_type), 
                            'consumption'
                        ] = data.electricity_consumption
                        break
        
        return matrix_data
    
    def _align_matrix_with_constraints(
        self,
        matrix: pd.DataFrame,
        row_constraints: pd.Series,
        column_constraints: pd.Series
    ) -> pd.DataFrame:
        """确保矩阵索引与约束索引一致"""
        
        # 获取矩阵的行和列
        matrix_rows = set(matrix.index.get_level_values('industry'))
        matrix_cols = set(matrix.index.get_level_values('city'))
        
        # 获取约束的行和列
        constraint_rows = set(row_constraints.index)
        constraint_cols = set(column_constraints.index)
        
        # 添加缺失的行和列
        missing_rows = constraint_rows - matrix_rows
        missing_cols = constraint_cols - matrix_cols
        
        for row in missing_rows:
            for city in matrix_cols:
                for energy_type in matrix.index.get_level_values('energy_type').unique():
                    matrix.loc[(city, row, energy_type), 'consumption'] = 0.0
        
        for col in missing_cols:
            for industry in matrix_rows:
                for energy_type in matrix.index.get_level_values('energy_type').unique():
                    matrix.loc[(col, industry, energy_type), 'consumption'] = 0.0
        
        return matrix
    
    def _apply_row_constraints(
        self, 
        matrix: pd.DataFrame, 
        row_constraints: pd.Series
    ) -> pd.DataFrame:
        """应用行约束"""
        
        balanced_matrix = matrix.copy()
        
        for row_name in row_constraints.index:
            if row_name in matrix.index.get_level_values('industry'):
                # 获取该行业的所有行
                row_mask = matrix.index.get_level_values('industry') == row_name
                current_sum = matrix.loc[row_mask, 'consumption'].sum()
                target_sum = row_constraints[row_name]
                
                if current_sum > 0:
                    # 按比例调整
                    scale_factor = target_sum / current_sum
                    balanced_matrix.loc[row_mask, 'consumption'] *= scale_factor
        
        return balanced_matrix
    
    def _apply_column_constraints(
        self, 
        matrix: pd.DataFrame, 
        column_constraints: pd.Series
    ) -> pd.DataFrame:
        """应用列约束"""
        
        balanced_matrix = matrix.copy()
        
        for col_name in column_constraints.index:
            if col_name in matrix.index.get_level_values('city'):
                # 获取该城市的所有列
                col_mask = matrix.index.get_level_values('city') == col_name
                current_sum = matrix.loc[col_mask, 'consumption'].sum()
                target_sum = column_constraints[col_name]
                
                if current_sum > 0:
                    # 按比例调整
                    scale_factor = target_sum / current_sum
                    balanced_matrix.loc[col_mask, 'consumption'] *= scale_factor
        
        return balanced_matrix
    
    def _check_convergence(
        self, 
        previous_matrix: pd.DataFrame, 
        current_matrix: pd.DataFrame, 
        tolerance: float
    ) -> bool:
        """检查是否收敛"""
        
        if previous_matrix.empty or current_matrix.empty:
            return False
        
        # 计算相对误差
        relative_error = np.abs(
            (current_matrix - previous_matrix) / (previous_matrix + 1e-10)
        ).max().max()
        
        return relative_error < tolerance
    
    def build_preliminary_macro_matrix(
        self, 
        provincial_energy_df: pd.DataFrame, 
        city_electricity_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        构建初步的宏观 "地区-行业" 矩阵。
        保持向后兼容的方法。
        """
        # 这个方法将在后续重构中逐步替换
        # 暂时返回空DataFrame
        return pd.DataFrame()


# 工厂函数
def get_balancing_service() -> BalancingService:
    """获取数据平衡服务实例"""
    return BalancingServiceImpl()

