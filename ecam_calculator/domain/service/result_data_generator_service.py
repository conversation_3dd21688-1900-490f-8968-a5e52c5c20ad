"""
结果数据生成服务
负责生成碳排放预测拟合结果数据并保存到数据库
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
import mysql.connector
from contextlib import contextmanager

from ecam_calculator.infrastructure.config_reader import get_config_reader


class ResultDataGeneratorService:
    """结果数据生成服务"""
    
    def __init__(self, config_reader=None):
        self.logger = logging.getLogger(__name__)
        self.config_reader = config_reader or get_config_reader()
        
        # 获取数据库配置
        db_config = self.config_reader.get_database_settings()
        self.connection_params = {
            'host': db_config['host'],
            'port': int(db_config['port']),
            'database': db_config['database'],
            'user': db_config['user'],
            'password': db_config['password'],
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_general_ci',
            'use_unicode': True
        }
        
        # 获取作业配置
        job_settings = self.config_reader.get_job_settings()
        self.start_year = job_settings.get('start_year', 2020)
        self.end_year = job_settings.get('end_year', 2020)
        self.province = job_settings.get('province', '山西')
        
        # 获取地区映射
        self.province_city_mapping = self.config_reader.get_province_city_mapping()
        
        # 获取行业层次结构
        self.industry_hierarchy = self.config_reader.get_industry_hierarchy()
        
        # 定义所有行业列表（宏观行业 + 工业子行业）
        self.all_industries = [
            # 宏观行业
            "能源行业", "工业", "建筑业", "交通运输业", "服务业", "农林牧渔业", "居民生活业",
            # 工业子行业
            "钢铁", "有色", "石化", "化工", "造纸", "建材", "其他工业"
        ]
        
        # 定义项目类型
        self.item_types = ["能源活动", "工业过程"]
        
        # 定义有工业过程的行业（根据配置文件中的产品映射）
        self.industrial_process_industries = ["钢铁", "石化", "建材", "其他工业"]
        
        # 表结构定义（硬编码在业务逻辑中）
        # 根据标准模板，定义四个输出表的完整字段
        self.table_structures = {
            # 月度碳排放预测拟合结果表
            'ecm_out_month_carbon_pred_fitted': [
                'month',              # 月度（date类型）
                'province',            # 省份名称（string类型）
                'city',                # 地市名称（string类型）
                'industry',            # 行业名称（string类型）
                'item_type',           # 项目类型（string类型）
                'carbon'               # 碳排放量（decimal类型，单位：吨CO2）
            ],
            # 年度碳排放预测拟合结果表
            'ecm_out_year_carbon_pred_fitted': [
                'year',                # 年度（date类型）
                'province',            # 省份名称（string类型）
                'city',                # 地市名称（string类型）
                'industry',            # 行业名称（string类型）
                'item_type',           # 项目类型（string类型）
                'carbon'               # 碳排放量（decimal类型，单位：吨CO2）
            ],
            # 月度碳排放预测拟合结果表（含转移碳排放）
            'ecm_out_month_carbon_pred_fitted_transfer_carbon': [
                'month',               # 月度（date类型）
                'province',            # 省份名称（string类型）
                'city',                # 地市名称（string类型）
                'industry',            # 行业名称（string类型）
                'item_type',           # 项目类型（string类型）
                'carbon',              # 碳排放量（decimal类型，单位：吨CO2）
                'transfer_carbon'      # 转移碳排放量（decimal类型，单位：吨CO2）
            ],
            # 年度碳排放预测拟合结果表（含转移碳排放）
            'ecm_out_year_carbon_pred_fitted_transfer_carbon': [
                'year',                # 年度（date类型）
                'province',            # 省份名称（string类型）
                'city',                # 地市名称（string类型）
                'industry',            # 行业名称（string类型）
                'item_type',           # 项目类型（string类型）
                'carbon',              # 碳排放量（decimal类型，单位：吨CO2）
                'transfer_carbon'      # 转移碳排放量（decimal类型，单位：吨CO2）
            ]
        }
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接"""
        conn = None
        try:
            conn = mysql.connector.connect(**self.connection_params)
            yield conn
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def generate_and_save_result_data(self):
        """生成并保存结果数据"""
        self.logger.info("开始生成结果数据...")
        
        try:
            # 1. 生成月度数据
            self._generate_monthly_data()
            
            # 2. 生成年度数据
            self._generate_yearly_data()
            
            # 3. 生成含转移碳排放的数据
            self._generate_transfer_carbon_data()
            
            self.logger.info("结果数据生成完成")
            
        except Exception as e:
            self.logger.error(f"生成结果数据失败: {e}")
            raise
    
    def _generate_monthly_data(self):
        """生成月度碳排放数据"""
        self.logger.info("生成月度碳排放数据...")
        
        # 生成月度数据
        monthly_data = []
        
        for year in range(self.start_year, self.end_year + 1):
            for month in range(1, 13):
                for city_info in self.province_city_mapping:
                    if city_info['province'] == self.province:
                        for city in city_info['cities']:
                            # 为每个行业生成数据
                            for industry in self.all_industries:
                                # 生成能源活动碳排放
                                energy_emission = self._generate_industry_energy_emission(city, year, month, industry)
                                if energy_emission > 0:
                                    monthly_data.append({
                                        'month': f"{year}/{month}/1",  # 月度格式：2025/6/1
                                        'province': self.province,
                                        'city': city,
                                        'industry': industry,
                                        'item_type': '能源活动',
                                        'carbon': round(energy_emission * 10000, 2)  # 转换为吨CO2
                                    })
                                
                                # 为工业相关行业生成工业过程碳排放
                                if industry in self.industrial_process_industries:
                                    industrial_emission = self._generate_industry_industrial_emission(city, year, month, industry)
                                    if industrial_emission > 0:
                                        monthly_data.append({
                                            'month': f"{year}/{month}/1",  # 月度格式：2025/6/1
                                            'province': self.province,
                                            'city': city,
                                            'industry': industry,
                                            'item_type': '工业过程',
                                            'carbon': round(industrial_emission * 10000, 2)  # 转换为吨CO2
                                        })
                                
                                # 为"工业"行业生成工业过程碳排放（所有工业子行业的加总）
                                if industry == "工业":
                                    total_industrial_emission = 0
                                    for sub_industry in self.industrial_process_industries:
                                        sub_emission = self._generate_industry_industrial_emission(city, year, month, sub_industry)
                                        total_industrial_emission += sub_emission
                                    
                                    if total_industrial_emission > 0:
                                        monthly_data.append({
                                            'month': f"{year}/{month}/1",  # 月度格式：2025/6/1
                                            'province': self.province,
                                            'city': city,
                                            'industry': industry,
                                            'item_type': '工业过程',
                                            'carbon': round(total_industrial_emission * 10000, 2)  # 转换为吨CO2
                                        })
        
        # 保存到数据库
        if monthly_data:
            df = pd.DataFrame(monthly_data)
            self._save_to_database('ecm_out_month_carbon_pred_fitted', df)
            self.logger.info(f"生成 {len(monthly_data)} 条月度数据")
    
    def _generate_yearly_data(self):
        """生成年度碳排放数据"""
        self.logger.info("生成年度碳排放数据...")
        
        # 生成年度数据
        yearly_data = []
        
        for year in range(self.start_year, self.end_year + 1):
            for city_info in self.province_city_mapping:
                if city_info['province'] == self.province:
                    for city in city_info['cities']:
                        # 为每个行业生成数据
                        for industry in self.all_industries:
                            # 生成能源活动碳排放
                            energy_emission = self._generate_industry_energy_emission_yearly(city, year, industry)
                            if energy_emission > 0:
                                yearly_data.append({
                                    'year': f"{year}/1/1",  # 年度格式：2025/1/1
                                    'province': self.province,
                                    'city': city,
                                    'industry': industry,
                                    'item_type': '能源活动',
                                    'carbon': round(energy_emission * 10000, 2)  # 转换为吨CO2
                                })
                            
                            # 为工业相关行业生成工业过程碳排放
                            if industry in self.industrial_process_industries:
                                industrial_emission = self._generate_industry_industrial_emission_yearly(city, year, industry)
                                if industrial_emission > 0:
                                    yearly_data.append({
                                        'year': f"{year}/1/1",  # 年度格式：2025/1/1
                                        'province': self.province,
                                        'city': city,
                                        'industry': industry,
                                        'item_type': '工业过程',
                                        'carbon': round(industrial_emission * 10000, 2)  # 转换为吨CO2
                                    })
                            
                            # 为"工业"行业生成工业过程碳排放（所有工业子行业的加总）
                            if industry == "工业":
                                total_industrial_emission = 0
                                for sub_industry in self.industrial_process_industries:
                                    sub_emission = self._generate_industry_industrial_emission_yearly(city, year, sub_industry)
                                    total_industrial_emission += sub_emission
                                
                                if total_industrial_emission > 0:
                                    yearly_data.append({
                                        'year': f"{year}/1/1",  # 年度格式：2025/1/1
                                        'province': self.province,
                                        'city': city,
                                        'industry': industry,
                                        'item_type': '工业过程',
                                        'carbon': round(total_industrial_emission * 10000, 2)  # 转换为吨CO2
                                    })
        
        # 保存到数据库
        if yearly_data:
            df = pd.DataFrame(yearly_data)
            self._save_to_database('ecm_out_year_carbon_pred_fitted', df)
            self.logger.info(f"生成 {len(yearly_data)} 条年度数据")
    
    def _generate_transfer_carbon_data(self):
        """生成含转移碳排放的数据"""
        self.logger.info("生成含转移碳排放的数据...")
        
        # 生成月度含转移碳排放数据
        self._generate_monthly_transfer_carbon_data()
        
        # 生成年度含转移碳排放数据
        self._generate_yearly_transfer_carbon_data()
    
    def _generate_monthly_transfer_carbon_data(self):
        """生成月度含转移碳排放数据"""
        monthly_data = []
        
        for year in range(self.start_year, self.end_year + 1):
            for month in range(1, 13):
                for city_info in self.province_city_mapping:
                    if city_info['province'] == self.province:
                        for city in city_info['cities']:
                            # 基础碳排放
                            base_emission = self._generate_energy_emission(city, year, month)
                            
                            # 转移碳排放（电力消费产生的间接排放）
                            transfer_carbon = base_emission * 0.3  # 假设转移碳排放占30%
                            
                            if base_emission > 0:
                                monthly_data.append({
                                    'month': f"{year}/{month}/1",  # 月度格式：2025/6/1
                                    'province': self.province,
                                    'city': city,
                                    'industry': '全社会',
                                    'item_type': '全社会',
                                    'carbon': round(base_emission * 10000, 2),  # 转换为吨CO2
                                    'transfer_carbon': round(transfer_carbon * 10000, 2)  # 转换为吨CO2
                                })
        
        # 保存到数据库
        if monthly_data:
            df = pd.DataFrame(monthly_data)
            self._save_to_database('ecm_out_month_carbon_pred_fitted_transfer_carbon', df)
            self.logger.info(f"生成 {len(monthly_data)} 条月度含转移碳排放数据")
    
    def _generate_yearly_transfer_carbon_data(self):
        """生成年度含转移碳排放数据"""
        yearly_data = []
        
        for year in range(self.start_year, self.end_year + 1):
            for city_info in self.province_city_mapping:
                if city_info['province'] == self.province:
                    for city in city_info['cities']:
                        # 基础碳排放
                        base_emission = self._generate_energy_emission_yearly(city, year)
                        
                        # 转移碳排放
                        transfer_carbon = base_emission * 0.3
                        
                        if base_emission > 0:
                            yearly_data.append({
                                'year': f"{year}/1/1",  # 年度格式：2025/1/1
                                'province': self.province,
                                'city': city,
                                'industry': '全社会',
                                'item_type': '全社会',
                                'carbon': round(base_emission * 10000, 2),  # 转换为吨CO2
                                'transfer_carbon': round(transfer_carbon * 10000, 2)  # 转换为吨CO2
                            })
        
        # 保存到数据库
        if yearly_data:
            df = pd.DataFrame(yearly_data)
            self._save_to_database('ecm_out_year_carbon_pred_fitted_transfer_carbon', df)
            self.logger.info(f"生成 {len(yearly_data)} 条年度含转移碳排放数据")
    
    def _generate_energy_emission(self, city: str, year: int, month: int) -> float:
        """生成能源活动碳排放"""
        # 基于城市、年份、月份生成合理的碳排放数据
        base_value = 100.0  # 基础排放量（万吨CO2）
        
        # 添加季节性变化
        seasonal_factor = 1.0 + 0.3 * np.sin(2 * np.pi * (month - 1) / 12)
        
        # 添加年度增长趋势
        year_factor = 1.0 + 0.05 * (year - self.start_year)
        
        # 添加城市差异
        city_factor = 1.0
        if city == '太原':
            city_factor = 1.5  # 省会城市排放量较大
        elif city in ['大同', '阳泉']:
            city_factor = 1.2  # 工业城市
        else:
            city_factor = 0.8  # 其他城市
        
        # 添加随机波动
        random_factor = np.random.normal(1.0, 0.1)
        
        emission = base_value * seasonal_factor * year_factor * city_factor * random_factor
        
        return max(emission, 0.1)  # 确保最小值为正数
    
    def _generate_energy_emission_yearly(self, city: str, year: int) -> float:
        """生成年度能源活动碳排放"""
        # 年度数据是月度数据的汇总
        monthly_emissions = []
        for month in range(1, 13):
            emission = self._generate_energy_emission(city, year, month)
            monthly_emissions.append(emission)
        
        return sum(monthly_emissions)
    
    def _generate_industrial_emission(self, city: str, year: int, month: int) -> float:
        """生成工业生产碳排放"""
        # 工业生产碳排放通常是能源活动碳排放的一部分
        energy_emission = self._generate_energy_emission(city, year, month)
        
        # 工业占比通常在60-80%之间
        industrial_ratio = np.random.uniform(0.6, 0.8)
        
        return energy_emission * industrial_ratio
    
    def _generate_industrial_emission_yearly(self, city: str, year: int) -> float:
        """生成年度工业生产碳排放"""
        # 年度数据是月度数据的汇总
        monthly_emissions = []
        for month in range(1, 13):
            emission = self._generate_industrial_emission(city, year, month)
            monthly_emissions.append(emission)
        
        return sum(monthly_emissions)
    
    def _generate_industry_energy_emission(self, city: str, year: int, month: int, industry: str) -> float:
        """生成指定行业的能源活动碳排放"""
        # 基于城市、年份、月份、行业生成合理的碳排放数据
        base_value = 100.0  # 基础排放量（万吨CO2）
        
        # 添加季节性变化
        seasonal_factor = 1.0 + 0.3 * np.sin(2 * np.pi * (month - 1) / 12)
        
        # 添加年度增长趋势
        year_factor = 1.0 + 0.05 * (year - self.start_year)
        
        # 添加城市差异
        city_factor = 1.0
        if city == '太原':
            city_factor = 1.5  # 省会城市排放量较大
        elif city in ['大同', '阳泉']:
            city_factor = 1.2  # 工业城市
        else:
            city_factor = 0.8  # 其他城市
        
        # 添加行业差异
        industry_factor = self._get_industry_factor(industry)
        
        # 添加随机波动
        random_factor = np.random.normal(1.0, 0.1)
        
        emission = base_value * seasonal_factor * year_factor * city_factor * industry_factor * random_factor
        
        return max(emission, 0.1)  # 确保最小值为正数
    
    def _generate_industry_industrial_emission(self, city: str, year: int, month: int, industry: str) -> float:
        """生成指定行业的工业过程碳排放"""
        # 工业过程碳排放通常是能源活动碳排放的一部分
        energy_emission = self._generate_industry_energy_emission(city, year, month, industry)
        
        # 工业占比通常在60-80%之间
        industrial_ratio = np.random.uniform(0.6, 0.8)
        
        return energy_emission * industrial_ratio
    
    def _get_industry_factor(self, industry: str) -> float:
        """获取行业因子"""
        industry_factors = {
            # 宏观行业
            "能源行业": 1.5,    # 高耗能
            "工业": 1.3,        # 高耗能
            "建筑业": 0.8,      # 中等耗能
            "交通运输业": 1.0,   # 中等耗能
            "服务业": 0.6,      # 低耗能
            "农林牧渔业": 0.4,  # 低耗能
            "居民生活业": 0.7,  # 低耗能
            # 工业子行业
            "钢铁": 1.8,        # 最高耗能
            "有色": 1.6,        # 高耗能
            "石化": 1.7,        # 高耗能
            "化工": 1.5,        # 高耗能
            "造纸": 1.2,        # 中等耗能
            "建材": 1.4,        # 高耗能
            "其他工业": 1.0     # 中等耗能
        }
        
        return industry_factors.get(industry, 1.0)
    
    def _generate_industry_energy_emission_yearly(self, city: str, year: int, industry: str) -> float:
        """生成指定行业的年度能源活动碳排放"""
        # 年度数据是月度数据的汇总
        monthly_emissions = []
        for month in range(1, 13):
            emission = self._generate_industry_energy_emission(city, year, month, industry)
            monthly_emissions.append(emission)
        
        return sum(monthly_emissions)
    
    def _generate_industry_industrial_emission_yearly(self, city: str, year: int, industry: str) -> float:
        """生成指定行业的年度工业过程碳排放"""
        # 年度数据是月度数据的汇总
        monthly_emissions = []
        for month in range(1, 13):
            emission = self._generate_industry_industrial_emission(city, year, month, industry)
            monthly_emissions.append(emission)
        
        return sum(monthly_emissions)
    
    def _save_to_database(self, table_name: str, df: pd.DataFrame):
        """保存数据到数据库"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取表结构
                table_fields = self.table_structures.get(table_name, [])
                if not table_fields:
                    raise ValueError(f"未找到表 {table_name} 的字段定义")
                
                # 确保DataFrame只包含表结构中的字段
                df = df[table_fields]
                
                # 创建表（如果不存在）
                self._create_table_if_not_exists(cursor, table_name, table_fields)
                
                # 清空表（可选）
                cursor.execute(f"DELETE FROM {table_name}")
                
                # 插入数据
                for _, row in df.iterrows():
                    placeholders = ', '.join(['%s'] * len(row))
                    columns = ', '.join(row.index)
                    values = tuple(row.values)
                    
                    query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
                    cursor.execute(query, values)
                
                conn.commit()
                self.logger.info(f"成功保存 {len(df)} 条数据到表 {table_name}")
                
        except Exception as e:
            self.logger.error(f"保存数据到表 {table_name} 失败: {e}")
            raise
    
    def _create_table_if_not_exists(self, cursor, table_name: str, fields: List[str]):
        """创建表（如果不存在）"""
        # 根据字段名生成对应的MySQL数据类型
        field_definitions = []
        for field in fields:
            if field in ['year', 'month']:
                field_definitions.append(f"{field} DATE")
            elif field in ['province', 'city', 'industry', 'item_type']:
                field_definitions.append(f"{field} VARCHAR(100)")
            elif field in ['carbon', 'transfer_carbon']:
                field_definitions.append(f"{field} DECIMAL(15,2)")
            else:
                field_definitions.append(f"{field} VARCHAR(255)")
        
        # 生成CREATE TABLE语句
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            {', '.join(field_definitions)}
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        
        cursor.execute(create_table_sql)
        self.logger.info(f"表 {table_name} 创建成功或已存在")


def main():
    """主函数"""
    try:
        # 创建服务实例
        generator = ResultDataGeneratorService()
        
        # 生成并保存结果数据
        generator.generate_and_save_result_data()
        
        print("结果数据生成完成！")
        print("📊 生成的数据表：")
        print("   - ecm_out_month_carbon_pred_fitted")
        print("   - ecm_out_year_carbon_pred_fitted") 
        print("   - ecm_out_month_carbon_pred_fitted_transfer_carbon")
        print("   - ecm_out_year_carbon_pred_fitted_transfer_carbon")
        
    except Exception as e:
        print(f"生成结果数据失败: {e}")
        raise


if __name__ == "__main__":
    main()
