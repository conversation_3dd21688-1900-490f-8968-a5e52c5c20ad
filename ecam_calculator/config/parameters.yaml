# 重构阶段配置文件
# 用于主程序到数据标准化阶段的重构
# 基于 parameters_backup.yaml 创建，逐步优化配置结构

# 1. 数据库连接配置
database_settings:
  host: "localhost"  # 本地开发环境连接到Docker暴露的端口
  port: 9999         # Docker暴露的端口
  database: "ecam_city"
  user: "user"
  password: "password"

# 2. 作业运行配置
job_settings:
  start_year: 2020
  end_year: 2020
  province: "山西"
  default_year: 2020

# 3. 省份和地市映射
province_city_mapping:
  - province: "山西"
    cities:
      - "太原"
      - "大同"
      - "阳泉"
      - "长治"
      - "晋城"
      - "朔州"
      - "晋中"
      - "运城"
      - "忻州"
      - "临汾"
      - "吕梁"

# 4. 数据增强配置
data_enhancement:
  spatial_downscaling:
    enabled: true
    weight_method: "electricity_proportion"  # 权重计算方法
    merge_strategy: "city_priority"         # 合并策略：city_priority, provincial_priority, weighted_average
    validation_rules:
      - "total_consistency"                 # 总量一致性检查
      - "industry_consistency"              # 行业一致性检查
    min_weight_threshold: 0.01              # 最小权重阈值
    max_allocation_error: 0.05              # 最大分配误差容忍度
  
  temporal_downscaling:
    enabled: true
    method: "denton"                        # 分解方法：denton, proportional, equal_split
    indicator_source: "monthly_electricity" # 指示器数据源
    validation_rules:
      - "annual_sum_consistency"            # 年度总和一致性
      - "seasonal_pattern_check"            # 季节性模式检查
    error_tolerance: 0.01                   # 误差容忍度（1%）
    min_monthly_value: 0.001                # 最小月度值阈值

# 6. 建模预测配置
modeling_prediction:
  ardl_model:
    enabled: true
    auto_tuning: true                    # 自动调优开关
    max_lags: 3                         # 最大滞后期数
    max_order: 2                        # 最大外生变量阶数
    trend: "c"                          # 趋势项类型
    
  prediction:
    enabled: true
    forecast_horizon: 12                # 预测期数（月）
    use_latest_electricity: true        # 使用最新电力数据
    
  model_selection:
    min_observations: 12                # 最小观测值数量

# 5. 标准化映射配置
standardization_mappings:
  # 功能1+2：列内映射 + 层次关系
  column_standardizations:
    # 4.1 地区标准化
    regions:
      - name: "山西"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["山西", "山西省"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["山西", "山西省"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["山西", "山西省"]
      - name: "太原"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["太原"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["太原"]
      - name: "大同"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["大同"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["大同"]
      - name: "阳泉"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["阳泉"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["阳泉"]
      - name: "长治"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["长治"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["长治"]
      - name: "晋城"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["晋城"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["晋城"]
      - name: "朔州"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["朔州"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["朔州"]
      - name: "晋中"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["晋中"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["晋中"]
      - name: "运城"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["运城"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["运城"]
      - name: "忻州"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["忻州"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["忻州"]
      - name: "临汾"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["临汾"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["临汾"]
      - name: "吕梁"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["吕梁"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["吕梁"]
    
    # 4.2 行业标准化
    industries:
      # 宏观行业
      - name: "能源行业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["1.火力发电", "2.供热", "3.煤炭洗选", "4.炼焦", "5.炼油及煤制油", "6.制气", "7.天然气液化", "8.煤制品加工", "9.回收能"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["火力发电", "供热", "煤炭洗选", "炼焦", "炼油及煤制油", "制气", "天然气液化", "煤制品加工", "回收能"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["1.电力、热力的生产和供应业用电量", "2.燃气生产和供应业用电量", "3.水的生产和供应业用电量", "电厂生产全部耗用电量"]
      
      - name: "工业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["2.工业", "一、采矿业", "二、制造业", "三、电力、热力、燃气及水生产和供应业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["一、采矿业", "二、制造业", "三、电力、热力、燃气及水生产和供应业", "2.工业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["二、工业用电量", "（二）制造业用电量", "（三）电力、燃气及水的生产和供应业用电量", "（一）采矿业用电量", "第二产业用电量"]
      
      - name: "建筑业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["3.建筑业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["3.建筑业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["三、建筑业用电量"]
      
      - name: "交通运输业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["4.交通运输业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["4.交通运输业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["四、交通运输业用电量"]
      
      - name: "服务业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["5.服务业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["5.服务业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["五、服务业用电量"]
      
      - name: "农林牧渔业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["6.农林牧渔业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["6.农林牧渔业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["六、农林牧渔业用电量"]
      
      - name: "居民生活业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["7.居民生活业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["7.居民生活业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["七、居民生活业用电量"]
      
      # 工业子行业
      - name: "钢铁"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["黑色金属冶炼和压延加工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["15.黑色金属冶炼和压延加工业", "3.黑色金属矿采选业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["19.黑色金属冶炼和压延加工业用电量", "3.黑色金属矿采选业用电量"]
      
      - name: "有色"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["有色金属冶炼和压延加工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["16.有色金属冶炼和压延加工业", "4.有色金属矿采选业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["20.有色金属冶炼和压延加工业用电量", "4.有色金属矿采选业用电量"]
      
      - name: "石化"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["石油、煤炭及其他燃料加工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["10.石油、煤炭及其他燃料加工业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["14.石油、煤炭及其他燃料加工业用电量"]
      
      - name: "化工"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["化学原料和化学制品制造业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["11.化学原料和化学制品制造业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["15.化学原料和化学制品制造业用电量"]
      
      - name: "造纸"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["造纸和纸制品业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["7.造纸和纸制品业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["11.造纸和纸制品业用电量"]
      
      - name: "建材"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["非金属矿物制品业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["14.非金属矿物制品业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["18.非金属矿物制品业用电量"]
      
      - name: "其他工业"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: []
          ecam_in_y_pro_ind_ene2_off:
            column: "industry"
            keywords: ["煤炭开采和洗选业", "黑色金属矿采选业", "医药制造业", "燃气生产和供应业", "农副食品加工业", "食品制造业", "通用设备制造业", "专用设备制造业", "金属制品业", "水的生产和供应业", "纺织业", "橡胶和塑料制品业", "家具制造业", "46.水的生产和供应业"]
          ecam_in_m_pro_ind_ele_off:
            column: "industry"
            keywords: ["26.电气机械和器材制造业用电量", "24.汽车制造业用电量", "23.专用设备制造业用电量", "22.通用设备制造业用电量", "21.金属制品业用电量", "2.食品制造业用电量", "17.橡胶和塑料制品业用电量", "16.化学纤维制造业用电量", "15.医药制造业用电量", "12.文教、工美、体育和娱乐用品制造业用电量", "11.印刷和记录媒介复制业用电量", "25.铁路.船舶.航空航天和其他运输设备制造业用电量", "28.仪器仪表制造业用电量", "1.农副食品加工业用电量"]
    
    # 4.3 能源品种标准化
    energy_types:
      - name: "原煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭", "煤"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["原煤", "原煤.1", "煤炭", "煤"]
      - name: "一般烟煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["一般烟煤", "一般烟煤吨)"]
      - name: "无烟煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["无烟煤"]
      - name: "洗精煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["洗精煤"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["洗精煤", "洗精煤.1", "洗精煤及其他洗煤"]
      - name: "其他洗煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["其他洗煤"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["其他洗煤", "其它洗煤"]
      - name: "褐煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["褐煤"]
      - name: "炼焦烟煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["炼焦烟煤"]
      - name: "煤制品"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["煤制品"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["煤制品", "煤制品.1", "煤制品（吨）", "型煤", "型煤.1", "煤  粉", "煤粉"]
      - name: "煤矸石"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["煤矸石", "煤矸石.1", "煤矸石用于燃料", "煤研石"]
      - name: "焦炭"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["焦炭"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["焦炭", "焦炭.1", "焦炭（吨）"]
      - name: "其他焦化产品"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["其他焦化产品"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["其他焦化产品", "其他焦化产品（吨）", "其他焦化焦产", "其它焦化产品"]
      - name: "天然气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["天然气"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["天然气", "天然气 "]
      - name: "液化天然气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["液化天然气"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["液化天然气", "液化天然气 "]
      - name: "液化石油气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["液化石油气"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["液化石油气", "液化石油气 ", "液化石油气.1", "液  化石油气"]
      - name: "焦炉煤气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["焦炉煤气"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["焦炉煤气", "焦炉煤气万立方米)", "焦炉煤气万立方米).1", "焦炉煤气（万立方米）"]
      - name: "高炉煤气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["高炉煤气", "高炉煤气万立方米)", "高炉煤气（万立方米）", "炉煤气高炉煤气立方米)"]
      - name: "转炉煤气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["转炉煤气"]
      - name: "其他煤气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["其他煤气"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["其他煤气", "发生炉煤气", "煤层气", "煤层气（煤田）"]
      - name: "原油"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["原油"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["原油", "原油（吨）"]
      - name: "汽油"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["汽油"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["汽油", "汽油.1", "汽油（吨）"]
      - name: "柴油"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["柴油"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["柴油", "柴油.1"]
      - name: "煤油"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["煤油"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["煤油", "煤油.1", "煤油（吨）"]
      - name: "燃料油"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["燃料油"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["燃料油", "燃料油.1"]
      - name: "润滑油"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["润滑油"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["润滑油"]
      - name: "溶剂油"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["溶剂油"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["溶剂油"]
      - name: "炼厂干气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["炼厂干气"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["炼厂干气"]
      - name: "石油沥青"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["石油沥青", "石油沥青（吨）", "石油沥青（吨）.1"]
      - name: "石油焦"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["石油焦"]
      - name: "石脑油"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["石脑油"]
      - name: "石蜡"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["石蜡"]
      - name: "其他石油制品"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["其他石油制品"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["其他石油制品", "其他石油制品.1", "其它石油制品", "其它石油制品（吨）", "其它石油制品（吨）.1", "石油", "石油制品"]
      - name: "电力"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["电力"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["电力", "电  力", "电力（万千瓦时）", "电力（万千瓦时）.1"]
      - name: "热力"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["热力"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["热力", "热  力", "热力（百万千焦）", "热力（百万千焦）.1"]
      - name: "其他能源"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["其他能源"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["其他能源", "其他燃料", "余热余压", "余热余压（百万千焦）", "余热余压（百万千焦）.1", "城市固体垃圾", "城市垃圾于燃料", "城市生活垃圾", "城市生活垃圾（用于燃料）", "城市垃圾用于燃料", "其它工业废料用于燃料", "生物燃料", "生物质废料用于燃料", "氢气", "油品合计", "能源消费总量", "合计"]
  
  # 功能3：跨列映射
  cross_column_mappings:
    product_to_industry:
      source_column: "product_name"
      target_column: "industry"
      mappings:
        "粗钢": "钢铁"
        "生铁": "钢铁"
        "钢材": "钢铁"
        "焦炭": "石化"
        "水泥": "建材"
        "平板玻璃": "建材"





