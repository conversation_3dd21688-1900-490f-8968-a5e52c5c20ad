# 业务参数配置文件

# 1. 表级配置 - 定义每个数据表的处理规则
table_configs:
  # 省级能源消费表
  ecam_in_y_pro_ind_ene_off:
    description: "年度省级分行业分品种能源消费量"
    table_type: "energy"
    energy_types_to_extract: ["原煤", "焦炭", "电力", "天然气", "汽油", "柴油", "煤油", "燃料油", "热力", "焦炉煤气", "高炉煤气", "转炉煤气", "其他洗煤", "其他焦化产品", "其他煤气", "其他石油制品", "其他能源", "油品合计", "洗精煤", "润滑油", "液化天然气", "液化石油气", "煤制品", "煤合计", "煤矸石", "石油沥青", "石油焦", "石脑油", "石蜡"]
    requires_standardization: true
    standardization_method: "折标煤"
    # 数据库表结构说明
    # 该表包含以下列：year, province, item, energy_type, value, unit, convert, method
    # 其中 item 列存储行业信息，province 列存储地区信息
    
  # 地市工业能源消费表
  ecam_in_y_pro_ind_ene2_off:
    description: "年度地市工业能源消费量"
    table_type: "energy"
    energy_types_to_extract: ["原煤", "焦炭", "电力", "天然气", "汽油", "柴油", "煤油", "燃料油", "热力", "焦炉煤气", "高炉煤气", "转炉煤气", "其他洗煤", "其他焦化产品", "其他煤气", "其他石油制品", "其他能源", "油品合计", "洗精煤", "润滑油", "液化天然气", "液化石油气", "煤制品", "煤合计", "煤矸石", "石油沥青", "石油焦", "石脑油", "石蜡"]
    requires_standardization: true
    standardization_method: "折标煤"
    # 此表列名已经是标准格式，无需映射
    key_fields:
      - "year"
      - "area"
      - "industry"
      - "energy_type"
      - "value"
      - "unit"
      
  # 工业产品产量表
  fct_y_prd_output:
    description: "年度工业产品产量"
    table_type: "product"
    products_to_extract: ["水泥", "粗钢", "钢材", "生铁", "焦炭", "原煤", "发电量", "平板玻璃", "电子计算机整机", "太阳能电池", "非常规天然气"]
    requires_standardization: false
    key_fields:
      - "year"
      - "area"
      - "product_name"
      - "record"
      - "unit"
      
  # 地市用电量表
  ecam_in_m_pro_ind_ele_off:
    description: "月度分地区分行业用电量"
    table_type: "electricity"
    requires_standardization: true
    standardization_method: "电力折标"
    key_fields:
      - "month"
      - "area"
      - "industry"
      - "electricity"
      
  # GDP数据表
  fct_y_gdp:
    description: "年度GDP数据"
    table_type: "economic"
    indicator_category: "GDP"
    indicators_to_extract: ["地区生产总值（亿元）", "第一产业增加值（亿元）", "第二产业增加值（亿元）", "第三产业增加值（亿元）", "工业增加值（亿元）", "建筑业增加值（亿元）"]
    requires_standardization: false
    key_fields:
      - "year"
      - "area"
      - "indicator"
      - "record"
      
  # 能耗强度表
  fct_y_all_ene_intsty:
    description: "年度能耗强度数据"
    table_type: "economic"
    indicator_category: "能耗强度"
    indicators_to_extract: ["单位地区生产总值能源消耗(等价值)", "万元地区生产总值能耗降低率"]
    requires_standardization: false
    key_fields:
      - "year"
      - "area"
      - "indicator"
      - "record"

  # 能源转换因子表
  ecam_in_energy_factor:
    description: "能源转换与碳排放因子表"
    table_type: "energy_factor"
    key_fields:
      - "year"
      - "energy_type"
      - "factor_type"
      - "value"
      - "unit"
      - "source"
    column_mapping:
      year_column: "year"
      energy_type_column: "energy_type"
      factor_column: "factor"
      value_column: "value"
      unit_column: "unit"
      source_column: "source"
      method_column: "method"

# 2. 能源品种配置
energy_types:
  fossil_fuels:
    - name: "原煤"
      standard_unit: "万吨标准煤"
      emission_category: "煤炭"
      conversion_method: "折标煤"
    - name: "焦炭"
      standard_unit: "万吨标准煤"
      emission_category: "煤炭"
      conversion_method: "折标煤"
    - name: "洗精煤"
      standard_unit: "万吨标准煤"
      emission_category: "煤炭"
      conversion_method: "折标煤"
    - name: "煤制品"
      standard_unit: "万吨标准煤"
      emission_category: "煤炭"
      conversion_method: "折标煤"
    - name: "煤矸石"
      standard_unit: "万吨标准煤"
      emission_category: "煤炭"
      conversion_method: "折标煤"
      
  petroleum_products:
    - name: "原油"
      standard_unit: "万吨标准煤"
      emission_category: "石油"
      conversion_method: "折标煤"
    - name: "汽油"
      standard_unit: "万吨标准煤"
      emission_category: "石油"
      conversion_method: "折标煤"
    - name: "柴油"
      standard_unit: "万吨标准煤"
      emission_category: "石油"
      conversion_method: "折标煤"
    - name: "煤油"
      standard_unit: "万吨标准煤"
      emission_category: "石油"
      conversion_method: "折标煤"
    - name: "燃料油"
      standard_unit: "万吨标准煤"
      emission_category: "石油"
      conversion_method: "折标煤"
      
  gas_energy:
    - name: "天然气"
      standard_unit: "万吨标准煤"
      emission_category: "天然气"
      conversion_method: "折标煤"
    - name: "焦炉煤气"
      standard_unit: "万吨标准煤"
      emission_category: "煤气"
      conversion_method: "折标煤"
    - name: "高炉煤气"
      standard_unit: "万吨标准煤"
      emission_category: "煤气"
      conversion_method: "折标煤"
    - name: "转炉煤气"
      standard_unit: "万吨标准煤"
      emission_category: "煤气"
      conversion_method: "折标煤"
      
  electricity_heat:
    - name: "电力"
      standard_unit: "万吨标准煤"
      emission_category: "电力"
      conversion_method: "电力折标"
    - name: "热力"
      standard_unit: "万吨标准煤"
      emission_category: "热力"
      conversion_method: "热力折标"

# 3. 指标分类配置
indicator_categories:
  GDP:
    description: "GDP相关经济指标"
    unit_standardization: "亿元"
    calculation_methods:
      - "直接统计"
      - "产业汇总"
      - "人均计算"
    business_usage: "约束计算、经济分析"
    
  能耗强度:
    description: "能耗强度相关指标"
    unit_standardization: "吨标准煤/万元"
    calculation_methods:
      - "能耗/GDP"
      - "年度变化率"
    business_usage: "约束计算、能效分析"

# 4. 行业映射配置 - 统一结构
# 4. 行业映射配置 (已迁移到 standardization_mappings.column_standardizations.industries)

# 5. 产品到行业映射配置 (已迁移到 standardization_mappings.cross_column_mappings.product_to_industry)

# 6. 省份和地市映射
province_city_mapping:
  - province: "山西"
    cities:
      - "太原"
      - "大同"
      - "阳泉"
      - "长治"
      - "晋城"
      - "朔州"
      - "晋中"
      - "运城"
      - "忻州"
      - "临汾"
      - "吕梁"

# 7. 碳排放因子配置
industrial_process_emission_factors:
  "水泥": 0.51  # 熟料生产过程
  "钢铁": 1.85  # 主要来自炼焦和高炉过程，这里简化为一个综合因子
  "电解铝": 1.5   # 阳极消耗过程
  "合成氨": 1.6   # 蒸汽重整过程
  "乙烯": 0.2    # 裂解过程的非燃烧排放
  "未分类": 0.0
  "其他": 0.0

# 8. 数据库连接配置
database_settings:
  host: "localhost"  # Mac环境下使用localhost连接Docker容器
  port: 9999         # Docker端口映射到Mac主机的端口
  database: "ecam_city"
  user: "user"
  password: "password"

# 9. 作业运行配置
job_settings:
  start_year: 2020
  end_year: 2020
  province: "山西"
  default_year: 2020

# 10. 单位转换配置
unit_conversions:
  weight:
    "万吨": 10000
    "吨": 1
  volume:
    "万立方米": 10000
    "立方米": 1
  energy:
    "万千瓦时": 10000
    "千瓦时": 1

# 11. 能源品种标准化映射
energy_type_standardization:
  "原煤": "煤炭"
  "焦炭": "焦炭"
  "电力": "电力"
  "天然气": "天然气"
  "汽油": "汽油"
  "柴油": "柴油"

# 4. 标准化映射配置
standardization_mappings:
  # 功能1+2：列内映射 + 层次关系
  column_standardizations:
    # 4.1 地区标准化
    regions:
      - name: "山西"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["山西", "山西省"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["山西", "山西省"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["山西", "山西省"]
      - name: "太原"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["太原"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["太原"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["太原"]
      - name: "大同"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["大同"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["大同"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["大同"]
      - name: "阳泉"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["阳泉"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["阳泉"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["阳泉"]
      - name: "长治"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["长治"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["长治"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["长治"]
      - name: "晋城"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["晋城"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["晋城"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["晋城"]
      - name: "朔州"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["朔州"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["朔州"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["朔州"]
      - name: "晋中"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["晋中"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["晋中"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["晋中"]
      - name: "运城"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["运城"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["运城"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["运城"]
      - name: "忻州"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["忻州"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["忻州"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["忻州"]
      - name: "临汾"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["临汾"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["临汾"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["临汾"]
      - name: "吕梁"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["吕梁"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["吕梁"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["吕梁"]
    
    # 4.2 行业标准化
    industries:
      # 宏观行业
      - name: "能源行业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["1.火力发电", "2.供热", "3.煤炭洗选", "4.炼焦", "5.炼油及煤制油", "6.制气", "7.天然气液化", "8.煤制品加工", "9.回收能"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["火力发电", "供热", "煤炭洗选", "炼焦", "炼油及煤制油", "制气", "天然气液化", "煤制品加工", "回收能"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["1.电力、热力的生产和供应业用电量", "2.燃气生产和供应业用电量", "3.水的生产和供应业用电量", "电厂生产全部耗用电量"]
      
      - name: "工业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["2.工业", "一、采矿业", "二、制造业", "三、电力、热力、燃气及水生产和供应业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["一、采矿业", "二、制造业", "三、电力、热力、燃气及水生产和供应业", "2.工业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["二、工业用电量", "（二）制造业用电量", "（三）电力、燃气及水的生产和供应业用电量", "（一）采矿业用电量", "第二产业用电量"]
      
      - name: "建筑业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["3.建筑业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["建筑业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["三、建筑业用电量", "房屋建筑业用电量", "土木工程建筑业用电量", "建筑安装业用电量", "建筑装饰、装修和其他建筑业用电量"]
      
      - name: "交通运输业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["4.交通运输、仓储和邮政业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["交通运输、仓储和邮政业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["四、交通运输、仓储、邮政业用电量", "铁路运输业用电量", "道路运输业用电量", "水上运输业用电量", "航空运输业用电量", "管道运输业用电量", "多式联运和运输代理业用电量", "装卸搬运和仓储业用电量", "邮政业用电量", "电气化铁路用电量", "城市公共交通运输用电量", "港口岸电用电量"]
      
      - name: "服务业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["5.批发和零售业、住宿和餐饮业", "6.其他"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["批发和零售业", "住宿和餐饮业", "其他"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["五、信息传输、软件和信息技术服务业用电量", "六、批发和零售业用电量", "七、住宿和餐饮业用电量", "八、金融业用电量", "九、房地产业用电量", "十、租赁和商务服务业用电量", "十一、公共服务及管理组织用电量", "电信、广播电视和卫星传输服务用电量", "互联网和相关服务用电量", "软件和信息技术服务业用电量", "科学研究和技术服务业用电量", "水利、环境和公共设施管理业用电量", "教育、文化、体育和娱乐业用电量", "卫生和社会工作用电量", "公共管理和社会组织、国际组织用电量", "租赁业用电量", "互联网数据服务用电量", "充换电服务业用电量", "地质勘查用电量", "科技推广和应用服务业用电量", "水利管理业用电量", "公共照明用电量", "教育用电量", "第三产业用电量"]
      
      - name: "农林牧渔业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["1.农、林、牧、渔业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["农、林、牧、渔业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["一、农、林、牧、渔业用电量", "农业用电量", "林业用电量", "畜牧业用电量", "渔业用电量", "农、林、牧、渔服务业用电量", "第一产业用电量", "排灌用电量"]
      
      - name: "居民生活业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["7.居民生活", "城镇", "乡村"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["居民生活", "城镇", "乡村"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["B、城乡居民生活用电量合计", "城镇居民用电量", "乡村居民用电量"]
      
      # 工业子行业
      - name: "钢铁"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["黑色金属冶炼和压延加工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["15.黑色金属冶炼和压延加工业", "3.黑色金属矿采选业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["19.黑色金属冶炼和压延加工业用电量", "3.黑色金属矿采选业用电量"]
      
      - name: "有色"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["有色金属冶炼和压延加工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["16.有色金属冶炼和压延加工业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["20.有色金属冶炼和压延加工业用电量"]
      
      - name: "石化"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["5.炼油及煤制油"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["10.石油、煤炭及其他燃料加工业", "2.石油和天然气开采业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["13.石油、煤炭及其他燃料加工业用电量", "2.石油和天然气开采业用电量"]
      
      - name: "化工"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["化学原料和化学制品制造业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["11.化学原料和化学制品制造业", "12.医药制造业", "13.橡胶和塑料制品业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["14.化学原料和化学制品制造业用电量", "医药制造业用电量", "橡胶和塑料制品业用电量"]
      
      - name: "造纸"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["造纸和纸制品业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["7.造纸和纸制品业", "8.印刷和记录媒介复制业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["造纸和纸制品业用电量", "印刷和记录媒介复制业用电量"]
      
      - name: "建材"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["非金属矿物制品业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["14.非金属矿物制品业", "4.非金属矿采选业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["18.非金属矿物制品业用电量", "5.非金属矿采选业用电量"]
      
      - name: "其他工业"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["3.煤炭洗选"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["1.煤炭开采和洗选业", "1.农副食品加工业", "2.食品制造业", "3.酒、饮料及精制茶制造业", "4.烟草制品业", "5.纺织服装、服饰业", "6.家具制造业", "9.文教、工美、体育和娱乐用品制造业", "17.金属制品业", "18.通用设备制造业", "19.专用设备制造业", "20.汽车制造业", "21.铁路、船舶、航空航天和其他运输设备制造业", "22.电气机械和器材制造业", "23.计算机、通信和其他电子设备制造业", "24.仪器仅表制造业", "25.废弃资源综合利用业", "26.金属制品、机械和设备修理业", "1.电力、热力生产和供应业", "2.增气生产和供应业", "3.水的生产和供应业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["1.煤炭开采和洗选业用电量", "农副食品加工业用电量", "食品制造业用电量", "酒、饮料及精制茶制造业用电量", "烟草制品业用电量", "纺织服装、服饰业用电量", "家具制造业用电量", "文教、工美、体育和娱乐用品制造业用电量", "21.金属制品业用电量", "22.通用设备制造业用电量", "23.专用设备制造业用电量", "24.汽车制造业用电量", "铁路、船舶、航空航天和其他运输设备制造业用电量", "26.电气机械和器材制造业用电量", "27.计算机、通信和其他电子设备制造业用电量", "仪器仪表制造业用电量", "废弃资源综合利用业用电量", "金属制品、机械和设备修理业用电量", "1.电力、热力的生产和供应业用电量", "燃气生产和供应业用电量", "水的生产和供应业用电量"]
    
    # 4.3 能源品种标准化
    energy_types:
      - name: "原煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭", "煤"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭", "煤"]
      - name: "焦炭"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["焦炭", "焦煤"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["焦炭", "焦煤"]
      - name: "电力"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["电力", "电"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["电力", "电"]
          ecam_in_m_pro_ind_ele_off:
            column: "energy_type"
            keywords: ["电力", "电"]
      - name: "天然气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["天然气", "气"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["天然气", "气"]
  
  # 功能3：跨列映射
  cross_column_mappings:
    product_to_industry:
      source_column: "product_name"
      target_column: "industry"
      mappings:
        "原煤": "其他工业"
        "粗钢": "钢铁"
        "钢材": "钢铁"
        "生铁": "钢铁"
        "焦炭": "石化"
        "水泥": "建材"
        "平板玻璃": "建材"
        "发电量": "其他工业"
        "电子计算机整机": "其他工业"
        "太阳能电池": "其他工业"
        "非常规天然气": "石化"
