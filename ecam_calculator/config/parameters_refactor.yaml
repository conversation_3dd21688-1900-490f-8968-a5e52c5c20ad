# 重构阶段配置文件
# 用于主程序到数据标准化阶段的重构
# 基于 parameters_backup.yaml 创建，逐步优化配置结构

# 1. 数据库连接配置
database_settings:
  host: "localhost"  # Mac环境下使用localhost连接Docker容器
  port: 9999         # Docker端口映射到Mac主机的端口
  database: "ecam_city"
  user: "user"
  password: "password"

# 2. 作业运行配置
job_settings:
  start_year: 2020
  end_year: 2020
  province: "山西"
  default_year: 2020

# 3. 省份和地市映射
province_city_mapping:
  - province: "山西"
    cities:
      - "太原"
      - "大同"
      - "阳泉"
      - "长治"
      - "晋城"
      - "朔州"
      - "晋中"
      - "运城"
      - "忻州"
      - "临汾"
      - "吕梁"

# 4. 标准化映射配置
standardization_mappings:
  # 功能1+2：列内映射 + 层次关系
  column_standardizations:
    # 4.1 地区标准化
    regions:
      - name: "山西"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["山西", "山西省"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["山西", "山西省"]
          fct_y_gdp:
            column: "area"
            keywords: ["山西", "山西省"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["山西", "山西省"]
          fct_y_prd_output:
            column: "area"
            keywords: ["山西", "山西省"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["山西", "山西省"]
      - name: "太原"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["太原"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["太原"]
          fct_y_gdp:
            column: "area"
            keywords: ["太原"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["太原"]
          fct_y_prd_output:
            column: "area"
            keywords: ["太原"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["太原"]
      - name: "大同"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["大同"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["大同"]
          fct_y_gdp:
            column: "area"
            keywords: ["大同"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["大同"]
          fct_y_prd_output:
            column: "area"
            keywords: ["大同"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["大同"]
      - name: "阳泉"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["阳泉"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["阳泉"]
          fct_y_gdp:
            column: "area"
            keywords: ["阳泉"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["阳泉"]
          fct_y_prd_output:
            column: "area"
            keywords: ["阳泉"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["阳泉"]
      - name: "长治"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["长治"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["长治"]
          fct_y_gdp:
            column: "area"
            keywords: ["长治"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["长治"]
          fct_y_prd_output:
            column: "area"
            keywords: ["长治"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["长治"]
      - name: "晋城"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["晋城"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["晋城"]
          fct_y_gdp:
            column: "area"
            keywords: ["晋城"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["晋城"]
          fct_y_prd_output:
            column: "area"
            keywords: ["晋城"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["晋城"]
      - name: "朔州"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["朔州"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["朔州"]
          fct_y_gdp:
            column: "area"
            keywords: ["朔州"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["朔州"]
          fct_y_prd_output:
            column: "area"
            keywords: ["朔州"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["朔州"]
      - name: "晋中"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["晋中"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["晋中"]
          fct_y_gdp:
            column: "area"
            keywords: ["晋中"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["晋中"]
          fct_y_prd_output:
            column: "area"
            keywords: ["晋中"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["晋中"]
      - name: "运城"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["运城"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["运城"]
          fct_y_gdp:
            column: "area"
            keywords: ["运城"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["运城"]
          fct_y_prd_output:
            column: "area"
            keywords: ["运城"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["运城"]
      - name: "忻州"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["忻州"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["忻州"]
          fct_y_gdp:
            column: "area"
            keywords: ["忻州"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["忻州"]
          fct_y_prd_output:
            column: "area"
            keywords: ["忻州"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["忻州"]
      - name: "临汾"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["临汾"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["临汾"]
          fct_y_gdp:
            column: "area"
            keywords: ["临汾"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["临汾"]
          fct_y_prd_output:
            column: "area"
            keywords: ["临汾"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["临汾"]
      - name: "吕梁"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["吕梁"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["吕梁"]
          fct_y_gdp:
            column: "area"
            keywords: ["吕梁"]
          fct_y_all_ene_intsty:
            column: "area"
            keywords: ["吕梁"]
          fct_y_prd_output:
            column: "area"
            keywords: ["吕梁"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["吕梁"]
    
    # 4.2 行业标准化
    industries:
      # 宏观行业
      - name: "能源行业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["1.火力发电", "2.供热", "3.煤炭洗选", "4.炼焦", "5.炼油及煤制油", "6.制气", "7.天然气液化", "8.煤制品加工", "9.回收能"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["火力发电", "供热", "煤炭洗选", "炼焦", "炼油及煤制油", "制气", "天然气液化", "煤制品加工", "回收能"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["1.电力、热力的生产和供应业用电量", "2.燃气生产和供应业用电量", "3.水的生产和供应业用电量", "电厂生产全部耗用电量"]
      
      - name: "工业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["2.工业", "一、采矿业", "二、制造业", "三、电力、热力、燃气及水生产和供应业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["一、采矿业", "二、制造业", "三、电力、热力、燃气及水生产和供应业", "2.工业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["二、工业用电量", "（二）制造业用电量", "（三）电力、燃气及水的生产和供应业用电量", "（一）采矿业用电量", "第二产业用电量"]
      
      - name: "建筑业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["3.建筑业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["3.建筑业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["三、建筑业用电量"]
      
      - name: "交通运输业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["4.交通运输业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["4.交通运输业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["四、交通运输业用电量"]
      
      - name: "服务业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["5.服务业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["5.服务业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["五、服务业用电量"]
      
      - name: "农林牧渔业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["6.农林牧渔业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["6.农林牧渔业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["六、农林牧渔业用电量"]
      
      - name: "居民生活业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["7.居民生活业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["7.居民生活业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["七、居民生活业用电量"]
      
      # 工业子行业
      - name: "钢铁"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["黑色金属冶炼和压延加工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["15.黑色金属冶炼和压延加工业", "3.黑色金属矿采选业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["19.黑色金属冶炼和压延加工业用电量", "3.黑色金属矿采选业用电量"]
      
      - name: "有色"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["有色金属冶炼和压延加工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["16.有色金属冶炼和压延加工业", "4.有色金属矿采选业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["20.有色金属冶炼和压延加工业用电量", "4.有色金属矿采选业用电量"]
      
      - name: "石化"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["石油、煤炭及其他燃料加工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["10.石油、煤炭及其他燃料加工业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["14.石油、煤炭及其他燃料加工业用电量"]
      
      - name: "化工"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["化学原料和化学制品制造业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["11.化学原料和化学制品制造业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["15.化学原料和化学制品制造业用电量"]
      
      - name: "造纸"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["造纸和纸制品业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["7.造纸和纸制品业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["11.造纸和纸制品业用电量"]
      
      - name: "建材"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["非金属矿物制品业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["14.非金属矿物制品业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["18.非金属矿物制品业用电量"]
      
      - name: "其他工业"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["其他制造业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["其他制造业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["其他制造业用电量"]
    
    # 4.3 能源品种标准化
    energy_types:
      - name: "原煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭", "煤"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭", "煤"]
      - name: "天然气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["天然气"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["天然气"]
      - name: "电力"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["电力"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["电力"]
      - name: "焦炭"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["焦炭"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["焦炭"]
  
  # 功能3：跨列映射
  cross_column_mappings:
    product_to_industry:
      source_column: "product_name"
      target_column: "industry"
      mappings:
        "原煤": "其他工业"
        "粗钢": "钢铁"
        "生铁": "钢铁"
        "钢材": "钢铁"
        "焦炭": "石化"
        "水泥": "建材"
        "平板玻璃": "建材"
        "发电量": "其他工业"
        "电子计算机整机": "其他工业"
        "太阳能电池": "其他工业"


