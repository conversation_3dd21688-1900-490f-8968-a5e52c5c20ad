"""
计算任务服务 - 负责业务流程编排
专注于协调各个领域服务，不包含具体业务逻辑
"""

import uuid
from typing import List, Optional, Dict, Any
import logging
from dataclasses import dataclass
from datetime import date

from ecam_calculator.domain.model.calculation_job import CalculationJob, JobStatus
from ecam_calculator.domain.model.value_objects import (
    RawData, CityEnergyMatrix, EnergyConsumptionInventory, CarbonEmissionInventory,
    Region, Industry, StandardizedData, StandardizedDataType
)
from ecam_calculator.domain.service.data_standardization_service import DataStandardizationService
from ecam_calculator.domain.service.data_enhancement_service import DataEnhancementService
from ecam_calculator.domain.service.energy_conversion_service import EnergyConversionService
from ecam_calculator.domain.service.constraint_calculation_service import ConstraintCalculationService
from ecam_calculator.domain.service.quality_check_service import QualityCheckService
from ecam_calculator.domain.service.inventory_construction_service import InventoryConstructionService
from ecam_calculator.domain.service.result_output_service import ResultOutputService
from ecam_calculator.domain.service.modeling_prediction_service import ModelingPredictionService
from ecam_calculator.domain.repository.job_repository import JobRepository
from ecam_calculator.infrastructure.config_reader import get_config_reader
import pandas as pd


@dataclass
class CalculationContext:
    """计算任务上下文"""
    start_year: int
    end_year: int
    province: Region
    cities: List[Region]
    job_id: str
    raw_data: Dict[str, Any]  # 存储DataFrame
    standardized_data: Dict[str, Any]  # 存储标准化后的DataFrame
    enhanced_data: Dict[str, Any]  # 存储增强后的DataFrame
    converted_data: Dict[str, Any]  # 存储转换后的DataFrame
    prediction_results: Dict[str, Any]  # 存储预测结果
    constraints: List[Any]  # 存储ConstraintData对象的列表
    city_matrices: Dict[str, CityEnergyMatrix]
    energy_inventory: Optional[EnergyConsumptionInventory] = None
    carbon_inventory: Optional[CarbonEmissionInventory] = None
    quality_report: Optional[Any] = None # 新增：用于存储质量检查报告


class CalculationJobService:
    """应用服务 - 负责业务流程编排"""

    def __init__(
        self,
        job_repository: JobRepository,
        quality_check_service: QualityCheckService,
        data_standardization_service: DataStandardizationService,
        data_enhancement_service: DataEnhancementService,
        constraint_calculation_service: ConstraintCalculationService,
        inventory_construction_service: InventoryConstructionService,
        result_output_service: ResultOutputService,
        energy_conversion_service: Optional[EnergyConversionService] = None,
        modeling_prediction_service: Optional[ModelingPredictionService] = None
    ):
        self.logger = logging.getLogger(__name__)
        self.job_repository = job_repository
        self.quality_check_service = quality_check_service
        self.data_standardization_service = data_standardization_service
        self.data_enhancement_service = data_enhancement_service
        self.constraint_calculation_service = constraint_calculation_service
        self.inventory_construction_service = inventory_construction_service
        self.result_output_service = result_output_service
        self.energy_conversion_service = energy_conversion_service
        self.modeling_prediction_service = modeling_prediction_service
        self.config_reader = get_config_reader()
        
        self.current_job_id = None

    def start_new_job(self, start_year: int, end_year: int, province_name: str):
        """启动一个新的计算任务并执行所有步骤"""
        try:
            # 1. 创建并保存任务
            job = CalculationJob()
            self.current_job_id = job.id
            self.job_repository.save(job)
            self.logger.info(f"任务 {job.id} 已创建，状态为 {job.status.name}")
        
            # 2. 创建上下文
            province_region = Region(name=province_name, level='province')
            city_names = self._get_cities_by_province(province_name)
            city_regions = [Region(name=city_name, level='city') for city_name in city_names]

            context = CalculationContext(
                start_year=start_year,
                end_year=end_year,
                province=province_region,
                cities=city_regions,
                job_id=str(job.id),
                raw_data={},
                standardized_data={},
                enhanced_data={},
                converted_data={},
                prediction_results={},
                constraints=[],
                city_matrices={}
            )

            if not context.cities:
                raise ValueError(f"在配置文件中找不到省份 '{province_name}' 或其下没有城市。")

            # 3. 执行计算流程
            self._execute_calculation_pipeline(job, context)

            return job, context

        except Exception as e:
            self.logger.error(f"启动计算任务失败: {e}")
            if self.current_job_id:
                self._mark_job_failed(job, str(e))
            raise

    def _execute_calculation_pipeline(self, job: CalculationJob, context: CalculationContext):
        """执行计算流程 - 业务流程编排"""
        try:
            # 步骤1: 获取原始数据
            self._step_raw_data_fetching(job, context)
            
            # 步骤2: 数据质量检查（暂时跳过）
            # self._step_data_quality_check(job, context)
            
            # 步骤3: 数据标准化
            self._step_data_standardization(job, context)
            
            # 步骤4: 数据增强
            self._step_data_enhancement(job, context)
            
            # 步骤5: 能源转换
            self._step_energy_conversion(job, context)
            
            # 步骤6: 建模预测
            self._step_modeling_prediction(job, context)
            
            # 步骤7: 生成数据（调用数据生成服务）
            self._step_generate_data(job, context)
            
            # 步骤8: 完成
            self._step_completion(job, context)
            
        except Exception as e:
            self.logger.error(f"计算流程执行失败: {e}")
            self._mark_job_failed(job, str(e))
            raise

    def _step_raw_data_fetching(self, job: CalculationJob, context: CalculationContext):
        """步骤1: 获取原始数据 - 使用简化的DatabaseReader"""
        self.logger.info(f"Job [{job.id}] - 开始执行原始数据获取...")
        
        # 获取计算参数
        start_year = context.start_year
        end_year = context.end_year
        province = context.province.name
        
        # 使用简化的DatabaseReader获取数据
        try:
            # 创建DatabaseReader实例（需要从配置获取数据库配置）
            from ecam_calculator.infrastructure.database_reader import DatabaseReader
            db_config = self.config_reader.get_database_settings()
            db_reader = DatabaseReader(db_config)
            
            # 获取所有数据（不做筛选）
            emission_factors_df, error1 = db_reader.read_emission_factors()
            energy_consumption_df, error2 = db_reader.read_energy_consumption()
            energy_consumption2_df, error2_2 = db_reader.read_energy_consumption2()  # 添加地市工业能源消费数据
            electricity_df, error3 = db_reader.read_electricity_consumption()
            gdp_df, error4 = db_reader.read_gdp_data()
            energy_intensity_df, error5 = db_reader.read_energy_intensity()
            product_output_df, error6 = db_reader.read_industrial_products_output()
            
            # 不进行任何过滤，保持所有原始数据
            self.logger.info(f"省级能源消费数据: {len(energy_consumption_df)} 条记录")
            self.logger.info(f"地市工业能源消费数据: {len(energy_consumption2_df)} 条记录")
            
            # 检查错误
            errors = [error1, error2, error2_2, error3, error4, error5, error6]
            error_messages = [e for e in errors if e]
            if error_messages:
                self.logger.warning(f"部分数据获取失败: {error_messages}")
            
            # 存储原始数据到上下文（直接存储DataFrame）
            context.raw_data = {
                'emission_factors': emission_factors_df,
                'energy_consumption': energy_consumption_df,
                'energy_consumption2': energy_consumption2_df,  # 添加地市工业能源消费数据
                'electricity': electricity_df,
                'gdp_data': gdp_df,
                'energy_intensity': energy_intensity_df,
                'product_output': product_output_df
            }
            
            # 记录数据统计
            total_records = sum(len(df) for df in context.raw_data.values() if not df.empty)
            self.logger.info(f"Job [{job.id}] - 原始数据获取完成，总计 {total_records} 条记录")
            
        except Exception as e:
            self.logger.error(f"Job [{job.id}] - 原始数据获取失败: {e}")
            raise

    def _step_data_standardization(self, job: CalculationJob, context: CalculationContext):
        """步骤3: 数据标准化 - 使用简化的标准化服务"""
        self.logger.info(f"Job [{job.id}] - 开始执行数据标准化...")
        
        try:
            # 调用简化的数据标准化服务
            standardized_results = self.data_standardization_service.standardize_all(context.raw_data)
            context.standardized_data = standardized_results
            
            # 记录标准化结果统计
            total_records = sum(len(df) for df in context.standardized_data.values() if not df.empty)
            self.logger.info(f"Job [{job.id}] - 数据标准化完成，总计 {total_records} 条记录")
            
        except Exception as e:
            self.logger.error(f"Job [{job.id}] - 数据标准化失败: {e}")
            raise

    def _step_data_enhancement(self, job: CalculationJob, context: CalculationContext):
        """步骤4: 数据增强 - 空间降尺度和时间降尺度"""
        self.logger.info(f"Job [{job.id}] - 开始执行数据增强...")
        
        try:
            # 调用数据增强服务
            enhanced_results = self.data_enhancement_service.enhance_data(
                standardized_data=context.standardized_data,
                calculation_context=context
            )
            
            # 将增强结果存储到上下文中
            context.enhanced_data = enhanced_results
            
            # 记录增强结果统计
            total_records = sum(len(df) for df in context.enhanced_data.values() if not df.empty)
            self.logger.info(f"Job [{job.id}] - 数据增强完成，总计 {total_records} 条记录")
            
        except Exception as e:
            self.logger.error(f"Job [{job.id}] - 数据增强失败: {e}")
            raise

    def _step_energy_conversion(self, job: CalculationJob, context: CalculationContext):
        """步骤5: 能源转换 - 将增强后的能源数据转换为标准煤当量"""
        self.logger.info(f"Job [{job.id}] - 开始执行能源转换...")
        
        try:
            # 确保能源转换服务已初始化
            if not self.energy_conversion_service:
                self.energy_conversion_service = EnergyConversionService()
            
            # 获取增强后的能源消费数据和转换因子数据
            spatial_enhanced_df = context.enhanced_data.get('spatial_downscaled_energy', pd.DataFrame())
            temporal_enhanced_df = context.enhanced_data.get('temporal_downscaled_energy', pd.DataFrame())
            conversion_factors_df = context.standardized_data.get('emission_factors', pd.DataFrame())
            
            # 处理空间降尺度后的数据
            if not spatial_enhanced_df.empty:
                converted_spatial_df = self.energy_conversion_service.convert_to_standard_coal(
                    spatial_enhanced_df, conversion_factors_df
                )
                context.converted_data['converted_spatial_energy'] = converted_spatial_df
                self.logger.info(f"Job [{job.id}] - 空间降尺度能源转换完成，处理了 {len(converted_spatial_df)} 行数据")
            
            # 处理时间降尺度后的数据
            if not temporal_enhanced_df.empty:
                converted_temporal_df = self.energy_conversion_service.convert_to_standard_coal(
                    temporal_enhanced_df, conversion_factors_df
                )
                context.converted_data['converted_temporal_energy'] = converted_temporal_df
                self.logger.info(f"Job [{job.id}] - 时间降尺度能源转换完成，处理了 {len(converted_temporal_df)} 行数据")
            
            if spatial_enhanced_df.empty and temporal_enhanced_df.empty:
                self.logger.warning("增强后的能源消费数据为空，跳过能源转换")
                
        except Exception as e:
            self.logger.error(f"Job [{job.id}] - 能源转换失败: {e}")
            raise

    def _step_modeling_prediction(self, job: CalculationJob, context: CalculationContext):
        """步骤6: 建模预测 - ARDL模型构建和基于电力数据的预测"""
        self.logger.info(f"Job [{job.id}] - 开始执行建模预测...")
        
        try:
            # 确保建模预测服务已初始化
            if not self.modeling_prediction_service:
                self.modeling_prediction_service = ModelingPredictionService()
            
            # 获取转换后的能源数据和电力数据
            converted_spatial_df = context.converted_data.get('converted_spatial_energy', pd.DataFrame())
            converted_temporal_df = context.converted_data.get('converted_temporal_energy', pd.DataFrame())
            electricity_df = context.standardized_data.get('electricity', pd.DataFrame())
            
            # 执行建模预测
            prediction_results = self.modeling_prediction_service.build_and_predict(
                converted_data={
                    'converted_spatial_energy': converted_spatial_df,
                    'converted_temporal_energy': converted_temporal_df
                },
                electricity_data=electricity_df,
                calculation_context=context
            )
            
            # 将预测结果存储到上下文中
            context.prediction_results = prediction_results
            
            # 记录预测结果统计
            total_predictions = len(prediction_results.get('prediction_results', []))
            total_models = len(prediction_results.get('ardl_models', {}))
            self.logger.info(f"Job [{job.id}] - 建模预测完成，构建了 {total_models} 个ARDL模型，生成 {total_predictions} 条预测结果")
            
        except Exception as e:
            self.logger.error(f"Job [{job.id}] - 建模预测失败: {e}")
            raise

    def _step_generate_data(self, job: CalculationJob, context: CalculationContext):
        """步骤5: 生成数据 - 调用数据生成服务"""
        self.logger.info(f"Job [{job.id}] - 开始生成结果数据...")
        
        try:
            # 这里应该调用结果数据生成服务
            # 暂时简化实现
            self.logger.info(f"Job [{job.id}] - 结果数据生成完成")
            
        except Exception as e:
            self.logger.error(f"Job [{job.id}] - 结果数据生成失败: {e}")
            raise

    def _step_completion(self, job: CalculationJob, context: CalculationContext):
        """步骤6: 完成任务"""
        self.logger.info(f"Job [{job.id}] - 计算任务完成")
        
        # 更新任务状态为完成
        job.status = JobStatus.COMPLETED
        self.job_repository.save(job)

    def _get_cities_by_province(self, province_name: str) -> List[str]:
        """获取指定省份的城市列表"""
        province_city_mapping = self.config_reader.get_province_city_mapping()
        
        for mapping in province_city_mapping:
            if mapping['province'] == province_name:
                return mapping['cities']
        
        return []

    def _mark_job_failed(self, job: CalculationJob, error_message: str):
        """标记任务为失败状态"""
        try:
            job.status = JobStatus.FAILED
            self.job_repository.save(job)
            self.logger.error(f"任务 {job.id} 已标记为失败: {error_message}")
        except Exception as e:
            self.logger.error(f"标记任务失败状态时出错: {e}")

    def _print_results_summary(self, context: CalculationContext):
        """打印结果摘要"""
        self.logger.info("=== 计算结果摘要 ===")
        self.logger.info(f"计算年份: {context.start_year}-{context.end_year}")
        self.logger.info(f"计算省份: {context.province.name}")
        self.logger.info(f"涉及城市: {len(context.cities)} 个")
        
        if context.standardized_data:
            total_records = sum(len(data) for data in context.standardized_data.values())
            self.logger.info(f"标准化数据记录数: {total_records}")
        
        self.logger.info("=== 摘要结束 ===")
