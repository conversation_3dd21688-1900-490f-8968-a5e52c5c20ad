#!/usr/bin/env python3
"""
主程序入口点 - 用于命令行调用
"""

import argparse
import logging
import sys
import os
from typing import Dict, Any
import yaml

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ecam_calculator.application.calculation_job_service import CalculationJobService
from ecam_calculator.infrastructure.persistence.job_repository_impl import JobRepositoryImpl
from ecam_calculator.domain.service import (
    DataStandardizationService,
    DataEnhancementService,
    ConstraintCalculationService,
    QualityCheckService,
    InventoryConstructionService,
    ResultOutputService,
    EnergyConversionService,
    ModelingPredictionService
)
from ecam_calculator.domain.service.data_enhancement_service import DataEnhancementServiceImpl


def setup_logging(log_level: str = "DEBUG") -> None:
    """设置日志级别"""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        numeric_level = logging.INFO
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_config_from_yaml() -> Dict[str, Any]:
    """从 config.yaml 文件加载配置"""
    config_paths = [
        '/app/ecam_calculator/config/parameters.yaml',  # 绝对路径
        'ecam_calculator/config/parameters.yaml',
        'config/parameters.yaml', 
        'config.yaml', 
        '../config.yaml'
    ]
    for path in config_paths:
        if os.path.exists(path):
            try:
                with open(path, 'r') as f:
                    return yaml.safe_load(f)
            except yaml.YAMLError as e:
                logging.warning(f"无法解析配置文件 {path}: {e}")
    return {}


def get_db_config(args: argparse.Namespace) -> Dict[str, Any]:
    """从命令行参数、环境变量或配置文件获取数据库配置"""
    
    # 1. 从 config.yaml 文件加载基础配置
    config = load_config_from_yaml().get('database_settings', {})

    # 2. 环境变量覆盖
    env_config = {
        'host': os.environ.get('ECAM_DB_HOST'),
        'port': os.environ.get('ECAM_DB_PORT'),
        'user': os.environ.get('ECAM_DB_USER'),
        'password': os.environ.get('ECAM_DB_PASSWORD'),
        'database': os.environ.get('ECAM_DB_NAME')
    }
    config.update({k: v for k, v in env_config.items() if v is not None})

    # 3. 命令行参数覆盖 (最高优先级)
    cli_config = {
        'host': args.db_host,
        'port': args.db_port,
        'user': args.db_user,
        'password': args.db_password,
        'database': args.db_name
    }
    config.update({k: v for k, v in cli_config.items() if v is not None})

    # 4. 验证配置完整性
    required_keys = ['host', 'database', 'user', 'password']
    missing_keys = [key for key in required_keys if key not in config]

    if missing_keys:
        raise ValueError(
            f"数据库配置不完整，缺少以下参数: {', '.join(missing_keys)}。\n"
            "请通过以下任一方式提供配置: \n"
            "  1. 在 config.yaml 文件中设置\n"
            "  2. 设置环境变量 (例如 ECAM_DB_HOST)\n"
            "  3. 使用命令行参数 (例如 --db-host)"
        )
    
    # 5. 设置可选参数的默认值
    config.setdefault('port', 3306)
    config.setdefault('charset', 'utf8mb4')
    
    try:
        config['port'] = int(config['port'])
    except (ValueError, TypeError):
        raise ValueError(f"数据库端口 '{config['port']}' 必须是一个有效的数字。")

    return config


def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="区域'电-能-碳'监测分析系统命令行工具"
    )
    
    # 数据库连接参数
    db_group = parser.add_argument_group('数据库连接参数')
    db_group.add_argument('--db-host', help='数据库主机地址')
    db_group.add_argument('--db-port', type=int, help='数据库端口 (默认: 3306)')
    db_group.add_argument('--db-user', help='数据库用户名')
    db_group.add_argument('--db-password', help='数据库密码')
    db_group.add_argument('--db-name', help='数据库名称')
    
    # 计算任务参数
    task_group = parser.add_argument_group('计算任务参数')
    task_group.add_argument('--start-year', type=int, help='起始年份 (默认读取配置文件)')
    task_group.add_argument('--end-year', type=int, help='结束年份 (默认读取配置文件)')
    task_group.add_argument('--province', help='省份名称 (默认读取配置文件)')
    
    # 其他参数
    parser.add_argument('--log-level', default='DEBUG', 
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='日志级别')
    
    args = parser.parse_args()
    
    # 如果未指定结束年份，则使用起始年份
    if not args.end_year:
        args.end_year = args.start_year
    
    return args


def create_job_service(db_config: Dict[str, Any]) -> CalculationJobService:
    """简单直接的依赖注入 - 创建计算任务服务"""
    
    # 创建基础设施服务
    job_repository = JobRepositoryImpl(db_config)
    
    # 创建领域服务
    quality_check_service = QualityCheckService()
    data_standardization_service = DataStandardizationService()
    data_enhancement_service = DataEnhancementServiceImpl()
    constraint_calculation_service = ConstraintCalculationService()
    inventory_construction_service = InventoryConstructionService()
    result_output_service = ResultOutputService()
    energy_conversion_service = EnergyConversionService()
    modeling_prediction_service = ModelingPredictionService()

    # 创建应用服务
    return CalculationJobService(
        job_repository=job_repository,
        quality_check_service=quality_check_service,
        data_standardization_service=data_standardization_service,
        data_enhancement_service=data_enhancement_service,
        constraint_calculation_service=constraint_calculation_service,
        inventory_construction_service=inventory_construction_service,
        result_output_service=result_output_service,
        energy_conversion_service=energy_conversion_service,
        modeling_prediction_service=modeling_prediction_service
    )


def get_task_parameters(args: argparse.Namespace) -> Dict[str, Any]:
    """获取任务参数"""
    from ecam_calculator.infrastructure.config_reader import get_config_reader
    config_reader = get_config_reader()
    
    # 从命令行、配置文件兜底
    start_year = args.start_year or config_reader.get_job_settings().get('start_year')
    end_year = args.end_year or config_reader.get_job_settings().get('end_year')
    
    # 如果结束年份仍未确定，则默认为起始年份
    if not end_year:
        end_year = start_year
    
    # 获取默认省份
    available_provinces = config_reader.get_province_city_mapping()
    default_province = available_provinces[0]['province'] if available_provinces else None
    province = args.province or default_province
    
    if not all([start_year, end_year, province]):
        raise ValueError("无法确定任务参数（年份和省份），请检查命令行输入或配置文件。")
    
    return {
        'start_year': start_year,
        'end_year': end_year,
        'province': province
    }


def print_results(job_service: CalculationJobService, job, context):
    """打印结果摘要"""
    logger = logging.getLogger(__name__)
    
    logger.info(f"任务 {job.id} 执行完成！")
    
    # 重新获取job的最终状态
    final_job = job_service.job_repository.find_by_id(job.id)
    if final_job:
        logger.info(f"任务状态: {final_job.status.name}")
        # 输出结果摘要
        job_service._print_results_summary(context)
    else:
        logger.error(f"无法获取任务 {job.id} 的最终状态。")


def main() -> int:
    """主函数 - 保持简单直接"""
    try:
        # 1. 解析命令行参数
        args = parse_args()
        
        # 2. 设置日志
        setup_logging(args.log_level)
        logger = logging.getLogger(__name__)
        
        logger.info("=== 区域'电-能-碳'监测分析系统 ===")
        logger.info("正在启动系统...")
        
        # 3. 获取数据库配置
        db_config = get_db_config(args)
        logger.info(f"数据库连接: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # 4. 创建计算任务服务
        job_service = create_job_service(db_config)
        logger.info("系统启动成功！")
        
        # 5. 获取任务参数
        task_params = get_task_parameters(args)
        logger.info(f"任务参数: 年份={task_params['start_year']}-{task_params['end_year']}, 省份={task_params['province']}")
        
        # 6. 执行计算任务
        logger.info("\n--- 启动计算任务 ---")
        job, context = job_service.start_new_job(
            start_year=task_params['start_year'], 
            end_year=task_params['end_year'], 
            province_name=task_params['province']
        )
        
        # 7. 显示结果
        print_results(job_service, job, context)
        
        logger.info("\n=== 系统运行完成 ===")
        return 0
        
    except Exception as e:
        logging.error(f"系统运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
