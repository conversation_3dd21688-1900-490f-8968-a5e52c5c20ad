import uuid
from typing import Dict, Optional, List

from ecam_calculator.domain.model.calculation_job import CalculationJob
from ecam_calculator.domain.repository.job_repository import JobRepository

class JobRepositoryImpl(JobRepository):
    """
    JobRepository 的数据库实现。
    """
    _jobs: Dict[uuid.UUID, CalculationJob] = {}

    def __init__(self, db_config: dict):
        # 注意：当前实现仍为内存实现，db_config 暂未使用。
        # 这是为了保持与其他 repository 构造函数签名的一致性。
        # 未来替换为真实数据库实现时，将在此处初始化数据库连接。
        self._jobs: Dict[uuid.UUID, CalculationJob] = {}

    def save(self, job: CalculationJob) -> None:
        """保存一个任务。如果已存在，则更新。"""
        print(f"任务 {job.id} 已保存")
        self._jobs[job.id] = job

    def find_by_id(self, job_id: uuid.UUID) -> Optional[CalculationJob]:
        """通过ID查找一个任务。"""
        print(f"查找任务 {job_id}")
        return self._jobs.get(job_id)

    def find_all(self) -> List[CalculationJob]:
        """查找所有任务。"""
        print("查询所有任务")
        return list(self._jobs.values())
