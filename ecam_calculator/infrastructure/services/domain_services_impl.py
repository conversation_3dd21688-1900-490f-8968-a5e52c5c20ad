from typing import Optional
import pandas as pd
import numpy as np
from ipfn import ipfn

from ecam_calculator.domain.service.constraint_calculation_service import ConstraintCalculationService
from ecam_calculator.domain.service.balancing_service import BalancingService

class ConstraintCalculationServiceImpl(ConstraintCalculationService):
    def _get_wide_intensity_df(self, intensity_series: pd.DataFrame) -> pd.DataFrame:
        df_long = intensity_series.copy()
        df_wide = df_long.pivot_table(index=['year', 'area'], columns='indicator', values='value').reset_index()
        df_wide.rename(columns={'单位地区生产总值能源消耗(等价值)': 'intensity_value', '万元地区生产总值能耗降低率': 'decline_rate'}, inplace=True)
        if 'intensity_value' not in df_wide.columns: df_wide['intensity_value'] = np.nan
        if 'decline_rate' not in df_wide.columns: df_wide['decline_rate'] = np.nan
        return df_wide

    def _calculate_comparable_intensity(self, intensity_df_wide: pd.DataFrame) -> pd.DataFrame:
        print("正在根据业务规则计算可比价单位GDP能耗...")
        df = intensity_df_wide.sort_values(['area', 'year']).reset_index(drop=True)
        def calculate_for_group(group):
            group = group.reset_index(drop=True)
            for i in range(len(group)):
                if i > 0 and pd.isna(group.loc[i, 'intensity_value']) and not pd.isna(group.loc[i-1, 'intensity_value']):
                    multiplier = (100 + group.loc[i, 'decline_rate']) / 100
                    group.loc[i, 'intensity_value'] = group.loc[i-1, 'intensity_value'] * multiplier
            group.rename(columns={'intensity_value': 'comparable_intensity'}, inplace=True)
            return group
        result_df = df.groupby('area').apply(calculate_for_group).reset_index(drop=True)
        if result_df['comparable_intensity'].isnull().any():
            missing_areas = result_df[result_df['comparable_intensity'].isnull()]['area'].unique()
            print(f"警告：地区 {missing_areas} 因缺少起始能耗强度绝对值，无法完成计算。")
        print("可比价单位GDP能耗计算完成。")
        return result_df

    def calculate_city_total_consumption(self, gdp_series: pd.DataFrame, intensity_series: pd.DataFrame) -> pd.DataFrame:
        print("正在计算城市总能耗约束...")
        if gdp_series.empty or intensity_series.empty: return pd.DataFrame()
        intensity_df_wide = self._get_wide_intensity_df(intensity_series)
        intensity_with_comparable = self._calculate_comparable_intensity(intensity_df_wide)
        if 'comparable_intensity' not in intensity_with_comparable.columns or intensity_with_comparable.empty: return pd.DataFrame()
        merged_df = pd.merge(gdp_series, intensity_with_comparable[['year', 'area', 'comparable_intensity']], on=['year', 'area'], how='left')
        merged_df['comparable_intensity'] = merged_df.groupby('area')['comparable_intensity'].ffill().bfill()
        if 'comparable_intensity' not in merged_df.columns or merged_df['comparable_intensity'].isnull().all(): return pd.DataFrame()
        merged_df['total_energy_consumption'] = merged_df['value'] * merged_df['comparable_intensity']
        print("城市总能耗约束计算完成。")
        return merged_df[['year', 'area', 'total_energy_consumption']]

class BalancingServiceImpl(BalancingService):
    def build_preliminary_macro_matrix(self, provincial_energy_df: pd.DataFrame, city_electricity_df: pd.DataFrame) -> pd.DataFrame:
        print("--- 正在构建初步的宏观矩阵 (数据下沉) ---")
        if provincial_energy_df.empty or city_electricity_df.empty: return pd.DataFrame()
        
        # 使用 standard_value 进行计算
        total_provincial_electricity = city_electricity_df.groupby('year')['standard_value'].transform('sum')
        city_electricity_df['electricity_ratio'] = city_electricity_df['standard_value'] / total_provincial_electricity
        
        provincial_subset = provincial_energy_df[['year', 'standard_industry', 'standard_value']].copy()
        provincial_subset.rename(columns={'standard_value': 'provincial_value'}, inplace=True)
        
        merged_df = pd.merge(city_electricity_df[['year', 'area', 'electricity_ratio']], provincial_subset, on='year')
        merged_df['estimated_value'] = merged_df['provincial_value'] * merged_df['electricity_ratio']
        
        macro_matrix = merged_df.pivot_table(values='estimated_value', index='standard_industry', columns='area', aggfunc='sum').fillna(0)
        print("初步宏观矩阵构建完成 (M1):")
        print(macro_matrix)
        return macro_matrix

    def balance_matrix(self, matrix_to_balance: pd.DataFrame, row_constraints: pd.Series, column_constraints: pd.Series, max_iteration: int = 1000, epsilon: float = 1e-4) -> Optional[pd.DataFrame]:
        print("正在使用IPF算法平衡数据...")
        print("\n--- IPF算法输入诊断 ---")
        print(f"待平衡矩阵 (M1) 的形状: {matrix_to_balance.shape}")
        with pd.option_context('display.max_rows', None, 'display.max_columns', None, 'display.width', 1000):
            print("--- 完整的初始矩阵 (M1) ---"); print(matrix_to_balance); print("-" * 30)
        if row_constraints is not None:
            print(f"行约束 (省级行业) 的长度: {len(row_constraints)}"); print(f"行约束的总和: {row_constraints.sum()}"); print("行约束索引:", row_constraints.index.tolist()); print("矩阵索引:", matrix_to_balance.index.tolist())
        if column_constraints is not None:
            print(f"列约束 (地市总量) 的长度: {len(column_constraints)}"); print(f"列约束的总和: {column_constraints.sum()}"); print("列约束索引:", column_constraints.index.tolist()); print("矩阵列名:", matrix_to_balance.columns.tolist())
        if row_constraints is not None and column_constraints is not None:
            if not np.isclose(row_constraints.sum(), column_constraints.sum()): print("!!! 警告：行约束总和与列约束总和不相等，IPF可能会失败。")
        print("-" * 21 + "\n")
        
        try:
            ipf = ipfn.ipfn(matrix_to_balance, 
                       aggregates=[row_constraints, column_constraints], 
                       dimensions=[[0], [1]],
                       max_iteration=max_iteration,
                       convergence_rate=epsilon)
            balanced_matrix = ipf.iteration()
            balanced_matrix = pd.DataFrame(balanced_matrix, index=matrix_to_balance.index, columns=matrix_to_balance.columns)
        except Exception as e:
            print(f"IPF算法执行失败: {e}")
            print("返回原矩阵")
            return matrix_to_balance # 返回原始矩阵以允许流程继续
            
        return balanced_matrix
