"""
配置文件读取器
用于读取和解析业务参数配置文件
"""

import yaml
import os
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path


class ConfigReader:
    """配置文件读取器"""
    
    def __init__(self, config_path: str = "ecam_calculator/config/parameters.yaml"):
        self.config_path = Path(config_path)
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def get_all_table_configs(self) -> Dict[str, Any]:
        """获取所有表配置"""
        return self._config.get('table_configs', {})
    
    def get_table_column_mappings(self, table_name: str) -> Dict[str, str]:
        """获取指定表的列名映射"""
        table_configs = self.get_all_table_configs()
        table_config = table_configs.get(table_name, {})
        return table_config.get('column_mappings', {})
    

    
    def get_table_config(self, table_name: str) -> Optional[Dict[str, Any]]:
        """获取指定表的配置"""
        table_configs = self.get_all_table_configs()
        return table_configs.get(table_name)
    
    def get_energy_factor_table_config(self) -> Optional[Dict[str, Any]]:
        """获取能源因子表的配置"""
        table_configs = self.get_all_table_configs()
        for table_name, config in table_configs.items():
            if config.get('type') == 'energy_factor':
                # 将表名添加到配置中，方便后续使用
                config['table_name'] = table_name
                return config
        return None

    def get_energy_types(self) -> Dict[str, List[Dict[str, str]]]:
        """获取能源品种配置"""
        return self._config.get('energy_types', {})
        
    def get_database_settings(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self._config.get('database_settings', {})

    def get_job_settings(self) -> Dict[str, Any]:
        """获取作业运行配置"""
        return self._config.get('job_settings', {})

    def get_indicator_categories(self) -> Dict[str, Dict[str, Any]]:
        """获取指标分类配置"""
        return self._config.get('indicator_categories', {})
    
    def get_industries(self) -> List[Dict[str, Any]]:
        """
        获取行业映射配置
        
        Returns:
            行业配置列表
        """
        return self._config.get('industries', [])
    
    def get_industry_hierarchy(self) -> Dict[str, str]:
        """
        获取行业层次结构映射
        
        Returns:
            子行业到父行业的映射字典
        """
        industries = self.get_industries()
        hierarchy = {}
        
        for industry in industries:
            name = industry['name']
            parent = industry['parent']
            hierarchy[name] = parent
        
        return hierarchy
    
    def get_industry_column_mappings(self) -> Dict[str, Dict[str, Dict[str, List[str]]]]:
        """
        获取行业列映射配置
        """
        industries = self.get_industries()
        mappings = {}
        
        for industry in industries:
            name = industry['name']
            column_mappings = industry.get('column_mappings', {})
            mappings[name] = column_mappings
        
        return mappings
    
    def get_standardization_mappings(self) -> Dict[str, Any]:
        """获取标准化映射配置"""
        mappings = self._config.get('standardization_mappings', {})
        if not mappings:
            logger = logging.getLogger(__name__)
            logger.warning("未找到标准化映射配置，将使用空配置")
        return mappings
    
    def get_column_standardizations(self, category: str) -> List[Dict[str, Any]]:
        """获取列标准化配置"""
        mappings = self.get_standardization_mappings()
        column_configs = mappings.get('column_standardizations', {})
        return column_configs.get(category, [])
    
    def get_cross_column_mappings(self) -> Dict[str, Any]:
        """获取跨列映射配置"""
        mappings = self.get_standardization_mappings()
        return mappings.get('cross_column_mappings', {})
    
    def get_hierarchy(self, category: str) -> Dict[str, str]:
        """获取层次关系"""
        standardizations = self.get_column_standardizations(category)
        hierarchy = {}
        for config in standardizations:
            name = config.get('name', '')
            parent = config.get('parent')
            if parent:
                hierarchy[name] = parent
        return hierarchy
    
    def get_product_to_industry_mapping(self) -> Dict[str, str]:
        """获取产品到行业映射"""
        mappings = self.get_standardization_mappings()
        cross_configs = mappings.get('cross_column_mappings', {})
        product_config = cross_configs.get('product_to_industry', {})
        return product_config.get('mappings', {})
    
    def validate_standardization_config(self) -> bool:
        """验证标准化配置的完整性"""
        try:
            mappings = self.get_standardization_mappings()
            if not mappings:
                return False
            
            # 验证必需的顶级键
            required_keys = ['column_standardizations', 'cross_column_mappings']
            for key in required_keys:
                if key not in mappings:
                    return False
            
            # 验证列标准化配置
            column_configs = mappings.get('column_standardizations', {})
            if not isinstance(column_configs, dict):
                return False
            
            # 验证跨列映射配置
            cross_configs = mappings.get('cross_column_mappings', {})
            if not isinstance(cross_configs, dict):
                return False
            
            return True
        except Exception:
            return False
    
    def get_province_city_mapping(self) -> List[Dict[str, Any]]:
        """
        获取省份城市映射
        
        Returns:
            省份城市映射配置列表
        """
        return self._config.get('province_city_mapping', [])
    
    def get_industrial_process_emission_factors(self) -> Dict[str, float]:
        """
        获取工业过程排放因子
        
        Returns:
            排放因子配置字典
        """
        return self._config.get('industrial_process_emission_factors', {})
    

    
    def get_energy_types_by_category(self, category: str) -> List[str]:
        """获取指定类别的能源品种"""
        energy_types = self.get_energy_types()
        category_types = energy_types.get(category, [])
        
        if isinstance(category_types, list):
            return [item['name'] if isinstance(item, dict) else item for item in category_types]
        return []
    
    def get_unit_conversions(self) -> Dict[str, Dict[str, float]]:
        """获取单位转换配置"""
        return self._config.get('unit_conversions', {})
    
    def get_energy_type_standardization(self) -> Dict[str, str]:
        """获取能源品种标准化映射"""
        return self._config.get('energy_type_standardization', {})
    
    def get_default_year(self) -> int:
        """获取默认年份"""
        job_settings = self.get_job_settings()
        return job_settings.get('default_year', 2020)


# 全局配置读取器实例
_config_reader = None

def get_config_reader(config_path: str = "ecam_calculator/config/parameters.yaml") -> ConfigReader:
    """获取全局配置读取器实例"""
    global _config_reader
    if _config_reader is None:
        if config_path:
            _config_reader = ConfigReader(config_path)
        else:
            _config_reader = ConfigReader()
    return _config_reader
