"""
简化的数据库读取器
直接读取原始数据，不做任何筛选，返回DataFrame和错误信息
"""

import pandas as pd
import mysql.connector
import logging
from datetime import date
from typing import Dict, List, Any, Optional, Tuple
from contextlib import contextmanager


class DatabaseReader:
    """数据库读取器 - 直接读取原始数据，不做筛选"""
    
    def __init__(self, db_config: Dict[str, Any]):
        self.connection_params = {
            'host': db_config['host'],
            'port': int(db_config['port']),
            'database': db_config['database'],
            'user': db_config['user'],
            'password': db_config['password'],
            'charset': 'utf8',
            'collation': 'utf8_general_ci',
            'use_unicode': True
        }
        self.logger = logging.getLogger(__name__)
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接"""
        conn = None
        try:
            self.logger.debug("尝试连接数据库...")
            conn = mysql.connector.connect(**self.connection_params)
            self.logger.info("数据库连接成功！")
            yield conn
        except mysql.connector.Error as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
        finally:
            if conn and conn.is_connected():
                self.logger.debug("正在关闭数据库连接...")
                conn.close()
    
    def read_emission_factors(self) -> Tuple[pd.DataFrame, Optional[str]]:
        """读取所有排放因子数据"""
        self.logger.info("正在获取所有能源排放因子数据...")
        
        try:
            with self._get_connection() as conn:
                query = """
                SELECT 
                    year, source, province as area, factor, method, 
                    industry, energy_type, unit, value 
                FROM ecam_in_energy_factor 
                ORDER BY year DESC, province
                """
                df = pd.read_sql(query, conn)
                
                if df.empty:
                    return pd.DataFrame(), "未找到任何排放因子数据"
                
                # 确保数据类型正确
                if 'value' in df.columns:
                    df['value'] = pd.to_numeric(df['value'], errors='coerce')
                
                self.logger.info(f"获取到{len(df)}条能源因子记录")
                return df, None
                
        except Exception as e:
            error_msg = f"读取排放因子数据失败: {e}"
            self.logger.error(error_msg)
            return pd.DataFrame(), error_msg
    
    def read_energy_consumption(self) -> Tuple[pd.DataFrame, Optional[str]]:
        """读取所有能源消费数据"""
        self.logger.info("正在获取所有年度省级能源消费量数据...")
        
        try:
            with self._get_connection() as conn:
                query = """
                SELECT 
                    YEAR(year) as year, 
                    province, 
                    item, 
                    `convert`, 
                    method, 
                    energy_type, 
                    value, 
                    unit 
                FROM ecam_in_y_pro_ind_ene_off 
                ORDER BY year, province
                """
                df = pd.read_sql(query, conn)
                
                if df.empty:
                    return pd.DataFrame(), "未找到任何能源消费数据"
                
                self.logger.info(f"获取到 {len(df)} 条省级终端消费与加工转换记录")
                return df, None
                
        except Exception as e:
            error_msg = f"读取能源消费数据失败: {e}"
            self.logger.error(error_msg)
            return pd.DataFrame(), error_msg
    
    def read_energy_consumption2(self) -> Tuple[pd.DataFrame, Optional[str]]:
        """读取地市工业能源消费数据"""
        self.logger.info("正在获取地市工业能源消费数据...")
        
        try:
            with self._get_connection() as conn:
                query = """
                SELECT 
                    year, 
                    area, 
                    industry, 
                    `convert`, 
                    method, 
                    energy_type, 
                    value, 
                    unit 
                FROM ecam_in_y_pro_ind_ene2_off 
                ORDER BY year, area
                """
                df = pd.read_sql(query, conn)
                
                if df.empty:
                    return pd.DataFrame(), "未找到任何地市工业能源消费数据"
                
                self.logger.info(f"获取到 {len(df)} 条地市工业能源消费记录")
                return df, None
                
        except Exception as e:
            error_msg = f"读取地市工业能源消费数据失败: {e}"
            self.logger.error(error_msg)
            return pd.DataFrame(), error_msg
    
    def read_electricity_consumption(self) -> Tuple[pd.DataFrame, Optional[str]]:
        """读取所有用电量数据"""
        self.logger.info("正在获取所有月度分行业用电量数据...")
        
        try:
            with self._get_connection() as conn:
                query = """
                SELECT * FROM ecam_in_m_pro_ind_ele_off 
                ORDER BY month, area
                """
                df = pd.read_sql(query, conn)
                
                if df.empty:
                    return pd.DataFrame(), "未找到任何用电量数据"
                
                self.logger.info(f"获取到{len(df)}条月度用电量记录")
                return df, None
                
        except Exception as e:
            error_msg = f"读取用电量数据失败: {e}"
            self.logger.error(error_msg)
            return pd.DataFrame(), error_msg
    
    def read_gdp_data(self) -> Tuple[pd.DataFrame, Optional[str]]:
        """读取所有GDP数据"""
        self.logger.info("正在获取所有年度GDP数据...")
        
        try:
            with self._get_connection() as conn:
                query = """
                SELECT 
                    year, 
                    area, 
                    indicator,
                    record
                FROM fct_y_gdp
                ORDER BY year, area
                """
                
                df = pd.read_sql(query, conn)
                
                if df.empty:
                    return pd.DataFrame(), "未找到任何GDP数据"
                
                self.logger.info(f"成功获取到 {len(df)} 条GDP记录")
                return df, None
                
        except Exception as e:
            error_msg = f"读取GDP数据失败: {e}"
            self.logger.error(error_msg)
            return pd.DataFrame(), error_msg
    
    def read_energy_intensity(self) -> Tuple[pd.DataFrame, Optional[str]]:
        """读取所有能耗强度数据"""
        self.logger.info("正在获取所有年度能耗强度数据...")
        
        try:
            with self._get_connection() as conn:
                query = """
                SELECT 
                    year, 
                    area, 
                    indicator,
                    record
                FROM fct_y_all_ene_intsty
                ORDER BY year, area
                """
                
                df = pd.read_sql(query, conn)
                
                if df.empty:
                    return pd.DataFrame(), "未找到任何能耗强度数据"
                
                self.logger.info(f"成功获取到 {len(df)} 条能耗强度记录")
                return df, None
                
        except Exception as e:
            error_msg = f"读取能耗强度数据失败: {e}"
            self.logger.error(error_msg)
            return pd.DataFrame(), error_msg
    
    def read_industrial_products_output(self) -> Tuple[pd.DataFrame, Optional[str]]:
        """读取所有工业产品产量数据"""
        self.logger.info("正在获取所有年度工业产品产量数据...")
        
        try:
            with self._get_connection() as conn:
                query = """
                SELECT * FROM fct_y_prd_output 
                ORDER BY year, area
                """
                df = pd.read_sql(query, conn)
                
                if df.empty:
                    return pd.DataFrame(), "未找到任何工业产品产量数据"
                
                self.logger.info(f"获取到{len(df)}条工业产品产量记录")
                return df, None
                
        except Exception as e:
            error_msg = f"读取工业产品产量数据失败: {e}"
            self.logger.error(error_msg)
            return pd.DataFrame(), error_msg

    def read_energy_factor(self) -> Tuple[pd.DataFrame, Optional[str]]:
        """读取能源因子表数据"""
        self.logger.info("正在获取能源因子表数据...")
        
        try:
            with self._get_connection() as conn:
                query = """
                SELECT 
                    year, 
                    energy_type, 
                    value as factor,
                    unit,
                    method,
                    source,
                    province,
                    industry
                FROM ecam_in_energy_factor 
                ORDER BY year, energy_type
                """
                df = pd.read_sql(query, conn)
                
                if df.empty:
                    return pd.DataFrame(), "未找到任何能源因子数据"
                
                self.logger.info(f"获取到 {len(df)} 条能源因子记录")
                return df, None
                
        except Exception as e:
            error_msg = f"读取能源因子数据失败: {e}"
            self.logger.error(error_msg)
            return pd.DataFrame(), error_msg

