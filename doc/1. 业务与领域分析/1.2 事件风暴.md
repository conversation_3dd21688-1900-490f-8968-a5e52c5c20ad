## 概述

事件风暴是领域驱动设计的核心实践，通过识别业务流程中的**命令**、**领域事件**、**聚合根**和**策略规则**，构建完整的业务知识图谱。本文档基于**统一语言表(T-XXX术语)**和**业务规则(BR-XXX)**，系统梳理了区域"电-能-碳"监测分析系统的核心业务流程。整个流程由一个 **核算任务 (Calculation Job)** 进行驱动和协调，并拆解为一系列独立的业务步骤，如**质量检查、清单构造、模型构建、预测分析和数据融合**等。

## 事件风暴方法说明

### 核心要素定义

- **命令(Command)**：表达用户意图的动作，触发业务流程
- **领域事件(Domain Event)**：业务中已发生的重要事实，用过去时命名
- **聚合根(Aggregate Root)**：维护业务不变量的一致性边界(★标记)
- **策略规则(Policy/Rule)**：响应事件的业务规则和约束
- **执行者(Actor)**：发起命令的角色或系统
- **后续动作**：事件发生后要执行的下一个命令

### 聚合根识别原则

根据**DDD战术设计**，聚合根(★)的识别遵循以下原则：
- 拥有全局唯一标识
- 维护业务不变量
- 控制状态转换
- 发布领域事件

## 核心事件风暴分析

### 事件风暴表

| 业务流程 | 命令 | 聚合根 | 领域事件 | 执行者 | 后续动作 |
|---------|------|--------|----------|--------|----------|
| **核算任务管理** | 启动一次新的核算 | ★核算任务 | 核算任务已启动 | 分析师/系统 | 加载初始数据 |
| | 加载初始数据 | ★核算任务 | 初始数据已加载 | 核算任务 | 检查数据质量 |
| **质量检查** | 检查数据质量 | ★质量检查器 | 数据质量检查已完成 | 质量检查系统 | 标准化数据格式 |
| | 标记异常数据 | ★质量检查器 | 异常数据已标记 | 质量检查系统 | 处理异常数据 |
| **数据标准化** | 标准化数据格式 | ★数据标准化器 | 数据格式已标准化 | 数据标准化系统 | 构造能源消费清单 |
| **清单构造** | 构造能源消费清单 | ★清单构造器 | 能源消费清单已生成 | 清单构造系统 | 构建能源消费模型 |
| | 构造碳排放清单 | ★清单构造器 | 碳排放清单已生成 | 清单构造系统 | 构建碳排放模型 |
| **模型构建** | 构建能源消费模型 | ★模型构建器 | 能源消费模型已构建 | 模型构建系统 | 执行碳排放预测 |
| | 构建碳排放模型 | ★模型构建器 | 碳排放模型已构建 | 模型构建系统 | 执行碳排放预测 |
| **预测分析** | 执行碳排放预测 | ★预测分析器 | 碳排放预测已完成 | 预测分析系统 | 执行数据融合 |
| **数据融合** | 融合分层次数据 | ★数据融合器 | 分层次数据已融合 | 数据融合系统 | 验证预测结果 |
| **结果验证** | 验证预测结果 | ★结果验证器 | 预测结果已验证 | 结果验证系统 | 输出预测结果 |
| **结果输出** | 输出预测结果 | ★结果输出器 | 预测结果已输出 | 结果输出系统 | 结束核算任务 |

## 事件时序与依赖关系

### 完整的业务流程链

```mermaid
graph TD
    A[启动一次新的核算] --> B[核算任务已启动]
    B --> C[加载初始数据]
    C --> D[初始数据已加载]
    D --> E[检查数据质量]
    E --> F[数据质量检查已完成]
    F --> G[标准化数据格式]
    G --> H[数据格式已标准化]
    H --> I[构造能源消费清单]
    I --> J[能源消费清单已生成]
    J --> K[构建能源消费模型]
    K --> L[能源消费模型已构建]
    L --> M[执行碳排放预测]
    M --> N[碳排放预测已完成]
    N --> O[执行数据融合]
    O --> P[分层次数据已融合]
    P --> Q[验证预测结果]
    Q --> R[预测结果已验证]
    R --> S[输出预测结果]
    S --> T[预测结果已输出]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#e3f2fd
    style D fill:#e8f5e8
    style E fill:#e3f2fd
    style F fill:#e8f5e8
    style G fill:#e3f2fd
    style H fill:#e8f5e8
    style I fill:#e3f2fd
    style J fill:#e8f5e8
    style K fill:#e3f2fd
    style L fill:#e8f5e8
    style M fill:#e3f2fd
    style N fill:#e8f5e8
    style O fill:#e3f2fd
    style P fill:#e8f5e8
    style Q fill:#e3f2fd
    style R fill:#e8f5e8
    style S fill:#e3f2fd
    style T fill:#e8f5e8
    style U fill:#e3f2fd
    style V fill:#e8f5e8
```

**流程说明**：
- **蓝色**：命令(Command) - 系统要执行的动作
- **绿色**：领域事件(Domain Event) - 已发生的事实

## 聚合根职责边界

### 核心聚合根及其职责

#### 1. 核算任务聚合根 (Calculation Job Aggregate)
- **职责**: 作为一个流程管理器，负责协调从数据加载到结果输出的整个端到端业务流程。它维护了整个任务的当前状态，并在收到下游聚合的完成事件后，触发下一个聚合开始工作。
- **事件**: 核算任务已启动、初始数据已加载、核算任务已完成、核算任务已失败。
- **不变量**: 任务的唯一标识、任务状态（进行中、已完成、失败）、任务的输入参数。

#### 2. 质量检查器聚合根 (Quality Checker Aggregate)
- **触发事件**: `初始数据已加载`
- **职责**: 负责数据质量检查和异常标记。
- **发布事件**: 数据质量检查已完成、异常数据已标记。
- **不变量**: 质量检查规则、异常处理策略。

#### 3. 清单构造器聚合根 (Inventory Constructor Aggregate)
- **触发事件**: `数据质量检查已完成`
- **职责**: 负责构造各种业务清单。
- **发布事件**: 能源消费清单已生成、碳排放清单已生成。
- **不变量**: 清单构造规则、数据完整性。

#### 4. 模型构建器聚合根 (Model Builder Aggregate)
- **触发事件**: `能源消费清单已生成` 或 `碳排放清单已生成`
- **职责**: 负责构建各种业务模型，并通过超参数调优寻找最佳模型。
- **发布事件**: 能源消费模型已构建、碳排放模型已构建。
- **不变量**: 模型构建规则、模型结构、调优目标函数。

#### 5. 数据融合器聚合根 (Data Fuser Aggregate)
- **触发事件**: `碳排放预测已完成`
- **职责**: 负责融合不同回归模型预测的分层次数据，确保地区分行业-地区数据的一致性。
- **发布事件**: 分层次数据已融合。
- **不变量**: 数据融合规则、一致性约束、层次关系、预测结果质量。

#### 6. 预测分析器聚合根 (Prediction Analyzer Aggregate)
- **触发事件**: `能源消费模型已构建` 或 `碳排放模型已构建`
- **职责**: 负责执行预测分析。
- **发布事件**: 碳排放预测已完成。
- **不变量**: 预测算法、预测参数。

#### 7. 结果验证器聚合根 (Result Validator Aggregate)
- **触发事件**: `分层次数据已融合`
- **职责**: 负责验证预测结果。
- **发布事件**: 预测结果已验证。
- **不变量**: 验证规则、质量标准。

#### 8. 结果输出器聚合根 (Result Outputter Aggregate)
- **触发事件**: `预测结果已验证`
- **职责**: 负责输出预测结果。
- **发布事件**: 预测结果已输出。
- **不变量**: 输出格式、数据标准。

## 策略规则与业务约束

### 核心业务规则映射

基于**业务规则文档(1.4 业务规则.md)**，事件风暴中的策略规则与正式业务规则的映射关系：

- **数据源优先级规则** ↔ BR-001 数据源优先级规则
- **数据完整性检查规则** ↔ BR-002 数据完整性检查规则
- **能源消费清单完整性规则** ↔ BR-004 能源消费清单完整性规则
- **数据一致性验证规则** ↔ BR-005 数据一致性验证规则
- **能源消费约束规则** ↔ BR-100 能源消费关系
- **数据融合策略规则** ↔ BR-110 数据融合一致性规则
- **排放计算规则** ↔ BR-200 最新转换系数要求
- **预测时间范围规则** ↔ BR-010 预测时间范围规则
- **数据输出格式规则** ↔ BR-011 预测不确定性量化规则

### 核心业务约束

1. **数据完整性约束**：确保获取的数据包含必要的地区、时间、行业和能源品种维度
2. **能源消费约束**：分行业分品种化石能源消费+可再生能源消费+电力调入调出=地区能源消费总量
3. **数据融合约束**：地区分行业预测数据之和应与地区总量预测数据保持一致
4. **数据输出约束**：预测结果应提供95%置信区间和主要不确定性来源分析

---

*本事件风暴分析为区域"电-能-碳"监测分析系统提供了完整的业务流程视图，为后续的战术设计和微服务架构设计奠定了坚实基础。文档严格遵循DDD原则，确保概念一致性和可追溯性。*
