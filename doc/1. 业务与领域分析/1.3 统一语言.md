## 1.3 统一语言

### 1.3.1 概述

统一语言是领域驱动设计的核心，旨在消除业务专家与技术团队之间的沟通障碍。本文档定义了“区域‘电-能-碳’监测分析系统”中的核心术语，确保所有项目成员在所有沟通渠道（文档、代码、会议）中使用相同、无歧义的词汇。

本文档的术语体系与**《1.1 领域概述》**和**《1.2 事件风暴》**保持严格一致，并按照核心业务流程进行组织。

### 1.3.2 核心流程与参与者 (Core Process & Actors)

| 编号 | 英文 | 中文 | 上下文 | 定义 |
| :---: | :--- | :--- | :--- | :--- |
| T-001 | Calculation Job | 核算任务 | 核心流程 | **(聚合根/实体)** 代表一次从“启动”到“输出”的完整端到端流程。是整个业务流程的状态机和协调者。 |
| T-002 | Process Step Aggregate | 流程步骤聚合 | 核心流程 | 一类无状态、生命周期短暂的聚合，负责执行流程中的某个具体步骤。例如 `QualityChecker`, `ModelBuilder`。 |
| T-003 | Value Object (VO) | 值对象 | 核心流程 | 在流程中传递的不可变数据包，如`QualityCheckedData`, `PredictionModel`。它们没有唯一ID。 |
| T-004 | Domain Service | 领域服务 | 核心流程 | 封装无状态的、跨对象的领域逻辑，如`PredictionService`, `BalancingService`。 |
| T-005 | Repository | 资源库 | 核心流程 | 领域层与持久化层的边界，负责领域对象的存取。如`JobRepository`。 |

### 1.3.3 数据输入与原始形态 (Data Input & Raw Forms)

| 编号 | 英文 | 中文 | 上下文 | 定义 |
| :---: | :--- | :--- | :--- | :--- |
| T-006 | Raw Data | 原始数据 | 数据输入 | 从外部数据源（如MySQL表）获取的、未经处理的原始记录。 |
| T-007 | Raw Data Repository | 原始数据资源库 | 数据输入 | 负责从多个异构的**输入表**中读取数据，并将其转换为统一内部结构的**防腐层 (ACL)**。 |
| T-008 | Statistical Caliber | 统计口径 | 数据输入 | 定义数据统计范围、方法和分类的标准，可能会随时间（如年份）发生变化。 |
| T-009 | Energy Factor | 能源/排放因子 | 数据输入 | 用于在不同能源单位或能源与碳排放间进行转换的系数。包括**能源折标系数**和**碳排放因子**。 |
| T-010 | Energy Intensity | 能耗强度 | 数据输入 | 指单位产值（如万元GDP）所消耗的能源量。 |

### 1.3.4 数据处理与清单构造 (Processing & Inventory Construction)

| 编号 | 英文 | 中文 | 上下文 | 定义 |
| :---: | :--- | :--- | :--- | :--- |
| T-011 | Quality Checker | 质量检查器 | 数据处理 | **(流程步骤聚合)** 负责对原始数据执行完整性、一致性等业务规则校验。 |
| T-012 | Data Standardizer | 数据标准化器 | 数据处理 | **(流程步骤聚合)** 负责对通过质量检查的数据进行清洗、格式统一，特别是对**不规范的行业文本**进行映射和聚合。 |
| T-013 | Inventory Constructor | 清单构造器 | 数据处理 | **(流程步骤聚合)** 负责执行“约束-分配-对齐”的核心逻辑，以构造清单。 |
| T-014 | Energy Consumption Inventory | 能源消费清单 | 数据处理 | **(值对象)** 一份结构化的、描述各行业化石能源消费**实物量**的数据清单。 |
| T-015 | Carbon Emission Inventory | 碳排放清单 | 数据处理 | **(值对象)** 一份结构化的、描述能源活动和工业过程碳排放的目录。 |
| T-016 | Activity Data | 活动数据 | 数据处理 | 描述导致温室气体排放的人类活动强度的量化数据，如能源消费量、工业产品产量。 |

### 1.3.5 核心算法与计算 (Core Algorithms & Calculations)

| 编号 | 英文 | 中文 | 上下文 | 定义 |
| :---: | :--- | :--- | :--- | :--- |
| T-017 | Constant-Price GDP | 不变价GDP | 核心算法 | **(值对象)** 为了消除价格波动影响，以某个基准年份的价格计算的GDP序列。 |
| T-018 | Comparable-Price Energy Intensity | 可比价能耗强度 | 核心算法 | 与不变价GDP对应，剔除了价格变动因素的、可在不同年份间直接比较的能耗强度序列。 |
| T-019 | Cumulative Index Method | 累计指数法 | 核心算法 | 一种通过累计乘以变化率或指数来计算“不变价”序列的核心算法。 |
| T-020 | Iterative Proportional Fitting (IPF) | 迭代比例拟合 | 核心算法 | 一种用于对多维表格数据进行平衡，使其在各维度上的总和满足预设约束的数学算法。 |
| T-021 | Balancing Service | 平衡服务 | 核心算法 | **(领域服务)** 封装了IPF算法，负责对多层级的清单数据进行对齐。 |
| T-022 | Constant Price Calculation Service | 不变价计算服务 | 核心算法 | **(领域服务)** 封装了累计指数法，负责计算不变价GDP和可比价能耗强度。 |

### 1.3.6 建模与预测 (Modeling & Prediction)

| 编号 | 英文 | 中文 | 上下文 | 定义 |
| :---: | :--- | :--- | :--- | :--- |
| T-023 | Model Builder | 模型构建器 | 建模与预测 | **(流程步骤聚合)** 负责构建、训练和**超参数调优**各类预测模型。 |
| T-024 | Prediction Analyzer | 预测分析器 | 建模与预测 | **(流程步骤聚合)** 负责调用`PredictionService`，执行预测并生成`PredictionResult`实体。 |
| T-025 | Prediction Service | 预测服务 | 建模与预测 | **(领域服务)** 封装了调用具体模型（如ARDL）执行预测的逻辑。 |
| T-026 | Prediction Result | 预测结果 | 建模与预测 | **(实体)** 封装一次预测分析的结果，其状态会从`Generated`变为`Fused`。 |
| T-027 | Data Fuser | 数据融合器 | 建模与预测 | **(流程步骤聚合)** 负责融合不同层级的预测数据，确保结果一致性。 |

### 1.3.7 结果输出 (Result Output)

| 编号 | 英文 | 中文 | 上下文 | 定义 |
| :---: | :--- | :--- | :--- | :--- |
| T-028 | Result Outputter | 结果输出器 | 结果输出 | **(流程步骤聚合)** 负责将最终结果格式化为`FinalEmissionData`值对象并交由资源库持久化。 |
| T-029 | Final Emission Data | 最终排放数据 | 结果输出 | **(值对象)** 封装了单条最终输出的、结构化的排放数据记录。 |

### 1.3.8 使用指南

1.  **术语权威性**: 本文档是项目中所有术语的唯一真实来源 (Single Source of Truth)。
2.  **强制使用**: 所有团队成员在文档、代码（类名、变量、API）、数据库设计和日常沟通中，**必须**使用本表定义的术语。
3.  **结构化组织**: 术语已按照核心业务流程进行分类，新增术语应归入最合适的类别。
4.  **持续演进**: 业务演进或认知深化时，可通过正式评审流程对本表进行更新。所有变更必须同步到所有相关的项目资产中。
5.  **跨团队协作**: 本文档是能源专家、数据科学家、软件工程师和业务分析师之间协作的基石，确保所有人对业务有共同的理解。
