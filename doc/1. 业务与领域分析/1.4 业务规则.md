
> [!tldr] 业务规则表使用指南
> 1. **规则体系管理**: 本表建立了碳排放核算系统的正式业务规则体系，`BR-[流程代码]-[序号]` 的编号格式便于规则的引用和跟踪。
> 2. **规则分层**: 规则按照核心业务流程划分为9大类，与《1.2 事件风暴》保持一致。
> 3. **规则重要性**: 每条规则设定了优先级（高、中、低），用于指导开发和测试的优先级。
> 4. **规则应用**:
> 	   - **开发**: 作为功能实现的技术规格和验收标准。
> 	   - **测试**: 作为测试用例设计的核心依据。
> 	   - **运维**: 作为系统运行时数据和流程的监控基准。
> 5. **规则更新机制**: 规则的任何变更都必须通过正式的评审流程，并同步更新到所有相关文档和代码中。

这份业务规则表与《1.3 统一语言》密切关联，共同构成领域驱动设计的核心知识体系，确保系统实现与业务需求的一致性。

## 1.4.1 数据获取 (Data Acquisition)

| 规则ID | 规则名称 | 规则描述 | 优先级 | 验证方式 | 违规处理 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| BR-ACQ-001 | 统计报表结构符合性 | 获取的`统计报表` (`Statistical Report`) 结构必须符合预定义的`报表模板` (`Report Template`)。 | 高 | 模板匹配验证 | 拒绝获取，记录结构不匹配错误 |

## 1.4.2 数据质量检查 (Data Quality Check)

| 规则ID | 规则名称 | 规则描述 | 优先级 | 验证方式 | 违规处理 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| BR-QC-001 | 数据质量评估强制性 | 所有获取的数据必须经过`质量评估作业` (`Quality Assessment Job`) 的处理。 | 高 | 自动化质量检查流程 | 拒绝进入下一环节，生成质量报告 |
| BR-QC-002 | 能源数据平衡原则 | 能源数据必须满足物质平衡`一致性规则` (`Consistency Rule`)，如：消费量 ≤ 购入量 + 库存变化。 | 高 | 平衡公式验证 | 标记平衡异常，启动人工审核流程 |
| BR-QC-004 | 数据质量标识要求 | 所有数据记录必须附加`数据质量标识` (`Data Quality Flag`)，区分实测、估算、推算等。 | 高 | 元数据检查 | 标记为“未标识”，使用默认最低等级 |
| BR-QC-005 | 数据验证审计日志 | `数据验证` (`Data Validation`) 过程必须生成详细的审计日志。 | 中 | 日志完整性检查 | 系统告警，提示补充日志记录 |

## 1.4.3 数据标准化 (Data Standardization)

| 规则ID | 规则名称 | 规则描述 | 优先级 | 验证方式 | 违规处理 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| BR-STD-001 | 原始记录保留原则 | `数据清洗作业` (`Data Cleaning Job`) 过程必须保留原始记录，所有修改应创建新版本。 | 高 | 版本历史审计 | 系统阻止覆盖操作 |
| BR-STD-002 | 数据格式国标符合性 | `数据标准化` (`Data Standardization`) 后的数据格式必须符合相关国家标准。 | 高 | 格式标准验证 | 转换为标准格式，记录转换日志 |
| BR-STD-003 | 填补数据估计值标识 | 通过`数据填补` (`Data Imputation`) 生成的数据必须明确标识为估计值，并记录所用方法。 | 高 | 元数据标记检查 | 自动添加“估计值”及方法标记 |

## 1.4.4 清单构造 (Inventory Construction)

| 规则ID | 规则名称 | 规则描述 | 优先级 | 验证方式 | 违规处理 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| BR-INV-001 | 能源消费关系守恒 | `碳排放清单` (`Carbon Emission Inventory`) 中的能源消费数据必须满足基本消费关系：分行业分品种消费 + 调入/调出 = 地区消费总量。 | 高 | 消费关系验证 | 标记不一致项，要求人工校准 |
| BR-INV-002 | 行业分类标准符合性 | 清单中的行业分类必须遵循《国民经济行业分类》(GB/T 4754)标准或配置文件中定义的映射关系。 | 高 | 分类映射验证 | 自动转换为标准分类 |
| BR-INV-003 | 数据对齐一致性 | 清单数据在经过**迭代比例拟合(IPF)算法**处理后，必须在所有维度上与上级约束数据保持总和一致。 | 高 | 溯源链完整性检查 | 标记为“溯源不完整” |
| BR-INV-004 | 地市能耗总量约束计算规则 | 地市的能源消费总量，必须通过该地市的**不变价GDP**与**可比价的单位GDP能耗**相乘得出。在计算不变价序列时，必须使用**累计指数法**，并根据预设的**时间点**对不同时期的统计口径进行分段处理。 | 高 | 总量对比验证 | 触发自动校准，或标记为“待审核” |

## 1.4.5 模型构建 (Model Construction)

| 规则ID | 规则名称 | 规则描述 | 优先级 | 验证方式 | 违规处理 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| BR-MOD-004 | 分层级独立建模原则 | 必须为每个分析层级（如：地区总览、地区分行业）建立并训练独立的预测模型，以确保预测的针对性和准确性。 | 高 | 模型存在性检查 | 缺少层级模型则无法进行预测 |

## 1.4.7 预测分析 (Prediction Analysis)

| 规则ID | 规则名称 | 规则描述 | 优先级 | 验证方式 | 违规处理 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| BR-PRED-001 | 关键排放源不确定性限制 | 关键`排放源` (`Emission Source`) 的`不确定性分析` (`Uncertainty Analysis`) 结果不应超过±10%。 | 高 | 不确定性阈值检查 | 标记为“高不确定性”，推荐改进数据 |
| BR-PRED-002 | 预测时间范围限制 | 默认的预测时间范围不得超过未来3年。 | 中 | 参数范围检查 | 限制预测范围，需特殊授权以扩展 |

## 1.4.8 数据融合 (Data Fusion)

| 规则ID | 规则名称 | 规则描述 | 优先级 | 验证方式 | 违规处理 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| BR-FUS-001 | 数据融合总量一致性 | `数据融合` (`Data Fusion`) 后的分层次数据必须满足`一致性约束` (`Consistency Constraint`)，即分项之和等于总体。 | 高 | 总量对比验证 | 触发自动校准，或标记为“待审核” |
| BR-FUS-002 | 融合策略动态调整 | `数据融合`的`融合策略` (`Fusion Strategy`) 应根据各预测结果的`数据质量标识`动态调整权重。 | 高 | 权重算法审核 | 重新计算最优权重 |

## 1.4.9 结果验证与输出 (Result Validation & Output)

输出的最终结果是结构化的排放数据。

---

*本业务规则表为系统的核心业务逻辑提供了明确、可执行的规范，并与《1.2 事件风暴》和《1.3 统一语言》共同构成了项目的核心领域知识库。*
