## 能源消费碳排放预测分析

### 领域概述

本领域聚焦于**区域能源消费碳排放的预测分析**，服务对象为各级行政区（从全省到各地市）及其下属行业部门。领域目标是通过**数据读取、清单构造和预测分析**，实现区域碳排放量的科学预测，支撑区域碳达峰与碳中和政策的科学决策。

### 领域边界与外部依赖

- **领域覆盖**：从结构化数据库读取数据，构造完整的能源消费碳排放清单，利用及时更新的电力数据和滞后更新的能耗统计数据构建预测模型，输出地区和行业碳排放量预测的结构化数据。
- **外部依赖**：依赖电网公司提供的区域用电数据、政府统计部门提供的能耗统计数据等外部数据源，系统本身不直接负责原始数据采集。
- **系统边界**：以标准化数据接口为入口，聚焦于**数据读取、清单构造和预测分析**，输出碳排放量预测的结构化数据。

### 数据来源与特点

- **电网公司区域用电数据**：由电网公司按月提供，覆盖各行政区及行业的用电量，具备高时效性和动态更新能力，是反映区域经济活动和能源消费变化的重要基础数据。
- **官方区域统计数据**：由政府统计部门按年发布，涵盖能源生产、消费、转换等多维度信息，数据权威但通常滞后一年，适用于年度核算、趋势分析和政策评估。
- **两类数据协同使用**：既保证了分析的实时性，也兼顾了权威性和完整性，通过能源消费建模实现数据融合。

### 业务范围

- **数据读取**【系统核心】：从结构化数据库读取电网用电数据和能耗统计数据
- **清单构造**【系统核心】：根据部分可得部分缺失的能耗数据构造完整的能源消费碳排放清单，包括能源消费建模、碳排放计算等
- **预测分析**【系统核心】：基于清单构造结果预测地区和行业碳排放量
- **数据输出**【系统输出】：输出碳排放量预测的结构化数据，支持标准格式导出

### 系统边界

本系统**不直接实现数据采集过程**，而是从标准化数据入口开始处理。系统通过定义明确的数据接口规范，确保与外部采集流程的顺畅集成。

### 系统与外部流程的关键依赖

1. 依赖标准化的数据采集培训，确保输入数据质量
2. 依赖领域专家对异常数据的人工审核
3. 系统输出的预测数据需通过能源管理委员会审核后应用

## 省级以下尺度碳排放预测

按照以下方式预测

### 子领域划分

1. **数据读取与清单构造**：负责从结构化数据库读取数据，构造完整的能源消费碳排放清单，包括能源消费建模、碳排放计算等，保障数据完整性和一致性。
2. **清单构造与模型校准**：实现基于电力数据和能耗统计数据的能源消费建模，支持多尺度（省、市、行业）碳排放量预测。
3. **预测分析与数据输出**：基于清单构造结果开展碳排放量预测分析，输出地区和行业碳排放量预测的结构化数据。

### 统一语言与业务规则

- 领域内所有对象、事件、规则均采用统一语言表中的标准术语，贯穿文档、代码、接口与团队沟通，消除歧义。
- 业务规则体系化管理，覆盖数据读取、清单构造、预测分析、数据输出等核心流程，明确每一环节的约束、优先级和违规处理方式。

### 领域协作与演进

- 领域模型以事件风暴为基础，动态反映业务流程和领域知识的演进。
- 统一语言和业务规则为领域扩展、跨团队协作和系统实现提供坚实基础。
- 领域专家、数据科学家、开发团队通过统一语言和规则协同，持续优化领域模型，推动碳排放预测业务的高质量发展。
