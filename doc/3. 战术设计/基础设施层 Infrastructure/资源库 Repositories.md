# 资源库 (Repositories)

## 1. 设计原则

资源库（Repository）是连接**领域层**和**基础设施层**的核心桥梁。它为领域层提供了一个模拟“内存中集合”的接口，来操作需要持久化的领域对象（主要是聚合根），同时隐藏了所有与具体数据库实现相关的细节。

在本系统中，资源库遵循以下设计原则：
- **一个聚合根，一个资源库**: 每个需要独立持久化和检索的聚合根都应该有其专属的资源库。
- **接口在领域层，实现在基础设施层**: 我们在领域层定义资源库的接口（它需要什么），但在基础设施层提供具体的实现（如何从MySQL中存取）。这符合依赖倒置原则。
- **封装数据转换**: 资源库是实现**防腐层 (ACL)** 和**开放主机服务 (OHS)** 的关键。它负责在领域对象和数据库记录之间进行转换。
- **返回领域对象**: 资源库的查询方法必须返回领域层定义的实体或值对象，绝不能泄露任何基础设施层的细节（如DAO或数据库模型）。

## 2. 核心资源库

### 2.1 `JobRepository`

*   **职责**: 负责`CalculationJob`（计算任务）聚合根的持久化和检索。
*   **接口定义**: `ecam_calculator.domain.repository.job_repository.JobRepository`
*   **实现**:
    *   **开发/测试环境**: `ecam_calculator.infrastructure.persistence.job_repository_impl.JobRepositoryImpl` (内存实现)
    *   **生产环境**: (待定) 将来实现一个基于数据库的实现，例如 `DbJobRepositoryImpl`。
*   **方法**:
  - `findById(jobId: UUID): CalculationJob?`：根据ID查找一个核算任务。
  - `save(job: CalculationJob): void`：创建或更新一个核算任务。
  - `findByName(name: String): List<CalculationJob>`：根据名称查找任务（用于UI展示等）。
  - `findActiveJobs(): List<CalculationJob>`：查找所有处于活动状态的任务。

### 2.2 `PredictionResultRepository`
- **关联实体**: `PredictionResult`
- **职责**: 管理 `PredictionResult` 实体的生命周期。虽然它不是聚合根，但由于它需要在流程中被独立加载和修改，因此需要自己的资源库。
- **接口方法**:
  - `findById(resultId: UUID): PredictionResult?`：根据ID查找一个预测结果。
  - `findByJobId(jobId: UUID): List<PredictionResult>`：查找一个任务关联的所有预测结果。
  - `save(result: PredictionResult): void`：创建或更新一个预测结果。

### 2.3 `FinalEmissionDataRepository`
- **关联值对象**: `FinalEmissionData`
- **职责**: **只写不读**。作为领域模型向外部输出数据的官方通道（OHS的一部分）。领域层不关心已输出的数据，只负责“发射后不管”。
- **接口方法**:
  - `saveAll(data: List<FinalEmissionData>): void`：批量保存最终的结构化排放数据。

### 2.4 `RawDataRepository` (防腐层实现)
- **关联外部系统**: 上游MySQL数据库中的11个`ecam_in_*`和`fct_*`原始数据表。
- **职责**: 作为**防腐层 (ACL)**，将外部数据库表的异构结构（不同的列名、数据类型、行业粒度）转换为领域模型内部统一的数据结构（如`TabularData`或特定的值对象），保护领域模型免受外部复杂数据结构的污染。
- **接口方法 (示例)**:
  - `fetchEnergyFactors(year: Year, province: String): TabularData`：从`ecam_in_energy_factor`读取能源折标系数和碳排放因子。
  - `fetchMonthlyIndustryElectricity(province: String, dateRange: DateRange): TabularData`：从`ecam_in_m_pro_ind_ele_off`读取月度分地区分行业用电量。
  - `fetchYearlyProvincialEnergyConsumption(province: String, dateRange: DateRange): TabularData`：从`ecam_in_y_pro_ind_ene_off`读取年度省级分行业分品种能源消费量。
  - `fetchYearlyCityIndustryEnergyConsumption(city: String, dateRange: DateRange): TabularData`：从`ecam_in_y_pro_ind_ene2_off`读取年度地市工业能源消费量。
  - `fetchYearlyGDP(area: String, dateRange: DateRange): TabularData`：从`fct_y_gdp`读取GDP数据。
  - `fetchYearlyEnergyIntensity(area: String, dateRange: DateRange): TabularData`：从`fct_y_all_ene_intsty`读取能耗强度数据。
  - `fetchYearlyIndustrialProductsOutput(area: String, dateRange: DateRange): TabularData`：从`fct_y_prd_output`读取工业产品产量。
  - *注：每个方法的具体实现会包含针对特定表的SQL查询、数据清洗和到`TabularData`值对象的转换逻辑。*

### 2.5 `ConfigReader` (配置读取器)
- **职责**: 读取和管理系统配置，特别是标准化映射配置。
- **接口定义**: `ecam_calculator.infrastructure.config_reader.ConfigReader`
- **核心方法**:
  - `get_standardization_mappings() -> Dict`: 获取标准化映射配置
  - `get_column_standardizations(category: str) -> List[Dict]`: 获取列标准化配置
  - `get_cross_column_mappings() -> Dict`: 获取跨列映射配置
  - `get_hierarchy(category: str) -> Dict[str, str]`: 获取层次关系
  - `get_industries() -> List[Dict]`: 获取行业配置（向后兼容）
  - `get_product_to_industry_mapping() -> Dict[str, str]`: 获取产品到行业映射（向后兼容）
- **配置结构**:
  ```yaml
  standardization_mappings:
    column_standardizations:
      regions: [...]      # 地区标准化配置
      industries: [...]   # 行业标准化配置
      energy_types: [...] # 能源品种标准化配置
    cross_column_mappings:
      product_to_industry: {...}  # 产品到行业映射
  ```

---
*资源库的设计清晰地划分了领域逻辑和持久化逻辑的边界，是实现一个可维护、可测试、可演进的系统的关键。*
