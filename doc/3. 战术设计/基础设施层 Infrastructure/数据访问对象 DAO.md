
# 数据访问与持久化设计 (Data Access and Persistence Design)

## 1. 架构决策：不设独立的DAO层

在传统的分层架构中，有时会在资源库（Repository）和数据库之间引入一个数据访问对象（DAO）层。DAO专门负责与数据库的直接交互（CRUD），而Repository则调用DAO并处理领域对象和持久化对象（PO）之间的转换。

经过考量，为了**简化架构、减少不必要的抽象和样板代码**，本项目决定**不设立独立的DAO层**。

DAO的职责将被合并到**资源库的实现类**中。这意味着：
- **`Repository`的实现类**（例如`JobRepositoryImpl`）将直接依赖于数据库访问技术（如JPA、MyBatis、SQLAlchemy等）。
- 它将直接负责执行SQL查询或调用ORM框架。
- 它将全权负责**领域对象（Entity/VO）** 与 **持久化对象（PO / 数据库表映射类）** 之间的相互转换。

这种简化的方法在保证职责分离的同时，提高了开发效率，对于本项目的规模和复杂度是完全合适的。

## 2. 持久化对象 (Persistence Object, PO) 设计

持久化对象（PO）是直接映射数据库表结构的对象。它们是基础设施层的内部细节，**绝不能**泄露到领域层。在我们的系统中，存在两类PO：

- **输入PO (Input POs)**: 用于从11个上游原始数据表中读取数据。这些PO的结构直接对应源表，它们由`RawDataRepository`在内部使用。
- **输出PO (Output POs)**: 用于将领域模型的计算结果持久化。这些PO由各个核心资源库（如`JobRepository`）在内部使用。

### 2.1 输入PO (Input POs) 示例

以下为几个关键输入表的PO伪代码，用于`RawDataRepository`的实现：

#### `EcamInMProIndEleOffPO` (月度用电量)
```java
@Entity
@Table(name = "ecam_in_m_pro_ind_ele_off")
public class EcamInMProIndEleOffPO {
    // Note: This table has no primary key, may need an artificial one
    private String month;
    private String area;
    private String industry;
    private Double electricity;
}
```

#### `FctYGdpPO` (年度GDP)
```java
@Entity
@Table(name = "fct_y_gdp")
public class FctYGdpPO {
    // Note: No primary key
    private String year;
    private String area;
    private String indicator;
    private String period;
    private String method;
    private String record;
    private String source;
}
```

### 2.2 输出PO (Output POs)

以下是用于持久化领域模型最终产物的核心PO：

#### `CalculationJobPO`
```java
@Entity
@Table(name = "calculation_jobs")
public class CalculationJobPO {
    @Id
    private UUID jobId;
    private String jobName;
    private String status; // (e.g., "STARTED", "COMPLETED", "FAILED")
    private String failureReason;
    private String jobContext; // JSON/Text a- blob to store context
    private Timestamp createdAt;
    private Timestamp updatedAt;
}
```

### `PredictionResultPO`
```java
@Entity
@Table(name = "prediction_results")
public class PredictionResultPO {
    @Id
    private UUID resultId;
    @ManyToOne
    private CalculationJobPO job; // Foreign Key to calculation_jobs
    private UUID modelId; // Can be a simple UUID or a link to a model registry
    private String targetDimension; // e.g., "region:xxx,industry:yyy"
    @Lob // Large Object for storing JSON or serialized data
    private String predictedValues; // e.g., JSON array of {date, value, confidence}
    private String status; // (e.g., "GENERATED", "FUSED")
    private Timestamp createdAt;
    private Timestamp updatedAt;
}
```

### `FinalEmissionDataPO`
```java
@Entity
@Table(name = "final_emission_data")
public class FinalEmissionDataPO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String dimension; // e- .g., "region:xxx,industry:yyy"
    private int year;
    private int month;
    private double value;
    private String unit;
    private double confidenceIntervalLower;
    private double confidenceIntervalUpper;
    
    @ManyToOne
    private CalculationJobPO job; // Foreign Key for traceability
}
```

---
*本文档明确了数据访问层的架构选择，并给出了核心数据库表的设计参考，为后续的开发实现提供了清晰的指引。*

