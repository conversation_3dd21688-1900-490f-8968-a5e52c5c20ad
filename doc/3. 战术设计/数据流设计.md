# 数据流设计

## 1. 整体数据流概览

### 1.1 配置驱动的数据流架构图

```
原始数据 → 配置驱动的标准化 → 业务逻辑处理 → 清单构建 → 最终输出
   │              │                    │              │           │
   ▼              ▼                    ▼              ▼           ▼
RawData → 通用标准化(地区/行业/能源) → 表特定业务逻辑 → Inventory → CSV/Excel
```

### 1.2 配置驱动的数据流层次结构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  CalculationJobService                                      │
│  ├── DataStandardizationOrchestrator                       │
│  ├── ConstraintCalculationOrchestrator                     │
│  └── InventoryConstructionOrchestrator                     │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    领域层 (Domain Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  配置驱动的标准化服务                                        │
│  ├── 通用标准化处理器                                        │
│  │   ├── RegionStandardizer (地区标准化)                    │
│  │   ├── IndustryStandardizer (行业标准化)                  │
│  │   └── EnergyStandardizer (能源标准化)                    │
│  │                                                           │
│  ├── 业务逻辑处理器                                          │
│  │   ├── EnergyConversionLogic (能源转换)                    │
│  │   ├── UnitConversionLogic (单位换算)                      │
│  │   └── DataValidationLogic (数据验证)                      │
│  │                                                           │
│  └── StandardizationOrchestrator (标准化编排器)             │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                 基础设施层 (Infrastructure Layer)             │
├─────────────────────────────────────────────────────────────┤
│  ConfigReader                                               │
│  ├── 全局标准化配置读取器                                    │
│  ├── 表级配置读取器                                          │
│  └── 业务逻辑配置读取器                                      │
│                                                             │
│  RawDataRepository                                          │
│  └── 原始数据查询器                                          │
└─────────────────────────────────────────────────────────────┘
```

## 2. 标准化的三种核心功能设计

### 2.1 标准化功能抽象

系统将数据标准化抽象为三种核心功能：

#### 2.1.1 功能1：列内映射（规范列名称到给定集合）
**功能描述**：将列中的原始值映射到标准化的名称集合
**应用场景**：地区、行业、能源品种的标准化

**配置示例**：
```yaml
standardization_mappings:
  column_standardizations:
    energy_types:
      - name: "原煤"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭"]
      - name: "焦炭"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["焦炭", "焦煤"]
```

**处理逻辑**：
1. 识别源列名（如`energy_type`）
2. 匹配关键词集合（如`["原煤", "煤炭"]`）
3. 映射到标准化名称（如`"原煤"`）
4. 生成新列（如`standard_energy_type`）

#### 2.1.2 功能2：列内层次（为标准化值赋予层次关系）
**功能描述**：在同一个列内建立父子关系，形成层次结构
**应用场景**：地区（省-市）、行业（宏观-子行业）的层次关系

**配置示例**：
```yaml
standardization_mappings:
  column_standardizations:
    industries:
      - name: "工业"
        parent: null  # 顶级节点
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["工业"]
      - name: "钢铁"
        parent: "工业"  # 子节点
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["钢铁", "黑色金属"]
```

**处理逻辑**：
1. 执行列内映射功能
2. 根据`parent`字段建立层次关系
3. 生成层次列（如`macro_industry`）

#### 2.1.3 功能3：跨列映射（基于一个列推导另一个列）
**功能描述**：基于一个列的信息，推导出另一个列的信息
**应用场景**：产品名称推导行业、能源品种推导排放因子

**配置示例**：
```yaml
standardization_mappings:
  cross_column_mappings:
    product_to_industry:
      source_column: "product_name"
      target_column: "industry"
      mappings:
        "粗钢": "钢铁"
        "水泥": "建材"
        "焦炭": "石化"
```

**处理逻辑**：
1. 识别源列（如`product_name`）
2. 应用映射规则（如`"粗钢" -> "钢铁"`）
3. 生成目标列（如`industry`）

### 2.2 标准化架构设计

#### 2.2.1 配置结构设计
```yaml
standardization_mappings:
  # 功能1+2：列内映射 + 层次关系
  column_standardizations:
    regions:
      - name: "山西"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["山西", "山西省"]
      - name: "太原"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["太原"]
    
    industries:
      - name: "工业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["工业"]
      - name: "钢铁"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["钢铁", "黑色金属"]
    
    energy_types:
      - name: "原煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭"]
  
  # 功能3：跨列映射
  cross_column_mappings:
    product_to_industry:
      source_column: "product_name"
      target_column: "industry"
      mappings:
        "粗钢": "钢铁"
        "水泥": "建材"
        "焦炭": "石化"
```

#### 2.2.2 代码架构设计
```python
# 标准化功能基类
class StandardizationFunction(ABC):
    @abstractmethod
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        pass

# 功能1+2：列内映射 + 层次关系
class ColumnStandardizer(StandardizationFunction):
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        # 执行列内映射
        # 建立层次关系
        pass

# 功能3：跨列映射
class CrossColumnMapper(StandardizationFunction):
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        # 基于源列推导目标列
        pass

# 标准化编排器
class StandardizationOrchestrator:
    def standardize(self, raw_data: RawData) -> pd.DataFrame:
        # 步骤1：列内标准化（地区、行业、能源品种）
        # 步骤2：跨列映射（产品到行业等）
        pass
```

### 2.3 标准化执行顺序

#### 2.3.1 执行顺序设计
```python
def standardize(self, raw_data: RawData) -> pd.DataFrame:
    df = raw_data.data.copy()
    
    # 步骤1：列内标准化（独立执行，无依赖）
    for category, standardizers in self.column_standardizers.items():
        for standardizer in standardizers:
            df = standardizer.apply(df, raw_data.table_name)
    
    # 步骤2：跨列映射（依赖列内标准化结果）
    for mapper in self.cross_column_mappers:
        df = mapper.apply(df, raw_data.table_name)
    
    return df
```

#### 2.3.2 依赖关系分析
- **列内标准化**：相互独立，可并行执行
- **跨列映射**：依赖列内标准化结果，需后执行
- **特殊处理**：产品到行业映射依赖产品名称标准化

## 3. 表特定业务逻辑链路

### 3.1 能源表业务逻辑链路
**配置驱动**:
```yaml
tables:
  ecam_in_y_pro_ind_ene_off:
    business_logic:
      - type: "energy_conversion"
        enabled: true
        method: "折标煤"
        
      - type: "unit_conversion"
        enabled: true
        target_unit: "万吨标准煤"
        
      - type: "data_validation"
        enabled: true
        rules:
          - "value > 0"
          - "unit not null"
```

**处理逻辑**:
1. **能源转换**: 根据配置的转换方法进行能源转换
2. **单位换算**: 将原始单位转换为目标单位
3. **数据验证**: 应用配置的验证规则
4. **结果输出**: 输出转换后的标准化数据

**业务规则**:
- 能源转换必须基于标准转换因子
- 单位换算必须保持数值的准确性
- 数据验证确保数据的完整性和合理性

#### 2.2.2 用电量表业务逻辑链路
**配置驱动**:
```yaml
tables:
  ecam_in_m_pro_ind_ele_off:
    business_logic:
      - type: "electricity_conversion"
        enabled: true
        method: "电力折标"
        
      - type: "monthly_aggregation"
        enabled: true
        aggregation_method: "sum"
```

**处理逻辑**:
1. **电力转换**: 将电力数据转换为标准格式
2. **月度聚合**: 对月度数据进行聚合处理
3. **数据验证**: 验证电力数据的合理性
4. **结果输出**: 输出标准化的电力数据

**业务规则**:
- 电力数据必须转换为标准单位
- 月度数据需要支持聚合处理
- 确保电力数据的时序完整性

#### 2.2.3 经济表业务逻辑链路
**配置驱动**:
```yaml
tables:
  fct_y_gdp:
    business_logic:
      - type: "indicator_classification"
        enabled: true
        rules:
          - pattern: ".*GDP.*"
            category: "gdp"
          - pattern: ".*能耗.*"
            category: "energy_intensity"
```

**处理逻辑**:
1. **指标分类**: 根据配置规则对经济指标进行分类
2. **数据验证**: 验证经济数据的合理性
3. **单位处理**: 处理经济数据的单位
4. **结果输出**: 输出分类后的经济数据

**业务规则**:
- 经济指标必须正确分类
- 确保经济数据的准确性
- 支持多种经济指标类型

### 2.3 数据质量检查链路

#### 2.3.1 配置驱动的质量检查
**配置驱动**:
```yaml
quality_rules:
  required_columns: ["year", "province", "item", "energy_type", "value"]
  unique_constraints: ["year", "province", "item", "energy_type"]
  validation_rules:
    - rule: "value > 0"
      message: "数值必须大于0"
    - rule: "unit not null"
      message: "单位不能为空"
```

**处理逻辑**:
1. **必需列检查**: 验证数据是否包含必需的列
2. **唯一性检查**: 验证数据的唯一性约束
3. **业务规则检查**: 应用配置的业务规则
4. **结果报告**: 生成数据质量检查报告

**业务规则**:
- 所有必需列必须存在且不为空
- 唯一性约束必须满足
- 业务规则必须通过验证

## 3. 配置驱动的数据流实现

### 3.1 标准化编排器实现

```python
class StandardizationOrchestrator:
    def __init__(self, config_reader: ConfigReader):
        self.config_reader = config_reader
        self.standardizers = {
            'region': RegionStandardizer(),
            'industry': IndustryStandardizer(),
            'energy': EnergyStandardizer()
        }
        self.business_logic_processors = {
            'energy_conversion': EnergyConversionLogic(),
            'unit_conversion': UnitConversionLogic(),
            'data_validation': DataValidationLogic()
        }
    
    def standardize(self, raw_data: RawData) -> pd.DataFrame:
        """执行完整的标准化流程"""
        df = raw_data.data.copy()
        
        # 1. 应用通用标准化
        global_rules = self.config_reader.get_global_standardization_rules()
        for rule_type, rule_config in global_rules.items():
            if rule_type in self.standardizers:
                df = self.standardizers[rule_type].standardize(df, rule_config)
        
        # 2. 应用表特定的业务逻辑
        table_config = self.config_reader.get_table_config(raw_data.table_name)
        business_logic = table_config.get('business_logic', [])
        
        for logic_config in business_logic:
            logic_type = logic_config.get('type')
            if logic_config.get('enabled', False):
                df = self._apply_business_logic(df, logic_type, logic_config)
        
        return df
```

### 3.2 配置读取器实现

```python
class ConfigReader:
    def __init__(self, config_path: str):
        self.config_path = config_path
        self._load_config()
    
    def get_global_standardization_rules(self) -> Dict:
        """获取全局标准化规则"""
        return self._config.get('standardization_rules', {})
    
    def get_table_config(self, table_name: str) -> Dict:
        """获取表级配置"""
        tables = self._config.get('tables', {})
        return tables.get(table_name, {})
    
    def get_business_logic_config(self, logic_type: str) -> Dict:
        """获取业务逻辑配置"""
        business_logic = self._config.get('business_logic', {})
        return business_logic.get(logic_type, {})
```

## 4. 数据流优势

### 4.1 配置驱动的优势
- **灵活性**: 通过配置文件调整数据流逻辑，无需修改代码
- **可维护性**: 数据流规则集中管理，易于维护和更新
- **可扩展性**: 新增数据流规则只需添加配置，无需修改核心逻辑

### 4.2 透明化设计的优势
- **操作友好**: 操作人员可以直接理解数据流配置，无需了解代码逻辑
- **调试简单**: 数据流过程完全可追溯，便于问题定位
- **文档即配置**: 配置本身就是最好的文档

### 4.3 职责分离的优势
- **逻辑清晰**: 通用标准化与业务逻辑分离，职责明确
- **复用性强**: 通用标准化逻辑可以在不同表中复用
- **测试简单**: 各组件可以独立测试，提高测试覆盖率

## 5. 总结

配置驱动的数据流设计通过以下方式优化了系统的数据处理流程：

1. **统一了标准化逻辑**: 地区、行业、能源品种的标准化逻辑统一管理
2. **分离了业务逻辑**: 表特定的业务逻辑独立配置和处理
3. **提高了可维护性**: 配置集中管理，易于维护和扩展
4. **增强了透明度**: 数据流过程完全可追溯和可理解

这种设计不仅解决了当前的技术债务，还为系统的长期发展奠定了坚实的基础。
