# 标准化重构方案

## 1. 重构背景与目标

### 1.1 重构背景
当前标准化系统存在以下问题：

#### 1.1.1 架构问题
- **双重实现**：存在两个 `DataStandardizationServiceImpl` 实现，造成混乱
  - `ecam_calculator/domain/service/data_standardization_service.py` - 新的重构版本
  - `ecam_calculator/infrastructure/domain_services_impl.py` - 旧的实现版本（当前使用）
- **硬编码业务逻辑**：行业映射、能源品种处理等逻辑分散在多个方法中
- **复杂的条件判断**：大量的 if-else 语句处理不同类型的数据
- **职责不清晰**：一个类承担了过多的责任

#### 1.1.2 配置问题
- **配置分散**：行业配置在 `industries` 部分，产品映射在 `product_to_industry_map`
- **配置不统一**：地区、行业、能源、产品使用不同的配置格式
- **扩展困难**：新增标准化类型需要修改多个地方

#### 1.1.3 功能问题
- **产品表特殊处理**：产品表缺少行业列，需要特殊处理逻辑
- **标准化执行顺序不明确**：存在潜在依赖关系
- **逻辑重复**：不同策略中有相似的标准化逻辑

### 1.2 重构目标
基于标准化的三种核心功能，重构为：
- **功能1**：列内映射（规范列名称到给定集合）
- **功能2**：列内层次（为标准化值赋予层次关系）
- **功能3**：跨列映射（基于一个列推导另一个列）

## 2. 当前代码状态分析

### 2.1 现有实现
```python
# 位置：ecam_calculator/infrastructure/domain_services_impl.py
class DataStandardizationServiceImpl(DataStandardizationService):
    def __init__(self, config_reader=None, database_connection=None):
        self.config_reader = config_reader or get_config_reader()
        self._strategies = self._build_strategies()
        self._industry_mapper = self._build_industry_mapper()
        
        # 初始化能源转换服务
        if self.database_connection:
            try:
                from ecam_calculator.infrastructure.energy_conversion_service import EnergyConversionService
                self.energy_conversion_service = EnergyConversionService(
                    self.config_reader, 
                    self.database_connection
                )
                self.logger.info("能源转换服务初始化成功")
            except ImportError as e:
                self.logger.warning(f"服务初始化失败: {e}")
                self.energy_conversion_service = None
        else:
            self.energy_conversion_service = None
            self.logger.info("未提供数据库连接，跳过能源转换服务初始化")
    
    def _build_strategies(self) -> Dict[str, Any]:
        """根据配置文件构建标准化策略"""
        strategies = {}
        table_configs = self.config_reader.get_all_table_configs()
        
        for table_name, config in table_configs.items():
            table_type = config.get('table_type')
            
            if table_type == 'energy':
                strategies[table_name] = self._standardize_energy
            elif table_type == 'product':
                strategies[table_name] = self._standardize_product
            elif table_type == 'economic':
                strategies[table_name] = self._standardize_economic
            elif table_type == 'electricity':
                strategies[table_name] = self._standardize_electricity
        
        # 添加向后兼容的映射
        fallback_strategies = {
            "年度省级分行业分品种能源消费量": self._standardize_energy,
            "年度地市工业能源消费量": self._standardize_energy,
            "年度地市工业能源消费量_ene2": self._standardize_energy,
            "年度地市工业能源消费量_ene_fallback": self._standardize_energy,
            "年度地市用电量": self._standardize_electricity,
        }
        
        for source_name, strategy in fallback_strategies.items():
            if source_name not in strategies:
                strategies[source_name] = strategy
        
        return strategies
```

### 2.2 现有功能
- **策略模式**：根据表类型选择标准化策略
- **行业映射**：使用 `IndustryMapper` 类处理行业标准化
- **能源转换**：集成能源转换服务
- **产品标准化**：处理产品到行业映射

### 2.3 存在的问题
- **配置分散**：行业配置在 `industries` 部分，产品映射在 `product_to_industry_map`
- **逻辑重复**：不同策略中有相似的标准化逻辑
- **扩展困难**：新增标准化类型需要修改多个地方

## 3. 新的服务架构设计

### 3.1 配置结构重构
```yaml
# 在 parameters.yaml 中新增
standardization_mappings:
  # 功能1+2：列内映射 + 层次关系
  column_standardizations:
    regions:
      - name: "山西"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["山西", "山西省"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["山西", "山西省"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["山西", "山西省"]
      - name: "太原"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["太原"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["太原"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["太原"]
    
    industries:
      - name: "工业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["工业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["工业"]
      - name: "钢铁"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["钢铁", "黑色金属"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["钢铁", "黑色金属"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["钢铁", "黑色金属"]
      - name: "有色"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["有色", "有色金属"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["有色", "有色金属"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["有色", "有色金属"]
      - name: "石化"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["石化", "石油化工"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["石化", "石油化工"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["石化", "石油化工"]
      - name: "化工"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["化工", "化学工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["化工", "化学工业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["化工", "化学工业"]
      - name: "造纸"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["造纸", "纸制品"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["造纸", "纸制品"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["造纸", "纸制品"]
      - name: "建材"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["建材", "非金属矿物制品"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["建材", "非金属矿物制品"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["建材", "非金属矿物制品"]
      - name: "其他工业"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["其他工业", "煤炭洗选"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["其他工业", "煤炭开采"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["其他工业", "煤炭开采"]
    
    energy_types:
      - name: "原煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭"]
      - name: "焦炭"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["焦炭", "焦煤"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["焦炭", "焦煤"]
      - name: "电力"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["电力", "电"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["电力", "电"]
          ecam_in_m_pro_ind_ele_off:
            column: "energy_type"
            keywords: ["电力", "电"]
      - name: "天然气"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["天然气", "气"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["天然气", "气"]
  
  # 功能3：跨列映射
  cross_column_mappings:
    product_to_industry:
      source_column: "product_name"
      target_column: "industry"
      mappings:
        "原煤": "其他工业"
        "粗钢": "钢铁"
        "钢材": "钢铁"
        "生铁": "钢铁"
        "焦炭": "石化"
        "水泥": "建材"
        "平板玻璃": "建材"
        "发电量": "其他工业"
        "电子计算机整机": "其他工业"
        "太阳能电池": "其他工业"
        "非常规天然气": "石化"
```

#### 2.1.2 保持向后兼容
- 保留现有的 `industries` 配置
- 保留现有的 `product_to_industry_map` 配置
- 在重构完成后逐步迁移

### 2.2 代码架构重构

#### 2.2.1 新增标准化功能基类
```python
from abc import ABC, abstractmethod
from typing import Dict, List, Any
import pandas as pd

class StandardizationFunction(ABC):
    """标准化功能基类"""
    @abstractmethod
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """应用标准化功能"""
        pass

class ColumnStandardizer(StandardizationFunction):
    """功能1+2：列内映射 + 层次关系"""
    
    def __init__(self, config: Dict):
        self.name = config.get('name', '')
        self.parent = config.get('parent')
        self.column_mappings = config.get('column_mappings', {})
    
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        table_config = self.column_mappings.get(table_name, {})
        column = table_config.get('column', '')
        keywords = table_config.get('keywords', [])
        
        if column in df.columns:
            # 功能1：列内映射
            df[f'standard_{column}'] = df[column].apply(
                lambda x: self._map_to_standard(x, keywords)
            )
            
            # 功能2：层次关系
            if self.parent:
                df[f'macro_{column}'] = self.parent
            else:
                df[f'macro_{column}'] = self.name
        
        return df
    
    def _map_to_standard(self, value: str, keywords: List[str]) -> str:
        """将原始值映射到标准化名称"""
        if pd.isna(value):
            return value
        
        # 精确匹配
        if value in keywords:
            return self.name
        
        # 模糊匹配
        for keyword in keywords:
            if keyword in value:
                return self.name
        
        return value

class CrossColumnMapper(StandardizationFunction):
    """功能3：跨列信息映射"""
    
    def __init__(self, config: Dict):
        self.source_column = config.get('source_column', '')
        self.target_column = config.get('target_column', '')
        self.mappings = config.get('mappings', {})
    
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        if self.source_column in df.columns:
            # 基于源列推导目标列
            df[self.target_column] = df[self.source_column].apply(
                lambda x: self.mappings.get(x, '未知')
            )
        
        return df
```

#### 2.2.2 标准化编排器
```python
class StandardizationOrchestrator:
    """标准化编排器"""
    
    def __init__(self, config_reader: ConfigReader):
        self.config_reader = config_reader
        self.column_standardizers = self._load_column_standardizers()
        self.cross_column_mappers = self._load_cross_column_mappers()
    
    def _load_column_standardizers(self) -> Dict[str, List[ColumnStandardizer]]:
        """加载列标准化器"""
        mappings = self.config_reader.get_standardization_mappings()
        column_configs = mappings.get('column_standardizations', {})
        
        standardizers = {}
        for category in ['regions', 'industries', 'energy_types']:
            configs = column_configs.get(category, [])
            standardizers[category] = [
                ColumnStandardizer(config) for config in configs
            ]
        
        return standardizers
    
    def _load_cross_column_mappers(self) -> List[CrossColumnMapper]:
        """加载跨列映射器"""
        mappings = self.config_reader.get_standardization_mappings()
        cross_configs = mappings.get('cross_column_mappings', {})
        
        mappers = []
        for mapping_name, config in cross_configs.items():
            mappers.append(CrossColumnMapper(config))
        
        return mappers
    
    def standardize(self, raw_data: RawData) -> pd.DataFrame:
        """执行标准化流程"""
        df = raw_data.data.copy()
        
        # 步骤1：列内标准化（独立执行，无依赖）
        for category, standardizers in self.column_standardizers.items():
            for standardizer in standardizers:
                df = standardizer.apply(df, raw_data.table_name)
        
        # 步骤2：跨列映射（依赖列内标准化结果）
        for mapper in self.cross_column_mappers:
            df = mapper.apply(df, raw_data.table_name)
        
        return df
```

#### 2.2.3 配置读取器扩展
```python
class ConfigReader:
    def get_standardization_mappings(self) -> Dict:
        """获取标准化映射配置"""
        return self._config.get('standardization_mappings', {})
    
    def get_column_standardizations(self, category: str) -> List[Dict]:
        """获取列标准化配置"""
        mappings = self.get_standardization_mappings()
        column_configs = mappings.get('column_standardizations', {})
        return column_configs.get(category, [])
    
    def get_cross_column_mappings(self) -> Dict:
        """获取跨列映射配置"""
        mappings = self.get_standardization_mappings()
        return mappings.get('cross_column_mappings', {})
    
    def get_hierarchy(self, category: str) -> Dict[str, str]:
        """获取层次关系"""
        standardizations = self.get_column_standardizations(category)
        hierarchy = {}
        for config in standardizations:
            name = config.get('name', '')
            parent = config.get('parent')
            if parent:
                hierarchy[name] = parent
        return hierarchy
```

### 2.3 数据标准化服务重构

#### 2.3.1 重构 DataStandardizationService
```python
class DataStandardizationServiceImpl:
    def __init__(self, config_reader: ConfigReader, energy_conversion_service: EnergyConversionService):
        self.config_reader = config_reader
        self.energy_conversion_service = energy_conversion_service
        self.orchestrator = StandardizationOrchestrator(config_reader)
        self.logger = logging.getLogger(__name__)
    
    def standardize(self, raw_data: RawData) -> List[StandardizedData]:
        """标准化数据"""
        if raw_data.data.empty:
            self.logger.warning(f"数据源 '{raw_data.table_name}' 为空，跳过标准化。")
            return []
        
        # 使用编排器执行标准化
        standardized_df = self.orchestrator.standardize(raw_data)
        
        # 转换为 StandardizedData 对象
        return self._convert_to_standardized_data(standardized_df, raw_data)
    
    def _convert_to_standardized_data(self, df: pd.DataFrame, raw_data: RawData) -> List[StandardizedData]:
        """转换为标准化数据对象"""
        standardized_list = []
        
        for _, row in df.iterrows():
            try:
                standardized_data = StandardizedData(
                    data_type=self._determine_data_type(raw_data.table_name),
                    year=int(row['year']),
                    province=row.get('standard_province', row.get('province', '')),
                    city=row.get('standard_area', row.get('area', '')),
                    source_table=raw_data.table_name,
                    attributes=self._extract_attributes(row, raw_data.table_name)
                )
                standardized_list.append(standardized_data)
            except Exception as e:
                self.logger.error(f"转换标准化数据失败: {row}, 错误: {e}")
                continue
        
        return standardized_list
```

## 3. 实施计划

### 3.1 第一阶段：配置重构（2-3天）
**目标**：新增配置结构，保持向后兼容

**任务**：
1. 在 `parameters.yaml` 中新增 `standardization_mappings` 部分
2. 实现地区层次结构（省级和地市级）
3. 保持现有的 `industries` 配置
4. 配置验证和测试

**交付物**：
- 更新后的 `parameters.yaml` 配置文件
- 配置验证脚本

### 3.2 第二阶段：核心功能实现（3-4天）
**目标**：实现三种核心功能

**任务**：
1. 实现 `StandardizationFunction` 基类
2. 实现 `ColumnStandardizer`（功能1+2）
3. 实现 `CrossColumnMapper`（功能3）
4. 实现 `StandardizationOrchestrator`
5. 单元测试

**交付物**：
- 标准化功能类
- 编排器类
- 单元测试

### 3.3 第三阶段：服务集成（2-3天）
**目标**：集成到现有服务中

**任务**：
1. 重构 `DataStandardizationServiceImpl`
2. 更新 `ConfigReader`
3. 集成测试
4. 性能测试

**交付物**：
- 重构后的服务类
- 集成测试
- 性能报告

### 3.4 第四阶段：迁移和清理（2-3天）
**目标**：完成迁移，清理旧代码

**任务**：
1. 迁移现有配置到新结构
2. 清理旧的标准化策略类
3. 更新文档
4. 最终测试

**交付物**：
- 迁移脚本
- 清理后的代码
- 更新后的文档

## 4. 风险评估与缓解

### 4.1 技术风险
**风险**：新架构可能影响现有功能
**缓解**：保持向后兼容，分阶段实施

### 4.2 配置风险
**风险**：配置结构变更可能导致配置错误
**缓解**：提供配置验证工具，详细文档

### 4.3 性能风险
**风险**：新架构可能影响性能
**缓解**：性能测试，优化关键路径

### 4.4 数据风险
**风险**：标准化逻辑变更可能影响数据质量
**缓解**：数据对比测试，逐步验证

## 5. 预期效益

### 5.1 技术效益
- **代码简化**：统一标准化逻辑，减少重复代码
- **可维护性**：清晰的架构，易于理解和维护
- **可扩展性**：支持新的标准化需求

### 5.2 业务效益
- **配置统一**：统一的配置结构，降低配置复杂度
- **功能完整**：支持所有标准化场景，包括产品到行业映射
- **执行清晰**：明确的执行顺序和依赖关系

## 6. 验收标准

### 6.1 功能验收
- [ ] 三种核心功能正常工作
- [ ] 配置结构统一且完整
- [ ] 向后兼容性保持
- [ ] 所有现有功能正常

### 6.2 性能验收
- [ ] 标准化性能不降低
- [ ] 内存使用合理
- [ ] 响应时间可接受

### 6.3 质量验收
- [ ] 单元测试覆盖率 > 90%
- [ ] 集成测试通过
- [ ] 代码审查通过
- [ ] 文档完整且准确
