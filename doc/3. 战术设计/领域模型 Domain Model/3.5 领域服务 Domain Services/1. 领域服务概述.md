# 1. 领域服务概述 (Domain Service Overview)

## 1. 设计原则

领域服务（Domain Service）用于封装那些不适合放在任何实体或值对象中的领域逻辑。当某个操作涉及到多个领域对象，或者其逻辑本身是无状态的（stateless），那么就应该使用领域服务。

在我们的模型中，领域服务主要负责协调和计算，保持了聚合和实体的职责单一。

## 2. 核心领域服务

### `PredictionService` (预测服务)
- **职责**:
  1.  接收一个 `PredictionModel` 值对象和一个 `PredictionInput` 值对象。
  2.  在内部，它会与基础设施层协作，反序列化 `PredictionModel` 中的模型数据。
  3.  调用模型执行预测计算。
  4.  返回一个包含预测结果的 `PredictionResultData` 值对象。
- **调用者**: 由 `预测分析器 (PredictionAnalyzer)` 聚合调用。
- **特点**:
  - **无状态**: 服务本身不持有任何状态，其输出完全取决于输入。
  - **依赖倒置**: 领域服务定义接口，而具体的模型执行逻辑（如与Python运行时交互）在基础设施层实现。这保持了领域层的纯净性。

### 2. 核心领域服务目录

本系统主要定义了以下几个核心领域服务：

#### 2.1 `PredictionService`
- **职责**: 封装了调用具体模型（如`ARDL`、`XGBoost`等）进行预测的底层实现细节。它隐藏了模型加载、执行和返回预测值的复杂性。
- **调用者**: `PredictionAnalyzer` (预测分析器) 聚合。
- **核心方法**: `predict(model: PredictionModel, input: PredictionInput): PredictionOutput`

#### 2.2 `ConstraintCalculationService` (约束计算服务)

*   **核心职责**：根据宏观经济数据（如GDP）和能源数据（如单位GDP能耗、能耗强度下降率），计算出用于后续平衡算法的**列约束**，即“地市能源消费总量”。
*   **实现者**：`ConstraintCalculationServiceImpl`
*   **使用场景**：在`InventoryConstructor`中，作为宏观调平的第一步，用于生成目标列向量。

#### 2.3 `BalancingService` (平衡服务)
- **职责**: 封装**迭代比例拟合 (IPF)** 算法，用于对多维表格数据进行平衡，使其在各维度上的总和满足预设的约束（例如：分行业能源消费之和 = 地区能源消费总量）。
- **调用者**: `InventoryConstructor` (清单构造器) 聚合。
- **核心方法**: `balance(matrix: MultiDimMatrix, constraints: List<Constraint>): MultiDimMatrix`

---
*随着业务变得更加复杂，可以引入更多的领域服务来封装跨聚合的业务规则或复杂的计算逻辑。*
*通过将这些复杂的、非聚合根核心职责的业务逻辑封装在领域服务中，我们保持了聚合的职责单一，并提高了这些核心算法的可测试性和复用性。*
