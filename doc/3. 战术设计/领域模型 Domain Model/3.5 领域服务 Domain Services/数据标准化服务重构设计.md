# 数据标准化服务重构设计

## 1. 重构背景

当前标准化系统存在以下问题：

### 1.1 现有架构问题
- **双重实现**：存在两个 `DataStandardizationServiceImpl` 实现，造成混乱
- **硬编码业务逻辑**：行业映射、能源品种处理等逻辑分散在多个方法中
- **复杂的条件判断**：大量的 if-else 语句处理不同类型的数据
- **职责不清晰**：一个类承担了过多的责任
- **配置不统一**：地区、行业、能源、产品使用不同的配置格式

### 1.2 重构目标
基于标准化的三种核心功能，重构为：
- **功能1**：列内映射（规范列名称到给定集合）
- **功能2**：列内层次（为标准化值赋予层次关系）
- **功能3**：跨列映射（基于一个列推导另一个列）

## 2. 当前代码状态分析

### 2.1 现有实现
```python
# 位置：ecam_calculator/infrastructure/domain_services_impl.py
class DataStandardizationServiceImpl(DataStandardizationService):
    def __init__(self, config_reader=None, database_connection=None):
        self.config_reader = config_reader or get_config_reader()
        self._strategies = self._build_strategies()
        self._industry_mapper = self._build_industry_mapper()
    
    def _build_strategies(self) -> Dict[str, Any]:
        """根据配置文件构建标准化策略"""
        strategies = {}
        table_configs = self.config_reader.get_all_table_configs()
        
        for table_name, config in table_configs.items():
            table_type = config.get('table_type')
            
            if table_type == 'energy':
                strategies[table_name] = self._standardize_energy
            elif table_type == 'product':
                strategies[table_name] = self._standardize_product
            elif table_type == 'economic':
                strategies[table_name] = self._standardize_economic
            elif table_type == 'electricity':
                strategies[table_name] = self._standardize_electricity
        
        return strategies
```

### 2.2 现有功能
- **策略模式**：根据表类型选择标准化策略
- **行业映射**：使用 `IndustryMapper` 类处理行业标准化
- **能源转换**：集成能源转换服务
- **产品标准化**：处理产品到行业映射

### 2.3 存在的问题
- **配置分散**：行业配置在 `industries` 部分，产品映射在 `product_to_industry_map`
- **逻辑重复**：不同策略中有相似的标准化逻辑
- **扩展困难**：新增标准化类型需要修改多个地方

## 3. 新的服务架构设计

### 3.1 基于三种核心功能的架构

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Any
import pandas as pd

class StandardizationFunction(ABC):
    """标准化功能基类"""
    @abstractmethod
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """应用标准化功能"""
        pass

class ColumnStandardizer(StandardizationFunction):
    """功能1+2：列内映射 + 层次关系"""
    
    def __init__(self, config: Dict):
        self.name = config.get('name', '')
        self.parent = config.get('parent')
        self.column_mappings = config.get('column_mappings', {})
    
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        table_config = self.column_mappings.get(table_name, {})
        column = table_config.get('column', '')
        keywords = table_config.get('keywords', [])
        
        if column in df.columns:
            # 功能1：列内映射
            df[f'standard_{column}'] = df[column].apply(
                lambda x: self._map_to_standard(x, keywords)
            )
            
            # 功能2：层次关系
            if self.parent:
                df[f'macro_{column}'] = self.parent
            else:
                df[f'macro_{column}'] = self.name
        
        return df
    
    def _map_to_standard(self, value: str, keywords: List[str]) -> str:
        """将原始值映射到标准化名称"""
        if pd.isna(value):
            return value
        
        # 精确匹配
        if value in keywords:
            return self.name
        
        # 模糊匹配
        for keyword in keywords:
            if keyword in value:
                return self.name
        
        return value

class CrossColumnMapper(StandardizationFunction):
    """功能3：跨列信息映射"""
    
    def __init__(self, config: Dict):
        self.source_column = config.get('source_column', '')
        self.target_column = config.get('target_column', '')
        self.mappings = config.get('mappings', {})
    
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        if self.source_column in df.columns:
            # 基于源列推导目标列
            df[self.target_column] = df[self.source_column].apply(
                lambda x: self.mappings.get(x, '未知')
            )
        
        return df
```

### 3.2 标准化编排器

```python
class StandardizationOrchestrator:
    """标准化编排器"""
    
    def __init__(self, config_reader: ConfigReader):
        self.config_reader = config_reader
        self.column_standardizers = self._load_column_standardizers()
        self.cross_column_mappers = self._load_cross_column_mappers()
    
    def _load_column_standardizers(self) -> Dict[str, List[ColumnStandardizer]]:
        """加载列标准化器"""
        mappings = self.config_reader.get_standardization_mappings()
        column_configs = mappings.get('column_standardizations', {})
        
        standardizers = {}
        for category in ['regions', 'industries', 'energy_types']:
            configs = column_configs.get(category, [])
            standardizers[category] = [
                ColumnStandardizer(config) for config in configs
            ]
        
        return standardizers
    
    def _load_cross_column_mappers(self) -> List[CrossColumnMapper]:
        """加载跨列映射器"""
        mappings = self.config_reader.get_standardization_mappings()
        cross_configs = mappings.get('cross_column_mappings', {})
        
        mappers = []
        for mapping_name, config in cross_configs.items():
            mappers.append(CrossColumnMapper(config))
        
        return mappers
    
    def standardize(self, raw_data: RawData) -> pd.DataFrame:
        """执行标准化流程"""
        df = raw_data.data.copy()
        
        # 步骤1：列内标准化（独立执行，无依赖）
        for category, standardizers in self.column_standardizers.items():
            for standardizer in standardizers:
                df = standardizer.apply(df, raw_data.table_name)
        
        # 步骤2：跨列映射（依赖列内标准化结果）
        for mapper in self.cross_column_mappers:
            df = mapper.apply(df, raw_data.table_name)
        
        return df
```

### 3.3 重构后的服务实现

```python
class DataStandardizationServiceImpl(DataStandardizationService):
    """数据标准化服务实现"""
    
    def __init__(self, config_reader: ConfigReader, energy_conversion_service: EnergyConversionService):
        self.config_reader = config_reader
        self.energy_conversion_service = energy_conversion_service
        self.orchestrator = StandardizationOrchestrator(config_reader)
        self.logger = logging.getLogger(__name__)
    
    def standardize(self, raw_data: RawData) -> List[StandardizedData]:
        """标准化数据"""
        if raw_data.data.empty:
            self.logger.warning(f"数据源 '{raw_data.table_name}' 为空，跳过标准化。")
            return []
        
        # 使用编排器执行标准化
        standardized_df = self.orchestrator.standardize(raw_data)
        
        # 转换为 StandardizedData 对象
        return self._convert_to_standardized_data(standardized_df, raw_data)
    
    def _convert_to_standardized_data(self, df: pd.DataFrame, raw_data: RawData) -> List[StandardizedData]:
        """转换为标准化数据对象"""
        standardized_list = []
        
        for _, row in df.iterrows():
            try:
                standardized_data = StandardizedData(
                    data_type=self._determine_data_type(raw_data.table_name),
                    year=int(row['year']),
                    province=row.get('standard_province', row.get('province', '')),
                    city=row.get('standard_area', row.get('area', '')),
                    source_table=raw_data.table_name,
                    attributes=self._extract_attributes(row, raw_data.table_name)
                )
                standardized_list.append(standardized_data)
            except Exception as e:
                self.logger.error(f"转换标准化数据失败: {row}, 错误: {e}")
                continue
        
        return standardized_list
```

## 4. 配置结构重构

### 4.1 新增配置结构
```yaml
# 在 parameters.yaml 中新增
standardization_mappings:
  # 功能1+2：列内映射 + 层次关系
  column_standardizations:
    regions:
      - name: "山西"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["山西", "山西省"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["山西", "山西省"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["山西", "山西省"]
      - name: "太原"
        parent: "山西"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "province"
            keywords: ["太原"]
          ecam_in_y_pro_ind_ene2_off:
            column: "area"
            keywords: ["太原"]
          ecam_in_m_pro_ind_ele_off:
            column: "area"
            keywords: ["太原"]
    
    industries:
      - name: "工业"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["工业"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["工业"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["工业"]
      - name: "钢铁"
        parent: "工业"
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "item"
            keywords: ["钢铁", "黑色金属"]
          ecam_in_y_pro_ind_ene2_off:
            column: "item"
            keywords: ["钢铁", "黑色金属"]
          ecam_in_m_pro_ind_ele_off:
            column: "item"
            keywords: ["钢铁", "黑色金属"]
    
    energy_types:
      - name: "原煤"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["原煤", "煤炭"]
      - name: "焦炭"
        parent: null
        column_mappings:
          ecam_in_y_pro_ind_ene_off:
            column: "energy_type"
            keywords: ["焦炭", "焦煤"]
          ecam_in_y_pro_ind_ene2_off:
            column: "energy_type"
            keywords: ["焦炭", "焦煤"]
  
  # 功能3：跨列映射
  cross_column_mappings:
    product_to_industry:
      source_column: "product_name"
      target_column: "industry"
      mappings:
        "原煤": "其他工业"
        "粗钢": "钢铁"
        "钢材": "钢铁"
        "生铁": "钢铁"
        "焦炭": "石化"
        "水泥": "建材"
        "平板玻璃": "建材"
        "发电量": "其他工业"
        "电子计算机整机": "其他工业"
        "太阳能电池": "其他工业"
        "非常规天然气": "石化"
```

## 5. 实施计划

### 5.1 第一阶段：配置重构（2-3天）
- 在 `parameters.yaml` 中新增 `standardization_mappings` 部分
- 实现地区层次结构（省级和地市级）
- 保持现有的 `industries` 配置
- 配置验证和测试

### 5.2 第二阶段：核心功能实现（3-4天）
- 实现 `StandardizationFunction` 基类
- 实现 `ColumnStandardizer`（功能1+2）
- 实现 `CrossColumnMapper`（功能3）
- 实现 `StandardizationOrchestrator`
- 单元测试

### 5.3 第三阶段：服务集成（2-3天）
- 重构 `DataStandardizationServiceImpl`
- 更新 `ConfigReader`
- 集成测试
- 性能测试

### 5.4 第四阶段：迁移和清理（2-3天）
- 迁移现有配置到新结构
- 清理旧的标准化策略类
- 更新文档
- 最终测试

## 6. 预期效益

### 6.1 技术效益
- **代码简化**：统一标准化逻辑，减少重复代码
- **可维护性**：清晰的架构，易于理解和维护
- **可扩展性**：支持新的标准化需求

### 6.2 业务效益
- **配置统一**：统一的配置结构，降低配置复杂度
- **功能完整**：支持所有标准化场景，包括产品到行业映射
- **执行清晰**：明确的执行顺序和依赖关系
        table_config = self.config_reader.get_table_config(raw_data.table_name)
        
        # 1. 数据验证
        self._validate_energy_data(raw_data, table_config)
        
        # 2. 行业名称标准化
        standardized_df = self._standardize_industry_names(raw_data.data, table_config)
        
        # 3. 能源品种标准化
        standardized_df = self._standardize_energy_types(standardized_df, table_config)
        
        # 4. 单位换算（折标）
        standardized_df = self._convert_to_standard_units(standardized_df, table_config)
        
        # 5. 数据质量检查
        self._validate_standardized_energy_data(standardized_df)
        
        return StandardizedEnergyData(
            source_table=raw_data.table_name,
            data=standardized_df,
            # ... 其他属性
        )
    
    def _validate_energy_data(self, raw_data: RawData, table_config: Dict):
        """验证能源数据的完整性"""
        required_fields = table_config.get('key_fields', [])
        missing_fields = [field for field in required_fields if field not in raw_data.data.columns]
        
        if missing_fields:
            raise ValueError(f"缺少必需字段: {missing_fields}")
    
    def _standardize_industry_names(self, df: pd.DataFrame, table_config: Dict) -> pd.DataFrame:
        """标准化行业名称"""
        mapping_config = self.config_reader.get_raw_name_to_standard_map()
        # 实现基于配置的行业名称映射逻辑
        
    def _standardize_energy_types(self, df: pd.DataFrame, table_config: Dict) -> pd.DataFrame:
        """标准化能源品种"""
        energy_types = table_config.get('energy_types_to_extract', [])
        # 实现基于配置的能源品种标准化逻辑
        
    def _convert_to_standard_units(self, df: pd.DataFrame, table_config: Dict) -> pd.DataFrame:
        """单位换算（折标）"""
        method = table_config.get('standardization_method')
        if method == '折标煤':
            return self._convert_to_standard_coal_equivalent(df)
        elif method == '电力折标':
            return self._convert_electricity_to_standard(df)
        else:
            raise ValueError(f"不支持的标准化方法: {method}")
```

### 2.4 产品数据标准化策略

```python
    def _standardize_product_data(self, raw_data: RawData) -> StandardizedProductData:
        """标准化产品数据"""
        table_config = self.config_reader.get_table_config(raw_data.table_name)
        
        # 1. 数据验证
        self._validate_product_data(raw_data, table_config)
        
        # 2. 产品名称到行业映射
        standardized_df = self._map_product_to_industry(raw_data.data, table_config)
        
        # 3. 排放类别分类
        standardized_df = self._classify_emission_categories(standardized_df)
        
        return StandardizedProductData(
            source_table=raw_data.table_name,
            data=standardized_df,
            # ... 其他属性
        )
    
    def _map_product_to_industry(self, df: pd.DataFrame, table_config: Dict) -> pd.DataFrame:
        """产品名称到行业映射"""
        product_mapping = self.config_reader.get_product_to_industry_map()
        # 实现基于配置的产品到行业映射逻辑
```

### 2.5 经济指标标准化策略

```python
    def _standardize_economic_data(self, raw_data: RawData) -> StandardizedEconomicData:
        """标准化经济指标数据"""
        table_config = self.config_reader.get_table_config(raw_data.table_name)
        
        # 1. 数据验证
        self._validate_economic_data(raw_data, table_config)
        
        # 2. 指标分类
        standardized_df = self._classify_indicators(raw_data.data, table_config)
        
        # 3. 业务用途标识
        standardized_df = self._identify_business_usage(standardized_df, table_config)
        
        return StandardizedEconomicData(
            source_table=raw_data.table_name,
            data=standardized_df,
            # ... 其他属性
        )
```

## 3. 配置驱动的映射策略

### 3.1 行业名称映射

```python
class IndustryMappingStrategy:
    """行业名称映射策略"""
    
    def __init__(self, config_reader: ConfigReader):
        self.config_reader = config_reader
        self.mapping_rules = config_reader.get_raw_name_to_standard_map()
        self.hierarchy_rules = config_reader.get_sub_to_macro_industry_map()
    
    def map_industry_name(self, original_name: str) -> Tuple[str, str]:
        """映射行业名称，返回(标准行业, 宏观行业)"""
        # 实现基于配置的行业名称映射逻辑
        pass
```

### 3.2 能源品种映射

```python
class EnergyTypeMappingStrategy:
    """能源品种映射策略"""
    
    def __init__(self, config_reader: ConfigReader):
        self.config_reader = config_reader
        self.energy_types = config_reader.get_energy_types()
    
    def map_energy_type(self, original_type: str) -> Dict[str, Any]:
        """映射能源品种，返回标准化的能源类型信息"""
        # 实现基于配置的能源品种映射逻辑
        pass
```

### 3.3 产品到行业映射

```python
class ProductToIndustryMappingStrategy:
    """产品到行业映射策略"""
    
    def __init__(self, config_reader: ConfigReader):
        self.config_reader = config_reader
        self.product_mapping = config_reader.get_product_to_industry_map()
    
    def map_product_to_industry(self, product_name: str) -> str:
        """映射产品名称到标准行业"""
        # 实现基于配置的产品到行业映射逻辑
        pass
```

## 4. 数据质量保证

### 4.1 数据验证规则

```python
class DataValidationService:
    """数据验证服务"""
    
    def validate_energy_data(self, data: pd.DataFrame, table_config: Dict) -> ValidationResult:
        """验证能源数据的质量"""
        # 实现数据质量验证逻辑
        
    def validate_product_data(self, data: pd.DataFrame, table_config: Dict) -> ValidationResult:
        """验证产品数据的质量"""
        # 实现数据质量验证逻辑
        
    def validate_economic_data(self, data: pd.DataFrame, table_config: Dict) -> ValidationResult:
        """验证经济指标数据的质量"""
        # 实现数据质量验证逻辑
```

### 4.2 数据完整性检查

```python
class DataCompletenessChecker:
    """数据完整性检查器"""
    
    def check_energy_data_completeness(self, data: StandardizedEnergyData) -> CompletenessReport:
        """检查能源数据的完整性"""
        # 实现数据完整性检查逻辑
        
    def check_product_data_completeness(self, data: StandardizedProductData) -> CompletenessReport:
        """检查产品数据的完整性"""
        # 实现数据完整性检查逻辑
```

## 5. 重构实施计划

### 5.1 第一阶段：基础设施准备
- 实现 `ConfigReader` 类
- 创建新的值对象类
- 建立配置驱动的映射策略

### 5.2 第二阶段：核心服务实现
- 实现 `DataStandardizationService` 接口
- 实现各种标准化策略
- 实现数据验证服务

### 5.3 第三阶段：集成和测试
- 集成新的标准化服务
- 编写单元测试和集成测试
- 验证业务逻辑的正确性

## 6. 重构的预期收益

### 6.1 技术债务解决
- **消除硬编码**: 所有业务规则都在配置文件中
- **简化代码**: 去除复杂的条件判断
- **类型安全**: 编译时就能发现类型错误

### 6.2 架构质量提升
- **配置驱动**: 业务规则调整无需修改代码
- **职责分离**: 每个服务都有明确的单一职责
- **易于测试**: 清晰的接口和依赖关系

---

*本重构设计基于配置驱动的原则，旨在彻底解决现有代码中的技术债务，建立清晰、可维护的数据标准化架构。*
