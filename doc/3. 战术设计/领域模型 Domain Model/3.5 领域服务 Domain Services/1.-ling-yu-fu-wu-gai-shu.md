## 2. 核心领域服务

本系统主要定义了以下几个核心领域服务：

### 2.1 `ConstraintCalculationService` (约束计算服务)

*   **核心职责**：根据宏观经济数据（如GDP）和能源数据（如单位GDP能耗、能耗强度下降率），计算出用于后续平衡算法的**列约束**，即“地市能源消费总量”。
*   **实现者**：`ConstraintCalculationServiceImpl`
*   **使用场景**：在`InventoryConstructor`中，作为宏观调平的第一步，用于生成目标列向量。

### 2.2 `BalancingService` (平衡服务)
- **职责**: 封装**迭代比例拟合 (IPF)** 算法。在当前设计中，它主要被用于执行清单构造流程中的**第一次调平**，即对“地区-宏观行业”的二维数据矩阵进行平衡，使其在各维度上的总和满足预设的省级和地市级总量约束。
- **调用者**: `InventoryConstructor` (清单构造器) 聚合。
- **核心方法**: `balance(matrix: TabularData, row_constraints: TabularData, col_constraints: TabularData): TabularData`
- **说明**: 该服务为后续更精细的、按比例分配的第二次调平提供关键的宏观约束数据。

### 2.3 `PredictionService` (预测服务)
- **职责**: 封装了调用具体模型（如`ARDL`、`XGBoost`等）进行预测的底层实现细节。它隐藏了模型加载、执行和返回预测值的复杂性，充当领域层与基础设施层（真正的Python模型运行时）之间的桥梁。
- **调用者**: `PredictionAnalyzer` (预测分析器) 聚合。
- **核心方法**: `predict(model: PredictionModel, input: PredictionInput): PredictionOutput`

---
*随着业务变得更加复杂，可以引入更多的领域服务来封装跨聚合的业务规则或复杂的计算逻辑。*
*通过将这些复杂的、非聚合根核心职责的业务逻辑封装在领域服务中，我们保持了聚合的职责单一，并提高了这些核心算法的可测试性和复用性。*
