# 数据模型 Data Model

## 1. 概述

本文档描述了"区域电-能-碳"监测分析系统中的核心数据模型，包括原始数据表、标准化数据结构和最终输出数据的业务含义和逻辑关系。

## 2. 原始数据表模型

### 2.1 能源消费数据表

#### 2.1.1 省级能源消费表 (`ecam_in_y_pro_ind_ene_off`)

**业务含义**: 记录省级层面各行业各能源品种的年度消费量
**数据特征**: 
- 时间维度：年度数据
- 地理维度：省级数据
- 行业维度：国民经济行业分类
- 能源维度：多种能源品种

**核心字段**:
- `year`: 统计年份
- `province`: 省份名称
- `industry`: 行业名称（原始）
- `energy_type`: 能源品种名称
- `consumption`: 消费量
- `unit`: 计量单位

**业务用途**: 
- 为地市数据下沉提供省级总量约束
- 作为能源消费清单的主要数据源
- 支持省级能源消费结构分析

#### 2.1.2 地市工业能源表 (`ecam_in_y_pro_ind_ene2_off`)

**业务含义**: 记录地市层面工业行业的能源消费数据
**数据特征**:
- 时间维度：年度数据
- 地理维度：地市级数据
- 行业维度：工业行业（制造业、采矿业等）
- 能源维度：多种能源品种

**核心字段**:
- `year`: 统计年份
- `city`: 城市名称
- `industry`: 行业名称（原始）
- `energy_type`: 能源品种名称
- `consumption`: 消费量
- `unit`: 计量单位

**业务用途**:
- 提供地市直接能源消费数据
- 优先于省级下沉数据使用
- 支持地市能源消费结构分析

#### 2.1.3 地市用电量表 (`ecam_in_m_pro_ind_ele_off`)

**业务含义**: 记录地市层面各行业的月度用电量数据
**数据特征**:
- 时间维度：月度数据
- 地理维度：地市级数据
- 行业维度：国民经济行业分类
- 能源维度：仅电力

**核心字段**:
- `year`: 统计年份
- `month`: 统计月份
- `city`: 城市名称
- `industry`: 行业名称（原始）
- `electricity_consumption`: 用电量
- `unit`: 计量单位（千瓦时）

**业务用途**:
- 计算地市各行业的用电比例
- 作为省级数据下沉的权重依据
- 支持地市用电结构分析

### 2.2 工业产品数据表

#### 2.2.1 工业产品产量表 (`fct_y_prd_output`)

**业务含义**: 记录各地区的工业产品年度产量数据
**数据特征**:
- 时间维度：年度数据
- 地理维度：省级和地市级数据
- 产品维度：主要工业产品
- 单位维度：产品特定单位

**核心字段**:
- `year`: 统计年份
- `province`: 省份名称
- `city`: 城市名称
- `product`: 产品名称
- `output`: 产量
- `unit`: 计量单位

**业务用途**:
- 计算工业过程碳排放
- 支持产品产量分析
- 为碳排放清单提供基础数据

### 2.3 经济指标数据表

#### 2.3.1 GDP数据表 (`fct_y_gdp`)

**业务含义**: 记录各地区的年度GDP数据
**数据特征**:
- 时间维度：年度数据
- 地理维度：省级和地市级数据
- 产业维度：第一、二、三产业
- 单位维度：万元

**核心字段**:
- `year`: 统计年份
- `province`: 省份名称
- `city`: 城市名称
- `industry`: 产业分类
- `gdp_value`: GDP数值
- `unit`: 计量单位

**业务用途**:
- 为IPF平衡提供行约束
- 支持经济结构分析
- 验证数据一致性

#### 2.3.2 能耗强度表 (`fct_y_all_ene_intsty`)

**业务含义**: 记录各地区的能源消费强度指标
**数据特征**:
- 时间维度：年度数据
- 地理维度：省级和地市级数据
- 指标维度：单位GDP能耗
- 单位维度：吨标准煤/万元

**核心字段**:
- `year`: 统计年份
- `province`: 省份名称
- `city`: 城市名称
- `intensity_value`: 强度数值
- `unit`: 计量单位

**业务用途**:
- 为IPF平衡提供列约束
- 支持能耗强度分析
- 验证数据合理性

## 3. 标准化数据模型

### 3.1 标准化数据值对象 (`StandardizedData`)

**业务含义**: 经过标准化处理后的统一数据格式
**数据特征**:
- 统一的字段结构和命名规范
- 标准化的地区、行业、能源品种分类
- 支持多种数据类型的统一处理
- 包含原始数据和标准化后的数据

**核心字段**:
- `data_type`: 数据类型（能源、产品、经济指标）
- `year`: 统计年份
- `province`, `city`: 地区信息
- `source_table`: 数据来源表
- `attributes`: 扩展属性（包含具体业务字段）

**业务用途**:
- 为下游计算提供统一的数据格式
- 支持跨表数据的统一处理
- 为清单构建提供标准化输入

### 3.2 标准化后的数据结构

#### 3.2.1 列内映射结果
**功能1：列内映射**产生的标准化列：
- `standard_province`: 标准化后的省份名称
- `standard_area`: 标准化后的地区名称
- `standard_item`: 标准化后的行业名称
- `standard_energy_type`: 标准化后的能源品种名称

#### 3.2.2 层次关系结果
**功能2：列内层次**产生的层次列：
- `macro_province`: 省级层次（如"山西"）
- `macro_area`: 地区层次（如"太原"属于"山西"）
- `macro_item`: 行业层次（如"钢铁"属于"工业"）
- `macro_energy_type`: 能源品种层次（如"原煤"属于"煤炭"）

#### 3.2.3 跨列映射结果
**功能3：跨列映射**产生的推导列：
- `industry`: 从产品名称推导的行业（如"粗钢"推导为"钢铁"）
- `emission_category`: 从能源品种推导的排放类别
- `conversion_factor`: 从能源品种推导的转换因子

### 3.3 标准化数据示例

```python
# 能源数据标准化示例
StandardizedData(
    data_type=StandardizedDataType.ENERGY,
    year=2021,
    province="山西",  # 标准化后的省份
    city="太原",      # 标准化后的城市
    source_table="ecam_in_y_pro_ind_ene_off",
    attributes={
        'standard_industry': '钢铁',      # 列内映射结果
        'macro_industry': '工业',         # 层次关系结果
        'standard_energy_type': '原煤',   # 列内映射结果
        'macro_energy_type': '煤炭',      # 层次关系结果
        'value': 1000.0,                 # 原始数值
        'unit': '万吨标准煤',             # 标准化单位
        'original_value': 1000.0,        # 原始数值
        'original_unit': '万吨',          # 原始单位
        'conversion_factor': 0.714,      # 转换因子
        'conversion_method': '折标煤'     # 转换方法
    }
)

# 产品数据标准化示例
StandardizedData(
    data_type=StandardizedDataType.PRODUCT,
    year=2021,
    province="山西",
    city="太原",
    source_table="fct_y_prd_output",
    attributes={
        'product_name': '粗钢',          # 原始产品名称
        'industry': '钢铁',              # 跨列映射结果
        'macro_industry': '工业',         # 层次关系结果
        'value': 500.0,                  # 产品产量
        'unit': '万吨',                  # 产品单位
        'emission_factor': 1.85,          # 工业过程排放因子
        'emission_category': '工业过程'     # 排放类别
    }
)
```

## 4. 中间数据模型

### 4.1 城市能源矩阵 (`CityEnergyMatrix`)

**业务含义**: 省级数据下沉到地市后的中间结果
**数据特征**:
- 三维矩阵结构：城市×行业×能源品种
- 包含数据下沉的方法和权重信息
- 支持数据质量验证
- 为IPF平衡提供基础数据

**核心字段**:
- `year`: 统计年份
- `matrix_data`: 三维矩阵数据
- `allocation_method`: 分配方法
- `source_data`: 源数据信息
- `validation_status`: 验证状态

**业务用途**:
- 为IPF平衡算法提供基础矩阵
- 为清单构建提供数据支撑
- 支持数据质量评估

### 4.2 约束条件数据 (`ConstraintData`)

**业务含义**: 用于IPF平衡算法的约束条件
**数据特征**:
- 行约束：省级行业总量
- 列约束：城市总能耗
- 约束来源和计算方法记录
- 支持约束条件的动态调整

**核心字段**:
- `year`: 统计年份
- `row_constraints`: 行约束数据
- `column_constraints`: 列约束数据
- `constraint_source`: 约束来源
- `calculation_method`: 计算方法

**业务用途**:
- 为IPF算法提供约束条件
- 确保数据平衡结果符合业务规则
- 支持约束条件的验证和调整

## 5. 最终输出数据模型

### 5.1 能源消费清单 (`EnergyConsumptionInventory`)

**业务含义**: 系统最终产出的能源消费清单
**数据特征**:
- 包含综合能耗和分品种能耗
- 支持地区-行业维度的汇总
- 数据质量指标和验证状态
- 完整的构建方法记录

**核心字段**:
- `year`: 统计年份
- `inventory_data`: 清单数据
- `construction_method`: 构建方法
- `validation_status`: 验证状态
- `data_completeness`: 数据完整性

**数据内容**:
- **综合能耗**: 地区-行业维度的总能耗数据
- **分品种能耗**: 地区-行业-能源品种的详细数据
- **数据质量指标**: 完整性、一致性、准确性指标

**业务用途**:
- 为碳排放计算提供基础数据
- 支持能源消费结构分析
- 为政策制定提供数据支撑

### 5.2 碳排放清单 (`CarbonEmissionInventory`)

**业务含义**: 系统最终产出的碳排放清单
**数据特征**:
- 包含能源燃烧和工业过程排放
- 支持多种排放因子的配置
- 不确定性分析和验证状态
- 完整的计算方法记录

**核心字段**:
- `year`: 统计年份
- `inventory_data`: 清单数据
- `calculation_method`: 计算方法
- `emission_factors_source`: 排放因子来源
- `validation_status`: 验证状态
- `uncertainty_analysis`: 不确定性分析

**数据内容**:
- **能源燃烧排放**: 基于能源消费量和排放因子
- **工业过程排放**: 基于产品产量和过程排放因子
- **排放源分类**: 区分不同类型的排放源

**业务用途**:
- 为碳减排政策提供数据支撑
- 支持碳排放结构分析
- 为碳交易提供基础数据

## 6. 数据关系模型

### 6.1 数据依赖关系

```
原始数据表 → 标准化数据 → 中间数据 → 最终输出
     │           │           │         │
     ▼           ▼           ▼         ▼
RawData → StandardizedData → CityEnergyMatrix → Inventory
```

**依赖规则**:
- 原始数据表是数据处理的起点
- 标准化数据依赖于原始数据的完整性和质量
- 中间数据依赖于标准化数据的正确性
- 最终输出依赖于中间数据的平衡结果

### 6.2 数据映射关系

#### 6.2.1 行业映射关系
- **原始行业名称** → **标准行业名称**
- **子行业** → **宏观行业**
- **产品名称** → **对应行业**

#### 6.2.2 能源品种映射关系
- **原始能源名称** → **标准能源名称**
- **能源分类** → **折标方法**
- **单位换算** → **标准煤当量**

#### 6.2.3 地理映射关系
- **省份** → **地市列表**
- **地市** → **所属省份**
- **区域** → **城市群**

### 6.3 数据一致性规则

#### 6.3.1 总量一致性
- 地市汇总 = 省级总量（允许误差范围内）
- 分品种汇总 = 综合能耗
- 各产业GDP汇总 = 总GDP

#### 6.3.2 时间一致性
- 所有数据使用相同的统计年份
- 月度数据聚合为年度数据
- 历史数据的时间连续性

#### 6.3.3 逻辑一致性
- 能源消费量与GDP的合理关系
- 能耗强度与能源消费量的关系
- 产品产量与行业分类的对应关系

## 7. 数据质量模型

### 7.1 质量维度

#### 7.1.1 完整性
- **字段完整性**: 必需字段的填充率
- **记录完整性**: 预期记录数的覆盖率
- **关联完整性**: 外键关系的完整性

#### 7.1.2 一致性
- **格式一致性**: 数据格式的统一性
- **编码一致性**: 分类编码的一致性
- **逻辑一致性**: 业务逻辑的一致性

#### 7.1.3 准确性
- **数值准确性**: 数值的合理性和精确性
- **分类准确性**: 分类的正确性
- **计算准确性**: 计算结果的正确性

### 7.2 质量评估方法

#### 7.2.1 自动检查
- **范围检查**: 数值是否在合理范围内
- **格式检查**: 数据格式是否符合规范
- **关联检查**: 关联关系是否正确

#### 7.2.2 业务验证
- **总量验证**: 汇总数据的一致性
- **趋势验证**: 历史数据的趋势合理性
- **逻辑验证**: 业务逻辑的正确性

#### 7.2.3 质量评分
- **综合评分**: 多维度质量的综合评估
- **权重分配**: 不同质量维度的权重
- **改进建议**: 基于质量评估的改进建议

## 8. 数据治理模型

### 8.1 数据生命周期管理

#### 8.1.1 数据采集
- **数据源管理**: 数据源的识别和分类
- **采集频率**: 数据的更新频率
- **采集方式**: 自动采集和手动采集

#### 8.1.2 数据处理
- **标准化处理**: 数据格式和内容的标准化
- **质量检查**: 数据质量的检查和验证
- **异常处理**: 数据异常的处理策略

#### 8.1.3 数据存储
- **存储策略**: 数据的存储方式和位置
- **备份策略**: 数据的备份和恢复
- **归档策略**: 历史数据的归档管理

### 8.2 数据安全管理

#### 8.2.1 访问控制
- **用户权限**: 不同用户的访问权限
- **数据脱敏**: 敏感数据的脱敏处理
- **审计日志**: 数据访问的审计记录

#### 8.2.2 数据保护
- **加密存储**: 敏感数据的加密存储
- **传输安全**: 数据传输的安全保护
- **备份保护**: 备份数据的安全保护

---

*本数据模型文档描述了系统中各个数据表的业务含义、逻辑结构和相互关系，为系统设计和数据治理提供指导。*
