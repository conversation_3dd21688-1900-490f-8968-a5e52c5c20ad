# 核算任务实体 (CalculationJob Entity)

## 1. 概述

`CalculationJob` 是驱动整个业务流程的**核心实体**与**聚合根**。它代表了一次从“启动核算”到“输出结果”的完整端到端流程。

它的主要职责是：
1.  作为一个**状态机**，管理和记录整个业务流程的当前状态。
2.  作为**实体**，持久化与一次特定核算任务相关的所有信息，包括对中间产物（值对象）和最终产物（实体）的引用。

## 2. 实体属性 (Properties)

| 属性名称 | 类型 | 描述 | 示例 |
| :--- | :--- | :--- | :--- |
| **`jobId`** | `UUID` | 实体的唯一标识符 (主键)。 | `e8a3b5c7-02f3-4c34-a81d-2b3c4d5e6f7a` |
| `jobName` | `String` | 用户为任务指定的名称。 | `"2024年第一季度碳排放预测"` |
| `status` | `Enum` | 任务当前所处的流程状态。 | `DataQualityChecking`, `ModelBuilding`, `Completed` |
| `failureReason` | `String` | 如果任务失败，记录失败原因。 | `"数据质量检查未通过：关键数据缺失超过阈值"` |
| `createdAt` | `Timestamp` | 任务创建时间。 | `2023-10-27T06:00:00Z` |
| `updatedAt` | `Timestamp` | 任务状态最后更新时间。 | `2023-10-27T12:00:00Z` |
| `jobContext` | `JSON/Text` | 一个可序列化的字段，用于存储流程中产生的临时值对象（如 `StandardizedData` VO），以便于调试和恢复。 | `{"standardizedData_ref": "path/to/data.json"}` |

## 3. 状态机 (State Machine)

`CalculationJob` 的生命周期遵循一个明确的状态流转路径，由领域事件驱动。

```mermaid
stateDiagram-v2
    direction LR
    [*] --> Started
    Started --> DataQualityChecking : LoadInitialData
    DataQualityChecking --> DataStandardizing : DataQualityChecked
    DataStandardizing --> InventoryConstructing : DataStandardized
    InventoryConstructing --> ModelBuilding : InventoryConstructed
    ModelBuilding --> PredictionAnalyzing : ModelBuilt
    PredictionAnalyzing --> DataFusing : PredictionAnalyzed
    DataFusing --> ResultOutputting : DataFused
    ResultOutputting --> Completed : ResultOutputted
    
    state Fork <<fork>>
    DataQualityChecking --> Fork
    Fork --> Failed: QualityCheckFailed

    state Fork2 <<fork>>
    DataStandardizing --> Fork2
    Fork2 --> Failed: StandardizationFailed

    state Fork3 <<fork>>
    InventoryConstructing --> Fork3
    Fork3 --> Failed: InventoryConstructionFailed
    
    state Fork4 <<fork>>
    ModelBuilding --> Fork4
    Fork4 --> Failed: ModelBuildFailed
    
    Failed --> [*]
    Completed --> [*]
```

## 4. 实体行为 (Behaviors / Methods)

作为聚合根，`CalculationJob` 的行为主要体现在响应命令和应用事件上。

- **`start(jobName)`**: 启动一个新的核算任务，设置状态为 `Started`，并发布 `CalculationJobStarted` 事件。
- **`advanceState(event)`**: 监听下游聚合发布的领域事件（如 `DataQualityChecked`），并根据事件类型将自身状态推进到下一个阶段（如 `DataStandardizing`）。
- **`fail(reason)`**: 当任何步骤失败时，将任务状态设置为 `Failed`，记录失败原因，并发布 `CalculationJobFailed` 事件。
- **`complete()`**: 在接收到 `ResultOutputted` 事件后，将状态设置为 `Completed`，完成整个流程。

## 5. 关联关系

- **拥有 (Owns)**: 一组 `PredictionResult` 实体。
- **引用 (References)**: 在其生命周期中，会临时性地创建和消费一系列值对象（`QualityCheckedData`, `StandardizedData`, `PredictionModel` 等）。这些值对象可以通过 `jobContext` 字段进行跟踪。

---
*`CalculationJob` 实体是整个领域模型的支柱。它将一系列无状态的流程步骤聚合串联起来，形成了一个完整的、有状态的、可跟踪的业务流程。*
