# 实体总览 (Entity Overview)

## 1. 设计哲学：精炼实体

在领域驱动设计中，精确地区分实体（Entity）和值对象（Value Object）至关重要。经过进一步的审视，我们采纳了“**精炼实体**”的设计原则：只有那些拥有**唯一身份标识 (ID)**、且其状态需要在不同业务步骤中被**跟踪和修改**的对象，才应被建模为实体。

其他在流程中作为一次性、不可变数据包传递的对象，则应被降级为**值对象**，以简化模型和持久化。

## 2. 实体分类

根据此原则，我们的领域模型中实体数量大大减少，核心概念更加突出。

### 2.1 流程驱动实体 (Process-Driving Entity)

这类实体是整个业务流程的“引擎”和状态机。

| 实体 (聚合根) | 职责 | 实体设计文档 |
| :--- | :--- | :--- |
| **`CalculationJob` (核算任务)** | 协调从数据加载到结果输出的整个端到端业务流程，管理流程的生命周期和状态。 | `3.1.2 核算任务实体.md` |

### 2.2 流程中可变实体 (Mutable In-Process Entity)

这类实体是在流程中被创建，并在后续步骤中其**状态会被明确修改**的业务对象。

| 实体 | 职责 | 设计文档 |
| :--- | :--- | :--- |
| **`PredictionResult` (预测结果)** | 封装一次模型预测的结果。它的状态会从 `Generated` 变为 `Fused`，其 `predictedValues` 也可能被修改。 | `3.1.3 预测结果实体.md` |

---
*我们的模型现在更加轻量，只有两个核心实体。其他所有在流程中传递的数据，都被建模为值对象。详细信息请参阅《值对象总览》。*
