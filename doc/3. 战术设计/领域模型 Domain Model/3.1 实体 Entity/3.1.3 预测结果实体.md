# 预测结果实体 (PredictionResult Entity)

## 1. 概述

`PredictionResult` 是由 `预测分析器 (PredictionAnalyzer)` 聚合创建的“流程产出实体”。它封装了一次特定模型在特定维度上的完整预测结果，是后续数据融合和最终输出的基础。

## 2. 实体属性 (Properties)

| 属性名称 | 类型 | 描述 | 示例 |
| :--- | :--- | :--- | :--- |
| **`resultId`** | `UUID` | 实体的唯一标识符 (主键)。 | `a1b2c3d4-e5f6-4a5b-8c7d-9e8f7a6b5c4d` |
| `jobId` | `UUID` | 关联的 `CalculationJob` 的ID。 | `e8a3b5c7-02f3-4c34-a81d-2b3c4d5e6f7a` |
| `modelId` | `UUID` | 用于生成此结果的 `PredictionModel` 的ID。 | `f47ac10b-58cc-4372-a567-0e02b2c3d479` |
| `targetDimension` | `String` | 此预测结果应用的业务维度。 | `"地区: XX省"`, `"地区: XX省, 行业: 制造业"` |
| `predictedValues` | `TimeSeriesData` | 包含预测结果的时间序列值对象。 | `[{timestamp: "2024-01", value: 105.2, lowerBound: 102.1, upperBound: 108.3}, ...]` |
| `status` | `Enum` | 实体的状态。 | `Generated`, `Fused` |
| `createdAt` | `Timestamp` | 创建时间戳。 | `2023-10-27T11:00:00Z` |

*注: `TimeSeriesData` 是一个值对象，封装了时间序列数据点。*

## 3. 实体行为 (Behaviors / Methods)

`PredictionResult` 实体本身不包含复杂的业务行为。它的主要职责是作为一个结构化的数据载体。其状态的变更（从 `Generated` 到 `Fused`）是由 `数据融合器 (DataFuser)` 聚合来驱动的。

### `updatePredictedValues(newValues: TimeSeriesData)`
- **职责**: 在数据融合过程中，允许 `DataFuser` 更新其预测值。
- **参数**: `newValues` - 经过融合算法调整后的新时间序列数据。
- **前置条件**: 只能由 `DataFuser` 聚合调用。

### `markAsFused()`
- **职责**: 将实体状态更新为 `Fused`。
- **前置条件**: 只能由 `DataFuser` 聚合调用。

## 4. 生命周期 (Lifecycle)

1.  **创建 (Creation)**: 由 `预测分析器 (PredictionAnalyzer)` 在成功执行一次模型预测后创建，状态为 `Generated`。
2.  **更新 (Update)**: 由 `数据融合器 (DataFuser)` 加载和更新。`predictedValues` 可能被调整，状态被更新为 `Fused`。
3.  **使用 (Usage)**: 由 `结果输出器 (ResultOutputter)` 加载，作为生成最终输出数据的来源。
4.  **归档/删除 (Archive/Deletion)**: 当其所属的 `CalculationJob` 完成或被归档时，关联的 `PredictionResult` 也可能被归档或删除。

## 5. 关联关系

- **拥有者**: `CalculationJob`。一个 `CalculationJob` 可以拥有多个 `PredictionResult`。
- **创建者**: `PredictionAnalyzer` 聚合。
- **修改者**: `DataFuser` 聚合。
- **使用者**: `ResultOutputter` 聚合。
- **关联**: 通过 `modelId` 关联到生成它的 `PredictionModel`。

---
*`PredictionResult` 实体清晰地记录了预测流程的直接产出，并通过其状态 (`status`) 字段，明确地展示了它在整个业务流程中所处的阶段。*
