# 结果输出服务 (ResultOutputService)

## 1. 角色

**结果输出服务** 是一个**无状态的领域服务**，是数据处理管道的最终环节。

- **核心职责**: 接收最终的计算结果（`EnergyConsumptionInventory` 和 `CarbonEmissionInventory`），并根据指定的格式（如 CSV, Excel）或目标（如数据库表），将它们持久化。
- **定位**: 封装了所有关于数据格式化、文件生成和数据库写入的逻辑，是连接领域模型和外部存储的出口。
- **当前状态**: **（待实现）**

## 2. 接口定义 (初步设想)

```python
# 建议位置: ecam_calculator/domain/service/result_output_service.py

class ResultOutputService(ABC):
    """
    领域服务接口：负责将计算结果持久化。
    """

    @abstractmethod
    def output_results(
        self, 
        energy_inventory: EnergyConsumptionInventory,
        carbon_inventory: CarbonEmissionInventory,
        output_format: str = 'csv',
        output_path: str = './results'
    ) -> None:
        """
        将清单结果输出为指定格式的文件。

        Args:
            energy_inventory (EnergyConsumptionInventory): 待输出的能源消费清单。
            carbon_inventory (CarbonEmissionInventory): 待输出的碳排放清单。
            output_format (str): 输出文件的格式 ('csv', 'excel', etc.)。
            output_path (str): 输出文件的保存路径。
        """
        raise NotImplementedError
```

## 3. 核心业务逻辑 (待实现)

服务的实现将包含：

- **格式化逻辑**: 根据 `output_format` 参数，选择不同的数据格式化策略。
- **文件写入**: 使用 `pandas` 或其他库，将 `DataFrame` 格式的清单数据写入到指定 `output_path` 的文件中。
- **数据库写入 (可选)**: 未来可以扩展，支持将结果写入到数据库的特定表中。
