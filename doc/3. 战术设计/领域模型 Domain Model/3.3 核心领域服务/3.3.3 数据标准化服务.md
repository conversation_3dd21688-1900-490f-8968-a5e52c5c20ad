# 数据标准化服务 (DataStandardizationService)

## 1. 角色

**数据标准化服务** 是一个**无状态的领域服务**，它是连接原始数据和领域模型的关键桥梁。

- **核心职责**: 接收经过质量检查的异构 `RawData` 对象，并根据三种核心功能的标准化架构，将其转换为一系列统一的、结构化的 `StandardizedData` 值对象。
- **定位**: 作为一个无状态的服务，它封装了所有关于数据清洗、转换、映射和标准化的领域知识。它是数据处理管道中承上启下的核心环节。
- **交互方式**: 由应用服务 (`CalculationJobService`) 在数据质量检查通过后直接调用。

## 2. 接口定义 (服务契约)

```python
# 位置: ecam_calculator/domain/service/data_standardization_service.py

class DataStandardizationService(ABC):
    """
    领域服务接口：负责将 RawData 转换为 StandardizedData。
    """

    @abstractmethod
    def standardize(self, raw_data: RawData) -> List[StandardizedData]:
        """
        根据 RawData 的来源（表名）使用标准化编排器进行标准化处理。

        Args:
            raw_data (RawData): 单个原始数据对象。

        Returns:
            List[StandardizedData]: 转换后的标准化数据对象列表。
        """
        raise NotImplementedError
```

## 3. 核心业务逻辑 (重构后)

该服务的实现采用**三种核心功能的标准化架构**：

### 3.1 功能1：列内映射（规范列名称到给定集合）
- **动态派发**: 根据配置中的 `column_mappings` 识别源列名和关键词集合
- **映射处理**: 将原始值映射到标准化的名称集合
- **结果生成**: 生成 `standard_*` 列（如 `standard_province`, `standard_industry`）

### 3.2 功能2：列内层次（为标准化值赋予层次关系）
- **层次识别**: 根据配置中的 `parent` 字段建立父子关系
- **层次生成**: 生成 `macro_*` 列（如 `macro_industry`, `macro_province`）
- **关系维护**: 维护地区（省-市）、行业（宏观-子行业）的层次结构

### 3.3 功能3：跨列映射（基于一个列推导另一个列）
- **源列识别**: 识别源列（如 `product_name`）
- **映射应用**: 应用映射规则（如产品名称到行业）
- **目标列生成**: 生成目标列（如 `industry`）

### 3.4 标准化编排器
- **执行顺序**: 先执行列内标准化，再执行跨列映射
- **依赖管理**: 确保跨列映射依赖列内标准化结果
- **统一输出**: 输出包含所有标准化结果的DataFrame

## 4. 标准化功能实现

### 4.1 标准化功能基类
```python
class StandardizationFunction(ABC):
    """标准化功能基类"""
    @abstractmethod
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """应用标准化功能"""
        pass
```

### 4.2 列标准化器
```python
class ColumnStandardizer(StandardizationFunction):
    """功能1+2：列内映射 + 层次关系"""
    
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        # 执行列内映射
        # 建立层次关系
        pass
```

### 4.3 跨列映射器
```python
class CrossColumnMapper(StandardizationFunction):
    """功能3：跨列信息映射"""
    
    def apply(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        # 基于源列推导目标列
        pass
```

## 5. 输入与输出

- **输入**: `原始数据 (RawData VO)`
- **输出**: `标准化数据 (StandardizedData VO)` 列表

### 5.1 标准化后的数据结构
- **列内映射结果**: `standard_province`, `standard_industry`, `standard_energy_type`
- **层次关系结果**: `macro_province`, `macro_industry`, `macro_energy_type`
- **跨列映射结果**: `industry`（从产品名称推导）, `emission_category`（从能源品种推导）

### 5.2 配置驱动
- **统一配置结构**: `standardization_mappings` 配置
- **动态加载**: 根据表名动态加载对应的标准化规则
- **向后兼容**: 保持现有配置的兼容性
