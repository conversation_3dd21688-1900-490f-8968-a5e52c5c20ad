# 数据增强服务 (DataEnhancementService)

## 1. 角色

**数据增强服务** 是一个**无状态的领域服务**，它在数据处理管道中扮演着"数据补全和细化"的关键角色。

- **核心职责**: 接收标准化后的能源消费数据，通过空间降尺度和时间降尺度两个步骤，将省级年度数据增强为地市级月度数据，填补数据在时间、行业、品种维度上的缺失。
- **定位**: 作为数据处理管道中的关键环节，位于数据标准化之后、能源转换之前，负责将粗粒度的省级数据细化为细粒度的地市级数据。
- **交互方式**: 由应用服务 (`CalculationJobService`) 在数据标准化完成后调用，接收标准化数据字典，返回增强后的数据字典。

## 2. 项目中的位置和上下文

### 2.1 在数据处理管道中的位置

```mermaid
graph LR
    A[原始数据获取] --> B[数据标准化]
    B --> C[数据增强]
    C --> D[能源转换]
    D --> E[清单构建]
    E --> F[结果输出]
```

**具体执行顺序**:

1. **步骤1**: `_step_raw_data_fetching()` - 获取原始数据
2. **步骤2**: `_step_data_standardization()` - 数据标准化
3. **步骤3**: `_step_data_enhancement()` - **数据增强** ← 当前服务位置
4. **步骤4**: `_step_energy_conversion()` - 能源转换
5. **步骤5**: `_step_generate_data()` - 生成数据
6. **步骤6**: `_step_completion()` - 完成任务

### 2.2 在代码架构中的位置

**文件路径**: `ecam_calculator/domain/service/data_enhancement_service.py`

**依赖关系**:

```python
# 应用层调用
CalculationJobService._step_data_enhancement()
    ↓
# 领域服务接口
DataEnhancementService.enhance_data()
    ↓
# 具体实现
DataEnhancementServiceImpl.enhance_data()
```

**依赖注入位置**: `ecam_calculator/main.py` 的 `create_job_service()` 函数

### 2.3 在计算上下文中的位置

**CalculationContext 数据结构**:

```python
@dataclass
class CalculationContext:
    start_year: int
    end_year: int
    province: Region
    cities: List[Region]
    job_id: str
    raw_data: Dict[str, Any]                    # 原始数据
    standardized_data: Dict[str, Any]           # 标准化数据 ← 输入来源
    enhanced_data: Dict[str, Any]               # 增强数据 ← 输出目标
    constraints: List[Any]
    city_matrices: Dict[str, CityEnergyMatrix]
    energy_inventory: Optional[EnergyConsumptionInventory] = None
    carbon_inventory: Optional[CarbonEmissionInventory] = None
```

## 3. 接口定义 (服务契约)

```python
# 位置: ecam_calculator/domain/service/data_enhancement_service.py

class DataEnhancementService(ABC):
    """
    领域服务接口：负责数据增强处理，包括空间降尺度和时间降尺度。
    """
    
    @abstractmethod
    def enhance_data(self, standardized_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        对标准化后的数据进行增强处理。

        Args:
            standardized_data (Dict[str, pd.DataFrame]): 标准化后的数据字典，包含：
                - 'energy_consumption': 省级能源消费数据
                - 'energy_consumption2': 地市工业能源消费数据  
                - 'electricity': 地市用电量数据
                - 'converted_energy': 省级转换后能源数据
                - 'converted_energy2': 地市工业转换后能源数据

        Returns:
            Dict[str, pd.DataFrame]: 增强后的数据字典，包含：
                - 'spatial_downscaled_energy': 空间降尺度后的地市能源数据
                - 'temporal_downscaled_energy': 时间降尺度后的地市月度能源数据
        """
        raise NotImplementedError
    
    @abstractmethod
    def spatial_downscaling(self, provincial_data: pd.DataFrame, city_data: pd.DataFrame, 
                           city_electricity: pd.DataFrame) -> pd.DataFrame:
        """
        执行空间降尺度处理。

        Args:
            provincial_data (pd.DataFrame): 省级能源消费数据
            city_data (pd.DataFrame): 地市工业能源消费数据
            city_electricity (pd.DataFrame): 地市年度分行业用电量数据

        Returns:
            pd.DataFrame: 空间降尺度后的地市能源消费数据
        """
        raise NotImplementedError
    
    @abstractmethod
    def temporal_downscaling(self, spatial_enhanced_data: pd.DataFrame, 
                            monthly_electricity: pd.DataFrame) -> pd.DataFrame:
        """
        执行时间降尺度处理。

        Args:
            spatial_enhanced_data (pd.DataFrame): 空间降尺度后的地市能源数据
            monthly_electricity (pd.DataFrame): 地市月度用电量数据

        Returns:
            pd.DataFrame: 时间降尺度后的地市月度能源消费数据
        """
        raise NotImplementedError
```

## 4. 传入参数详解

### 4.1 在CalculationJobService中的调用方式

**调用位置**: `ecam_calculator/application/calculation_job_service.py`

```python
def _step_data_enhancement(self, job: CalculationJob, context: CalculationContext):
    """步骤4: 数据增强 - 空间降尺度和时间降尺度"""
    self.logger.info(f"Job [{job.id}] - 开始执行数据增强...")
    
    try:
        # 确保数据增强服务已初始化
        if not self.data_enhancement_service:
            self.data_enhancement_service = DataEnhancementService()
        
        # 获取标准化和转换后的数据
        enhanced_results = self.data_enhancement_service.enhance_data(
            standardized_data=context.standardized_data,
            calculation_context=context
        )
        
        # 将增强结果存储到上下文中
        context.enhanced_data = enhanced_results
        
        # 记录增强结果统计
        total_records = sum(len(df) for df in context.enhanced_data.values() if not df.empty)
        self.logger.info(f"Job [{job.id}] - 数据增强完成，总计 {total_records} 条记录")
        
    except Exception as e:
        self.logger.error(f"Job [{job.id}] - 数据增强失败: {e}")
        raise
```

### 4.2 传入参数的具体内容

**主要参数**: `context.standardized_data` (Dict[str, pd.DataFrame])

**具体数据源**:

```python
standardized_data = {
    # 原始标准化数据
    'emission_factors': pd.DataFrame,           # 排放因子数据 (273条记录)
    'energy_consumption': pd.DataFrame,         # 省级能源消费数据 (25,760条记录)
    'energy_consumption2': pd.DataFrame,        # 地市工业能源消费数据 (46,535条记录)
    'electricity': pd.DataFrame,                # 地市用电量数据 (74,496条记录)
    'gdp_data': pd.DataFrame,                   # GDP数据 (3,360条记录)
    'energy_intensity': pd.DataFrame,           # 能耗强度数据 (204条记录)
    'product_output': pd.DataFrame,              # 工业产品产量数据 (1,812条记录)
    
    # 注意：此时还没有能源转换后的数据
    # 'converted_energy' 等字段将在数据增强完成后由能源转换服务生成
}
```

**计算上下文参数**: `context` (CalculationContext)

```python
context = CalculationContext(
    start_year=2020,                    # 计算起始年份
    end_year=2020,                      # 计算结束年份
    province=Region(name="山西", level="province"),  # 目标省份
    cities=[Region(name="太原", level="city"), ...],  # 目标城市列表
    job_id="uuid-string",               # 任务ID
    # ... 其他字段
)
```

### 4.3 数据增强服务的依赖注入

**在main.py中的配置**:

```python
def create_job_service(db_config: Dict[str, Any]) -> CalculationJobService:
    """简单直接的依赖注入 - 创建计算任务服务"""
    
    # 创建基础设施服务
    job_repository = JobRepositoryImpl(db_config)
    
    # 创建领域服务
    quality_check_service = QualityCheckService()
    data_standardization_service = DataStandardizationService()
    constraint_calculation_service = ConstraintCalculationService()
    inventory_construction_service = InventoryConstructionService()
    result_output_service = ResultOutputService()
    energy_conversion_service = EnergyConversionService()
    data_enhancement_service = DataEnhancementService()  # 新增数据增强服务
    
    # 创建应用服务
    return CalculationJobService(
        job_repository=job_repository,
        quality_check_service=quality_check_service,
        data_standardization_service=data_standardization_service,
        constraint_calculation_service=constraint_calculation_service,
        inventory_construction_service=inventory_construction_service,
        result_output_service=result_output_service,
        energy_conversion_service=energy_conversion_service,
        data_enhancement_service=data_enhancement_service  # 注入数据增强服务
    )
```

### 4.4 配置文件中的参数

**配置文件位置**: `ecam_calculator/config/parameters.yaml`

```yaml
# 数据增强配置
data_enhancement:
  spatial_downscaling:
    enabled: true
    weight_method: "electricity_proportion"  # 权重计算方法
    merge_strategy: "city_priority"         # 合并策略
    validation_rules:
      - "total_consistency"                 # 总量一致性检查
      - "industry_consistency"              # 行业一致性检查
  
  temporal_downscaling:
    enabled: true
    method: "denton"                        # 分解方法
    indicator_source: "monthly_electricity" # 指示器数据源
    validation_rules:
      - "annual_sum_consistency"            # 年度总和一致性
      - "seasonal_pattern_check"            # 季节性模式检查

# 省份和地市映射 (用于空间降尺度)
province_city_mapping:
  - province: "山西"
    cities:
      - "太原"
      - "大同"
      - "阳泉"
      # ... 其他城市
```

## 5. 核心业务逻辑

### 5.1 空间降尺度 (Spatial Downscaling)

**业务目标**: 将省级年度能源消费数据下沉到地市级，填补地市数据在时间、行业、品种维度上的缺失。

**处理流程**:

1. **数据识别**: 识别省级能源消费数据和地市工业能源消费数据
2. **缺失分析**: 分析地市数据在时间、行业、品种维度的缺失情况
3. **权重计算**: 基于地市年度分行业用电量计算权重矩阵
4. **数据分配**: 将省级数据按权重分配到各地市
5. **数据合并**: 将地市直接数据与省级下沉数据合并

**业务规则**:

- **优先级原则**: 地市直接数据 > 省级下沉数据
- **权重基础**: 使用地市年度分行业用电量作为分配权重
- **完整性保证**: 确保所有地市-行业-品种组合都有数据
- **一致性验证**: 验证下沉后数据与省级总量的一致性

**输入数据结构**:

```python
# 省级能源消费数据
provincial_data: pd.DataFrame = {
    'year': int,           # 年份
    'province': str,       # 省份
    'standard_industry': str,  # 标准化行业
    'standard_energy_type': str,  # 标准化能源品种
    'standard_coal_equivalent': float,  # 标准煤当量
    'value': float,        # 原始数值
    'unit': str           # 单位
}

# 地市工业能源消费数据
city_data: pd.DataFrame = {
    'year': int,           # 年份
    'standard_area': str,  # 标准化地市
    'standard_industry': str,  # 标准化行业
    'standard_energy_type': str,  # 标准化能源品种
    'standard_coal_equivalent': float,  # 标准煤当量
    'value': float,        # 原始数值
    'unit': str           # 单位
}

# 地市年度分行业用电量
city_electricity: pd.DataFrame = {
    'year': int,           # 年份
    'standard_area': str,  # 标准化地市
    'standard_industry': str,  # 标准化行业
    'value': float,        # 用电量
    'unit': str           # 单位
}
```

**输出数据结构**:

```python
# 空间降尺度后的地市能源数据
spatial_enhanced_data: pd.DataFrame = {
    'year': int,           # 年份
    'standard_area': str,  # 标准化地市
    'standard_industry': str,  # 标准化行业
    'standard_energy_type': str,  # 标准化能源品种
    'standard_coal_equivalent': float,  # 标准煤当量
    'value': float,        # 原始数值
    'unit': str,          # 单位
    'data_source': str,   # 数据来源 ('direct' | 'downscaled')
    'allocation_weight': float  # 分配权重
}
```

### 5.2 时间降尺度 (Temporal Downscaling)

**业务目标**: 将地市年度能源消费数据分解为月度数据，填补时间维度的缺失。

**处理流程**:

1. **数据准备**: 准备年度能源消费数据和月度用电量指示器
2. **Denton方法**: 使用timedissagg包的denton方法进行时间分解
3. **约束调整**: 确保月度数据总和等于年度数据
4. **质量验证**: 验证分解结果的合理性和一致性

**技术实现**:

```python
from timedissagg import denton

def temporal_downscaling(self, annual_data: pd.DataFrame, monthly_indicator: pd.DataFrame) -> pd.DataFrame:
    """
    使用Denton方法进行时间降尺度
    """
    # 1. 数据分组和排序
    annual_data = annual_data.sort_values(['standard_area', 'standard_industry', 'standard_energy_type', 'year'])
    
    # 2. 对每个地市-行业-品种组合执行Denton分解
    monthly_results = []
    
    for (area, industry, energy_type), group in annual_data.groupby(['standard_area', 'standard_industry', 'standard_energy_type']):
        # 获取年度数据
        annual_values = group['standard_coal_equivalent'].values
        
        # 获取对应的月度指示器
        monthly_indicator_group = monthly_indicator[
            (monthly_indicator['standard_area'] == area) & 
            (monthly_indicator['standard_industry'] == industry)
        ]
        
        if not monthly_indicator_group.empty:
            # 使用Denton方法分解
            monthly_values = denton(
                indicator=monthly_indicator_group['value'].values,
                annual=annual_values,
                freq='M'  # 月度频率
            )
            
            # 构建月度数据
            monthly_data = pd.DataFrame({
                'year': group['year'].iloc[0],
                'month': range(1, 13),
                'standard_area': area,
                'standard_industry': industry,
                'standard_energy_type': energy_type,
                'standard_coal_equivalent': monthly_values,
                'data_source': 'temporal_downscaled',
                'decomposition_method': 'denton'
            })
            
            monthly_results.append(monthly_data)
    
    return pd.concat(monthly_results, ignore_index=True)
```

**业务规则**:

- **约束条件**: 月度数据总和必须等于年度数据
- **指示器使用**: 使用月度用电量作为分解指示器
- **平滑性**: 确保月度数据变化平滑，避免剧烈波动
- **完整性**: 确保所有年度数据都能分解为月度数据

**输入数据结构**:

```python
# 空间降尺度后的地市能源数据
spatial_enhanced_data: pd.DataFrame = {
    'year': int,           # 年份
    'standard_area': str,  # 标准化地市
    'standard_industry': str,  # 标准化行业
    'standard_energy_type': str,  # 标准化能源品种
    'standard_coal_equivalent': float,  # 标准煤当量
    'data_source': str,   # 数据来源
    'allocation_weight': float  # 分配权重
}

# 地市月度用电量数据
monthly_electricity: pd.DataFrame = {
    'year': int,           # 年份
    'month': int,          # 月份
    'standard_area': str,  # 标准化地市
    'standard_industry': str,  # 标准化行业
    'value': float,        # 月度用电量
    'unit': str           # 单位
}
```

**输出数据结构**:

```python
# 时间降尺度后的地市月度能源数据
temporal_enhanced_data: pd.DataFrame = {
    'year': int,           # 年份
    'month': int,          # 月份
    'standard_area': str,  # 标准化地市
    'standard_industry': str,  # 标准化行业
    'standard_energy_type': str,  # 标准化能源品种
    'standard_coal_equivalent': float,  # 标准煤当量
    'data_source': str,   # 数据来源 ('direct' | 'downscaled' | 'temporal_downscaled')
    'decomposition_method': str,  # 分解方法 ('denton')
    'allocation_weight': float  # 分配权重
}
```

## 6. 数据增强编排器

### 6.1 编排器设计

```python
class DataEnhancementOrchestrator:
    """数据增强编排器 - 管理空间降尺度和时间降尺度的执行顺序"""
    
    def __init__(self, config_reader):
        self.config_reader = config_reader
        self.spatial_enhancer = SpatialDownscalingService()
        self.temporal_enhancer = TemporalDownscalingService()
        self.logger = logging.getLogger(__name__)
    
    def enhance_data(self, standardized_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """执行完整的数据增强流程"""
        self.logger.info("开始数据增强处理")
        
        # 步骤1: 空间降尺度
        spatial_result = self.spatial_enhancer.downscale(
            provincial_data=standardized_data.get('converted_energy', pd.DataFrame()),
            city_data=standardized_data.get('converted_energy2', pd.DataFrame()),
            city_electricity=standardized_data.get('electricity', pd.DataFrame())
        )
        
        # 步骤2: 时间降尺度
        temporal_result = self.temporal_enhancer.downscale(
            spatial_data=spatial_result,
            monthly_electricity=standardized_data.get('electricity', pd.DataFrame())
        )
        
        return {
            'spatial_downscaled_energy': spatial_result,
            'temporal_downscaled_energy': temporal_result
        }
```

### 6.2 错误处理策略

**数据缺失处理**:

- **省级数据缺失**: 跳过该省份的空间降尺度
- **地市用电量缺失**: 使用均匀分配权重
- **月度指示器缺失**: 使用历史平均值或趋势外推

**数据质量保证**:

- **一致性检查**: 验证降尺度后数据与原始数据的一致性
- **合理性验证**: 检查月度数据的季节性和趋势合理性
- **异常检测**: 识别和处理异常值

## 7. 配置驱动设计

### 7.1 配置结构

```yaml
# config/parameters.yaml
data_enhancement:
  spatial_downscaling:
    enabled: true
    weight_method: "electricity_proportion"  # 权重计算方法
    merge_strategy: "city_priority"         # 合并策略
    validation_rules:
      - "total_consistency"                 # 总量一致性检查
      - "industry_consistency"              # 行业一致性检查
  
  temporal_downscaling:
    enabled: true
    method: "denton"                        # 分解方法
    indicator_source: "monthly_electricity" # 指示器数据源
    validation_rules:
      - "annual_sum_consistency"            # 年度总和一致性
      - "seasonal_pattern_check"            # 季节性模式检查
```

### 7.2 动态配置加载

```python
class DataEnhancementConfig:
    """数据增强配置管理"""
    
    def __init__(self, config_reader):
        self.config_reader = config_reader
        self.spatial_config = self.config_reader.get_spatial_downscaling_config()
        self.temporal_config = self.config_reader.get_temporal_downscaling_config()
    
    def get_weight_method(self) -> str:
        return self.spatial_config.get('weight_method', 'electricity_proportion')
    
    def get_decomposition_method(self) -> str:
        return self.temporal_config.get('method', 'denton')
```

## 8. 输入与输出

### 8.1 输入数据

- **标准化能源数据**: 经过标准化但尚未转换的省级和地市能源消费数据
- **用电量数据**: 地市年度和月度分行业用电量数据
- **配置参数**: 降尺度方法和验证规则配置

### 8.2 输出数据

- **空间降尺度结果**: 填补缺失的地市年度能源消费数据
- **时间降尺度结果**: 填补缺失的地市月度能源消费数据
- **质量报告**: 数据增强过程的质量指标和验证结果

**注意**: 数据增强服务的输出将作为能源转换服务的输入，能源转换服务会基于增强后的数据执行标准煤当量转换。

### 8.3 数据质量指标

- **完整性**: 数据覆盖的完整性指标
- **一致性**: 降尺度前后数据的一致性指标
- **合理性**: 月度数据的季节性和趋势合理性指标

## 9. 业务价值

### 9.1 数据补全

- **空间维度**: 将省级数据下沉到地市级，填补地市数据缺失
- **时间维度**: 将年度数据分解为月度数据，提供更细粒度的时间信息
- **行业维度**: 确保所有行业都有完整的能源消费数据

### 9.2 数据质量提升

- **一致性保证**: 确保不同层级数据的一致性
- **合理性验证**: 通过业务规则验证增强后数据的合理性
- **可追溯性**: 记录数据来源和处理方法，支持数据溯源

### 9.3 业务支持

- **清单构建**: 为后续的能源消费清单构建提供完整的地市月度数据
- **能源转换**: 为能源转换服务提供完整的地市月度数据，支持标准煤当量转换
- **碳排放计算**: 支持基于月度数据的碳排放计算和分析
- **政策制定**: 为政策制定提供更细粒度的数据支撑

---

*本服务设计遵循配置驱动原则，支持灵活的业务规则调整，确保数据增强过程的可靠性和可维护性。*
