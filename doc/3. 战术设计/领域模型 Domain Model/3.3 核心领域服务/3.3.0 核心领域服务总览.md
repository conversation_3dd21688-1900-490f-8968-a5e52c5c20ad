# 核心领域服务总览 (Core Domain Services Overview)

## 1. 设计原则的演进

在项目初期，我们将数据处理的各个阶段（如质量检查、标准化、清单构建）建模为了**聚合 (Aggregate)**。这种方法的初衷是封装每个阶段的状态和业务规则，确保数据的一致性。

然而，随着我们对业务需求的深入理解和对整体架构的迭代，我们认识到项目的核心是一个**线性的、可预测的数据处理管道 (Data Pipeline)**。每个处理步骤接收明确的输入，执行确定的转换逻辑，然后产生输出供下一步使用。这些步骤本身并不维护持久化的状态，也不需要复杂的生命周期管理。

因此，为了使设计更贴近实际实现、更精简、更易于理解，我们决定将这些概念从“聚合”**重构为“无状态的领域服务 (Stateless Domain Service)”**。

## 2. 领域服务的核心职责

在本模块中，每个“核心领域服务”都代表了数据处理管道中的一个关键业务步骤。它们遵循以下设计原则：

- **无状态 (Stateless)**: 服务本身不存储任何与单次计算任务相关的状态。所有必要的数据都通过方法参数传入，所有结果都通过返回值传出。这使得服务天然就是可重用的和线程安全的。
- **职责单一 (Single Responsibility)**: 每个服务都聚焦于一个明确的业务转换任务（例如，只负责数据质量检查，或只负责清单构建）。
- **依赖倒置 (Dependency Inversion)**: 服务的接口定义在领域层，而其具体实现则位于基础设施层，遵循依赖倒置原则。
- **接口明确 (Explicit Interfaces)**: 服务的输入和输出都使用领域层定义的值对象（Value Objects），确保了类型安全和业务含义的清晰。

## 3. 核心服务列表

以下是数据处理管道中定义的核心领域服务：

- **质量检查服务 (QualityCheckerService)**: 负责对原始数据进行质量校验。
- **数据标准化服务 (DataStandardizationService)**: 将异构的原始数据转换为统一的、标准化的领域模型。
- **数据增强服务 (DataEnhancementService)**: 通过空间降尺度和时间降尺度，将省级年度数据增强为地市级月度数据。
- **清单构造服务 (InventoryConstructionService)**: 基于标准化和约束数据，执行核心的能源消费和碳排放清单计算。
- **数据融合服务 (DataFusionService)**: （针对预测场景）融合历史数据和预测数据。
- **模型构建服务 (ModelBuilderService)**: （针对预测场景）构建预测模型。
- **预测分析服务 (PredictionAnalyzerService)**: （针对预测场景）执行预测分析。
- **结果输出服务 (ResultOutputService)**: 负责将最终的计算结果（清单）持久化为文件或写入数据库。
