# 3.3.7 数据融合器聚合 (Data Fuser Aggregate)

## 1. 概述

`数据融合器 (DataFuser)` 是负责确保预测数据在不同层级间保持一致性的流程步骤聚合。例如，它需要确保所有“地区分行业”的预测值之和，与独立的“地区总览”预测值相符。这是一个无状态的聚合，其核心职责是根据预设的业务规则和融合策略，对预测结果进行调整。

## 2. 聚合根 (Aggregate Root)

- **类型**: 流程步骤聚合（Stateless Process Aggregate）
- **生命周期**: 短暂，仅在处理 `FuseDataCommand` 命令期间存在。

### 2.1 职责 (Responsibilities)

- 响应 `FuseDataCommand` 命令。
- 加载 `PredictionAnalyzed` 事件中指定的所有 `PredictionResult` 实体。
- 根据 `targetDimension` 对 `PredictionResult` 进行分组（例如，分为“总览”组和“分项”组）。
- 应用一个 `FusionStrategy`（在基础设施层实现，如“自下而上求和”或“自上而下调整”），以满足 `BR-FUS-001` 定义的一致性约束。
- 更新 `PredictionResult` 实体的预测值，并将它们的状态更新为 `Fused`。
- 保存所有更新后的 `PredictionResult` 实体。
- 发布 `DataFused` 事件。

## 3. 关联的实体与值对象

- **`PredictionResult` (实体)**:
  - **职责**: 封装一次预测分析的结果。`DataFuser` 加载这些实体，调整其 `predictedValues`，并更新其 `status`。
- **`FusionStrategy` (策略模式)**:
  - **职责**: 封装具体的数据融合算法。这是一个在基础设施层实现的策略接口，允许灵活替换融合逻辑（例如，是强制让总览等于分项之和，还是按比例调整分项来匹配总览）。
- **`ConsistencyConstraint` (值对象)**:
  - **职责**: 定义数据必须满足的数学或逻辑关系。例如，`Sum(SubSectors) = Total`。

## 4. 命令 (Commands)

- **`FuseDataCommand`**:
  - **意图**: 指示系统对一组相关的预测结果执行数据融合。
  - **参数**:
    - `jobId`: 关联的核算任务ID。
    - `resultIds`: 需要进行融合的 `PredictionResult` 实体ID列表。

## 5. 领域事件 (Domain Events)

- **`DataFused`**:
  - **意图**: 宣告相关的预测结果集已经完成融合，数据在层级间已保持一致。
  - **参数**:
    - `jobId`: 关联的核算任务ID。
    - `fusedResultIds`: 经过融合处理的 `PredictionResult` 实体ID列表。
  - **后续动作**: `CalculationJob` 监听到此事件后，会触发最后一个流程聚合 `结果输出器 (ResultOutputter)` 开始工作。

## 6. 关联的业务规则

| 规则ID | 规则描述 |
| :--- | :--- |
| BR-FUS-001 | 数据融合一致性约束 |
| BR-FUS-002 | 数据融合策略选择 |

---
*`DataFuser` 体现了领域知识中对数据整体性、一致性的要求。它将复杂的调整逻辑封装起来，确保了交付给下游的最终数据是可靠和自洽的。*
