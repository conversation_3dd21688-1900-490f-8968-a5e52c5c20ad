# 建模预测服务 (ModelingPredictionService)

## 1. 角色

**建模预测服务** 是一个**无状态的领域服务**，它在数据处理管道中扮演着"预测建模和未来数据生成"的关键角色。

- **核心职责**: 接收能源转换后的地市级月度能源消费数据（标准煤当量），采用statsmodels的ARDL模型为各地区的各行业建立电力-能源消费总量预测模型，并基于电力数据的最新数据将能源消费总量预测到对应的时间点。
- **定位**: 作为数据处理管道中的关键环节，位于能源转换之后、清单构建之前，负责将历史数据转化为未来预测数据。
- **交互方式**: 由应用服务 (`CalculationJobService`) 在能源转换完成后调用，接收转换后的能源数据，返回预测模型和预测结果。

## 2. 项目中的位置和上下文

### 2.1 在数据处理管道中的位置

```mermaid
graph LR
    A[原始数据获取] --> B[数据标准化]
    B --> C[数据增强]
    C --> D[能源转换]
    D --> E[建模预测]
    E --> F[清单构建]
    F --> G[结果输出]
```

**具体执行顺序**:

1. **步骤1**: `_step_raw_data_fetching()` - 获取原始数据
2. **步骤2**: `_step_data_standardization()` - 数据标准化
3. **步骤3**: `_step_data_enhancement()` - 数据增强
4. **步骤4**: `_step_energy_conversion()` - 能源转换
5. **步骤5**: `_step_modeling_prediction()` - **建模预测** ← 当前服务位置
6. **步骤6**: `_step_generate_data()` - 生成数据
7. **步骤7**: `_step_completion()` - 完成任务

### 2.2 在代码架构中的位置

**文件路径**: `ecam_calculator/domain/service/modeling_prediction_service.py`

**依赖关系**:

```python
# 应用层调用
CalculationJobService._step_modeling_prediction()
    ↓
# 领域服务接口
ModelingPredictionService.build_and_predict()
    ↓
# 具体实现
ModelingPredictionServiceImpl.build_and_predict()
```

**依赖注入位置**: `ecam_calculator/main.py` 的 `create_job_service()` 函数

### 2.3 在计算上下文中的位置

**CalculationContext 数据结构**:

```python
@dataclass
class CalculationContext:
    start_year: int
    end_year: int
    province: Region
    cities: List[Region]
    job_id: str
    raw_data: Dict[str, Any]                    # 原始数据
    standardized_data: Dict[str, Any]           # 标准化数据
    enhanced_data: Dict[str, Any]                # 增强数据
    converted_data: Dict[str, Any]              # 转换数据 ← 输入来源
    prediction_results: Dict[str, Any]          # 预测结果 ← 输出目标
    constraints: List[Any]
    city_matrices: Dict[str, CityEnergyMatrix]
    energy_inventory: Optional[EnergyConsumptionInventory] = None
    carbon_inventory: Optional[CarbonEmissionInventory] = None
```

## 3. 服务实现

```python
# 位置: ecam_calculator/domain/service/modeling_prediction_service.py

class ModelingPredictionService:
    """
    建模预测服务：负责ARDL模型构建和基于电力数据的预测。
    """
    
    def __init__(self, config_reader=None):
        self.config_reader = config_reader or get_config_reader()
        self.logger = logging.getLogger(__name__)
        self.logger.info("建模预测服务初始化完成")
    
    def build_and_predict(self, converted_data: Dict[str, pd.DataFrame], 
                        electricity_data: pd.DataFrame,
                        calculation_context: CalculationContext) -> Dict[str, Any]:
        """
        对转换后的能源数据进行建模预测处理。

        Args:
            converted_data (Dict[str, pd.DataFrame]): 转换后的能源数据字典
            electricity_data (pd.DataFrame): 电力消费数据，包含历史数据和最新数据
            calculation_context (CalculationContext): 计算上下文

        Returns:
            Dict[str, Any]: 预测结果字典
        """
        # 1. 构建ARDL模型
        ardl_models = self.build_ardl_models(
            converted_data.get('converted_temporal_energy', pd.DataFrame()),
            electricity_data
        )
        
        # 2. 基于电力数据进行预测
        prediction_results = self.predict_with_latest_electricity(
            ardl_models, electricity_data
        )
        
        return {
            'ardl_models': ardl_models,
            'prediction_results': prediction_results
        }
```

## 4. 传入参数详解

### 4.1 在CalculationJobService中的调用方式

**调用位置**: `ecam_calculator/application/calculation_job_service.py`

```python
def _step_modeling_prediction(self, job: CalculationJob, context: CalculationContext):
    """步骤5: 建模预测 - ARDL模型构建和基于电力数据的预测"""
    self.logger.info(f"Job [{job.id}] - 开始执行建模预测...")
    
    try:
        # 确保建模预测服务已初始化
        if not self.modeling_prediction_service:
            self.modeling_prediction_service = ModelingPredictionService()
        
        # 获取转换后的能源数据和电力数据
        converted_spatial_df = context.converted_data.get('converted_spatial_energy', pd.DataFrame())
        converted_temporal_df = context.converted_data.get('converted_temporal_energy', pd.DataFrame())
        electricity_df = context.standardized_data.get('electricity', pd.DataFrame())
        
        # 执行建模预测
        prediction_results = self.modeling_prediction_service.build_and_predict(
            converted_data={
                'converted_spatial_energy': converted_spatial_df,
                'converted_temporal_energy': converted_temporal_df
            },
            electricity_data=electricity_df,
            calculation_context=context
        )
        
        # 将预测结果存储到上下文中
        context.prediction_results = prediction_results
        
        # 记录预测结果统计
        total_predictions = len(prediction_results.get('prediction_results', []))
        self.logger.info(f"Job [{job.id}] - 建模预测完成，生成 {total_predictions} 条预测结果")
        
    except Exception as e:
        self.logger.error(f"Job [{job.id}] - 建模预测失败: {e}")
        raise
```

### 4.2 传入参数的具体内容

**主要参数**: `context.converted_data` (Dict[str, pd.DataFrame])

**具体数据源**:

```python
converted_data = {
    # 能源转换后的数据
    'converted_spatial_energy': pd.DataFrame,    # 空间降尺度转换后的地市能源数据
    'converted_temporal_energy': pd.DataFrame,   # 时间降尺度转换后的地市月度能源数据
}

# 电力消费数据
electricity_data: pd.DataFrame = {
    'year': int,                    # 年份
    'month': int,                   # 月份（月度数据）
    'standard_area': str,           # 标准化地市
    'standard_industry': str,       # 标准化行业
    'value': float,                 # 电力消费量
    'unit': str,                    # 单位
    'data_source': str              # 数据来源
}
```

**计算上下文参数**: `context` (CalculationContext)

```python
context = CalculationContext(
    start_year=2020,                    # 计算起始年份
    end_year=2020,                      # 计算结束年份
    province=Region(name="山西", level="province"),  # 目标省份
    cities=[Region(name="太原", level="city"), ...],  # 目标城市列表
    job_id="uuid-string",               # 任务ID
    # ... 其他字段
)
```

### 4.3 建模预测服务的依赖注入

**在main.py中的配置**:

```python
def create_job_service(db_config: Dict[str, Any]) -> CalculationJobService:
    """简单直接的依赖注入 - 创建计算任务服务"""
    
    # 创建基础设施服务
    job_repository = JobRepositoryImpl(db_config)
    
    # 创建领域服务
    quality_check_service = QualityCheckService()
    data_standardization_service = DataStandardizationService()
    constraint_calculation_service = ConstraintCalculationService()
    inventory_construction_service = InventoryConstructionService()
    result_output_service = ResultOutputService()
    energy_conversion_service = EnergyConversionService()
    data_enhancement_service = DataEnhancementService()
    modeling_prediction_service = ModelingPredictionService()  # 新增建模预测服务
    
    # 创建应用服务
    return CalculationJobService(
        job_repository=job_repository,
        quality_check_service=quality_check_service,
        data_standardization_service=data_standardization_service,
        constraint_calculation_service=constraint_calculation_service,
        inventory_construction_service=inventory_construction_service,
        result_output_service=result_output_service,
        energy_conversion_service=energy_conversion_service,
        data_enhancement_service=data_enhancement_service,
        modeling_prediction_service=modeling_prediction_service  # 注入建模预测服务
    )
```

### 4.4 配置文件中的参数

**配置文件位置**: `ecam_calculator/config/parameters.yaml`

```yaml
# 建模预测配置
modeling_prediction:
  ardl_model:
    enabled: true
    auto_tuning: true                    # 自动调优开关
    max_lags: 3                          # 最大滞后期数
    max_order: 2                         # 最大外生变量阶数
    trend: "c"                           # 趋势项类型
    validation_method: "time_series_split" # 验证方法
    optimization_criteria: "aic"          # 优化准则
    
  prediction:
    enabled: true
    forecast_horizon: 12                 # 预测期数（月）
    confidence_level: 0.95               # 置信水平
    use_latest_electricity: true         # 使用最新电力数据
    
  model_selection:
    min_observations: 24                 # 最小观测值数量
    max_observations: 120                # 最大观测值数量
    cross_validation_folds: 5            # 交叉验证折数
    
# 地区和行业配置
prediction_targets:
  regions: ["太原", "大同", "阳泉", "长治", "晋城", "朔州", "晋中", "运城", "忻州", "临汾", "吕梁"]
  industries: ["制造业", "电力热力燃气及水生产和供应业", "建筑业", "交通运输仓储和邮政业"]
```

## 5. 核心业务逻辑

### 5.1 ARDL模型构建 (ARDL Model Building)

**业务目标**: 为各地区的各行业建立电力-能源消费总量（标准煤当量）的ARDL模型，实现自动调优。

**处理流程**:

1. **数据准备**: 准备地市级月度能源消费数据（标准煤当量）和电力消费数据
2. **模型选择**: 使用statsmodels的ARDL模型，支持自动阶数选择
3. **参数调优**: 通过AIC/BIC准则进行自动调优
4. **模型存储**: 将训练好的模型按地区-行业分组存储

**技术实现**:

```python
from statsmodels.tsa.ardl import ARDL
from statsmodels.tsa.ardl import ardl_select_order

def build_ardl_models(self, energy_data: pd.DataFrame, 
                     electricity_data: pd.DataFrame) -> Dict[str, Any]:
    """
    为各地区的各行业建立ARDL模型
    """
    self.logger.info("开始构建ARDL模型")
    
    # 1. 数据预处理和分组
    grouped_data = self._prepare_modeling_data(energy_data, electricity_data)
    
    ardl_models = {}
    
    # 2. 为每个地区-行业组合构建模型
    for (area, industry), group_data in grouped_data.items():
        self.logger.info(f"为 {area}-{industry} 构建ARDL模型")
        
        try:
            # 3. 自动阶数选择
            optimal_order = self._select_optimal_order(group_data)
            
            # 4. 模型训练
            model = self._train_ardl_model(group_data, optimal_order)
            
            # 5. 存储模型
            ardl_models[f"{area}_{industry}"] = {
                'model': model,
                'optimal_order': optimal_order,
                'area': area,
                'industry': industry,
                'r_squared': model.rsquared,
                'aic': model.aic,
                'bic': model.bic
            }
            
        except Exception as e:
            self.logger.error(f"为 {area}-{industry} 构建ARDL模型失败: {e}")
            continue
    
    self.logger.info(f"ARDL模型构建完成，共构建 {len(ardl_models)} 个模型")
    return ardl_models

def _select_optimal_order(self, data: pd.DataFrame) -> Dict[str, int]:
    """
    自动选择ARDL模型的最优阶数
    """
    # 准备数据
    y = data['total_energy_consumption']  # 能源消费总量（标准煤当量）
    x = data[['electricity_consumption']]  # 电力消费量
    
    # 使用statsmodels的自动阶数选择
    try:
        selected_order = ardl_select_order(
            endog=y,
            exog=x,
            maxlag=self.config.get('max_lags', 3),
            maxorder=self.config.get('max_order', 2),
            trend=self.config.get('trend', 'c')
        )
        
        # 提取最优阶数
        optimal_order = {
            'lags': selected_order.bic_lags,
            'order': selected_order.bic_order
        }
        
        return optimal_order
        
    except Exception as e:
        self.logger.warning(f"自动阶数选择失败，使用默认参数: {e}")
        return {'lags': 1, 'order': 1}

def _train_ardl_model(self, data: pd.DataFrame, optimal_order: Dict[str, int]) -> Any:
    """
    训练ARDL模型
    """
    y = data['total_energy_consumption']
    x = data[['electricity_consumption']]
    
    # 创建ARDL模型
    model = ARDL(
        endog=y,
        lags=optimal_order['lags'],
        exog=x,
        order=optimal_order['order'],
        trend=self.config.get('trend', 'c')
    )
    
    # 拟合模型
    fitted_model = model.fit()
    
    return fitted_model
```

**业务规则**:

- **数据要求**: 至少需要12个月的观测数据才能构建模型
- **自动调优**: 使用AIC/BIC准则自动选择最优阶数
- **异常处理**: 对于数据不足或质量较差的地区-行业组合，跳过建模

**输入数据结构**:

```python
# 地市级月度能源消费数据（标准煤当量）
energy_data: pd.DataFrame = {
    'year': int,                    # 年份
    'month': int,                   # 月份
    'standard_area': str,          # 标准化地市
    'standard_industry': str,       # 标准化行业
    'total_energy_consumption': float,  # 能源消费总量（标准煤当量）
    'data_source': str             # 数据来源
}

# 地市级月度电力消费数据
electricity_data: pd.DataFrame = {
    'year': int,                    # 年份
    'month': int,                   # 月份
    'standard_area': str,          # 标准化地市
    'standard_industry': str,       # 标准化行业
    'electricity_consumption': float,  # 电力消费量
    'unit': str,                   # 单位
    'data_source': str             # 数据来源
}
```

**输出数据结构**:

```python
# ARDL模型字典
ardl_models: Dict[str, Any] = {
    'area_industry_key': {
        'model': ARDL_fitted_model,     # 训练好的ARDL模型
        'optimal_order': Dict[str, int], # 最优阶数
        'validation_metrics': Dict[str, float], # 验证指标
        'training_data': pd.DataFrame,  # 训练数据
        'area': str,                    # 地区
        'industry': str                 # 行业
    }
}
```

### 5.2 基于电力数据的预测 (Electricity-based Prediction)

**业务目标**: 使用训练好的ARDL模型和电力数据中的最新数据，将能源消费总量预测到和月度电力对应的时间点。

**处理流程**:

1. **最新数据识别**: 识别电力数据中的最新数据（月度能源消费数据之后的部分）
2. **模型应用**: 使用对应的ARDL模型进行预测
3. **结果整合**: 将预测结果整合为统一格式

**技术实现**:

```python
def predict_with_latest_electricity(self, ardl_models: Dict[str, Any], 
                                  latest_electricity_data: pd.DataFrame) -> pd.DataFrame:
    """
    使用ARDL模型和电力数据的最新数据进行预测
    """
    self.logger.info("开始基于电力数据进行预测")
    
    # 1. 识别最新电力数据
    latest_data = self._identify_latest_electricity_data(latest_electricity_data)
    
    prediction_results = []
    
    # 2. 为每个地区-行业组合进行预测
    for (area, industry), electricity_group in latest_data.groupby(['standard_area', 'standard_industry']):
        model_key = f"{area}_{industry}"
        
        if model_key not in ardl_models:
            self.logger.warning(f"未找到 {area}-{industry} 的ARDL模型，跳过预测")
            continue
        
        try:
            # 3. 使用ARDL模型进行预测
            predictions = self._apply_ardl_prediction(
                ardl_models[model_key], 
                electricity_group
            )
            
            prediction_results.append(predictions)
            
        except Exception as e:
            self.logger.error(f"为 {area}-{industry} 进行预测失败: {e}")
            continue
    
    # 4. 整合所有预测结果
    if prediction_results:
        final_predictions = pd.concat(prediction_results, ignore_index=True)
        self.logger.info(f"基于电力数据的预测完成，生成 {len(final_predictions)} 条预测结果")
        return final_predictions
    else:
        self.logger.warning("未生成任何预测结果")
        return pd.DataFrame()

def _apply_ardl_prediction(self, model_info: Dict[str, Any], 
                          electricity_data: pd.DataFrame) -> pd.DataFrame:
    """
    使用ARDL模型进行预测
    """
    model = model_info['model']
    area = model_info['area']
    industry = model_info['industry']
    
    # 准备预测用的外生变量（电力消费量）
    exog_oos = electricity_data[['electricity_consumption']].values
    
    # 进行预测
    predictions = model.predict(
        start=len(model.fittedvalues),
        end=len(model.fittedvalues) + len(electricity_data) - 1,
        exog_oos=exog_oos
    )
    
    # 构建预测结果DataFrame
    prediction_df = pd.DataFrame({
        'year': electricity_data['year'],
        'month': electricity_data['month'],
        'standard_area': area,
        'standard_industry': industry,
        'predicted_energy_consumption': predictions,
        'electricity_consumption': electricity_data['electricity_consumption'],
        'prediction_method': 'ardl',
        'model_id': f"{area}_{industry}"
    })
    
    return prediction_df
```

**业务规则**:

- **数据时效性**: 使用电力数据中的最新数据（月度能源消费数据之后的部分）
- **异常处理**: 对于无法预测的地区-行业组合，跳过预测

**输入数据结构**:

```python
# ARDL模型字典（来自模型构建阶段）
ardl_models: Dict[str, Any] = {
    'area_industry_key': {
        'model': ARDL_fitted_model,
        'optimal_order': Dict[str, int],
        'validation_metrics': Dict[str, float],
        'training_data': pd.DataFrame,
        'area': str,
        'industry': str
    }
}

# 电力数据中的最新数据
latest_electricity_data: pd.DataFrame = {
    'year': int,                    # 年份（2021年及以后）
    'month': int,                   # 月份
    'standard_area': str,          # 标准化地市
    'standard_industry': str,       # 标准化行业
    'electricity_consumption': float,  # 电力消费量
    'unit': str,                   # 单位
    'data_source': str             # 数据来源
}
```

**输出数据结构**:

```python
# 预测结果数据
prediction_results: pd.DataFrame = {
    'year': int,                           # 预测年份
    'month': int,                          # 预测月份
    'standard_area': str,                  # 标准化地市
    'standard_industry': str,              # 标准化行业
    'predicted_energy_consumption': float, # 预测的能源消费总量（标准煤当量）
    'electricity_consumption': float,      # 对应的电力消费量
    'prediction_method': str,              # 预测方法 ('ardl')
    'model_id': str                        # 模型ID
}
```

## 6. 错误处理策略

**模型构建失败处理**:

- **数据不足**: 跳过数据不足的地区-行业组合
- **模型收敛失败**: 使用简化模型或默认参数

**预测失败处理**:

- **模型缺失**: 使用历史平均值或趋势外推
- **数据异常**: 使用异常检测和修正

## 7. 配置参数

**配置文件位置**: `ecam_calculator/config/parameters.yaml`

```yaml
# 建模预测配置
modeling_prediction:
  ardl_model:
    enabled: true
    auto_tuning: true                    # 自动调优开关
    max_lags: 3                         # 最大滞后期数
    max_order: 2                        # 最大外生变量阶数
    trend: "c"                          # 趋势项类型
    
  prediction:
    enabled: true
    forecast_horizon: 12                # 预测期数（月）
    use_latest_electricity: true        # 使用最新电力数据
    
  model_selection:
    min_observations: 12                # 最小观测值数量
```

## 8. 输入与输出

### 8.1 输入数据

- **转换后能源数据**: 经过能源转换的地市级月度能源消费数据（标准煤当量）
- **电力消费数据**: 地市级月度电力消费数据，包含历史数据和最新数据
- **配置参数**: ARDL模型参数和预测配置

### 8.2 输出数据

- **ARDL模型**: 各地区各行业的训练好的ARDL模型
- **预测结果**: 基于电力数据最新数据的能源消费预测
- **模型指标**: 模型性能和质量指标

**注意**: 建模预测服务的输出将作为清单构建服务的输入，为后续的碳排放计算提供预测数据。

### 8.3 数据质量指标

- **模型性能**: R²、AIC、BIC等统计指标
- **预测精度**: MSE、MAE等预测误差指标

## 9. 业务价值

### 9.1 预测能力

- **时间维度**: 将历史数据转化为未来预测数据
- **空间维度**: 为各地市提供个性化的预测模型
- **行业维度**: 考虑不同行业的能源消费特征

### 9.2 模型质量

- **自动调优**: 通过统计方法自动选择最优模型参数
- **性能监控**: 提供详细的模型性能指标

### 9.3 业务支持

- **政策制定**: 为政策制定提供未来能源消费预测
- **碳排放计算**: 支持基于预测数据的碳排放计算
- **能源规划**: 为能源规划和资源配置提供数据支撑

## 10. 技术说明

### 10.1 关于模型验证

对于时间序列数据，特别是数据点较少的情况，我们采用简单的验证方法：

- **样本外预测**: 使用最后几个时间点的数据进行预测验证
- **模型诊断**: 检查残差的自相关性和正态性
- **统计指标**: 使用R²、AIC、BIC等指标评估模型质量

这样可以避免过度复杂的验证过程，更适合实际业务场景。

---

*本服务设计遵循统计建模最佳实践，支持自动调优，确保预测结果的可靠性。*
