# 模型构建服务 (ModelBuilderService)

## 1. 角色

**模型构建服务** 是一个**无状态的领域服务**，负责为系统的预测功能构建和训练机器学习或统计模型。

- **核心职责**: 接收历史清单数据 (`EnergyConsumptionInventory`)，并根据预设算法（如时间序列分析、回归模型等）构建预测模型。
- **定位**: 封装了所有关于模型训练、参数调优和验证的复杂逻辑。
- **当前状态**: **（待设计与实现）** - 此服务属于系统的预测模块，在当前的核心核算功能中尚未实现。

## 2. 接口定义 (初步设想)

```python
# 建议位置: ecam_calculator/domain/service/model_builder_service.py

class ModelBuilderService(ABC):
    """
    领域服务接口：负责构建预测模型。
    """

    @abstractmethod
    def build_model(
        self, 
        historical_inventory: EnergyConsumptionInventory
    ) -> PredictionModel:
        """
        根据历史清单数据构建预测模型。

        Args:
            historical_inventory (EnergyConsumptionInventory): 用于训练模型的历史能源消费清单。

        Returns:
            PredictionModel: 一个封装了训练好的模型及其元数据的值对象。
        """
        raise NotImplementedError
```
