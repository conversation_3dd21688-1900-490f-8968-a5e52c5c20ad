# 清单构造服务 (InventoryConstructionService)

## 1. 角色

**清单构造服务** 是一个**无状态的领域服务**，是执行能源消费与碳排放清单核算的核心计算引擎。

- **核心职责**: 接收 `StandardizedData` 和 `ConstraintData` 作为输入，应用一系列核算方法学和公式，最终计算出《能源消费清单》和《碳排放清单》。
- **定位**: 作为一个无状态的服务，它封装了所有关于清单核算的复杂领域知识和计算逻辑。它是数据处理管道中将标准化数据转化为最终结果的决定性步骤。
- **交互方式**: 由应用服务 (`CalculationJobService`) 在数据标准化和约束计算都完成后直接调用。

## 2. 接口定义 (服务契约)

```python
# 建议位置: ecam_calculator/domain/service/inventory_construction_service.py

class InventoryConstructionService(ABC):
    """
    领域服务接口：负责构建能源消费清单和碳排放清单。
    """

    @abstractmethod
    def construct_inventories(
        self, 
        standardized_data: List[StandardizedData], 
        constraint_data: List[ConstraintData]
    ) -> Tuple[EnergyConsumptionInventory, CarbonEmissionInventory]:
        """
        根据标准化数据和约束数据，执行清单核算。

        Args:
            standardized_data (List[StandardizedData]): 标准化后的数据。
            constraint_data (List[ConstraintData]): 约束数据，用于数据修正和平衡。

        Returns:
            Tuple[EnergyConsumptionInventory, CarbonEmissionInventory]: 
                返回一个包含能源消费清单和碳排放清单的元组。
        """
        raise NotImplementedError
```

## 3. 核心业务逻辑 (待实现)

该服务将实现 IPCC 清单核算方法学中的核心步骤，大致流程如下：

1.  **数据筛选与聚合**: 从 `standardized_data` 列表中筛选出特定年份、特定地区的能源活动数据。
2.  **约束应用**: 使用 `constraint_data` 对能源活动数据进行校准或修正，确保宏观数据和微观数据的平衡。
3.  **能源消费清单计算**:
    - 基于校准后的能源活动数据，按行业、按能源品种进行汇总，形成结构化的能源消费清单 (`EnergyConsumptionInventory`)。
4.  **碳排放清单计算**:
    - 将能源消费清单中的各类能源消耗量，乘以对应的排放因子（从 `EmissionFactor` 值对象中获取）。
    - 将计算出的排放量按行业、按能源品种进行汇总，形成结构化的碳排放清单 (`CarbonEmissionInventory`)。

## 4. 输入与输出

- **输入**:
    - `标准化数据 (StandardizedData VO)` 列表
    - `约束数据 (ConstraintData VO)` 列表
- **输出**:
    - `能源消费清单 (EnergyConsumptionInventory VO)`
    - `碳排放清单 (CarbonEmissionInventory VO)`
