# 质量检查服务 (QualityCheckerService)

## 1. 角色

**质量检查服务** 是一个**无状态的领域服务**，它在数据处理管道中扮演着“看门人”的角色。

- **核心职责**: 对从外部数据源加载的原始数据 (`RawData`) 执行一系列预定义的业务规则和质量标准校验。
- **定位**: 它不是一个实体或聚合，因为它不维护自己的状态。它是一系列函数的集合，封装了数据质量校验的核心领域知识。
- **交互方式**: 应用服务 (`CalculationJobService`) 在获取原始数据后，会立即调用此服务。服务处理完成后，会返回一个包含校验结果的值对象。

## 2. 接口定义 (服务契约)

```python
# 位置: ecam_calculator/domain/service/quality_check_service.py

class QualityCheckService(ABC):
    """
    领域服务接口：负责对原始数据进行质量检查。
    """
    
    @abstractmethod
    def check_quality(self, raw_data: List[RawData]) -> DataQualityReport:
        """
        对一组原始数据执行质量检查。

        Args:
            raw_data (List[RawData]): 从数据源获取的原始数据列表。

        Returns:
            DataQualityReport: 包含检查结果（如错误、警告、统计数据）的报告。
        """
        raise NotImplementedError
```

## 3. 输入 (Input)

- **`原始数据 (RawData VO)`**: 一个或多个 `RawData` 值对象的列表，每个对象都包含了从特定数据源获取的表格数据 (`TabularData`)。

## 4. 输出 (Output)

- **`数据质量报告 (DataQualityReport VO)`**: 这是一个关键的值对象，它封装了质量检查的全部结果。
    - **is_valid (bool)**: 一个布尔标志，清晰地表明数据是否通过了所有关键的质量检查。
    - **error_messages (List[str])**: 如果 `is_valid` 为 `False`，这里会包含具体的错误信息列表。
    - **warnings (List[str])**: 即使数据有效，也可能存在一些需要注意的警告信息。
    - **statistics (Dict[str, Any])**: 可能包含关于数据的描述性统计信息，如行数、空值比例等。

## 5. 核心业务逻辑 (待实现)

质量检查服务将包含一系列可配置的检查规则，例如：

- **完整性检查**: 检查关键字段（如年份、地区、指标名称）是否存在空值。
- **一致性检查**: 检查同一指标的单位是否一致，或者时间序列是否连贯。
- **范围检查**: 检查数值是否在预期的合理范围内（例如，GDP增长率不会是负数）。
- **格式检查**: 确保日期、数字等字段的格式符合规定。

这些规则的具体实现将位于基础设施层的 `QualityCheckServiceImpl` 中。
