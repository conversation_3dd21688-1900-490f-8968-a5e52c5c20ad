# 1. 领域事件概述 (Domain Event Overview)

## 1. 设计原则

领域事件（Domain Event）是领域模型中用于广播“**业务流程中已发生的、有意义的事实**”的核心机制。它们是连接不同聚合、驱动流程前进的“胶水”。

在我们的设计中，遵循以下原则：
- **命名**: 使用过去时态（如 `ModelBuilt`），清晰地描述已发生的事实。
- **不可变性**: 事件一旦发布，就不能被修改。
- **数据载体**: 事件携带足够的数据（如ID或值对象），以便订阅者可以独立做出响应，而无需再回头查询发布者。
- **解耦**: 发布者不关心谁在监听，订阅者不关心谁是发布者，实现了聚合之间的松耦合。

## 2. 核心领域事件目录 (Event Catalog)

下表列出了本领域模型中的所有核心事件，它们共同驱动了 `CalculationJob` 的状态流转。

| 事件名称 | 触发时机 | 发布者 | 主要订阅者 | 核心参数 (Payload) |
| :--- | :--- | :--- | :--- | :--- |
| `CalculationJobStarted` | 核算任务被创建时 | `CalculationJob` | （应用层、日志） | `jobId`, `jobName` |
| `DataQualityChecked` | 数据质量检查完成 | `QualityChecker` | `CalculationJob` | `jobId`, `QualityCheckedData` VO |
| `DataStandardized` | 数据标准化完成 | `DataStandardizer` | `CalculationJob` | `jobId`, `StandardizedData` VO |
| `InventoryConstructed` | 清单构造完成 | `InventoryConstructor` | `CalculationJob` | `jobId`, Inventory VOs |
| `ModelBuilt` | 预测模型构建完成 | `ModelBuilder` | `CalculationJob` | `jobId` |
| `PredictionAnalyzed` | 预测分析完成 | `PredictionAnalyzer` | `CalculationJob` | `jobId`, `predictionResultId` |
| `DataFused` | 数据融合完成 | `DataFuser` | `CalculationJob` | `jobId`, `predictionResultId` |
| `ResultOutputted` | 最终结果输出完成 | `ResultOutputter` | `CalculationJob` | `jobId` |
| `CalculationJobCompleted` | 任务成功结束 | `CalculationJob` | （应用层、通知） | `jobId` |
| `CalculationJobFailed` | 任务中途失败 | `CalculationJob` | （应用层、通知） | `jobId`, `reason` |

---
*这个事件目录是开发阶段的重要参考，它清晰地定义了系统中各个部分的交互契约。*
