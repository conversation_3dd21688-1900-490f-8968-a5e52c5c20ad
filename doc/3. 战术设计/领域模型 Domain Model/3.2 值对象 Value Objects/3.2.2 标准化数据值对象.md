# 标准化数据值对象重构设计

## 1. 重构背景

为了解决现有代码中的技术债务，需要对标准化数据值对象进行彻底重构。重构的核心目标是：

- **消除硬编码**: 所有业务规则都从配置文件中读取
- **重构数据结构**: 按业务逻辑重新设计数据模型
- **简化代码逻辑**: 去除复杂的条件判断和补丁代码
- **建立清晰的数据流**: 从原始数据到最终清单的完整链路

## 2. 新的值对象体系设计

### 2.1 原始数据值对象 (RawData VO)

```python
@dataclass(frozen=True)
class RawData:
    """原始数据值对象 - 直接从数据库读取"""
    table_name: str                    # 数据库表名
    data: pd.DataFrame                 # 原始数据
    metadata: Dict[str, Any]          # 表结构元数据
    source_name: str                   # 数据源名称
```

### 2.2 标准化能源数据值对象 (StandardizedEnergyData VO)

```python
@dataclass(frozen=True)
class StandardizedEnergyData:
    """标准化能源数据值对象 - 能源消费专用"""
    year: int,                         # 年份
    province: str,                     # 省份
    city: str,                         # 城市
    source_table: str,                 # 来源表名
    original_industry: str,            # 原始行业名称
    standard_industry: str,            # 标准化后的行业名称
    energy_type: str,                  # 能源品种
    original_value: float,             # 原始数值
    original_unit: str,                # 原始单位
    standard_value: float,             # 标准值（折标后）
    standard_unit: str,                # 标准单位（万吨标准煤）
    conversion_factor: float,          # 折标系数
    conversion_method: str             # 折标方法
```

**适用数据源**:
- `ecam_in_y_pro_ind_ene_off` (省级能源消费)
- `ecam_in_y_pro_ind_ene2_off` (地市工业能源)
- `ecam_in_m_pro_ind_ele_off` (地市用电量)

### 2.3 标准化工业产品数据值对象 (StandardizedProductData VO)

```python
@dataclass(frozen=True)
class StandardizedProductData:
    """标准化工业产品数据值对象 - 工业产品专用"""
    year: int,                         # 年份
    province: str,                     # 省份
    city: str,                         # 城市
    source_table: str,                 # 来源表名
    original_product: str,             # 原始产品名称
    standard_industry: str,            # 标准化后的行业名称
    original_value: float,             # 原始产量
    original_unit: str,                # 原始单位
    emission_category: str             # 排放类别
```

**适用数据源**:
- `fct_y_prd_output` (工业产品产量)

### 2.4 标准化经济指标数据值对象 (StandardizedEconomicData VO)

```python
@dataclass(frozen=True)
class StandardizedEconomicData:
    """标准化经济指标数据值对象 - GDP和能耗强度专用"""
    year: int,                         # 年份
    province: str,                     # 省份
    city: str,                         # 城市
    source_table: str,                 # 来源表名
    indicator: str,                    # 指标名称
    indicator_category: str,           # 指标类别（GDP/能耗强度）
    original_value: float,             # 原始数值
    original_unit: str,                # 原始单位
    business_usage: str                # 业务用途
```

**适用数据源**:
- `fct_y_gdp` (GDP数据)
- `fct_y_all_ene_intsty` (能耗强度数据)

### 2.5 中间结果值对象

#### 2.5.1 城市能源矩阵值对象 (CityEnergyMatrix VO)

```python
@dataclass(frozen=True)
class CityEnergyMatrix:
    """城市能源矩阵值对象 - 数据下沉后的中间结果"""
    year: int,                         # 年份
    matrix_data: pd.DataFrame,         # 城市-行业-能源品种矩阵
    allocation_method: str,            # 分配方法（如用电比例）
    source_data: Dict[str, Any],      # 源数据信息
    validation_status: str             # 验证状态
```

**业务含义**: 省级数据下沉到地市后的中间结果，用于后续的清单构建
**数据来源**: 标准化能源数据 + 地市用电量权重
**业务用途**: 为IPF平衡提供基础矩阵，为清单构建提供数据支撑
**关键特征**: 
- 包含城市-行业-能源品种的三维矩阵
- 记录数据下沉的方法和权重
- 支持数据质量验证和一致性检查

#### 2.5.2 约束条件值对象 (ConstraintData VO)

```python
@dataclass(frozen=True)
class ConstraintData:
    """约束条件值对象 - 用于IPF平衡的约束数据"""
    year: int,                         # 年份
    row_constraints: pd.Series,        # 行约束（省级行业总量）
    column_constraints: pd.Series,     # 列约束（城市总能耗）
    constraint_source: str,            # 约束来源
    calculation_method: str            # 计算方法
```

**业务含义**: 用于IPF平衡算法的约束条件，确保数据平衡结果符合业务约束
**数据来源**: GDP + 能耗强度计算得出
**业务用途**: 
- 为IPF算法提供行约束和列约束
- 确保数据平衡结果符合业务规则
- 支持约束条件的动态调整和验证

#### 2.5.3 数据质量报告值对象 (DataQualityReport VO)

```python
@dataclass(frozen=True)
class DataQualityReport:
    """数据质量报告值对象 - 记录数据处理过程中的质量信息"""
    processing_stage: str,             # 处理阶段
    completeness_score: float,         # 完整性得分
    consistency_score: float,          # 一致性得分
    accuracy_score: float,             # 准确性得分
    issues_found: List[str],           # 发现的问题
    recommendations: List[str]         # 改进建议
```

**业务含义**: 记录和跟踪数据处理过程中的质量指标
**业务用途**: 
- 监控数据质量变化趋势
- 识别数据质量问题
- 为数据改进提供指导

### 2.6 最终输出值对象

#### 2.6.1 能源消费清单值对象 (EnergyConsumptionInventory VO)

```python
@dataclass(frozen=True)
class EnergyConsumptionInventory:
    """能源消费清单值对象 - 最终输出"""
    year: int,                         # 年份
    inventory_data: pd.DataFrame,      # 包含综合能耗和分品种能耗
    construction_method: str,          # 构建方法
    validation_status: str,            # 验证状态
    data_completeness: float           # 数据完整性指标
```

**业务含义**: 系统最终产出的能源消费清单，包含综合能耗和分品种能耗
**数据内容**: 
- 综合能耗：地区-行业维度的总能耗数据
- 分品种能耗：地区-行业-能源品种的详细数据
- 数据质量指标和验证状态

#### 2.6.2 碳排放清单值对象 (CarbonEmissionInventory VO)

```python
@dataclass(frozen=True)
class CarbonEmissionInventory:
    """碳排放清单值对象 - 最终输出"""
    year: int,                         # 年份
    inventory_data: pd.DataFrame,      # 碳排放数据
    calculation_method: str,           # 计算方法
    emission_factors_source: str,      # 排放因子来源
    validation_status: str,            # 验证状态
    uncertainty_analysis: Dict[str, Any]  # 不确定性分析
```

**业务含义**: 系统最终产出的碳排放清单，包含能源燃烧和工业过程排放
**数据内容**: 
- 能源燃烧排放：基于能源消费量和排放因子
- 工业过程排放：基于产品产量和过程排放因子
- 排放源分类和不确定性分析

## 3. 值对象重构的核心原则

### 3.1 业务逻辑分离
- **能源数据**: 专注于折标计算和能源清单构建
- **产品数据**: 专注于工业过程排放计算
- **经济指标**: 专注于约束计算和验证

### 3.2 类型安全
- **强类型**: 使用类型注解确保编译时类型检查
- **数据验证**: 运行时验证数据结构的正确性
- **错误处理**: 优雅处理配置错误和数据异常

### 3.3 数据溯源
- **来源追踪**: 每个数据点都能追溯到原始来源
- **处理记录**: 记录数据处理的每个步骤
- **质量指标**: 提供数据质量评估指标

## 4. 重构实施计划

### 4.1 第一阶段：删除旧代码
- 删除现有的 `StandardizedData` 类
- 删除所有硬编码的业务逻辑
- 清理相关的测试代码

### 4.2 第二阶段：实现新的值对象
- 实现新的值对象类体系
- 建立类型安全的数据结构
- 定义清晰的数据流转接口

### 4.3 第三阶段：集成测试
- 验证新的数据结构
- 测试数据流转逻辑
- 确保业务逻辑正确性

## 5. 重构的预期收益

### 5.1 技术债务解决
- **消除硬编码**: 所有业务规则都在配置文件中
- **简化代码**: 去除复杂的条件判断和补丁代码
- **类型安全**: 编译时就能发现类型错误
- **职责清晰**: 每个值对象都有明确的单一职责

### 5.2 架构质量提升
- **配置驱动**: 业务规则调整无需修改代码
- **数据溯源**: 每个数据点都能追溯到原始来源
- **易于测试**: 清晰的接口和依赖关系
- **易于维护**: 模块化设计，便于理解和修改

---

*本重构设计基于配置驱动的原则，旨在彻底解决现有代码中的技术债务，建立清晰、可维护的数据标准化架构。*
