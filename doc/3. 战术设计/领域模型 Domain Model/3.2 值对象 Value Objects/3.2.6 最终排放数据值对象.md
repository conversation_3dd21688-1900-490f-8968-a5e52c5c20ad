# 最终排放数据值对象 (FinalEmissionData VO)

## 1. 概述

`FinalEmissionData` 是由 `结果输出器 (ResultOutputter)` 聚合创建的、代表业务流程最终产出的**值对象**。

它是一个**不可变的 (Immutable)** 数据包，封装了单条最终输出的、结构化的排放数据记录。`结果输出器` 会创建一组这样的值对象，然后交由仓储（Repository）进行持久化。

## 2. 属性 (Properties)

| 属性名称 | 类型 | 描述 | 示例 |
| :--- | :--- | :--- | :--- |
| `dimension` | `String` | 该数据点所属的业务维度。 | `"地区: XX省"` |
| `year` | `Integer` | 数据点所属的年份。 | `2024` |
| `month` | `Integer` | 数据点所属的月份。 | `1` |
| `value` | `BigDecimal` | 预测的数值。 | `105.20` |
| `unit` | `String` | 数据的单位。 | `"万吨二氧化碳"` |
| `confidenceIntervalLower` | `BigDecimal` | 置信区间的下限。 | `102.10` |
| `confidenceIntervalUpper` | `BigDecimal` | 置信区间的上限。 | `108.30` |

## 3. 行为 (Behaviors)

作为值对象，`FinalEmissionData` 不包含任何业务行为。

## 4. 生命周期 (Lifecycle)

- **创建 (Creation)**: 由 `结果输出器 (ResultOutputter)` 聚合在处理完 `PredictionResult` 后，在内存中创建。
- **传递 (Passing)**: 作为一组数据被传递给 `FinalEmissionDataRepository` 进行持久化。
- **消亡 (Demise)**: 在被成功持久化后，其在内存中的生命周期即告结束。

---
*`FinalEmissionData` 作为值对象，是业务价值的最终载体，其设计的核心在于数据的稳定、精确和结构化。*
