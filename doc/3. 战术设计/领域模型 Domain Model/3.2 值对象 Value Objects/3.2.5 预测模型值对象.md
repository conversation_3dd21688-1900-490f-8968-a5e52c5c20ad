# 预测模型值对象 (PredictionModel VO)

## 1. 概述

`PredictionModel` 是一个核心的**值对象**，由 `模型构建器 (ModelBuilder)` 聚合创建。它是一个**不可变的 (Immutable)** 数据包，封装了一个已经训练好、经过超参数调优的预测模型的所有信息。

作为值对象，它没有独立的身份标识 (ID)，其“值”由其所有属性共同定义。

## 2. 属性 (Properties)

| 属性名称 | 类型 | 描述 | 示例 |
| :--- | :--- | :--- | :--- |
| `targetDimension` | `String` | 此模型应用的业务维度。 | `"地区: XX省"` |
| `modelType` | `String` | 模型的算法类型。 | `"ARDL"` |
| `serializedModel` | `byte[]` / `String` | 序列化后的模型对象，或其在对象存储中的路径/密钥。 | `[binary data]` 或 `"models/f47ac10b.pkl"` |
| `performanceMetrics` | `Map<String, Double>` | 模型的性能评估指标。 | `{"R²": 0.92, "MAE": 5.8}` |
| `optimalParameters` | `Map<String, Object>` | 超参数调优找到的最佳参数集。 | `{"p": 2, "q": 1, "trend": "c"}` |

## 3. 行为 (Behaviors)

作为值对象，`PredictionModel` 的核心行为是**提供信息**和**被传递**。具体的 `predict()` 预测逻辑，应由一个**领域服务 (`PredictionService`)** 来执行，该服务会接收一个 `PredictionModel` 值对象和一个 `PredictionInput` 值对象作为参数。

这样做可以保持值对象的纯粹性，使其只作为数据载体，而将行为委托给服务。

---
*`PredictionModel` 作为值对象，清晰地封装了建模的结果，同时避免了不必要的实体复杂性。*
