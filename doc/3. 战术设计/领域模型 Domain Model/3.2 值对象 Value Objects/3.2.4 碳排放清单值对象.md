# 碳排放清单值对象 (CarbonEmissionInventory VO)

## 1. 概述

`CarbonEmissionInventory` 是由 `清单构造器 (InventoryConstructor)` 聚合创建的**值对象**。

它是一个**不可变的 (Immutable)** 数据包，代表了一份结构化的、用于后续模型构建的历史碳排放数据清单。

## 2. 属性 (Properties)

| 属性名称 | 类型 | 描述 | 示例 |
| :--- | :--- | :--- | :--- |
| `inventoryType` | `Enum` | 清单类型。 | `CarbonEmission` |
| `inventoryData` | `TabularData` | 结构化的清单数据。 | `(表格数据，列: dimension, year, month, emission_source, value, unit)` |
| `constructedAt` | `Timestamp` | 清单被构建的时间戳。 | `2023-10-27T09:00:00Z` |

*注: `TabularData` 是一个基础值对象。*

## 3. 行为 (Behaviors)

作为值对象，`CarbonEmissionInventory` 不包含任何业务行为。

## 4. 生命周期 (Lifecycle)

- **创建 (Creation)**: 由 `清单构造器 (InventoryConstructor)` 聚合在处理完 `StandardizedData` 后，在内存中创建。
- **传递 (Passing)**: 作为 `InventoryConstructed` 事件的一部分，或通过命令直接传递给 `模型构建器 (ModelBuilder)` 聚合。
- **消亡 (Demise)**: 一旦被 `ModelBuilder` 消费，它的生命周期即告结束。

---
*`CarbonEmissionInventory` 作为值对象，为碳排放模型构建步骤提供了标准、可靠的数据输入。*
