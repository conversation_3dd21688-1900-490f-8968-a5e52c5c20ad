# 值对象总览 (Value Object Overview)

## 1. 设计原则与技术选型

在我们的领域模型中，值对象（Value Object, VO）是描述事物属性、而**非身份**的核心构件。它们是**不可变的 (Immutable)**，其相等性由其所有属性的值共同决定，而不是由某个ID决定。

**技术选型：Pydantic**
为确保数据在处理管道中流转时的健壮性和正确性，我们选择 **`pydantic.BaseModel`** 作为所有值对象的基类。相比标准的 `@dataclass`，`pydantic` 提供了以下关键优势：
- **运行时类型检查与强制转换**：在对象实例化时自动验证数据类型，能从源头上杜绝大量因数据类型错误导致的问题。
- **清晰的错误报告**：当验证失败时，能提供详细的错误信息，极大地简化了调试过程。
- **声明式的验证逻辑**：可以通过装饰器方便地添加自定义的业务规则验证。

我们将流程中所有作为数据包传递的业务对象都建模为基于 `pydantic` 的值对象，这有助于保持数据处理管道的清晰和函数式特性。

## 2. 核心值对象

下表列出了在数据处理流程中起关键作用的核心值对象。

| 值对象 (VO) | 所在阶段 | 职责 |
| :--- | :--- | :--- |
| **`TabularData`** | 数据提取 | 从 `RawDataRepository` 返回的原始、未经验证的二维表格数据。 |
| **`QualityCheckedData`** | 质量检查 | 封装通过了初步验证规则的原始数据，是`QualityChecker`服务的输出。 |
| **`StandardizedData`** | 数据标准化 | **（建议重构方向）** 一个统一的、标准化的数据容器。它取代了多个特定的`Standardized...Data`类，内部通过枚举区分数据类型（能源、产品、经济等），使标准化阶段的输出高度统一。|
| **`CityEnergyMatrix`** | 数据下沉/平衡 | 封装经过省级数据下沉和平衡处理后的城市-行业-能源矩阵。包含了数据和其操作行为。|
| **`ConstraintData`** | 约束计算 | 封装用于数据平衡步骤的行/列约束总和。 |
| **`EnergyConsumptionInventory`**| 清单构建 | 封装结构化的能源消费清单最终结果。 |
| **`CarbonEmissionInventory`** | 清单构建 | 封装结构化的碳排放清单最终结果。 |

## 3. 基础/辅助值对象

这些值对象用于构成核心值对象的属性，或在计算过程中作为参数传递，以增强代码的类型安全和业务表达力。

| 值对象 (VO) | 职责 |
| :--- | :--- |
| **`Region` (区域)** | **（建议引入）** 表示地理区域（省、市），取代原始的 `string` 类型。 |
| **`IndustryCategory` (行业)**| **（建议引入）** 表示标准化的国民经济行业分类，取代原始的 `string` 类型。|
| **`DataQualityReport`** | 封装数据质量检查的结果，包含评分、问题和建议。 |
| **`JobStatus` (枚举)** | 表示`CalculationJob`实体的状态。 |

---
*通过合理设计和使用值对象，我们的数据处理流程将变得更加健壮、类型安全和富有表达力。*
