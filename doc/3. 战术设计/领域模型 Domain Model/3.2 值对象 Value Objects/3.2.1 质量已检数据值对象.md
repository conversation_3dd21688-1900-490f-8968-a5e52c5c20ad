# 质量已检数据值对象 (QualityCheckedData VO)

## 1. 概述

`QualityCheckedData` 是由 `质量检查器 (QualityChecker)` 聚合创建的、代表流程第一步产出的**值对象**。

它是一个**不可变的 (Immutable)** 数据包，封装了那些从外部系统加载、并通过了初步业务规则验证的原始数据。它的核心职责是作为一个干净的数据快照，被下游的 `数据标准化器 (DataStandardizer)` 聚合所消费。

## 2. 属性 (Properties)

| 属性名称 | 类型 | 描述 | 示例 |
| :--- | :--- | :--- | :--- |
| `sourceType` | `String` | 原始数据的来源类型。 | `"StatisticalReport"`, `"TimeSeriesAPI"` |
| `rawData` | `Object` | 原始数据的有效载荷。 | `(JSON, XML, or other data structure)` |
| `checkedAt` | `Timestamp` | 数据被检查的时间戳。 | `2023-10-27T07:00:00Z` |

## 3. 行为 (Behaviors)

作为值对象，`QualityCheckedData` 不包含任何业务行为，它仅用于封装和传递数据。

## 4. 生命周期 (Lifecycle)

- **创建 (Creation)**: 由 `质量检查器 (QualityChecker)` 聚合在处理完加载的原始数据后，在内存中创建。
- **传递 (Passing)**: 作为 `DataQualityChecked` 事件的一部分，或通过命令直接传递给 `数据标准化器 (DataStandardizer)` 聚合。
- **消亡 (Demise)**: 一旦被 `DataStandardizer` 消费，它的生命周期即告结束。它通常不会被持久化到独立的数据库表中，最多作为 `CalculationJob` 实体的一个序列化字段进行临时存储或日志记录。

---
*`QualityCheckedData` 作为值对象，确保了只有符合基本质量要求的数据才能进入后续的复杂处理流程，同时保持了模型的简洁性。*
