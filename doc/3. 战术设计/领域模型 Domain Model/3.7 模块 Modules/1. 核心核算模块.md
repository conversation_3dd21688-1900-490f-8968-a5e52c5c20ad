# 1. 核心核算模块 (Calculation Module)

## 1. 模块概述 (Module Overview)

`核心核算模块` 是本系统领域模型的核心，它封装了从数据获取、处理、建模、预测到最终结果输出的整个端到端业务流程。

本模块的设计高度内聚，所有包含的领域对象都为了“**完成一次区域电能碳核算与预测**”这一共同目标而服务。它作为一个整体，对外提供清晰、稳定的业务能力。

## 2. 核心职责 (Core Responsibilities)

- **流程编排**: 通过 `CalculationJob` 聚合，管理和跟踪整个核算预测流程的状态。
- **数据处理**: 负责数据的质量检查、标准化和清单构造。
- **建模预测**: 负责预测模型的构建、训练、调优和执行。
- **结果生成**: 负责预测结果的融合、验证和最终输出。

## 3. 包含的领域对象 (Contained Domain Objects)

本模块是以下领域对象的“家”，负责将它们组织在一起：

### 3.1 聚合 (Aggregates)

- **流程驱动聚合**:
  - `CalculationJob` (核算任务)
- **流程步骤聚合**:
  - `QualityChecker` (质量检查器)
  - `DataStandardizer` (数据标准化器)
  - `InventoryConstructor` (清单构造器)
  - `ModelBuilder` (模型构建器)
  - `PredictionAnalyzer` (预测分析器)
  - `DataFuser` (数据融合器)
  - `ResultOutputter` (结果输出器)

*详细信息请参阅 `../3.3 聚合/3.3.0 聚合总览.md`*

### 3.2 实体 (Entities)

- `CalculationJob` (核算任务)
- `PredictionResult` (预测结果)

*详细信息请参阅 `../3.1 实体/3.1.1 实体总览.md`*

### 3.3 值对象 (Value Objects)

- **核心流程值对象**:
  - `QualityCheckedData`, `StandardizedData`, `EnergyConsumptionInventory`, `CarbonEmissionInventory`, `PredictionModel`, `FinalEmissionData`
- **基础值对象**:
  - `Region`, `EnergyCategory`, `TimeSeriesData`, `TabularData` 等

*详细信息请参阅 `../3.2 值对象/值对象总览.md`*

### 3.4 领域服务 (Domain Services)

- `PredictionService`

*详细信息请参阅 `../3.5 领域服务/1. 领域服务概述.md`*

## 4. 模块间交互 (Inter-Module Interaction)

在当前设计中，`核心核算模块` 是唯一的领域模块，因此是自包含的。

未来若系统扩展，可能会出现其他模块，如：
- **`用户管理模块 (User Management Module)`**: 负责用户、权限和访问控制。
- **`报表分析模块 (Reporting Module)`**: 负责对本模块输出的 `FinalEmissionData` 进行复杂的查询、可视化和报表生成。

届时，本模块将通过**领域事件**和**开放主机服务 (OHS)**（如 `CalculationJob` 的查询接口、`FinalEmissionDataRepository` 的查询接口）与其他模块进行交互。

---
*本模块文档是理解整个领域模型的顶层视图和入口点。*
