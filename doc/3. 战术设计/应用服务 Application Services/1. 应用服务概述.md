# 1. 应用服务概述 (Application Service Overview)

## 1. 设计原则与职责

应用服务（Application Service）是领域模型的直接“客户”，它构成了系统的API和用例（Use Case）的边界。它不包含任何业务规则或领域知识，其核心职责是**协调和编排**。

在本系统中，应用服务遵循以下设计原则：
- **保持精简**: 应用服务本身应该是瘦的。它负责调用领域对象（聚合、实体）来完成工作，但业务逻辑本身封装在领域对象内部。
- **事务边界**: 一个应用服务方法通常定义了一个业务用例的事务边界。操作要么完全成功，要么完全失败。
- **DTO转换与校验**:
  - 应用服务是外部世界（如Web API、定时任务调度器）进入系统的入口。它接收**数据传输对象（DTO）**。
  - 在调用领域层之前，它负责**校验DTO的合法性**（例如，非空、格式、范围等）。这就是我们之前讨论的“校验器”职责所在。
  - 它负责将DTO转换为领域层可以理解的**命令对象（Command）或参数**。这就是“转换器”的职责。
- **安全性**: 应用服务是执行授权检查的理想位置，确保当前用户或调用者有权执行请求的操作。
- **不返回领域对象**: 为了保持领域模型的封装，应用服务不应直接将领域实体返回给外部调用者。它应该将领域对象的查询结果转换为一个专门用于展示的DTO。

## 2. 核心应用服务定义

根据我们的系统设计，核心的用例都围绕着“核算任务”的生命周期展开。因此，我们定义一个主要的应用服务：

### `CalculationJobService`
- **概述**: 这是驱动和管理 `CalculationJob` 整个流程的应用层入口。外部调用者（如UI后端）通过这个服务来与系统交互。
- **接口方法 (Use Cases)**:

#### `startNewJob(command: StartJobCommand): UUID`
- **职责**: 启动一个新的端到端核算任务。
- **流程**:
  1.  **校验**: 验证 `StartJobCommand` DTO/命令对象的合法性。
  2.  **授权**: 检查当前用户是否有权限启动任务。
  3.  **创建聚合根**: `job = CalculationJob.start()`，创建一个新的计算任务实例。
  4.  **获取所需数据**: (如果需要) 调用 `RawDataRepository` 获取外部数据。
  5.  **持久化**: 使用 `JobRepository` 保存新的任务实例。
  6.  **返回结果**: 返回 `job.id`。
- **关联DTO/Command**:
  ```
  // Command DTO from API
  public class StartJobCommand {
      private String jobName;
      private String description;
      private Map<String, Object> jobParameters; // e.g., date ranges, region filters
  }
  ```

#### `executeStandardizationPhase(jobId: UUID): StandardizationResultDTO`
- **职责**: 执行数据标准化阶段，使用新的标准化编排器。
- **流程**:
  1. **获取任务**: 使用 `JobRepository` 找到 `CalculationJob` 实体。
  2. **获取原始数据**: 调用 `RawDataRepository` 获取所有表的数据。
  3. **执行标准化**: 使用 `StandardizationOrchestrator` 执行标准化流程：
     - 步骤1：列内标准化（地区、行业、能源品种）
     - 步骤2：跨列映射（产品到行业等）
  4. **转换结果**: 将标准化后的DataFrame转换为 `StandardizedData` 对象。
  5. **更新任务状态**: 标记标准化阶段完成。
  6. **返回结果**: 返回标准化结果DTO。
- **关联DTO**:
  ```
  public class StandardizationResultDTO {
      private UUID jobId;
      private int totalRecords;
      private int successCount;
      private int errorCount;
      private double processingTime;
      private List<String> errors;
  }
  ```

#### `getJobDetails(jobId: UUID): JobDetailsDTO`
- **职责**: 获取一个特定核算任务的详细状态和信息。
- **流程**:
  1.  **输入**: `jobId`
  2.  **查询**: 使用 `JobRepository` 找到 `CalculationJob` 实体。
  3.  **返回**: 将 `CalculationJob` 实体转换为一个DTO (Data Transfer Object)，返回给表现层。
- **关联DTO**:
  ```
  // DTO for UI display
  public class JobDetailsDTO {
      private UUID jobId;
      private String jobName;
      private String status;
      private double progress;
      private String currentStep;
      private String failureReason;
      private List<String> logs;
      private Timestamp createdAt;
      private Timestamp updatedAt;
  }
  ```

#### `listAllJobs(filter: JobFilter): List<JobSummaryDTO>`
- **职责**: 列出所有（或经过筛选的）核算任务的摘要信息。
- **流程**:
  1.  **查询**: 使用 `JobRepository` 查找任务列表。
  2.  **返回**: 将 `CalculationJob` 实体列表转换为DTO列表。
- **关联DTO**:
  ```
  public class JobSummaryDTO {
      private UUID jobId;
      private String jobName;
      private String status;
      private Timestamp createdAt;
  }
  ```

---
*应用服务层清晰地定义了系统的用例，并作为一道坚固的屏障，保护了内部领域模型的复杂性和一致性。*
