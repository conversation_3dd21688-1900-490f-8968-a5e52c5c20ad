# 项目规则

## 1. 核心设计原则

### 1.1 配置驱动原则
- **业务规则配置化**: 所有业务规则、映射关系、计算因子都通过配置文件定义
- **动态调整**: 业务规则调整无需修改代码，只需更新配置文件
- **版本控制**: 配置文件纳入版本控制，支持规则变更的追踪和回滚

### 1.2 职责分离原则
- **数据获取**: 专注于从数据库获取原始数据，不涉及业务逻辑
- **数据标准化**: 专注于数据清洗、映射和格式转换
- **清单构建**: 专注于基于标准化数据构建最终清单

### 1.3 数据溯源原则
- **来源追踪**: 每个处理后的数据点都能追溯到原始数据源
- **处理记录**: 记录数据处理的每个步骤和转换规则
- **质量评估**: 提供数据质量评估指标和改进建议

## 2. 数据处理链路设计

### 2.1 数据标准化链路

#### 2.1.1 能源数据标准化链路
**链路1**: 省级能源数据标准化
- **输入**: `ecam_in_y_pro_ind_ene_off` (省级能源消费表)
- **处理**: 行业名称标准化、能源品种标准化、单位折标计算
- **输出**: `StandardizedEnergyData` 集合
- **配置**: 通过 `table_configs.ecam_in_y_pro_ind_ene_off` 配置

**链路2**: 地市工业能源数据标准化
- **输入**: `ecam_in_y_pro_ind_ene2_off` (地市工业能源表)
- **处理**: 行业名称标准化、能源品种标准化、单位折标计算
- **输出**: `StandardizedEnergyData` 集合
- **配置**: 通过 `table_configs.ecam_in_y_pro_ind_ene2_off` 配置

**链路3**: 地市用电量数据标准化
- **输入**: `ecam_in_m_pro_ind_ele_off` (地市用电量表)
- **处理**: 月度数据聚合、行业名称标准化、单位统一
- **输出**: `StandardizedMonthlyElectricityData` 集合
- **配置**: 通过 `table_configs.ecam_in_m_pro_ind_ele_off` 配置

#### 2.1.2 工业产品数据标准化链路
**链路4**: 工业产品产量数据标准化
- **输入**: `fct_y_prd_output` (工业产品产量表)
- **处理**: 产品名称标准化、行业映射、排放类别分类
- **输出**: `StandardizedProductData` 集合
- **配置**: 通过 `table_configs.fct_y_prd_output` 配置

#### 2.1.3 经济指标数据标准化链路
**链路5**: GDP与能耗强度数据标准化
- **输入**: `fct_y_gdp` (GDP表) + `fct_y_all_ene_intsty` (能耗强度表)
- **处理**: 指标分类、业务用途标识、数据验证
- **输出**: `StandardizedEconomicData` 集合
- **配置**: 通过 `table_configs.fct_y_gdp` 和 `table_configs.fct_y_all_ene_intsty` 配置

### 2.2 数据下沉链路

#### 2.2.1 用电比例计算链路
**链路6**: 地市用电比例计算
- **输入**: 标准化后的地市用电量数据
- **处理**: 年度聚合、比例计算、权重分配
- **输出**: 地市-行业用电比例矩阵
- **业务规则**: 用电比例作为数据下沉的主要权重

#### 2.2.2 省级数据下沉链路
**链路7**: 省级能源数据下沉到地市
- **输入**: 省级能源消费数据 + 地市用电比例矩阵
- **处理**: 总量分配、数据合并、一致性检查
- **输出**: `CityEnergyMatrix` (城市能源矩阵)
- **业务规则**: 地市直接数据优先，省级下沉数据补充

#### 2.2.3 数据合并策略链路
**链路8**: 地市直接数据与省级下沉数据合并
- **输入**: 地市直接数据 + 省级下沉数据
- **处理**: 优先级判断、数据覆盖、一致性验证
- **输出**: 合并后的城市能源矩阵
- **合并原则**: 地市直接数据 > 省级下沉数据

### 2.3 清单构建链路

#### 2.3.1 综合能耗清单构建链路
**链路9**: 综合能耗清单构建
- **输入**: `CityEnergyMatrix` + 标准化能源数据
- **处理**: 行业汇总、品种汇总、数据验证
- **输出**: `EnergyConsumptionInventory` (综合能耗部分)
- **业务规则**: 综合能耗等于各分品种能耗之和

#### 2.3.2 分品种能耗清单构建链路
**链路10**: 分品种能耗清单构建
- **输入**: `CityEnergyMatrix` + 标准化能源数据
- **处理**: 品种保持、数据完整性、单位统一
- **输出**: `EnergyConsumptionInventory` (分品种能耗部分)
- **业务规则**: 不进行能源品种的合并或拆分

#### 2.3.3 碳排放清单构建链路
**链路11**: 碳排放清单构建
- **输入**: 分品种能耗清单 + 产品产量数据 + 排放因子
- **处理**: 能源燃烧排放计算、工业过程排放计算、排放源分类
- **输出**: `CarbonEmissionInventory`
- **业务规则**: 能源燃烧排放基于能源消费，工业过程排放基于产品产量

## 3. 数据流转规则

### 3.1 数据依赖关系
```
标准化数据 → 约束计算 → 数据下沉 → 清单构建
     │           │         │         │
     ▼           ▼         ▼         ▼
StandardizedData → ConstraintData → CityEnergyMatrix → Inventory
```

**依赖规则**:
- 数据标准化必须在约束计算之前完成
- 约束计算必须在数据下沉之前完成
- 数据下沉必须在清单构建之前完成

### 3.2 数据质量保证

#### 3.2.1 完整性检查
- **必需字段**: 检查每个阶段必需字段的完整性
- **数据覆盖**: 确保所有地区-行业组合都有对应数据
- **关联性**: 验证相关数据之间的关联关系

#### 3.2.2 一致性检查
- **总量一致**: 验证下沉后的数据与省级总量的一致性
- **品种一致**: 验证综合能耗与分品种能耗的一致性
- **时间一致**: 验证不同数据源的时间一致性

#### 3.2.3 准确性检查
- **数值范围**: 检查数值是否在合理范围内
- **单位一致**: 验证单位换算的正确性
- **逻辑关系**: 检查业务逻辑的正确性

### 3.3 错误处理策略

#### 3.3.1 数据缺失处理
- **默认值**: 为缺失数据提供合理的默认值
- **插值方法**: 使用历史数据或相关数据进行插值
- **异常标记**: 标记异常数据，便于后续分析

#### 3.3.2 数据异常处理
- **异常检测**: 自动检测数据异常
- **异常分类**: 将异常分为不同类型
- **处理策略**: 为不同类型的异常制定处理策略

## 4. 系统架构设计

### 4.1 分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  CalculationJobService                                      │
│  ├── DataStandardizationOrchestrator                       │
│  ├── ConstraintCalculationOrchestrator                     │
│  └── InventoryConstructionOrchestrator                     │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    领域层 (Domain Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  数据标准化服务                                              │
│  ├── DataStandardizationService                            │
│  ├── IndustryMappingStrategy                               │
│  └── EnergyTypeMappingStrategy                             │
│                                                             │
│  约束计算服务                                                │
│  ├── ConstraintCalculationService                          │
│  └── BalancingService                                       │
│                                                             │
│  清单构建服务                                                │
│  ├── InventoryConstructionService                          │
│  └── CarbonEmissionCalculationService                      │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                 基础设施层 (Infrastructure Layer)             │
├─────────────────────────────────────────────────────────────┤
│  ConfigReader                                               │
│  RawDataRepository                                          │
│  DomainServicesImpl                                         │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 服务职责划分

#### 4.2.1 数据标准化服务
- **职责**: 负责所有原始数据的标准化处理
- **策略**: 根据数据表类型选择不同的标准化策略
- **输出**: 标准化的值对象集合

#### 4.2.2 约束计算服务
- **职责**: 计算IPF平衡算法所需的约束条件
- **输入**: 标准化后的GDP和能耗强度数据
- **输出**: 行约束和列约束数据

#### 4.2.3 数据平衡服务
- **职责**: 执行IPF平衡算法，平衡城市能源矩阵
- **输入**: 城市能源矩阵和约束条件
- **输出**: 平衡后的城市能源矩阵

#### 4.2.4 清单构建服务
- **职责**: 基于平衡后的数据构建最终清单
- **输入**: 平衡后的城市能源矩阵
- **输出**: 能源消费清单和碳排放清单

## 5. 配置管理策略

### 5.1 配置文件结构
- **表配置**: 定义每个数据表的处理规则和映射关系
- **能源类型配置**: 定义能源品种的分类和折标方法
- **指标配置**: 定义GDP和能耗强度的业务用途
- **映射配置**: 定义各种名称映射关系

### 5.2 配置验证机制
- **语法验证**: 检查YAML语法的正确性
- **业务验证**: 验证配置的业务逻辑合理性
- **依赖验证**: 检查配置项之间的依赖关系

### 5.3 配置热更新
- **动态加载**: 支持配置文件的动态加载
- **版本管理**: 支持配置版本的管理和回滚
- **变更通知**: 配置变更时通知相关服务

## 6. 性能优化策略

### 6.1 数据处理优化
- **批量处理**: 支持批量数据处理，提高处理效率
- **并行计算**: 利用多核处理器进行并行计算
- **缓存机制**: 缓存中间结果，避免重复计算

### 6.2 存储优化
- **数据压缩**: 对历史数据进行压缩存储
- **索引优化**: 优化数据库查询索引
- **分区策略**: 按时间或地区进行数据分区

### 6.3 查询优化
- **查询计划**: 优化SQL查询计划
- **连接策略**: 优化多表连接策略
- **聚合优化**: 优化数据聚合计算

## 7. 监控和可观测性

### 7.1 数据流监控
- **处理状态**: 监控每个处理阶段的完成状态
- **处理时间**: 记录每个阶段的处理时间
- **数据量统计**: 统计各阶段的数据量变化

### 7.2 质量指标监控
- **完整性指标**: 监控数据完整性
- **一致性指标**: 监控数据一致性
- **准确性指标**: 监控数据准确性

### 7.3 性能监控
- **处理性能**: 监控数据处理性能
- **资源使用**: 监控系统资源使用情况
- **异常告警**: 设置异常情况告警机制

## 8. 测试策略

### 8.1 单元测试
- **服务测试**: 测试各个领域服务的业务逻辑
- **值对象测试**: 测试值对象的数据验证逻辑
- **配置测试**: 测试配置读取和验证逻辑

### 8.2 集成测试
- **数据流测试**: 测试完整的数据处理流程
- **配置集成测试**: 测试配置与代码的集成
- **数据库集成测试**: 测试与数据库的交互

### 8.3 性能测试
- **负载测试**: 测试系统在不同负载下的性能
- **压力测试**: 测试系统的极限处理能力
- **稳定性测试**: 测试系统长期运行的稳定性

---

*本规则文档定义了系统的核心设计原则、数据处理链路、架构设计和实施策略，为系统开发和维护提供指导。*

*详细的实施计划和进度跟踪请参考 `doc/重构实施/` 目录下的相关文档。*
