## 实际用到的数据表结构

```sql
create table ecam_in_energy_factor 
(  
    year        date            not null comment 'year',  
    source      varchar(50)     not null comment 'source',  
    province    varchar(50)     not null comment 'province',  
    factor      varchar(50)     not null comment 'factor',  
    method      varchar(50)     not null,  
    industry    varchar(50)     not null comment 'industry',  
    energy_type varchar(50)     not null comment 'energy_type',  
    unit        varchar(50)     null comment 'unit',  
    value       decimal(30, 12) null comment 'value',  
    primary key (year, source, province, factor, method, industry, energy_type)  
)  
    comment 'ecam_in_energy_factor' charset = utf8  
                                    row_format = DYNAMIC;  
  
create table ecam_in_m_pro_ind_ele_off  
(  
    month       text   null,  
    area        text   null,  
    industry    text   null,  
    electricity double null  
)  
    collate = utf8mb4_unicode_ci  
    row_format = DYNAMIC;  
  
create table ecam_in_y_pro_all_car_off  
(  
    year        date            not null comment 'year',  
    province    varchar(50)     not null comment 'province',  
    item        varchar(50)     not null comment 'item',  
    carbon_type varchar(50)     not null comment 'carbon_type',  
    value       decimal(30, 12) null comment 'value',  
    unit        varchar(50)     null comment 'unit',  
    source      varchar(50)     null comment 'source',  
    primary key (year, province, item, carbon_type)  
)  
    comment 'ecam_in_y_pro_all_car_off' charset = utf8  
                                        row_format = DYNAMIC;  
  
create table ecam_in_y_pro_ind_ene2_off  
(  
    year        double null,  
    area        text   null,  
    industry    text   null,  
    energy_type text   null,  
    value       text   null,  
    unit        text   null,  
    `convert`   text   null,  
    method      text   null  
)  
    collate = utf8mb4_unicode_ci  
    row_format = DYNAMIC;  
  
create table ecam_in_y_pro_ind_ene3_off  
(  
    year        date            not null,  
    province    varchar(50)     not null,  
    industry    varchar(50)     not null,  
    `convert`   varchar(50)     not null,  
    method      varchar(50)     not null,  
    energy_type varchar(50)     not null,  
    value       decimal(30, 12) null,  
    unit        varchar(50)     null,  
    primary key (year, province, industry, `convert`, method, energy_type)  
)  
    comment 'ecam_in_y_pro_ind_ene3_off' charset = utf8  
                                         row_format = DYNAMIC;  
  
create table ecam_in_y_pro_ind_ene_off  
(  
    year        date            not null comment 'year',  
    province    varchar(50)     not null comment 'province',  
    item        varchar(50)     not null comment 'item',  
    `convert`   varchar(50)     not null,  
    method      varchar(50)     not null,  
    energy_type varchar(50)     not null comment 'energy_type',  
    value       decimal(30, 12) null comment 'value',  
    unit        varchar(50)     null comment 'unit',  
    primary key (year, province, item, `convert`, method, energy_type)  
)  
    comment 'ecam_in_y_pro_ind_ene_off' charset = utf8  
                                        row_format = DYNAMIC;  
  
create table fct_m_power_dispatch  
(  
    month  bigint null,  
    area   text   null,  
    record double null,  
    method text   null,  
    source text   null,  
    unit   text   null  
)  
    collate = utf8mb4_unicode_ci  
    row_format = DYNAMIC;  
  
create table fct_m_power_generation  
(  
    month     bigint null,  
    area      text   null,  
    record    double null,  
    unit_type text   null,  
    item      text   null,  
    source    text   null,  
    unit      text   null  
)  
    collate = utf8mb4_unicode_ci  
    row_format = DYNAMIC;  
  
create table fct_y_all_ene_intsty  
(  
    year      bigint null,  
    area      text   null,  
    indicator text   null,  
    period text null,  
    method    text   null,  
    record    double null,  
    source    text   null  
)  
    collate = utf8mb4_unicode_ci  
    row_format = DYNAMIC;  
  
create table fct_y_gdp  
(  
    year      text null,  
    area      text null,  
    indicator text null,  
    period text null,  
    method    text null,  
    record    text null,  
    source    text null  
)  
    collate = utf8mb4_unicode_ci  
    row_format = DYNAMIC;  
  
create table fct_y_prd_output  
(  
    year         bigint null,  
    area         text   null,  
    product_name text   null,  
    record       double null,  
    unit         text   null,  
    source       text   null  
)  
    collate = utf8mb4_unicode_ci  
    row_format = DYNAMIC;
```

## ecam_in_energy_factor 能源折标系数/碳排放因子

```csv
year,source,province,factor,method,industry,energy_type,unit,value
2013-01-01,GBT2589-2020综合能耗计算通则,全国,折标系数,发电煤耗计算法,通用,电力,吨标煤/万千瓦时,3.020000000000
2013-01-01,全行业碳排放核算_能源排放因子,全国,co2,实物量折碳排放因子,通用,其他洗煤,吨CO2/吨,1.289072000000
2013-01-01,全行业碳排放核算_能源排放因子,全国,co2,实物量折碳排放因子,通用,其他焦化产品,吨CO2/吨,3.242951000000
```

## ecam_in_m_pro_ind_ele_off 月度分地区分行业用电量

其中一个地区一个月的完整记录如下

```csv
month,area,industry,electricity
2025-02-01,临汾市,全社会用电量,216089.953
2025-02-01,临汾市,A、全行业用电量合计,171817.933
2025-02-01,临汾市,第一产业用电量,2189.7346
2025-02-01,临汾市,第二产业用电量,130492.2249
2025-02-01,临汾市,第三产业用电量,39135.9735
2025-02-01,临汾市,B、城乡居民生活用电量合计,44272.02
2025-02-01,临汾市,城镇居民用电量,15295.7425
2025-02-01,临汾市,乡村居民用电量,28976.2775
2025-02-01,临汾市,全行业用电分类用电量,171817.933
2025-02-01,临汾市,一、农、林、牧、渔业用电量,5059.8336
2025-02-01,临汾市,1.农业用电量,734.3073
2025-02-01,临汾市,2.林业用电量,39.8727
2025-02-01,临汾市,3.畜牧业用电量,1393.5063
2025-02-01,临汾市,4.渔业用电量,22.0483
2025-02-01,临汾市,5.农、林、牧、渔服务业用电量,2870.099
2025-02-01,临汾市,其中：排灌用电量,2738.3183
2025-02-01,临汾市,二、工业用电量,129869.5832
2025-02-01,临汾市,（一）采矿业用电量,37274.3393
2025-02-01,临汾市,1.煤炭开采和洗选业用电量,31661.2639
2025-02-01,临汾市,2.石油和天燃气开采业用电量,4440.0744
2025-02-01,临汾市,3.黑色金属矿采选业用电量,348.0746
2025-02-01,临汾市,4.有色金属矿采选业用电量,3.7945
2025-02-01,临汾市,5.非金属矿采选业用电量,64.2474
2025-02-01,临汾市,6.其他采矿业用电量,756.8845
2025-02-01,临汾市,（二）制造业用电量,73670.1191
2025-02-01,临汾市,1.农副食品加工业用电量,648.897
2025-02-01,临汾市,2.食品制造业用电量,179.2572
2025-02-01,临汾市,3.酒、饮料及精制茶制造业用电量,89.0972
2025-02-01,临汾市,4.烟草制品业用电量,0.4478
2025-02-01,临汾市,5.纺织业用电量,17.2574
2025-02-01,临汾市,6.纺织服装、服饰业用电量,26.7972
2025-02-01,临汾市,7.皮革、毛皮、羽毛及其制品和制鞋业用电量,3.7713
2025-02-01,临汾市,8.木材加工和木、竹、藤、棕、草制品业用电量,113.4448
2025-02-01,临汾市,9.家具制造业用电量,36.8061
2025-02-01,临汾市,10.造纸和纸制品业用电量,710.3398
2025-02-01,临汾市,11.印刷和记录媒介复制业用电量,21.0663
2025-02-01,临汾市,12.文教、工美、体育和娱乐用品制造业用电量,6.2524
2025-02-01,临汾市,其中：体育用品制造用电量,1.6789
2025-02-01,临汾市,13.石油、煤炭及其他燃料加工业用电量,22662.382
2025-02-01,临汾市,其中：煤化工用电量,4531.6614
2025-02-01,临汾市,14.化学原料和化学制品制造业用电量,5138.5009
2025-02-01,临汾市,其中：氯碱用电量,0.1338
2025-02-01,临汾市,电石用电量,0
2025-02-01,临汾市,黄磷用电量,0
2025-02-01,临汾市,肥料制造用电量,173.8834
2025-02-01,临汾市,15.医药制造业用电量,425.6844
2025-02-01,临汾市,其中：中成药生产用电量,23.3895
2025-02-01,临汾市,生物药品制品制造用电量,8.1273
2025-02-01,临汾市,16.化学纤维制造业用电量,53.0237
2025-02-01,临汾市,17.橡胶和塑料制品业用电量,298.3615
2025-02-01,临汾市,其中：橡胶制品业用电量,222.7225
2025-02-01,临汾市,塑料制品业用电量,75.639
2025-02-01,临汾市,18.非金属矿物制品业用电量,2400.3079
2025-02-01,临汾市,其中：水泥制造用电量,906.042
2025-02-01,临汾市,玻璃制造用电量,121.9573
2025-02-01,临汾市,陶瓷制品制造用电量,4.5887
2025-02-01,临汾市,碳化硅用电量,0.2663
2025-02-01,临汾市,19.黑色金属冶炼和压延加工业用电量,26406.8291
2025-02-01,临汾市,其中：钢铁用电量,26377.394
2025-02-01,临汾市,铁合金冶炼用电量,29.4351
2025-02-01,临汾市,20.有色金属冶炼和压延加工业用电量,3441.2961
2025-02-01,临汾市,其中：铝冶炼用电量,0
2025-02-01,临汾市,铅锌冶炼用电量,0
2025-02-01,临汾市,稀有稀土金属冶炼用电量,0
2025-02-01,临汾市,21.金属制品业用电量,2199.8492
2025-02-01,临汾市,其中：结构性金属制品制造用电量,181.2933
2025-02-01,临汾市,22.通用设备制造业用电量,7666.1005
2025-02-01,临汾市,其中：风能原动设备制造用电量,1.9423
2025-02-01,临汾市,23.专用设备制造业用电量,114.7663
2025-02-01,临汾市,24.汽车制造业用电量,288.6212
2025-02-01,临汾市,其中：新能源车整车制造用电量,0
2025-02-01,临汾市,25.铁路.船舶.航空航天和其他运输设备制造业用电量,0.6043
2025-02-01,临汾市,其中：铁路运输设备制造用电量,0
2025-02-01,临汾市,城市轨道交通设备制造用电量,0
2025-02-01,临汾市,航空、航天器及设备制造用电量,0
2025-02-01,临汾市,26.电气机械和器材制造业用电量,201.6203
2025-02-01,临汾市,其中：光伏设备及元器件制造用电量,0
2025-02-01,临汾市,27.计算机、通信和其他电子设备制造业用电量,19.1419
2025-02-01,临汾市,其中：计算机制造用电量,0
2025-02-01,临汾市,通信设备制造用电量,0.126
2025-02-01,临汾市,28.仪器仪表制造业用电量,0.3989
2025-02-01,临汾市,29.其他制造业用电量,104.4293
2025-02-01,临汾市,30.废弃资源综合利用业用电量,304.2665
2025-02-01,临汾市,31.金属制品、机械和设备修理业用电量,90.5006
2025-02-01,临汾市,（三）电力、燃气及水的生产和供应业用电量,18925.1248
2025-02-01,临汾市,1.电力、热力的生产和供应业用电量,16204.2581
2025-02-01,临汾市,其中：电厂生产全部耗用电量,3380.6088
2025-02-01,临汾市,线路损失电量用电量,5241.2548
2025-02-01,临汾市,抽水蓄能抽水耗用电量,0
2025-02-01,临汾市,2.燃气生产和供应业用电量,1277.386
2025-02-01,临汾市,3.水的生产和供应业用电量,1443.4807
2025-02-01,临汾市,三、建筑业用电量,1468.3674
2025-02-01,临汾市,1.房屋建筑业用电量,684.2959
2025-02-01,临汾市,2.土木工程建筑业用电量,212.662
2025-02-01,临汾市,3.建筑安装业用电量,147.9276
2025-02-01,临汾市,4.建筑装饰、装修和其他建筑业用电量,423.4819
2025-02-01,临汾市,四、交通运输、仓储、邮政业用电量,10623.9558
2025-02-01,临汾市,1.铁路运输业用电量,8199.0415
2025-02-01,临汾市,其中：电气化铁路用电量,0
2025-02-01,临汾市,2.道路运输业用电量,915.4267
2025-02-01,临汾市,其中：城市公共交通运输用电量,23.2339
2025-02-01,临汾市,3.水上运输业用电量,0.2356
2025-02-01,临汾市,其中：港口岸电用电量,0
2025-02-01,临汾市,4.航空运输业用电量,24.839
2025-02-01,临汾市,5.管道运输业用电量,1046.373
2025-02-01,临汾市,6.多式联运和运输代理业用电量,38.1982
2025-02-01,临汾市,7.装卸搬运和仓储业用电量,338.5516
2025-02-01,临汾市,8.邮政业用电量,61.2902
2025-02-01,临汾市,五、信息传输、软件和信息技术服务业用电量,1907.2457
2025-02-01,临汾市,1.电信、广播电视和卫星传输服务用电量,528.1034
2025-02-01,临汾市,2.互联网和相关服务用电量,1300.9011
2025-02-01,临汾市,其中：互联网数据服务用电量,5.9763
2025-02-01,临汾市,3.软件和信息技术服务业用电量,78.2412
2025-02-01,临汾市,六、批发和零售业用电量,8821.0129
2025-02-01,临汾市,其中：充换电服务业用电量,3612.3308
2025-02-01,临汾市,七、住宿和餐饮业用电量,2522.6698
2025-02-01,临汾市,八、金融业用电量,483.7455
2025-02-01,临汾市,九、房地产业用电量,754.0297
2025-02-01,临汾市,十、租赁和商务服务业用电量,536.9311
2025-02-01,临汾市,其中：租赁业用电量,34.5355
2025-02-01,临汾市,十一、公共服务及管理组织用电量,9770.5583
2025-02-01,临汾市,1.科学研究和技术服务业用电量,158.1679
2025-02-01,临汾市,其中:地质勘查用电量,11.4817
2025-02-01,临汾市,科技推广和应用服务业用电量,20.0344
2025-02-01,临汾市,2.水利、环境和公共设施管理业用电量,1243.7237
2025-02-01,临汾市,其中：水利管理业用电量,72.2061
2025-02-01,临汾市,公共照明用电量,1041.6915
2025-02-01,临汾市,4.教育、文化、体育和娱乐业用电量,3165.6837
2025-02-01,临汾市,其中：教育用电量,2753.0322
2025-02-01,临汾市,5.卫生和社会工作用电量,1439.9548
2025-02-01,临汾市,6.公共管理和社会组织、国际组织用电量,2502.1036
2025-02-01,临汾市,开采专业及辅助性活动用电量,755.2251
```

## ecam_in_y_pro_ind_ene_off   月度分地区分行业分品种能源消费量

包含的item有 一.可供本地区消费的能源量   1.一次能源生产量   2.外省(区、市)调入量   3.进口量   4.境内飞机和轮船在境外的加油量   5.本省(区、市)调出量(-)   6.出口量(-)   7.境外飞机和轮船在境内的加油量(-)   8.库存增(-)、减(+)量 二.加工转换投入(-)产出(+)量   1.火力发电   2.供热   3.煤炭洗选   4.炼焦   5.炼油及煤制油     #油品再投入量(-)   6.制气     #焦炭再投入量(-)   7.天然气液化   8.煤制品加工   9.回收能 三.损失量 四.终端消费量 1.农、林、牧、渔业 2.工业     #用作原料、材料   3.建筑业   4.交通运输、仓储和邮政业   5.批发和零售业、住宿和餐饮业   6.其他   7.居民生活     城镇     乡村

包含的energy_type有 其他洗煤 其他焦化产品 其他煤气 其他石油制品 其他能源 原油 原煤 天然气 柴油 汽油 油品合计 洗精煤 润滑油 液化天然气 液化石油气 溶剂油 炼厂干气 热力 焦炉煤气 焦炭 煤制品 煤合计 煤油 煤矸石 燃料油 电力 石油沥青 石油焦 石脑油 石蜡 转炉煤气 高炉煤气

```csv
year,province,item,convert,method,energy_type,value,unit
2022-01-01,山西,四.终端消费量,实物量,"",其他洗煤,507.360000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",其他焦化产品,347.980000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",其他煤气,51.950000000000,亿立方米
2022-01-01,山西,四.终端消费量,实物量,"",其他石油制品,3.050000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",其他能源,42.480000000000,万吨标准煤
2022-01-01,山西,四.终端消费量,实物量,"",原煤,3442.510000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",天然气,69.740000000000,亿立方米
2022-01-01,山西,四.终端消费量,实物量,"",柴油,415.340000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",汽油,228.570000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",油品合计,710.320000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",润滑油,0.900000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",液化天然气,12.250000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",液化石油气,6.110000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",热力,55830.910000000000,万百万千焦
2022-01-01,山西,四.终端消费量,实物量,"",焦炉煤气,161.020000000000,亿立方米
2022-01-01,山西,四.终端消费量,实物量,"",焦炭,2752.440000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",煤制品,125.530000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",煤合计,4075.390000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",煤油,22.830000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",煤矸石,8.040000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",燃料油,1.410000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",电力,2754.330000000000,亿千瓦小时
2022-01-01,山西,四.终端消费量,实物量,"",石油沥青,9.530000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",石油焦,21.970000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",石蜡,0.630000000000,万吨
2022-01-01,山西,四.终端消费量,实物量,"",转炉煤气,49.830000000000,亿立方米
2022-01-01,山西,四.终端消费量,实物量,"",高炉煤气,630.970000000000,亿立方米

```

## ecam_in_y_pro_ind_ene2_off  工业及其细分行业的能源消费量

用于计算各地市的能源活动碳排放量，具体到各地市的数据，industry的名称不规范的更多了

```
year,area,industry,energy_type,value,unit,convert,method
2010,太原,全部工业企业,原煤,51854779.98,吨,实物量,""
2010,太原,(二)重工业,原煤,3403992,吨,实物量,""
2010,太原,(三)制造业,原煤,900,吨,实物量,""
2010,太原,非金属矿物制造业,原煤,900,吨,实物量,""
2010,太原,(四)电力、燃气及水的生产和供应业,原煤,3403092,吨,实物量,""
2010,太原,电力、热力的生产和供应业,原煤,3403092,吨,实物量,""
2010,太原,全部工业企业,无烟煤,6277.59,吨,实物量,""
2010,太原,全部工业企业,炼焦烟煤,39103788.32,吨,实物量,""
2010,太原,(二)重工业,炼焦烟煤,3403992,吨,实物量,""
2010,太原,(三)制造业,炼焦烟煤,900,吨,实物量,""
```

## fct_y_all_ene_intsty 能耗强度（单位GDP能耗及下降率）

用于和GDP配合，计算地区能源消费总量（标准量，吨标准煤）

```
year,area,indicator,period,method,record,source
2007,太原市,单位地区生产总值能源消耗(等价值),当期,值,2.44,山西统计年鉴
2008,太原市,单位地区生产总值能源消耗(等价值),当期,值,2.23,山西统计年鉴
2009,太原市,单位地区生产总值能源消耗(等价值),当期,值,1.83,山西统计年鉴
2010,太原市,单位地区生产总值能源消耗(等价值),当期,值,1.4,山西统计年鉴
2011,太原市,单位地区生产总值能源消耗(等价值),当期,值,1.35,山西统计年鉴
2012,太原市,单位地区生产总值能源消耗(等价值),当期,值,1.2837,山西统计年鉴
2013,太原市,单位地区生产总值能源消耗(等价值),当期,值,1.2296,山西统计年鉴
2014,太原市,单位地区生产总值能源消耗(等价值),当期,值,1.04855454810846,山西统计年鉴
2015,太原市,单位地区生产总值能源消耗(等价值),当期,值,0.960800360339785,山西统计年鉴
2016,太原市,单位地区生产总值能源消耗(等价值),当期,值,0.8946,山西统计年鉴
2017,太原市,单位地区生产总值能源消耗(等价值),当期,值,0.8592,山西统计年鉴
2018,太原市,万元地区生产总值能耗降低率,当期,变化率,-3.64,山西统计年鉴
2019,太原市,万元地区生产总值能耗降低率,当期,变化率,-3.05,山西统计年鉴
2020,太原市,万元地区生产总值能耗降低率,当期,变化率,-2.8471,山西统计年鉴
2021,太原市,万元地区生产总值能耗降低率,当期,变化率,-5.2,山西统计年鉴
2022,太原市,万元地区生产总值能耗降低率,当期,变化率,-2.9,山西统计年鉴
2023,太原市,万元地区生产总值能耗降低率,当期,变化率,-2.6,山西统计年鉴
```

## fct_y_gdp GDP

用于和能耗强度表配合，计算地区能源消费总量

```
year,area,indicator,period,method,record,source
2005,太原市,地区生产总值（亿元）,当期,值,893.162,外部数据
2006,太原市,地区生产总值（亿元）,当期,值,1013.6482,外部数据
2007,太原市,地区生产总值（亿元）,当期,值,1254.9447,外部数据
2008,太原市,地区生产总值（亿元）,当期,值,1468.0851,外部数据
2009,太原市,地区生产总值（亿元）,当期,值,1545.2409,外部数据
2010,太原市,地区生产总值（亿元）,当期,值,1778.0539,外部数据
2011,太原市,地区生产总值（亿元）,当期,值,2080.1243,外部数据
2012,太原市,地区生产总值（亿元）,当期,值,2311.4326,外部数据
2013,太原市,地区生产总值（亿元）,当期,值,2412.8724,外部数据
2014,太原市,地区生产总值（亿元）,当期,值,2531.0917,外部数据
2015,太原市,地区生产总值（亿元）,当期,值,2735.3442,外部数据
2016,太原市,地区生产总值（亿元）,当期,值,2955.6045,外部数据
2017,太原市,地区生产总值（亿元）,当期,值,3382.1819,外部数据
2018,太原市,地区生产总值（亿元）,当期,值,3884.4778,外部数据
2019,太原市,地区生产总值（亿元）,当期,值,4016.1904,外部数据
2020,太原市,地区生产总值（亿元）,当期,值,4153.251,外部数据
2021,太原市,地区生产总值（亿元）,当期,值,5121.6058,外部数据
2022,太原市,地区生产总值（亿元）,当期,值,5571.1745,外部数据
2024,太原市,地区生产总值（亿元）,当期,值,5418.87,外部数据
2023,太原市,地区生产总值（亿元）,当期,值,5573.72,外部数据
```

## fct_y_prd_output  工业产品产量

用于和因子表配合，计算工业过程碳排放

```
year,area,product_name,record,unit,source
2009,临汾市,原煤,2493.9,万吨,山西统计年鉴
2009,临汾市,发电量,62.132263,亿千瓦时,山西统计年鉴
2009,临汾市,粗钢,605.7977,万吨,山西统计年鉴
2009,临汾市,钢材,446.3194,万吨,山西统计年鉴
2009,临汾市,生铁,917.293091,万吨,山西统计年鉴
2009,临汾市,焦炭,1707.23675,万吨,山西统计年鉴
2009,临汾市,水泥,248.30289199999999,万吨,山西统计年鉴
2009,临汾市,平板玻璃,0,万重量箱,山西统计年鉴
2009,临汾市,电石,0,万吨,山西统计年鉴
2009,临汾市,交流电动机,0,万千瓦,山西统计年鉴
2010,临汾市,原煤,3596.222071,万吨,山西统计年鉴
```

## 能源消费清单构造逻辑

一个地区的能源消费清单就是一个地区全社会以及各个行业的化石能源消费的实物量

行业结构如下，各个表中的行业都要通过聚合计算映射到这张表上：
1. 全社会
	1. 农林牧渔业
	2. 能源行业
	3. 工业
		1. 钢铁
		2. 有色金属
		3. 化工
		4. 石化
		5. 建材
		6. 造纸和纸制品
		7. 其他
	4. 建筑业
	5. 交通运输业
	6. 服务业
	7. 居民生活业

对于每个行业，包含的能源品种如下：其他洗煤 其他焦化产品 其他煤气 其他石油制品 其他能源 原油 原煤 天然气 柴油 汽油  洗精煤 润滑油 液化天然气 液化石油气 溶剂油 炼厂干气 热力 焦炉煤气 焦炭 煤制品 煤油 煤矸石 燃料油 电力 石油沥青 石油焦 石脑油 石蜡 转炉煤气 高炉煤气

具体构造过程如下：
对于省级以下地区，首先考虑通过能耗强度和gdp表计算得到地区能源消费总量数据，以此作为各地市总量的约束条件；
然后优先考虑大行业的能源消费（ene）以及工业细分行业（ene2）的能源消费的记录值；
没有记录的，以用电量作为权重，ecam_in_y_pro_ind_ene_off 和 ecam_in_y_pro_ind_ene2_off 中的能源消费（实物量）数据为基数，将省级总量分配到各地市
最后考虑数据一致性，能源折标计算实物量到分地区分行业的标准量，借由ipf，使得地区-行业的数据能够对齐，调整的比例返回到实物量数据上

## 碳排放清单的构造逻辑

基于能源消费清单，以及工业产品产量数据，分别计算能源活动以及工业过程碳排放量数据，行业结构同能源清单一致


## 开发环境要求

序号	项目	版本
1	OS	CentOS 7.X
2	Python	3.7.2（Linux版本）
3	数据库	Mysql5.7
4	Python第三方依赖	按需