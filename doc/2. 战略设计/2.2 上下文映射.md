# 2.2 上下文映射 (Context Map)

## 2.2.1 概述

本上下文映射图清晰地定义了 **区域“电-能-碳”监测分析系统** 的边界，以及它与其所依赖的 **MySQL数据库** 中上下游数据表之间的关系。

遵循领域驱动设计（DDD）的战略原则，我们将整个应用视为一个独立的、核心的 **限界上下文**。这种方法使我们能够保护核心业务逻辑（领域模型）的完整性，避免其被外部数据结构（数据库表 Schema）直接影响。

## 2.2.2 上下文映射图

```mermaid
graph TD
    subgraph Upstream["上游 (MySQL数据库)"]
        RawDataTables[("原始数据表(统计数据, 电网数据等)")]
    end

    subgraph CoreDomain["核心域: 监测分析系统 (应用)"]
        direction LR
        ACL[防腐层 (ACL)(Repository实现)]
        DomainModel("核心领域模型\n(聚合, 实体, 服务)")
        OHS[/"开放主机服务 (OHS)"\n(Repository实现)/]

        ACL --> DomainModel
        DomainModel --> OHS
    end

    subgraph Downstream["下游 (MySQL数据库)"]
        ReportingTables[("报告数据表(最终分析结果)")]
    end

    RawDataTables -- "数据读取" --> ACL
    OHS -- "数据写入" --> ReportingTables

    linkStyle 0 stroke:#6c757d,stroke-dasharray: 5 5;
    linkStyle 1 stroke:#6c757d,stroke-dasharray: 5 5;

    classDef core fill:#e3f2fd,stroke:#1976d2,stroke-width:2px;
    classDef db fill:#e8f5e9,stroke:#388e3c,stroke-width:2px;
    class DomainModel,ACL,OHS core;
    class RawDataTables,ReportingTables db;
```

## 2.2.3 关系与模式详解

### 1. 上游关系: 防腐层 (Anticorruption Layer, ACL)

*   **关系**:
    *   **上游**: `原始数据表` (存储在MySQL中)。
    *   **下游**: `核心领域模型` (我们的应用程序)。
    *   **描述**: 我们的核心应用是上游数据表的“客户”或“消费者”。上游表的结构、命名和范式可能会为了存储效率或其他目的而设计，不一定符合我们领域模型的需要。

*   **集成模式: 防腐层 (ACL)**:
    *   **目的**: 建立一个隔离层，防止上游数据表的“腐败”侵入我们的核心领域模型。
    *   **实现**: 在我们的代码中，**资源库 (Repository) 模式** 是实现ACL的关键。`Repository` 的实现类将负责与数据库交互，执行SQL查询，读取原始数据。然后，它会将这些原始数据**转换并组装**成我们领域模型所能理解的、干净的领域对象（如 `StatisticalReport` 实体、`TimeSeries` 值对象等）。
    *   **价值**: 即使未来原始数据表的结构发生变化（如增减字段、修改表名），我们只需要修改 `Repository` 的实现代码，而核心的领域模型和业务逻辑**完全不受影响**。

### 2. 下游关系: 开放主机服务 (Open Host Service, OHS)

*   **关系**:
    *   **上游**: `核心领域模型` (我们的应用程序)。
    *   **下游**: `报告数据表` (存储在MySQL中)。
    *   **描述**: 我们的核心应用是数据的“生产者”或“供应商”。其他系统（如BI报表、数据分析工具）可能会直接消费这些报告表中的数据。

*   **集成模式: 开放主机服务 (OHS) 与公共语言 (Published Language, PL)**:
    *   **目的**: 定义一套稳定、清晰的数据输出契约，方便下游消费。
    *   **实现**:
        *   **OHS**: 这里的“服务”不是指Web API，而是指我们的应用提供数据输出的这一能力。同样，`Repository` 模式也扮演了OHS的角色，它负责将我们核心领域模型的计算结果（如 `EmissionReport` 实体）**转换并持久化**到下游的报告数据表中。
        *   **PL**: `报告数据表` 的 **表结构 (Schema)** 本身，就是我们向外部发布的“公共语言”。我们承诺这个表的结构是相对稳定的，其字段名、数据类型都是经过深思熟虑的，易于理解和使用。
    *   **价值**: 为下游的数据消费者提供了一个清晰、可靠的数据契约，使得系统集成更加简单、高效。

---
*本上下文映射明确了应用与数据库之间的架构边界，是连接领域模型与数据持久化的核心设计。* 