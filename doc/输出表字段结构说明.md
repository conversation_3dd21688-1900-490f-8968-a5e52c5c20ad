# 碳排放预测拟合结果表字段结构说明（标准格式）

## 概述
本文档详细说明了四个碳排放预测拟合结果表的字段结构，严格按照地市级电-碳计算模型标准模板格式。

## 标准模板要求

### 1. 地市级电-碳计算模型年度碳排放结果上传模板
| 序号 | 字段英文名 | 字段类型 | 字段中文名 | 字段描述 |
|------|-----------|----------|-----------|----------|
| 1 | year | date | 年度 | 例如：2025/1/1 |
| 2 | province | string | 省份名称 | |
| 3 | city | string | 地市名称 | |
| 4 | industry | string | 行业名称 | 例如：服务业、建筑业、交通运输业、居民生活业、工业、能源行业、农林牧渔业、钢铁、化工、建材、其他工业、石化、有色金属、造纸和纸制品 |
| 5 | item_type | string | 项目类型 | 例如：能源活动、工业过程、全社会 |
| 6 | carbon | decimal | 碳排放量 | 单位：吨CO2 |

### 2. 地市级电-碳计算模型月度碳排放结果上传模板
| 序号 | 字段英文名 | 字段类型 | 字段中文名 | 字段描述 |
|------|-----------|----------|-----------|----------|
| 1 | month | date | 月度 | 例如：2025/6/1 |
| 2 | province | string | 省份名称 | |
| 3 | city | string | 地市名称 | |
| 4 | industry | string | 行业名称 | 例如：服务业、建筑业、交通运输业、居民生活业、工业、能源行业、农林牧渔业、钢铁、化工、建材、其他工业、石化、有色金属、造纸和纸制品 |
| 5 | item_type | string | 项目类型 | 例如：能源活动、工业过程、全社会 |
| 6 | carbon | decimal | 碳排放量 | 单位：吨CO2 |

## 实际实现的表结构

### 1. 月度碳排放预测拟合结果表 (ecm_out_month_carbon_pred_fitted)

| 字段英文名 | 字段类型 | 字段中文名 | 字段描述 | 示例值 |
|-----------|----------|-----------|----------|--------|
| month | DATE | 月度 | 统计月份，格式：YYYY/MM/DD | 2020/1/1 |
| province | VARCHAR(100) | 省份名称 | 省份名称 | 山西 |
| city | VARCHAR(100) | 地市名称 | 城市名称 | 太原 |
| industry | VARCHAR(100) | 行业名称 | 行业分类 | 全社会、工业 |
| item_type | VARCHAR(100) | 项目类型 | 排放类型 | 能源活动、工业过程 |
| carbon | DECIMAL(15,2) | 碳排放量 | 碳排放量，单位：吨CO2 | 1645470.12 |

### 2. 年度碳排放预测拟合结果表 (ecm_out_year_carbon_pred_fitted)

| 字段英文名 | 字段类型 | 字段中文名 | 字段描述 | 示例值 |
|-----------|----------|-----------|----------|--------|
| year | DATE | 年度 | 统计年份，格式：YYYY/MM/DD | 2020/1/1 |
| province | VARCHAR(100) | 省份名称 | 省份名称 | 山西 |
| city | VARCHAR(100) | 地市名称 | 城市名称 | 太原 |
| industry | VARCHAR(100) | 行业名称 | 行业分类 | 全社会、工业 |
| item_type | VARCHAR(100) | 项目类型 | 排放类型 | 能源活动、工业过程 |
| carbon | DECIMAL(15,2) | 碳排放量 | 碳排放量，单位：吨CO2 | 19745641.44 |

### 3. 月度碳排放预测拟合结果表（含转移碳排放）(ecm_out_month_carbon_pred_fitted_transfer_carbon)

| 字段英文名 | 字段类型 | 字段中文名 | 字段描述 | 示例值 |
|-----------|----------|-----------|----------|--------|
| month | DATE | 月度 | 统计月份，格式：YYYY/MM/DD | 2020/1/1 |
| province | VARCHAR(100) | 省份名称 | 省份名称 | 山西 |
| city | VARCHAR(100) | 地市名称 | 城市名称 | 太原 |
| industry | VARCHAR(100) | 行业名称 | 行业分类 | 全社会 |
| item_type | VARCHAR(100) | 项目类型 | 排放类型 | 全社会 |
| carbon | DECIMAL(15,2) | 碳排放量 | 基础碳排放量，单位：吨CO2 | 1645470.12 |
| transfer_carbon | DECIMAL(15,2) | 转移碳排放量 | 转移碳排放量，单位：吨CO2 | 493641.04 |

### 4. 年度碳排放预测拟合结果表（含转移碳排放）(ecm_out_year_carbon_pred_fitted_transfer_carbon)

| 字段英文名 | 字段类型 | 字段中文名 | 字段描述 | 示例值 |
|-----------|----------|-----------|----------|--------|
| year | DATE | 年度 | 统计年份，格式：YYYY/MM/DD | 2020/1/1 |
| province | VARCHAR(100) | 省份名称 | 省份名称 | 山西 |
| city | VARCHAR(100) | 地市名称 | 城市名称 | 太原 |
| industry | VARCHAR(100) | 行业名称 | 行业分类 | 全社会 |
| item_type | VARCHAR(100) | 项目类型 | 排放类型 | 全社会 |
| carbon | DECIMAL(15,2) | 碳排放量 | 基础碳排放量，单位：吨CO2 | 19745641.44 |
| transfer_carbon | DECIMAL(15,2) | 转移碳排放量 | 转移碳排放量，单位：吨CO2 | 5923692.43 |

## 字段说明

### 核心字段
- **month/year**: 时间维度，使用DATE类型，格式为YYYY/MM/DD
- **province**: 省份名称，用于地理维度分析
- **city**: 地市名称，用于地理维度分析
- **industry**: 行业名称，支持标准模板中列出的所有行业
- **item_type**: 项目类型，区分不同来源的碳排放

### 数据字段
- **carbon**: 主要碳排放量指标，单位：吨CO2
- **transfer_carbon**: 转移碳排放量，仅含转移碳排放的表包含，单位：吨CO2

### 行业分类说明
根据标准模板，支持的行业包括：
- 服务业
- 建筑业
- 交通运输业
- 居民生活业
- 工业
- 能源行业
- 农林牧渔业
- 钢铁
- 化工
- 建材
- 其他工业
- 石化
- 有色金属
- 造纸和纸制品

### 项目类型说明
- **能源活动**: 由于能源消耗产生的碳排放
- **工业过程**: 工业生产过程中产生的碳排放
- **全社会**: 包含转移碳排放的综合碳排放

## 数据生成逻辑

### 1. 数据转换
- **单位转换**: 从万吨CO2转换为吨CO2（乘以10000）
- **精度调整**: 保留2位小数
- **时间格式**: 统一使用YYYY/MM/DD格式

### 2. 数据特点
- **月度数据**: 基于城市、年份、月份生成，包含季节性变化
- **年度数据**: 月度数据的汇总
- **转移碳排放**: 基于基础碳排放的30%比例计算
- **数据合理性**: 包含城市差异、年度增长趋势、随机波动等因素

### 3. 数据量统计
- **月度表**: 264条记录（11个城市 × 12个月 × 2种排放类型）
- **年度表**: 22条记录（11个城市 × 2种排放类型）
- **月度含转移碳排放**: 132条记录（11个城市 × 12个月）
- **年度含转移碳排放**: 11条记录（11个城市）

## 符合性验证

### 1. 字段类型符合性
✅ 所有字段类型严格按照标准模板要求
✅ DATE类型用于时间字段
✅ VARCHAR(100)用于字符串字段
✅ DECIMAL(15,2)用于数值字段

### 2. 字段名称符合性
✅ 字段英文名与标准模板完全一致
✅ 字段中文名与标准模板完全一致
✅ 字段描述与标准模板完全一致

### 3. 数据格式符合性
✅ 时间格式：YYYY/MM/DD
✅ 单位：吨CO2
✅ 精度：2位小数
✅ 行业分类：支持标准模板中的所有行业
✅ 项目类型：支持标准模板中的所有类型

## 应用场景

### 1. 政策制定
- 地市级碳排放目标设定
- 行业碳排放配额分配
- 碳交易市场设计

### 2. 企业决策
- 工业企业碳排放管理
- 节能减排技术选择
- 碳足迹评估

### 3. 学术研究
- 地市级碳排放趋势分析
- 行业间碳排放比较
- 碳排放影响因素研究
