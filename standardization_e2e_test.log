2025-09-03 09:55:04,228 - __main__ - INFO - ================================================================================
2025-09-03 09:55:04,228 - __main__ - INFO - 数据标准化端到端测试开始
2025-09-03 09:55:04,228 - __main__ - INFO - ================================================================================
2025-09-03 09:55:04,229 - __main__ - INFO - 步骤1: 初始化组件
2025-09-03 09:55:04,260 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:55:04,260 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:55:04,260 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:55:04,260 - __main__ - INFO - ✓ 组件初始化完成
2025-09-03 09:55:04,260 - __main__ - INFO - 
步骤2: 读取原始数据
2025-09-03 09:55:04,260 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:55:04,292 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:55:04,303 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:55:04,304 - __main__ - INFO - ✓ 表 '排放因子' 读取成功: 273 行
2025-09-03 09:55:04,304 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:55:04,324 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:55:04,448 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:55:04,449 - __main__ - INFO - ✓ 表 '能源消费' 读取成功: 25760 行
2025-09-03 09:55:04,449 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:55:04,467 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:55:04,970 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:55:04,971 - __main__ - INFO - ✓ 表 '地市工业能源消费' 读取成功: 46535 行
2025-09-03 09:55:04,971 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:55:04,990 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:55:05,808 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:55:05,809 - __main__ - INFO - ✓ 表 '用电量' 读取成功: 74496 行
2025-09-03 09:55:05,809 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:55:05,827 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:55:05,864 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:55:05,865 - __main__ - INFO - ✓ 表 'GDP数据' 读取成功: 3360 行
2025-09-03 09:55:05,865 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:55:05,883 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:55:05,887 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:55:05,888 - __main__ - INFO - ✓ 表 '能耗强度' 读取成功: 204 行
2025-09-03 09:55:05,888 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:55:05,908 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:55:05,929 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:55:05,930 - __main__ - INFO - ✓ 表 '工业产品产量' 读取成功: 1812 行
2025-09-03 09:55:05,930 - __main__ - INFO - ✓ 原始数据读取完成，共 7 个数据源
2025-09-03 09:55:05,930 - __main__ - INFO - 
步骤3: 执行数据标准化
2025-09-03 09:55:05,930 - __main__ - INFO - ------------------------------------------------------------
2025-09-03 09:55:05,930 - ecam_calculator.domain.service.data_standardization_service - INFO - 开始标准化 7 个数据源
2025-09-03 09:55:05,930 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:55:05,930 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:55:05,930 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:55:05,930 - ecam_calculator.domain.service.data_standardization_service - INFO - 数据源 '排放因子' 标准化完成，产出 273 行数据
2025-09-03 09:55:05,932 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:55:06,178 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:55:06,179 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:55:06,179 - ecam_calculator.domain.service.data_standardization_service - INFO - 数据源 '能源消费' 标准化完成，产出 25760 行数据
2025-09-03 09:55:06,182 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: 地市工业能源消费
2025-09-03 09:55:06,182 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:55:06,182 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 '地市工业能源消费' 标准化完成，输出 46535 行数据
2025-09-03 09:55:06,182 - ecam_calculator.domain.service.data_standardization_service - INFO - 数据源 '地市工业能源消费' 标准化完成，产出 46535 行数据
2025-09-03 09:55:06,184 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:55:07,159 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.data_standardization_service - INFO - 数据源 '用电量' 标准化完成，产出 74496 行数据
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.data_standardization_service - INFO - 数据源 'GDP数据' 标准化完成，产出 3360 行数据
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.data_standardization_service - INFO - 数据源 '能耗强度' 标准化完成，产出 204 行数据
2025-09-03 09:55:07,160 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:55:07,161 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:55:07,161 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:55:07,161 - ecam_calculator.domain.service.data_standardization_service - INFO - 数据源 '工业产品产量' 标准化完成，产出 1812 行数据
2025-09-03 09:55:07,161 - __main__ - INFO - 
步骤4: 标准化结果统计
2025-09-03 09:55:07,161 - __main__ - INFO - ------------------------------------------------------------
2025-09-03 09:55:07,161 - __main__ - INFO - 原始数据总行数: 152,440
2025-09-03 09:55:07,161 - __main__ - INFO - 标准化后总行数: 152,440
2025-09-03 09:55:07,161 - __main__ - INFO - 数据完整性保持率: 100.0%
2025-09-03 09:55:07,161 - __main__ - INFO - 
步骤5: 各表标准化详情
2025-09-03 09:55:07,161 - __main__ - INFO - ------------------------------------------------------------
2025-09-03 09:55:07,162 - __main__ - INFO - 表 '排放因子':
2025-09-03 09:55:07,162 - __main__ - INFO -   原始行数: 273
2025-09-03 09:55:07,162 - __main__ - INFO -   标准化行数: 273
2025-09-03 09:55:07,162 - __main__ - INFO -   标准化列数: 0
2025-09-03 09:55:07,162 - __main__ - INFO -   层次关系列数: 0
2025-09-03 09:55:07,162 - __main__ - INFO -   数据样本:
2025-09-03 09:55:07,162 - __main__ - INFO -     year: 2023-01-01 | area: 全国 | industry: 供热
2025-09-03 09:55:07,162 - __main__ - INFO -     year: 2023-01-01 | area: 全国 | industry: 供热
2025-09-03 09:55:07,162 - __main__ - INFO - 
2025-09-03 09:55:07,162 - __main__ - INFO - 表 '能源消费':
2025-09-03 09:55:07,162 - __main__ - INFO -   原始行数: 25,760
2025-09-03 09:55:07,163 - __main__ - INFO -   标准化行数: 25,760
2025-09-03 09:55:07,163 - __main__ - INFO -   标准化列数: 3
2025-09-03 09:55:07,163 - __main__ - INFO -   层次关系列数: 3
2025-09-03 09:55:07,163 - __main__ - INFO -     standard_province: 25,760/25,760 (100.0%)
2025-09-03 09:55:07,164 - __main__ - INFO -     standard_item: 7,360/25,760 (28.6%)
2025-09-03 09:55:07,164 - __main__ - INFO -     standard_energy_type: 12,075/25,760 (46.9%)
2025-09-03 09:55:07,165 - __main__ - INFO -   数据样本:
2025-09-03 09:55:07,165 - __main__ - INFO -     year: 2000 | standard_province: 山西
2025-09-03 09:55:07,166 - __main__ - INFO -     year: 2000 | standard_province: 山西 | standard_energy_type: 原煤
2025-09-03 09:55:07,166 - __main__ - INFO - 
2025-09-03 09:55:07,166 - __main__ - INFO - 表 '地市工业能源消费':
2025-09-03 09:55:07,166 - __main__ - INFO -   原始行数: 46,535
2025-09-03 09:55:07,166 - __main__ - INFO -   标准化行数: 46,535
2025-09-03 09:55:07,166 - __main__ - INFO -   标准化列数: 0
2025-09-03 09:55:07,166 - __main__ - INFO -   层次关系列数: 0
2025-09-03 09:55:07,168 - __main__ - INFO -   数据样本:
2025-09-03 09:55:07,168 - __main__ - INFO -     area: 大同 | industry: 全部
2025-09-03 09:55:07,168 - __main__ - INFO -     area: 大同 | industry: 全部
2025-09-03 09:55:07,168 - __main__ - INFO - 
2025-09-03 09:55:07,168 - __main__ - INFO - 表 '用电量':
2025-09-03 09:55:07,168 - __main__ - INFO -   原始行数: 74,496
2025-09-03 09:55:07,168 - __main__ - INFO -   标准化行数: 74,496
2025-09-03 09:55:07,168 - __main__ - INFO -   标准化列数: 2
2025-09-03 09:55:07,168 - __main__ - INFO -   层次关系列数: 2
2025-09-03 09:55:07,170 - __main__ - INFO -     standard_area: 74,496/74,496 (100.0%)
2025-09-03 09:55:07,171 - __main__ - INFO -     standard_industry: 19,382/74,496 (26.0%)
2025-09-03 09:55:07,174 - __main__ - INFO -   数据样本:
2025-09-03 09:55:07,175 - __main__ - INFO -     area: 临汾 | industry: B、城乡居民生活用电量合计 | standard_area: 临汾
2025-09-03 09:55:07,175 - __main__ - INFO -     area: 临汾 | industry: 八、金融业用电量 | standard_area: 临汾
2025-09-03 09:55:07,176 - __main__ - INFO - 
2025-09-03 09:55:07,176 - __main__ - INFO - 表 'GDP数据':
2025-09-03 09:55:07,176 - __main__ - INFO -   原始行数: 3,360
2025-09-03 09:55:07,176 - __main__ - INFO -   标准化行数: 3,360
2025-09-03 09:55:07,176 - __main__ - INFO -   标准化列数: 0
2025-09-03 09:55:07,176 - __main__ - INFO -   层次关系列数: 0
2025-09-03 09:55:07,178 - __main__ - INFO -   数据样本:
2025-09-03 09:55:07,178 - __main__ - INFO -     year: 2005
2025-09-03 09:55:07,178 - __main__ - INFO -     year: 2005
2025-09-03 09:55:07,178 - __main__ - INFO - 
2025-09-03 09:55:07,178 - __main__ - INFO - 表 '能耗强度':
2025-09-03 09:55:07,178 - __main__ - INFO -   原始行数: 204
2025-09-03 09:55:07,178 - __main__ - INFO -   标准化行数: 204
2025-09-03 09:55:07,178 - __main__ - INFO -   标准化列数: 0
2025-09-03 09:55:07,178 - __main__ - INFO -   层次关系列数: 0
2025-09-03 09:55:07,179 - __main__ - INFO -   数据样本:
2025-09-03 09:55:07,179 - __main__ - INFO -     year: 2007 | area: 临汾
2025-09-03 09:55:07,180 - __main__ - INFO -     year: 2007 | area: 吕梁
2025-09-03 09:55:07,180 - __main__ - INFO - 
2025-09-03 09:55:07,180 - __main__ - INFO - 表 '工业产品产量':
2025-09-03 09:55:07,180 - __main__ - INFO -   原始行数: 1,812
2025-09-03 09:55:07,180 - __main__ - INFO -   标准化行数: 1,812
2025-09-03 09:55:07,180 - __main__ - INFO -   标准化列数: 0
2025-09-03 09:55:07,180 - __main__ - INFO -   层次关系列数: 0
2025-09-03 09:55:07,180 - __main__ - INFO -   数据样本:
2025-09-03 09:55:07,180 - __main__ - INFO -     year: 2009 | area: 临汾 | industry: 未知
2025-09-03 09:55:07,181 - __main__ - INFO -     year: 2009 | area: 临汾 | industry: 未知
2025-09-03 09:55:07,181 - __main__ - INFO - 
2025-09-03 09:55:07,181 - __main__ - INFO - 
步骤6: 主程序运行输出格式
2025-09-03 09:55:07,181 - __main__ - INFO - ------------------------------------------------------------
2025-09-03 09:55:07,181 - __main__ - INFO - 主程序标准化步骤输出:
2025-09-03 09:55:07,181 - __main__ - INFO - 
2025-09-03 09:55:07,182 - __main__ - INFO - 表 用电量:
2025-09-03 09:55:07,182 - __main__ - INFO -   总数据: 74,496 行
2025-09-03 09:55:07,182 - __main__ - INFO -   成功标准化: 19,382 行 (26.0%)
2025-09-03 09:55:07,183 - __main__ - INFO -   标准化分布 (前5):
2025-09-03 09:55:07,183 - __main__ - INFO -     其他工业: 9,548 行
2025-09-03 09:55:07,184 - __main__ - INFO -     工业: 3,047 行
2025-09-03 09:55:07,184 - __main__ - INFO -     能源行业: 2,728 行
2025-09-03 09:55:07,184 - __main__ - INFO -     钢铁: 1,364 行
2025-09-03 09:55:07,184 - __main__ - INFO -     有色: 1,331 行
2025-09-03 09:55:07,184 - __main__ - INFO - 
2025-09-03 09:55:07,184 - __main__ - INFO - 
步骤7: 成功标准化数据摘要
2025-09-03 09:55:07,184 - __main__ - INFO - ------------------------------------------------------------
2025-09-03 09:55:07,211 - __main__ - INFO - 成功标准化的数据:
2025-09-03 09:55:07,212 - __main__ - INFO -   能源消费: 25,760 行
2025-09-03 09:55:07,212 - __main__ - INFO -   用电量: 74,496 行
2025-09-03 09:55:07,212 - __main__ - INFO - 总计: 100,256 行
2025-09-03 09:55:07,212 - __main__ - INFO - 
================================================================================
2025-09-03 09:55:07,212 - __main__ - INFO - 数据标准化端到端测试完成
2025-09-03 09:55:07,212 - __main__ - INFO - ================================================================================
