MANIFEST.in
README.md
pyproject.toml
requirements.txt
setup.py
doc/4. 技术实现路线图.md
doc/bu-chong-ling-yu-zhi-shi.md
doc/补充领域知识.md
doc/1. 业务与领域分析/1.1 领域概述.md
doc/1. 业务与领域分析/1.2 事件风暴.md
doc/1. 业务与领域分析/1.3 统一语言.md
doc/1. 业务与领域分析/1.4 业务规则.md
doc/2. 战略设计/2.1 系统概述.md
doc/2. 战略设计/2.2 上下文映射.md
doc/3. 战术设计/数据流设计.md
doc/3. 战术设计/标准化重构方案.md
doc/3. 战术设计/项目规则.md
doc/3. 战术设计/基础设施层 Infrastructure/数据访问对象 DAO.md
doc/3. 战术设计/基础设施层 Infrastructure/资源库 Repositories.md
doc/3. 战术设计/应用服务 Application Services/1. 应用服务概述.md
doc/3. 战术设计/领域模型 Domain Model/数据模型 Data Model.md
doc/3. 战术设计/领域模型 Domain Model/3.1 实体 Entity/3.1.1 实体总览.md
doc/3. 战术设计/领域模型 Domain Model/3.1 实体 Entity/3.1.2 核算任务实体.md
doc/3. 战术设计/领域模型 Domain Model/3.1 实体 Entity/3.1.3 预测结果实体.md
doc/3. 战术设计/领域模型 Domain Model/3.2 值对象 Value Objects/3.2.0 值对象总览.md
doc/3. 战术设计/领域模型 Domain Model/3.2 值对象 Value Objects/3.2.1 质量已检数据值对象.md
doc/3. 战术设计/领域模型 Domain Model/3.2 值对象 Value Objects/3.2.2 标准化数据值对象.md
doc/3. 战术设计/领域模型 Domain Model/3.2 值对象 Value Objects/3.2.3 能源消费清单值对象.md
doc/3. 战术设计/领域模型 Domain Model/3.2 值对象 Value Objects/3.2.4 碳排放清单值对象.md
doc/3. 战术设计/领域模型 Domain Model/3.2 值对象 Value Objects/3.2.5 预测模型值对象.md
doc/3. 战术设计/领域模型 Domain Model/3.2 值对象 Value Objects/3.2.6 最终排放数据值对象.md
doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.0 核心领域服务总览.md
doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.2 质量检查服务.md
doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.3 数据标准化服务.md
doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.4 清单构造服务.md
doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.5 模型构建器服务.md
doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.6 预测分析器服务.md
doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.7 数据融合器服务.md
doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.8 结果输出器服务.md
doc/3. 战术设计/领域模型 Domain Model/3.4 领域事件 Domain Events/1. 领域事件概述.md
doc/3. 战术设计/领域模型 Domain Model/3.5 领域服务 Domain Services/1. 领域服务概述.md
doc/3. 战术设计/领域模型 Domain Model/3.5 领域服务 Domain Services/1.-ling-yu-fu-wu-gai-shu.md
doc/3. 战术设计/领域模型 Domain Model/3.5 领域服务 Domain Services/数据标准化服务重构设计.md
doc/3. 战术设计/领域模型 Domain Model/3.7 模块 Modules/1. 核心核算模块.md
doc/附录/数据关系.md
ecam_calculator/__init__.py
ecam_calculator/main.py
ecam_calculator.egg-info/PKG-INFO
ecam_calculator.egg-info/SOURCES.txt
ecam_calculator.egg-info/dependency_links.txt
ecam_calculator.egg-info/entry_points.txt
ecam_calculator.egg-info/not-zip-safe
ecam_calculator.egg-info/requires.txt
ecam_calculator.egg-info/top_level.txt
ecam_calculator/application/__init__.py
ecam_calculator/application/calculation_job_service.py
ecam_calculator/application/error_handling_service.py
ecam_calculator/application/performance_monitoring_service.py
ecam_calculator/config/parameters.yaml
ecam_calculator/domain/__init__.py
ecam_calculator/domain/model/__init__.py
ecam_calculator/domain/model/calculation_job.py
ecam_calculator/domain/model/entities.py
ecam_calculator/domain/model/job_status.py
ecam_calculator/domain/model/quality_checker.py
ecam_calculator/domain/model/value_objects.py
ecam_calculator/domain/repository/job_repository.py
ecam_calculator/domain/repository/raw_data_repository.py
ecam_calculator/domain/service/__init__.py
ecam_calculator/domain/service/balancing_service.py
ecam_calculator/domain/service/constraint_calculation_service.py
ecam_calculator/domain/service/data_standardization_service.py
ecam_calculator/domain/service/inventory_construction_service.py
ecam_calculator/domain/service/quality_check_service.py
ecam_calculator/domain/service/result_output_service.py
ecam_calculator/domain/service/standardization_functions.py
ecam_calculator/domain/service/standardization_orchestrator.py
ecam_calculator/infrastructure/__init__.py
ecam_calculator/infrastructure/carbon_emission_service.py
ecam_calculator/infrastructure/config_reader.py
ecam_calculator/infrastructure/energy_conversion_service.py
ecam_calculator/infrastructure/memory_calculation_job_repository.py
ecam_calculator/infrastructure/raw_data_repository_impl.py
ecam_calculator/infrastructure/persistence/__init__.py
ecam_calculator/infrastructure/persistence/job_repository_impl.py
ecam_calculator/infrastructure/services/__init__.py
ecam_calculator/infrastructure/services/constraint_calculation_service_impl.py
ecam_calculator/infrastructure/services/data_standardization_service_impl.py
ecam_calculator/infrastructure/services/inventory_construction_service_impl.py
ecam_calculator/infrastructure/services/quality_check_service_impl.py
ecam_calculator/infrastructure/services/result_output_service_impl.py