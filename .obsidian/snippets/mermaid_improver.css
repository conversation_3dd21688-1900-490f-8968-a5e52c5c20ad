/* Mermaid图表增强交互方案 - 纯CSS优化版 */

/* 基础容器设置 */
.mermaid {
  position: relative;
  overflow: hidden; /* 隐藏滚动条 */
  background: rgba(250, 250, 250, 0.35);
  border-radius: 10px;
  padding: 16px;
  margin: 2rem 0;
  transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
  max-height: 75vh;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  cursor: grab; /* 指示可拖动 */
  /* 启用硬件加速 */
  will-change: transform, background, box-shadow;
  /* 隔离创建新的堆叠上下文，提高渲染性能 */
  isolation: isolate;
}

/* 图表容器 */
.mermaid-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

/* 默认状态 - 缩小显示整图 */
.mermaid > svg {
  display: block;
  margin: 0 auto;
  max-width: 100%;
  height: auto;
  transform: scale(0.7);
  transform-origin: center center; /* 改为中心缩放 */
  transition: transform 0.4s cubic-bezier(0.22, 1, 0.36, 1);
  /* 减少重绘 */
  will-change: transform;
}

/* 悬停状态 - 放大到正常大小 */
.mermaid:hover > svg {
  transform: scale(1);
}

/* 悬停时容器样式 */
.mermaid:hover {
  background: rgba(250, 250, 250, 0.5);
  border-color: rgba(0, 0, 0, 0.12);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 模拟拖动状态 */
.mermaid:active {
  cursor: grabbing; /* 指示正在拖动 */
  transition: background 0.3s ease;
}

/* 完全隐藏滚动条 */
.mermaid::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}

/* 深色模式适配 */
.theme-dark .mermaid {
  background: rgba(30, 30, 30, 0.35);
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.theme-dark .mermaid:hover {
  background: rgba(35, 35, 35, 0.55);
  border-color: rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25), 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 增强图表交互 - 节点和边缘高亮 */
.mermaid g.node rect, 
.mermaid g.node circle,
.mermaid g.node ellipse, 
.mermaid g.node polygon,
.mermaid g.node path {
  transition: filter 0.2s ease, opacity 0.2s ease, stroke-width 0.2s ease;
}

.mermaid g.edgePath path {
  transition: stroke-width 0.2s ease, filter 0.2s ease, opacity 0.2s ease;
}

/* 节点悬停效果 */
.mermaid g.node:hover rect,
.mermaid g.node:hover circle, 
.mermaid g.node:hover ellipse,
.mermaid g.node:hover polygon,
.mermaid g.node:hover path {
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.2));
  stroke-width: 2px;
}

.theme-dark .mermaid g.node:hover rect,
.theme-dark .mermaid g.node:hover circle, 
.theme-dark .mermaid g.node:hover ellipse,
.theme-dark .mermaid g.node:hover polygon,
.theme-dark .mermaid g.node:hover path {
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
}

/* 边缘悬停效果 */
.mermaid g.edgePath:hover path {
  stroke-width: 2.5px;
}

/* 添加加载状态样式 */
.mermaid:not(:has(svg)) {
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mermaid:not(:has(svg))::after {
  content: "加载中...";
  font-size: 14px;
  color: rgba(0, 0, 0, 0.5);
}

.theme-dark .mermaid:not(:has(svg))::after {
  color: rgba(255, 255, 255, 0.5);
}

/* 图表尺寸类型支持 */
/* 超大复杂图表 */
.mermaid.diagram-xl > svg {
  transform: scale(0.45);
}

.mermaid.diagram-xl:hover > svg {
  transform: scale(0.85);
}

/* 大型图表 */
.mermaid.diagram-lg > svg {
  transform: scale(0.6);
}

.mermaid.diagram-lg:hover > svg {
  transform: scale(0.9);
}

/* 标准图表 (默认) */

/* 小型图表 */
.mermaid.diagram-sm > svg {
  transform: scale(0.8);
}

.mermaid.diagram-sm:hover > svg {
  transform: scale(1);
}

/* 微型图表 */
.mermaid.diagram-xs {
  padding: 12px;
}

.mermaid.diagram-xs > svg {
  transform: scale(0.9);
}

.mermaid.diagram-xs:hover > svg {
  transform: scale(1.1);
}

/* 主题切换优化 */
@media (prefers-color-scheme: dark) {
  .mermaid.theme-auto {
    background: rgba(30, 30, 30, 0.35);
    border-color: rgba(255, 255, 255, 0.05);
  }
  
  .mermaid.theme-auto:hover {
    background: rgba(35, 35, 35, 0.55);
    border-color: rgba(255, 255, 255, 0.08);
  }
}

/* 触摸板增强支持 - 提高CSS对手势的响应能力 */
.mermaid {
  touch-action: pinch-zoom; /* 启用触控板的缩放手势 */
}

/* 交互细节优化 */
.mermaid:active > svg {
  transition-duration: 0.1s;
}

/* 鼠标活动状态视觉反馈 */
.mermaid:active {
  background-color: rgba(245, 245, 245, 0.9);
  transform: scale(1.005); /* 非常微小的放大效果 */
}

.theme-dark .mermaid:active {
  background-color: rgba(40, 40, 40, 0.6);
}

/* 键盘焦点可访问性 */
.mermaid:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.6);
}

.theme-dark .mermaid:focus {
  box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.6);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .mermaid {
    padding: 12px;
  }
  
  .mermaid > svg {
    transform: scale(0.6);
  }
  
  .mermaid:hover > svg {
    transform: scale(0.9);
  }
}

/* 打印优化 */
@media print {
  .mermaid {
    break-inside: avoid;
    background: transparent;
    border: 1px solid #ddd;
    box-shadow: none;
    max-height: none;
    overflow: visible;
  }
  
  .mermaid > svg {
    transform: scale(1);
  }
}

/* 动画速度控制类 */
.mermaid.transition-fast > svg {
  transition-duration: 0.25s;
}

.mermaid.transition-slow > svg {
  transition-duration: 0.6s;
}

/* 图表标题支持 */
.mermaid-title {
  text-align: center;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  opacity: 0.8;
  font-style: italic;
}

/* 图表脚注支持 */
.mermaid-caption {
  text-align: center;
  font-size: 0.8rem;
  margin-top: 0.5rem;
  opacity: 0.7;
}

/* 添加平滑的拖动惯性效果 - 使用纯CSS模拟 */
@keyframes drag-inertia {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(var(--drag-x, 0)) translateY(var(--drag-y, 0)); }
}

.mermaid:active > svg {
  animation: drag-inertia 0.1s ease-out forwards paused;
}

/* 放大缩小提示区域 */
.mermaid::before {
  content: "";
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.mermaid:hover::before {
  opacity: 0.5;
}

.theme-dark .mermaid::before {
  border-color: rgba(255, 255, 255, 0.2);
}