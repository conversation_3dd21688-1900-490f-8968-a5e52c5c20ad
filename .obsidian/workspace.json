{"main": {"id": "9d1e7680a58db675", "type": "split", "children": [{"id": "70b22cd2a30cc677", "type": "tabs", "children": [{"id": "f5f3cd9d0b3a4a3f", "type": "leaf", "state": {"type": "markdown", "state": {"file": "doc/补充领域知识.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "补充领域知识"}}]}], "direction": "vertical"}, "left": {"id": "2b268ca14f7ca3c9", "type": "split", "children": [{"id": "75e6925da857bced", "type": "tabs", "children": [{"id": "8dde71b37bed9e16", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "149b40c24f333ba8", "type": "leaf", "state": {"type": "search", "state": {"query": "规则", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "546cb22ded173d64", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}]}], "direction": "horizontal", "width": 330.5}, "right": {"id": "30f6b753eb33f022", "type": "split", "children": [{"id": "92fb949ea2b5b4b1", "type": "tabs", "children": [{"id": "0785e265ee688361", "type": "leaf", "state": {"type": "outline", "state": {"file": "1. 业务与领域分析/1.2 事件风暴.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "1.2 事件风暴 的大纲"}}, {"id": "bb5d54cfaded913f", "type": "leaf", "state": {"type": "backlink", "state": {"file": "1. 业务与领域分析/1.2 事件风暴.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "1.2 事件风暴 的反向链接列表"}}, {"id": "016057a21bcb6404", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "1. 业务与领域分析/1.2 事件风暴.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "1.2 事件风暴 的出链列表"}}, {"id": "f1b2989d8cafbd02", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "6a46e98d23796dee", "type": "leaf", "state": {"type": "infio-chat-view", "state": {}, "icon": "wand-sparkles", "title": "Infio chat"}}, {"id": "1e463eb5a9389520", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency", "showSearch": false, "searchQuery": ""}, "icon": "lucide-archive", "title": "添加笔记属性"}}, {"id": "f59702ce1c1b3892", "type": "leaf", "state": {"type": "footnotes", "state": {"file": "1. 业务与领域分析/1.2 事件风暴.md"}, "icon": "lucide-file-signature", "title": "Footnotes"}}, {"id": "6f0de39ae0712d57", "type": "leaf", "state": {"type": "file-properties", "state": {"file": "1. 业务与领域分析/1.2 事件风暴.md"}, "icon": "lucide-info", "title": "1.2 事件风暴的笔记属性"}}], "currentTab": 7}], "direction": "horizontal", "width": 365.5, "collapsed": true}, "left-ribbon": {"hiddenItems": {"infio-copilot:打开 Infio Copilot": false, "switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "markdown-table-editor:Open Markdown Table Editor": false, "workspaces:管理工作区布局": false}}, "active": "f5f3cd9d0b3a4a3f", "lastOpenFiles": ["results/d647ca09-9bc1-4752-85e0-eafa1a67e8ea/energy_consumption_inventory.csv", "results/d647ca09-9bc1-4752-85e0-eafa1a67e8ea/carbon_emission_inventory.csv", "results/d647ca09-9bc1-4752-85e0-eafa1a67e8ea", "results/50e47eef-8843-4141-9e4e-0eec8dc5e8dc/energy_consumption_inventory.csv", "results/50e47eef-8843-4141-9e4e-0eec8dc5e8dc/carbon_emission_inventory.csv", "results/50e47eef-8843-4141-9e4e-0eec8dc5e8dc", "results/8852567e-d036-40f6-b45c-94dfa9a522e0/energy_consumption_inventory.csv", "results/8852567e-d036-40f6-b45c-94dfa9a522e0/carbon_emission_inventory.csv", "results/8852567e-d036-40f6-b45c-94dfa9a522e0", "results/2c58b19e-e088-413a-89cd-e1b3f7b9d0ac/energy_consumption_inventory.csv", "results/2c58b19e-e088-413a-89cd-e1b3f7b9d0ac/carbon_emission_inventory.csv", "doc/3. - 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.7 数据融合器服务.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.8 结果输出器服务.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.7 数据融合器服务.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.6 预测分析器服务.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.5 模型构建器服务.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.4 清单构造服务.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.3 数据标准化服务.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.2 质量检查服务.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.0 核心领域服务总览.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.8 结果输出器聚合.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.7 数据融合器聚合.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.6 预测分析器聚合.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.5 模型构建器聚合.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.3 数据标准化器聚合.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.4 清单构造器聚合.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.2 质量检查器聚合.md", "doc/3. 战术设计/领域模型 Domain Model/3.3 核心领域服务/3.3.0 聚合总览.md", "ecam_deploy/README.md", "Linux安装说明.md", "INSTALL.md", "README.md", "doc/重构实施/重构进度跟踪.md", "doc/重构实施/技术债务清单.md", "doc/重构实施/重构实施说明.md", "doc/3. 战术设计/领域模型 Domain Model/数据模型 Data Model.md", "images/test-architecture-light.svg", "images/test-architecture-dark.svg", "images/test-architecture.svg", "diagrams/output/3-tactical/overview/tactical-design-overview.svg", "diagrams/output/3-tactical/overview/tactical-design-overview.png", "diagrams/output/3-tactical/aggregates/quality-assessment-context.svg", "diagrams/output/3-tactical/aggregates/quality-assessment-context.png", "diagrams/output/3-tactical/aggregates/quality-job-states.svg", "diagrams/output/3-tactical/aggregates/quality-job-states.png", "未命名.canvas"]}