.iconize-inline-title-wrapper {
  width: var(--line-width);
  max-width: var(--max-width);
  margin-inline: var(--content-margin);
}

.iconize-title-icon {
  max-width: var(--max-width);
  margin-right: var(--size-4-2);
}

.iconize-icon-in-link {
  transform: translateY(20%);
  margin-right: var(--size-2-2);
  display: inline-flex;
}

.iconize-icon {
  border: 1px solid transparent;
  margin: 0px 4px 0px 0px;
  display: flex;
  align-self: center;
  margin: auto 0;
}

.nav-folder-title,
.nav-file-title {
  align-items: center;
}

.iconize-setting input[type='color'] {
  margin: 0 6px;
}

.iconize-modal.prompt-results {
  margin: 0;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.prompt .iconize-subheadline {
  margin-top: 12px;
  font-size: 12px;
  color: gray;
  grid-column-start: 1;
  grid-column-end: 6;
}

@media (max-width: 640px) {
  .iconize-modal.prompt-results {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .prompt .iconize-subheadline {
    grid-column-end: 4;
  }
}

.iconize-modal.prompt-results .suggestion-item {
  cursor: pointer;
  white-space: pre-wrap;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-direction: column-reverse;
  text-align: center;
  font-size: 13px;
  color: var(--text-muted);
  padding: 16px 8px;
  line-break: auto;
  word-break: break-word;
  line-height: 1.3;
}

.iconize-modal.prompt-results .suggestion-item.suggestion-item__center {
  justify-content: center;
}

.iconize-icon-preview {
  font-size: 22px;
}

.iconize-icon-preview img {
  width: 16px;
  height: 16px;
}

.iconize-icon-preview svg {
  width: 24px;
  height: 24px;
  color: currentColor;
  margin-bottom: 4px;
}

.iconize-dragover {
  position: relative;
}

.iconize-dragover-el {
  position: absolute;
  width: 100%;
  height: 100%;
  color: var(--text-normal);
  background-color: var(--background-secondary-alt);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Custom rule modal. */
.iconize-custom-modal .modal-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconize-custom-modal .modal-content input {
  width: 100%;
  margin-right: 0.5rem;
}
