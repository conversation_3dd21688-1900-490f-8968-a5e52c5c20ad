/**
These are the styles for the plugin settings window.
Partially replaces by Obsidian styles.
**/
.diagram-zoom-drag-settings {
    padding: 16px;
    overflow-y: scroll;
}

.diagram-zoom-drag-settings .react-obsidian-settings-item {
    padding: 12px;
    margin-bottom: 12px;
    transition: box-shadow 0.3s ease;
    border-bottom: 1px solid var(--color-base-30);
}

.diagram-zoom-drag-settings
    .react-obsidian-settings-item
    .setting-item
    .button-active {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}

.diagram-zoom-drag-settings .react-obsidian-settings-item:last-child {
    border-bottom: none;
}

.diagram-zoom-drag-settings .react-obsidian-settings-item.no-border {
    border-bottom: none !important;
}

.diagram-zoom-drag-settings input[type='text'],
.diagram-zoom-drag-settings input[type='number'] {
    background-color: var(--background-secondary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    padding: 6px 8px;
    width: 100%;
}

.diagram-zoom-drag-settings .clickable-icon {
    color: var(--text-muted);
    transition: color 0.2s ease;
}

.diagram-zoom-drag-settings .clickable-icon:hover {
    color: var(--text-normal);
}

.diagram-zoom-drag-settings .invalid {
    border: 2px solid red !important;
}

.diagram-zoom-drag-settings .shake {
    animation: shake 0.5s;
}

@keyframes shake {
    0% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    50% {
        transform: translateX(5px);
    }
    75% {
        transform: translateX(-5px);
    }
    100% {
        transform: translateX(0);
    }
}



.diagram-container {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: grab;
}

.diagram-container[data-diagram-zoom-drag-rendering-mode='preview'] {
    margin-bottom: 40px;
}


.diagram-container.folded {
}

.diagram-container.is-fullscreen {
    background-color: var(--background-primary);
}

.diagram-content {
    position: relative;
    transform-origin: center;
    cursor: grab;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.diagram-zoom-drag-panel {
    position: absolute;
    display: grid;
    gap: 5px;
    background: rgba(var(--background-primary-rgb), 0.7);
    padding: 5px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.diagram-zoom-drag-panel.visible {
    visibility: visible;
    transition: opacity 0.5s ease;
}

.diagram-zoom-drag-panel.hidden {
    visibility: hidden;
    transition: opacity 0.5s ease;
    cursor: grab;
}

.diagram-zoom-drag-panel.hidden > button {
    cursor: grab;
    pointer-events: none;
}

.diagram-container.folded
    .diagram-service-panel.diagram-zoom-drag-panel.hidden
    #fullscreen-button {
    visibility: visible;
    pointer-events: all;
}

.live-preview-parent {
    all: unset !important;          /* Полный сброс стилей */
    display: block !important;      /* Блочный поток */
    width: auto !important;         /* Подстраивается под детей */
    height: auto !important;        /* Подстраивается под детей */
    min-width: 0 !important;        /* Разрешает сжатие */
    min-height: 0 !important;       /* Разрешает сжатие */
    overflow: visible !important;   /* Отключает обрезку */
    contain: style !important;      /* Блокирует влияние внешних стилей */
    padding-bottom: 28px !important;
    transition: all 0.3s ease;
}
.live-preview {
    margin-top: 28px;
}
