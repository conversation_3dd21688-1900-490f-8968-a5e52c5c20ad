"use strict";var e,t=require("obsidian");!function(e){e.Default=".diagram-zoom-drag",e.<PERSON><PERSON>=".mermaid",e.<PERSON><PERSON><PERSON><PERSON>=".block-language-mehrmaid",e.PlantUML=".block-language-plantuml",e.Graphviz=".block-language-dot"}(e||(e={}));class n{plugin;constructor(e){this.plugin=e,this.plugin=e}get defaultSettings(){return{supported_diagrams:Object.entries(e).map((([e,t])=>({name:e,selector:t,on:!0,panels:{move:{on:!0},zoom:{on:!0},service:{on:!0}}}))),panelsConfig:{service:{enabled:!0,position:{top:"0px",right:"0px"}},move:{enabled:!0,position:{bottom:"0px",right:"0px"}},zoom:{enabled:!0,position:{top:"50%",right:"0px"}}},diagramsPerPage:5,foldByDefault:!1,automaticFoldingOnFocusChange:!1,hideOnMouseOutDiagram:!1,diagramExpanded:{width:"400",widthUnit:"px",height:"400",heightUnit:"px"},diagramFolded:{width:"200",widthUnit:"px",height:"200",heightUnit:"px"},addHidingButton:!0}}async loadSettings(){const e=await this.plugin.loadData(),t=this.defaultSettings,n=Object.assign({},t,e);this.plugin.settings={...n}}async saveSettings(){const e={...this.plugin.settings};await this.plugin.saveData(e)}async resetSettings(){const e=this.plugin.manifest.dir;if(e){const n=t.normalizePath(`${e}/data.json`);await this.plugin.app.vault.adapter.exists(n)&&await this.plugin.app.vault.adapter.remove(n),await this.loadSettings()}}}var i,r,a,o,s,l,c,u,d,h,p,m,g={},f=[],v=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,_=Array.isArray;function y(e,t){for(var n in t)e[n]=t[n];return e}function b(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function w(e,t,n){var r,a,o,s={};for(o in t)"key"==o?r=t[o]:"ref"==o?a=t[o]:s[o]=t[o];if(arguments.length>2&&(s.children=arguments.length>3?i.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(o in e.defaultProps)void 0===s[o]&&(s[o]=e.defaultProps[o]);return E(e,s,r,a,null)}function E(e,t,n,i,o){var s={type:e,props:t,key:n,ref:i,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==o?++a:o,__i:-1,__u:0};return null==o&&null!=r.vnode&&r.vnode(s),s}function S(e){return e.children}function x(e,t){this.props=e,this.context=t}function C(e,t){if(null==t)return e.__?C(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?C(e):null}function k(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return k(e)}}function P(e){(!e.__d&&(e.__d=!0)&&o.push(e)&&!D.__r++||s!=r.debounceRendering)&&((s=r.debounceRendering)||l)(D)}function D(){for(var e,t,n,i,a,s,l,u=1;o.length;)o.length>u&&o.sort(c),e=o.shift(),u=o.length,e.__d&&(n=void 0,a=(i=(t=e).__v).__e,s=[],l=[],t.__P&&((n=y({},i)).__v=i.__v+1,r.vnode&&r.vnode(n),O(t.__P,n,i,t.__n,t.__P.namespaceURI,32&i.__u?[a]:null,s,null==a?C(i):a,!!(32&i.__u),l),n.__v=i.__v,n.__.__k[n.__i]=n,M(s,n,l),n.__e!=a&&k(n)));D.__r=0}function I(e,t,n,i,r,a,o,s,l,c,u){var d,h,p,m,v,y,b=i&&i.__k||f,w=t.length;for(l=function(e,t,n,i,r){var a,o,s,l,c,u=n.length,d=u,h=0;for(e.__k=new Array(r),a=0;a<r;a++)null!=(o=t[a])&&"boolean"!=typeof o&&"function"!=typeof o?(l=a+h,(o=e.__k[a]="string"==typeof o||"number"==typeof o||"bigint"==typeof o||o.constructor==String?E(null,o,null,null,null):_(o)?E(S,{children:o},null,null,null):null==o.constructor&&o.__b>0?E(o.type,o.props,o.key,o.ref?o.ref:null,o.__v):o).__=e,o.__b=e.__b+1,s=null,-1!=(c=o.__i=$(o,n,l,d))&&(d--,(s=n[c])&&(s.__u|=2)),null==s||null==s.__v?(-1==c&&(r>u?h--:r<u&&h++),"function"!=typeof o.type&&(o.__u|=4)):c!=l&&(c==l-1?h--:c==l+1?h++:(c>l?h--:h++,o.__u|=4))):e.__k[a]=null;if(d)for(a=0;a<u;a++)null!=(s=n[a])&&!(2&s.__u)&&(s.__e==i&&(i=C(s)),j(s,s));return i}(n,t,b,l,w),d=0;d<w;d++)null!=(p=n.__k[d])&&(h=-1==p.__i?g:b[p.__i]||g,p.__i=d,y=O(e,p,h,r,a,o,s,l,c,u),m=p.__e,p.ref&&h.ref!=p.ref&&(h.ref&&B(h.ref,null,p),u.push(p.ref,p.__c||m,p)),null==v&&null!=m&&(v=m),4&p.__u||h.__k===p.__k?l=A(p,l,e):"function"==typeof p.type&&void 0!==y?l=y:m&&(l=m.nextSibling),p.__u&=-7);return n.__e=v,l}function A(e,t,n){var i,r;if("function"==typeof e.type){for(i=e.__k,r=0;i&&r<i.length;r++)i[r]&&(i[r].__=e,t=A(i[r],t,n));return t}e.__e!=t&&(t&&e.type&&!n.contains(t)&&(t=C(e)),n.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8==t.nodeType);return t}function T(e,t){return t=t||[],null==e||"boolean"==typeof e||(_(e)?e.some((function(e){T(e,t)})):t.push(e)),t}function $(e,t,n,i){var r,a,o=e.key,s=e.type,l=t[n];if(null===l&&null==e.key||l&&o==l.key&&s==l.type&&!(2&l.__u))return n;if(i>(null==l||2&l.__u?0:1))for(r=n-1,a=n+1;r>=0||a<t.length;){if(r>=0){if((l=t[r])&&!(2&l.__u)&&o==l.key&&s==l.type)return r;r--}if(a<t.length){if((l=t[a])&&!(2&l.__u)&&o==l.key&&s==l.type)return a;a++}}return-1}function z(e,t,n){"-"==t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||v.test(t)?n:n+"px"}function R(e,t,n,i,r){var a,o;e:if("style"==t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof i&&(e.style.cssText=i=""),i)for(t in i)n&&t in n||z(e.style,t,"");if(n)for(t in n)i&&n[t]==i[t]||z(e.style,t,n[t])}else if("o"==t[0]&&"n"==t[1])a=t!=(t=t.replace(u,"$1")),o=t.toLowerCase(),t=o in e||"onFocusOut"==t||"onFocusIn"==t?o.slice(2):t.slice(2),e.l||(e.l={}),e.l[t+a]=n,n?i?n.u=i.u:(n.u=d,e.addEventListener(t,a?p:h,a)):e.removeEventListener(t,a?p:h,a);else{if("http://www.w3.org/2000/svg"==r)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!=t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==n?"":n))}}function N(e){return function(t){if(this.l){var n=this.l[t.type+e];if(null==t.t)t.t=d++;else if(t.t<n.u)return;return n(r.event?r.event(t):t)}}}function O(e,t,n,i,a,o,s,l,c,u){var d,h,p,m,g,f,v,w,E,C,k,P,D,A,T,$,z,R=t.type;if(null!=t.constructor)return null;128&n.__u&&(c=!!(32&n.__u),o=[l=t.__e=n.__e]),(d=r.__b)&&d(t);e:if("function"==typeof R)try{if(w=t.props,E="prototype"in R&&R.prototype.render,C=(d=R.contextType)&&i[d.__c],k=d?C?C.props.value:d.__:i,n.__c?v=(h=t.__c=n.__c).__=h.__E:(E?t.__c=h=new R(w,k):(t.__c=h=new x(w,k),h.constructor=R,h.render=H),C&&C.sub(h),h.props=w,h.state||(h.state={}),h.context=k,h.__n=i,p=h.__d=!0,h.__h=[],h._sb=[]),E&&null==h.__s&&(h.__s=h.state),E&&null!=R.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=y({},h.__s)),y(h.__s,R.getDerivedStateFromProps(w,h.__s))),m=h.props,g=h.state,h.__v=t,p)E&&null==R.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),E&&null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(E&&null==R.getDerivedStateFromProps&&w!==m&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(w,k),!h.__e&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(w,h.__s,k)||t.__v==n.__v){for(t.__v!=n.__v&&(h.props=w,h.state=h.__s,h.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.some((function(e){e&&(e.__=t)})),P=0;P<h._sb.length;P++)h.__h.push(h._sb[P]);h._sb=[],h.__h.length&&s.push(h);break e}null!=h.componentWillUpdate&&h.componentWillUpdate(w,h.__s,k),E&&null!=h.componentDidUpdate&&h.__h.push((function(){h.componentDidUpdate(m,g,f)}))}if(h.context=k,h.props=w,h.__P=e,h.__e=!1,D=r.__r,A=0,E){for(h.state=h.__s,h.__d=!1,D&&D(t),d=h.render(h.props,h.state,h.context),T=0;T<h._sb.length;T++)h.__h.push(h._sb[T]);h._sb=[]}else do{h.__d=!1,D&&D(t),d=h.render(h.props,h.state,h.context),h.state=h.__s}while(h.__d&&++A<25);h.state=h.__s,null!=h.getChildContext&&(i=y(y({},i),h.getChildContext())),E&&!p&&null!=h.getSnapshotBeforeUpdate&&(f=h.getSnapshotBeforeUpdate(m,g)),$=d,null!=d&&d.type===S&&null==d.key&&($=F(d.props.children)),l=I(e,_($)?$:[$],t,n,i,a,o,s,l,c,u),h.base=t.__e,t.__u&=-161,h.__h.length&&s.push(h),v&&(h.__E=h.__=null)}catch(e){if(t.__v=null,c||null!=o)if(e.then){for(t.__u|=c?160:128;l&&8==l.nodeType&&l.nextSibling;)l=l.nextSibling;o[o.indexOf(l)]=null,t.__e=l}else for(z=o.length;z--;)b(o[z]);else t.__e=n.__e,t.__k=n.__k;r.__e(e,t,n)}else null==o&&t.__v==n.__v?(t.__k=n.__k,t.__e=n.__e):l=t.__e=L(n.__e,t,n,i,a,o,s,c,u);return(d=r.diffed)&&d(t),128&t.__u?void 0:l}function M(e,t,n){for(var i=0;i<n.length;i++)B(n[i],n[++i],n[++i]);r.__c&&r.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){r.__e(e,t.__v)}}))}function F(e){return"object"!=typeof e||null==e||e.__b&&e.__b>0?e:_(e)?e.map(F):y({},e)}function L(e,t,n,a,o,s,l,c,u){var d,h,p,m,f,v,y,w=n.props,E=t.props,S=t.type;if("svg"==S?o="http://www.w3.org/2000/svg":"math"==S?o="http://www.w3.org/1998/Math/MathML":o||(o="http://www.w3.org/1999/xhtml"),null!=s)for(d=0;d<s.length;d++)if((f=s[d])&&"setAttribute"in f==!!S&&(S?f.localName==S:3==f.nodeType)){e=f,s[d]=null;break}if(null==e){if(null==S)return document.createTextNode(E);e=document.createElementNS(o,S,E.is&&E),c&&(r.__m&&r.__m(t,s),c=!1),s=null}if(null==S)w===E||c&&e.data==E||(e.data=E);else{if(s=s&&i.call(e.childNodes),w=n.props||g,!c&&null!=s)for(w={},d=0;d<e.attributes.length;d++)w[(f=e.attributes[d]).name]=f.value;for(d in w)if(f=w[d],"children"==d);else if("dangerouslySetInnerHTML"==d)p=f;else if(!(d in E)){if("value"==d&&"defaultValue"in E||"checked"==d&&"defaultChecked"in E)continue;R(e,d,null,f,o)}for(d in E)f=E[d],"children"==d?m=f:"dangerouslySetInnerHTML"==d?h=f:"value"==d?v=f:"checked"==d?y=f:c&&"function"!=typeof f||w[d]===f||R(e,d,f,w[d],o);if(h)c||p&&(h.__html==p.__html||h.__html==e.innerHTML)||(e.innerHTML=h.__html),t.__k=[];else if(p&&(e.innerHTML=""),I("template"==t.type?e.content:e,_(m)?m:[m],t,n,a,"foreignObject"==S?"http://www.w3.org/1999/xhtml":o,s,l,s?s[0]:n.__k&&C(n,0),c,u),null!=s)for(d=s.length;d--;)b(s[d]);c||(d="value","progress"==S&&null==v?e.removeAttribute("value"):null!=v&&(v!==e[d]||"progress"==S&&!v||"option"==S&&v!=w[d])&&R(e,d,v,w[d],o),d="checked",null!=y&&y!=e[d]&&R(e,d,y,w[d],o))}return e}function B(e,t,n){try{if("function"==typeof e){var i="function"==typeof e.__u;i&&e.__u(),i&&null==t||(e.__u=e(t))}else e.current=t}catch(e){r.__e(e,n)}}function j(e,t,n){var i,a;if(r.unmount&&r.unmount(e),(i=e.ref)&&(i.current&&i.current!=e.__e||B(i,null,t)),null!=(i=e.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){r.__e(e,t)}i.base=i.__P=null}if(i=e.__k)for(a=0;a<i.length;a++)i[a]&&j(i[a],t,n||"function"!=typeof e.type);n||b(e.__e),e.__c=e.__=e.__e=void 0}function H(e,t,n){return this.constructor(e,n)}function U(e,t,n){var a,o,s,l;t==document&&(t=document.documentElement),r.__&&r.__(e,t),o=(a="function"==typeof n)?null:n&&n.__k||t.__k,s=[],l=[],O(t,e=(!a&&n||t).__k=w(S,null,[e]),o||g,g,t.namespaceURI,!a&&n?[n]:o?null:t.firstChild?i.call(t.childNodes):null,s,!a&&n?n:o?o.__e:t.firstChild,a,l),M(s,e,l)}function W(e,t){U(e,t,W)}function Y(e,t,n){var r,a,o,s,l=y({},e.props);for(o in e.type&&e.type.defaultProps&&(s=e.type.defaultProps),t)"key"==o?r=t[o]:"ref"==o?a=t[o]:l[o]=void 0===t[o]&&null!=s?s[o]:t[o];return arguments.length>2&&(l.children=arguments.length>3?i.call(arguments,2):n),E(e.type,l,r||e.key,a||e.ref,null)}function V(e){function t(e){var n,i;return this.getChildContext||(n=new Set,(i={})[t.__c]=this,this.getChildContext=function(){return i},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(e){this.props.value!=e.value&&n.forEach((function(e){e.__e=!0,P(e)}))},this.sub=function(e){n.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n&&n.delete(e),t&&t.call(e)}}),e.children}return t.__c="__cC"+m++,t.__=e,t.Provider=t.__l=(t.Consumer=function(e,t){return e.children(t)}).contextType=t,t}i=f.slice,r={__e:function(e,t,n,i){for(var r,a,o;t=t.__;)if((r=t.__c)&&!r.__)try{if((a=r.constructor)&&null!=a.getDerivedStateFromError&&(r.setState(a.getDerivedStateFromError(e)),o=r.__d),null!=r.componentDidCatch&&(r.componentDidCatch(e,i||{}),o=r.__d),o)return r.__E=r}catch(t){e=t}throw e}},a=0,x.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=y({},this.state),"function"==typeof e&&(e=e(y({},n),this.props)),e&&y(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),P(this))},x.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),P(this))},x.prototype.render=S,o=[],l="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,c=function(e,t){return e.__v.__b-t.__v.__b},D.__r=0,u=/(PointerCapture)$|Capture$/i,d=0,h=N(!1),p=N(!0),m=0;var q,G,X,Z,J=0,K=[],Q=r,ee=Q.__b,te=Q.__r,ne=Q.diffed,ie=Q.__c,re=Q.unmount,ae=Q.__;function oe(e,t){Q.__h&&Q.__h(G,e,J||t),J=0;var n=G.__H||(G.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function se(e){return J=1,le(xe,e)}function le(e,t,n){var i=oe(q++,2);if(i.t=e,!i.__c&&(i.__=[n?n(t):xe(void 0,t),function(e){var t=i.__N?i.__N[0]:i.__[0],n=i.t(t,e);t!==n&&(i.__N=[n,i.__[1]],i.__c.setState({}))}],i.__c=G,!G.__f)){var r=function(e,t,n){if(!i.__c.__H)return!0;var r=i.__c.__H.__.filter((function(e){return!!e.__c}));if(r.every((function(e){return!e.__N})))return!a||a.call(this,e,t,n);var o=i.__c.props!==e;return r.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(o=!0)}})),a&&a.call(this,e,t,n)||o};G.__f=!0;var a=G.shouldComponentUpdate,o=G.componentWillUpdate;G.componentWillUpdate=function(e,t,n){if(this.__e){var i=a;a=void 0,r(e,t,n),a=i}o&&o.call(this,e,t,n)},G.shouldComponentUpdate=r}return i.__N||i.__}function ce(e,t){var n=oe(q++,3);!Q.__s&&Se(n.__H,t)&&(n.__=e,n.u=t,G.__H.__h.push(n))}function ue(e,t){var n=oe(q++,4);!Q.__s&&Se(n.__H,t)&&(n.__=e,n.u=t,G.__h.push(n))}function de(e){return J=5,pe((function(){return{current:e}}),[])}function he(e,t,n){J=6,ue((function(){if("function"==typeof e){var n=e(t());return function(){e(null),n&&"function"==typeof n&&n()}}if(e)return e.current=t(),function(){return e.current=null}}),null==n?n:n.concat(e))}function pe(e,t){var n=oe(q++,7);return Se(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function me(e,t){return J=8,pe((function(){return e}),t)}function ge(e){var t=G.context[e.__c],n=oe(q++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(G)),t.props.value):e.__}function fe(e,t){Q.useDebugValue&&Q.useDebugValue(t?t(e):e)}function ve(){var e=oe(q++,11);if(!e.__){for(var t=G.__v;null!==t&&!t.__m&&null!==t.__;)t=t.__;var n=t.__m||(t.__m=[0,0]);e.__="P"+n[0]+"-"+n[1]++}return e.__}function _e(){for(var e;e=K.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(we),e.__H.__h.forEach(Ee),e.__H.__h=[]}catch(t){e.__H.__h=[],Q.__e(t,e.__v)}}Q.__b=function(e){G=null,ee&&ee(e)},Q.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),ae&&ae(e,t)},Q.__r=function(e){te&&te(e),q=0;var t=(G=e.__c).__H;t&&(X===G?(t.__h=[],G.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.u=e.__N=void 0}))):(t.__h.forEach(we),t.__h.forEach(Ee),t.__h=[],q=0)),X=G},Q.diffed=function(e){ne&&ne(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==K.push(t)&&Z===Q.requestAnimationFrame||((Z=Q.requestAnimationFrame)||be)(_e)),t.__H.__.forEach((function(e){e.u&&(e.__H=e.u),e.u=void 0}))),X=G=null},Q.__c=function(e,t){t.some((function(e){try{e.__h.forEach(we),e.__h=e.__h.filter((function(e){return!e.__||Ee(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],Q.__e(n,e.__v)}})),ie&&ie(e,t)},Q.unmount=function(e){re&&re(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{we(e)}catch(e){t=e}})),n.__H=void 0,t&&Q.__e(t,n.__v))};var ye="function"==typeof requestAnimationFrame;function be(e){var t,n=function(){clearTimeout(i),ye&&cancelAnimationFrame(t),setTimeout(e)},i=setTimeout(n,35);ye&&(t=requestAnimationFrame(n))}function we(e){var t=G,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),G=t}function Ee(e){var t=G;e.__c=e.__(),G=t}function Se(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function xe(e,t){return"function"==typeof t?t(e):t}function Ce(e,t){for(var n in t)e[n]=t[n];return e}function ke(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var i in t)if("__source"!==i&&e[i]!==t[i])return!0;return!1}function Pe(e,t){var n=t(),i=se({t:{__:n,u:t}}),r=i[0].t,a=i[1];return ue((function(){r.__=n,r.u=t,De(r)&&a({t:r})}),[e,n,t]),ce((function(){return De(r)&&a({t:r}),e((function(){De(r)&&a({t:r})}))}),[e]),n}function De(e){var t,n,i=e.u,r=e.__;try{var a=i();return!((t=r)===(n=a)&&(0!==t||1/t==1/n)||t!=t&&n!=n)}catch(e){return!0}}function Ie(e){e()}function Ae(e){return e}function Te(){return[!1,Ie]}var $e=ue;function ze(e,t){this.props=e,this.context=t}function Re(e,t){function n(e){var n=this.props.ref,i=n==e.ref;return!i&&n&&(n.call?n(null):n.current=null),t?!t(this.props,e)||!i:ke(this.props,e)}function i(t){return this.shouldComponentUpdate=n,w(e,t)}return i.displayName="Memo("+(e.displayName||e.name)+")",i.prototype.isReactComponent=!0,i.__f=!0,i}(ze.prototype=new x).isPureReactComponent=!0,ze.prototype.shouldComponentUpdate=function(e,t){return ke(this.props,e)||ke(this.state,t)};var Ne=r.__b;r.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),Ne&&Ne(e)};var Oe="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function Me(e){function t(t){var n=Ce({},t);return delete n.ref,e(n,t.ref||null)}return t.$$typeof=Oe,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}var Fe=function(e,t){return null==e?null:T(T(e).map(t))},Le={map:Fe,forEach:Fe,count:function(e){return e?T(e).length:0},only:function(e){var t=T(e);if(1!==t.length)throw"Children.only";return t[0]},toArray:T},Be=r.__e;r.__e=function(e,t,n,i){if(e.then)for(var r,a=t;a=a.__;)if((r=a.__c)&&r.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),r.__c(e,t);Be(e,t,n,i)};var je=r.unmount;function He(e,t,n){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),e.__c.__H=null),null!=(e=Ce({},e)).__c&&(e.__c.__P===n&&(e.__c.__P=t),e.__c.__e=!0,e.__c=null),e.__k=e.__k&&e.__k.map((function(e){return He(e,t,n)}))),e}function Ue(e,t,n){return e&&n&&(e.__v=null,e.__k=e.__k&&e.__k.map((function(e){return Ue(e,t,n)})),e.__c&&e.__c.__P===t&&(e.__e&&n.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=n)),e}function We(){this.__u=0,this.o=null,this.__b=null}function Ye(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function Ve(){this.i=null,this.l=null}r.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),je&&je(e)},(We.prototype=new x).__c=function(e,t){var n=t.__c,i=this;null==i.o&&(i.o=[]),i.o.push(n);var r=Ye(i.__v),a=!1,o=function(){a||(a=!0,n.__R=null,r?r(s):s())};n.__R=o;var s=function(){if(! --i.__u){if(i.state.__a){var e=i.state.__a;i.__v.__k[0]=Ue(e,e.__c.__P,e.__c.__O)}var t;for(i.setState({__a:i.__b=null});t=i.o.pop();)t.forceUpdate()}};i.__u++||32&t.__u||i.setState({__a:i.__b=i.__v.__k[0]}),e.then(o,o)},We.prototype.componentWillUnmount=function(){this.o=[]},We.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),i=this.__v.__k[0].__c;this.__v.__k[0]=He(this.__b,n,i.__O=i.__P)}this.__b=null}var r=t.__a&&w(S,null,e.fallback);return r&&(r.__u&=-33),[w(S,null,t.__a?null:e.children),r]};var qe=function(e,t,n){if(++n[1]===n[0]&&e.l.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.l.size))for(n=e.i;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.i=n=n[2]}};function Ge(e){return this.getChildContext=function(){return e.context},e.children}function Xe(e){var t=this,n=e.h;if(t.componentWillUnmount=function(){U(null,t.v),t.v=null,t.h=null},t.h&&t.h!==n&&t.componentWillUnmount(),!t.v){for(var i=t.__v;null!==i&&!i.__m&&null!==i.__;)i=i.__;t.h=n,t.v={nodeType:1,parentNode:n,childNodes:[],__k:{__m:i.__m},contains:function(){return!0},insertBefore:function(e,n){this.childNodes.push(e),t.h.insertBefore(e,n)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.h.removeChild(e)}}}U(w(Ge,{context:t.context},e.__v),t.v)}(Ve.prototype=new x).__a=function(e){var t=this,n=Ye(t.__v),i=t.l.get(e);return i[0]++,function(r){var a=function(){t.props.revealOrder?(i.push(r),qe(t,e,i)):r()};n?n(a):a()}},Ve.prototype.render=function(e){this.i=null,this.l=new Map;var t=T(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.l.set(t[n],this.i=[1,0,this.i]);return e.children},Ve.prototype.componentDidUpdate=Ve.prototype.componentDidMount=function(){var e=this;this.l.forEach((function(t,n){qe(e,n,t)}))};var Ze="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,Je=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Ke=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,Qe=/[A-Z0-9]/g,et="undefined"!=typeof document,tt=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(e)};function nt(e,t,n){return null==t.__k&&(t.textContent=""),U(e,t),"function"==typeof n&&n(),e?e.__c:null}x.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(x.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var it=r.event;function rt(){}function at(){return this.cancelBubble}function ot(){return this.defaultPrevented}r.event=function(e){return it&&(e=it(e)),e.persist=rt,e.isPropagationStopped=at,e.isDefaultPrevented=ot,e.nativeEvent=e};var st,lt={enumerable:!1,configurable:!0,get:function(){return this.class}},ct=r.vnode;r.vnode=function(e){"string"==typeof e.type&&function(e){var t=e.props,n=e.type,i={},r=-1===n.indexOf("-");for(var a in t){var o=t[a];if(!("value"===a&&"defaultValue"in t&&null==o||et&&"children"===a&&"noscript"===n||"class"===a||"className"===a)){var s=a.toLowerCase();"defaultValue"===a&&"value"in t&&null==t.value?a="value":"download"===a&&!0===o?o="":"translate"===s&&"no"===o?o=!1:"o"===s[0]&&"n"===s[1]?"ondoubleclick"===s?a="ondblclick":"onchange"!==s||"input"!==n&&"textarea"!==n||tt(t.type)?"onfocus"===s?a="onfocusin":"onblur"===s?a="onfocusout":Ke.test(a)&&(a=s):s=a="oninput":r&&Je.test(a)?a=a.replace(Qe,"-$&").toLowerCase():null===o&&(o=void 0),"oninput"===s&&i[a=s]&&(a="oninputCapture"),i[a]=o}}"select"==n&&i.multiple&&Array.isArray(i.value)&&(i.value=T(t.children).forEach((function(e){e.props.selected=-1!=i.value.indexOf(e.props.value)}))),"select"==n&&null!=i.defaultValue&&(i.value=T(t.children).forEach((function(e){e.props.selected=i.multiple?-1!=i.defaultValue.indexOf(e.props.value):i.defaultValue==e.props.value}))),t.class&&!t.className?(i.class=t.class,Object.defineProperty(i,"className",lt)):(t.className&&!t.class||t.class&&t.className)&&(i.class=i.className=t.className),e.props=i}(e),e.$$typeof=Ze,ct&&ct(e)};var ut=r.__r;r.__r=function(e){ut&&ut(e),st=e.__c};var dt=r.diffed;r.diffed=function(e){dt&&dt(e);var t=e.props,n=e.__e;null!=n&&"textarea"===e.type&&"value"in t&&t.value!==n.value&&(n.value=null==t.value?"":t.value),st=null};var ht={ReactCurrentDispatcher:{current:{readContext:function(e){return st.__n[e.__c].props.value},useCallback:me,useContext:ge,useDebugValue:fe,useDeferredValue:Ae,useEffect:ce,useId:ve,useImperativeHandle:he,useInsertionEffect:$e,useLayoutEffect:ue,useMemo:pe,useReducer:le,useRef:de,useState:se,useSyncExternalStore:Pe,useTransition:Te}}};function pt(e){return!!e&&e.$$typeof===Ze}function mt(e){return!!e.__k&&(U(null,e),!0)}var gt={useState:se,useId:ve,useReducer:le,useEffect:ce,useLayoutEffect:ue,useInsertionEffect:$e,useTransition:Te,useDeferredValue:Ae,useSyncExternalStore:Pe,startTransition:Ie,useRef:de,useImperativeHandle:he,useMemo:pe,useCallback:me,useContext:ge,useDebugValue:fe,version:"18.3.1",Children:Le,render:nt,hydrate:function(e,t,n){return W(e,t),"function"==typeof n&&n(),e?e.__c:null},unmountComponentAtNode:mt,createPortal:function(e,t){var n=w(Xe,{__v:e,h:t});return n.containerInfo=t,n},createElement:w,createContext:V,createFactory:function(e){return w.bind(null,e)},cloneElement:function(e){return pt(e)?Y.apply(null,arguments):e},createRef:function(){return{current:null}},Fragment:S,isValidElement:pt,isElement:pt,isFragment:function(e){return pt(e)&&e.type===S},isMemo:function(e){return!!e&&!!e.displayName&&("string"==typeof e.displayName||e.displayName instanceof String)&&e.displayName.startsWith("Memo(")},findDOMNode:function(e){return e&&(e.base||1===e.nodeType&&e)||null},Component:x,PureComponent:ze,memo:Re,forwardRef:Me,flushSync:function(e,t){return e(t)},unstable_batchedUpdates:function(e,t){return e(t)},StrictMode:S,Suspense:We,SuspenseList:Ve,lazy:function(e){var t,n,i;function r(r){if(t||(t=e()).then((function(e){n=e.default||e}),(function(e){i=e})),i)throw i;if(!n)throw t;return w(n,r)}return r.displayName="Lazy",r.__f=!0,r},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:ht};function ft(e){return{render:function(t){nt(t,e)},unmount:function(){mt(e)}}}const vt=V(void 0),_t=({app:e,plugin:t,children:n})=>{const[i,r]=se(0),[a,o]=se("/diagram-section"),s=me((()=>{r((e=>e+1))}),[]),l=pe((()=>({app:e,plugin:t,forceReload:s,reloadCount:i,currentPath:a,setCurrentPath:o})),[e,t,s,i,a,o]);return gt.createElement(vt.Provider,{value:l},n)},yt=()=>{const e=ge(vt);if(void 0===e)throw new Error("useSettingsContext must be used within a SettingProvider");return e};function bt(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var wt,Et={};
/**
 * react-router v7.6.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function St(e={}){let t,{initialEntries:n=["/"],initialIndex:i,v5Compat:r=!1}=e;t=n.map(((e,t)=>u(e,"string"==typeof e?null:e.state,0===t?"default":void 0)));let a=l(null==i?t.length-1:i),o="POP",s=null;function l(e){return Math.min(Math.max(e,0),t.length-1)}function c(){return t[a]}function u(e,n=null,i){let r=function(e,t,n=null,i){let r={pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?Dt(t):t,state:n,key:t&&t.key||i||kt()};return r}(t?c().pathname:"/",e,n,i);return Ct("/"===r.pathname.charAt(0),`relative pathnames are not supported in memory history: ${JSON.stringify(e)}`),r}function d(e){return"string"==typeof e?e:Pt(e)}let h={get index(){return a},get action(){return o},get location(){return c()},createHref:d,createURL:e=>new URL(d(e),"http://localhost"),encodeLocation(e){let t="string"==typeof e?Dt(e):e;return{pathname:t.pathname||"",search:t.search||"",hash:t.hash||""}},push(e,n){o="PUSH";let i=u(e,n);a+=1,t.splice(a,t.length,i),r&&s&&s({action:o,location:i,delta:1})},replace(e,n){o="REPLACE";let i=u(e,n);t[a]=i,r&&s&&s({action:o,location:i,delta:0})},go(e){o="POP";let n=l(a+e),i=t[n];a=n,s&&s({action:o,location:i,delta:e})},listen:e=>(s=e,()=>{s=null})};return h}function xt(e,t){if(!1===e||null==e)throw new Error(t)}function Ct(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(e){}}}function kt(){return Math.random().toString(36).substring(2,10)}function Pt({pathname:e="/",search:t="",hash:n=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),n&&"#"!==n&&(e+="#"===n.charAt(0)?n:"#"+n),e}function Dt(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let i=e.indexOf("?");i>=0&&(t.search=e.substring(i),e=e.substring(0,i)),e&&(t.pathname=e)}return t}function It(e,t,n="/"){return function(e,t,n,i){let r="string"==typeof t?Dt(t):t,a=Ut(r.pathname||"/",n);if(null==a)return null;let o=At(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let s=null;for(let e=0;null==s&&e<o.length;++e){let t=Ht(a);s=Bt(o[e],t,i)}return s}(e,t,n,!1)}function At(e,t=[],n=[],i=""){let r=(e,r,a)=>{let o={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:r,route:e};o.relativePath.startsWith("/")&&(xt(o.relativePath.startsWith(i),`Absolute route path "${o.relativePath}" nested under path "${i}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),o.relativePath=o.relativePath.slice(i.length));let s=qt([i,o.relativePath]),l=n.concat(o);e.children&&e.children.length>0&&(xt(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${s}".`),At(e.children,t,l,s)),(null!=e.path||e.index)&&t.push({path:s,score:Lt(s,e.index),routesMeta:l})};return e.forEach(((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let n of Tt(e.path))r(e,t,n);else r(e,t)})),t}function Tt(e){let t=e.split("/");if(0===t.length)return[];let[n,...i]=t,r=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===i.length)return r?[a,""]:[a];let o=Tt(i.join("/")),s=[];return s.push(...o.map((e=>""===e?a:[a,e].join("/")))),r&&s.push(...o),s.map((t=>e.startsWith("/")&&""===t?"/":t))}!function(){if(wt)return Et;wt=1,Object.defineProperty(Et,"__esModule",{value:!0}),Et.parse=function(e,t){const n=new a,i=e.length;if(i<2)return n;const r=t?.decode||l;let c=0;do{const t=e.indexOf("=",c);if(-1===t)break;const a=e.indexOf(";",c),l=-1===a?i:a;if(t>l){c=e.lastIndexOf(";",t-1)+1;continue}const u=o(e,c,t),d=s(e,t,u),h=e.slice(u,d);if(void 0===n[h]){let i=o(e,t+1,l),a=s(e,l,i);const c=r(e.slice(i,a));n[h]=c}c=l+1}while(c<i);return n},Et.serialize=function(a,o,s){const l=s?.encode||encodeURIComponent;if(!e.test(a))throw new TypeError(`argument name is invalid: ${a}`);const c=l(o);if(!t.test(c))throw new TypeError(`argument val is invalid: ${o}`);let u=a+"="+c;if(!s)return u;if(void 0!==s.maxAge){if(!Number.isInteger(s.maxAge))throw new TypeError(`option maxAge is invalid: ${s.maxAge}`);u+="; Max-Age="+s.maxAge}if(s.domain){if(!n.test(s.domain))throw new TypeError(`option domain is invalid: ${s.domain}`);u+="; Domain="+s.domain}if(s.path){if(!i.test(s.path))throw new TypeError(`option path is invalid: ${s.path}`);u+="; Path="+s.path}if(s.expires){if(!function(e){return"[object Date]"===r.call(e)}(s.expires)||!Number.isFinite(s.expires.valueOf()))throw new TypeError(`option expires is invalid: ${s.expires}`);u+="; Expires="+s.expires.toUTCString()}s.httpOnly&&(u+="; HttpOnly");s.secure&&(u+="; Secure");s.partitioned&&(u+="; Partitioned");if(s.priority){switch("string"==typeof s.priority?s.priority.toLowerCase():void 0){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${s.priority}`)}}if(s.sameSite){switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${s.sameSite}`)}}return u};const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,i=/^[\u0020-\u003A\u003D-\u007E]*$/,r=Object.prototype.toString,a=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function o(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function s(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function l(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}();var $t=/^:[\w-]+$/,zt=3,Rt=2,Nt=1,Ot=10,Mt=-2,Ft=e=>"*"===e;function Lt(e,t){let n=e.split("/"),i=n.length;return n.some(Ft)&&(i+=Mt),t&&(i+=Rt),n.filter((e=>!Ft(e))).reduce(((e,t)=>e+($t.test(t)?zt:""===t?Nt:Ot)),i)}function Bt(e,t,n=!1){let{routesMeta:i}=e,r={},a="/",o=[];for(let e=0;e<i.length;++e){let s=i[e],l=e===i.length-1,c="/"===a?t:t.slice(a.length)||"/",u=jt({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},c),d=s.route;if(!u&&l&&n&&!i[i.length-1].route.index&&(u=jt({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},c)),!u)return null;Object.assign(r,u.params),o.push({params:r,pathname:qt([a,u.pathname]),pathnameBase:Gt(qt([a,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(a=qt([a,u.pathnameBase]))}return o}function jt(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,i]=function(e,t=!1,n=!0){Ct("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let i=[],r="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(i.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(i.push({paramName:"*"}),r+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?r+="\\/*$":""!==e&&"/"!==e&&(r+="(?:(?=\\/|$))");let a=new RegExp(r,t?void 0:"i");return[a,i]}(e.path,e.caseSensitive,e.end),r=t.match(n);if(!r)return null;let a=r[0],o=a.replace(/(.)\/+$/,"$1"),s=r.slice(1);return{params:i.reduce(((e,{paramName:t,isOptional:n},i)=>{if("*"===t){let e=s[i]||"";o=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const r=s[i];return e[t]=n&&!r?void 0:(r||"").replace(/%2F/g,"/"),e}),{}),pathname:a,pathnameBase:o,pattern:e}}function Ht(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return Ct(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function Ut(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,i=e.charAt(n);return i&&"/"!==i?null:e.slice(n)||"/"}function Wt(e,t,n,i){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(i)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Yt(e){let t=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function Vt(e,t,n,i=!1){let r;"string"==typeof e?r=Dt(e):(r={...e},xt(!r.pathname||!r.pathname.includes("?"),Wt("?","pathname","search",r)),xt(!r.pathname||!r.pathname.includes("#"),Wt("#","pathname","hash",r)),xt(!r.search||!r.search.includes("#"),Wt("#","search","hash",r)));let a,o=""===e||""===r.pathname,s=o?"/":r.pathname;if(null==s)a=n;else{let e=t.length-1;if(!i&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}a=e>=0?t[e]:"/"}let l=function(e,t="/"){let{pathname:n,search:i="",hash:r=""}="string"==typeof e?Dt(e):e,a=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:a,search:Xt(i),hash:Zt(r)}}(r,a),c=s&&"/"!==s&&s.endsWith("/"),u=(o||"."===s)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!u||(l.pathname+="/"),l}var qt=e=>e.join("/").replace(/\/\/+/g,"/"),Gt=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Xt=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Zt=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";var Jt=["POST","PUT","PATCH","DELETE"];new Set(Jt);var Kt=["GET",...Jt];new Set(Kt);var Qt=V(null);Qt.displayName="DataRouter";var en=V(null);en.displayName="DataRouterState";var tn=V({isTransitioning:!1});tn.displayName="ViewTransition",V(new Map).displayName="Fetchers",V(null).displayName="Await";var nn=V(null);nn.displayName="Navigation";var rn=V(null);rn.displayName="Location";var an=V({outlet:null,matches:[],isDataRoute:!1});an.displayName="Route";var on=V(null);function sn(){return null!=ge(rn)}function ln(){return xt(sn(),"useLocation() may be used only in the context of a <Router> component."),ge(rn).location}on.displayName="RouteError";var cn="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function un(e){ge(nn).static||ue(e)}function dn(){let{isDataRoute:e}=ge(an);return e?function(){let{router:e}=function(e){let t=ge(Qt);return xt(t,_n(e)),t}("useNavigate"),t=yn("useNavigate"),n=de(!1);return un((()=>{n.current=!0})),me((async(i,r={})=>{Ct(n.current,cn),n.current&&("number"==typeof i?e.navigate(i):await e.navigate(i,{fromRouteId:t,...r}))}),[e,t])}():function(){xt(sn(),"useNavigate() may be used only in the context of a <Router> component.");let e=ge(Qt),{basename:t,navigator:n}=ge(nn),{matches:i}=ge(an),{pathname:r}=ln(),a=JSON.stringify(Yt(i)),o=de(!1);return un((()=>{o.current=!0})),me(((i,s={})=>{if(Ct(o.current,cn),!o.current)return;if("number"==typeof i)return void n.go(i);let l=Vt(i,JSON.parse(a),r,"path"===s.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:qt([t,l.pathname])),(s.replace?n.replace:n.push)(l,s.state,s)}),[t,n,a,r,e])}()}function hn(e,{relative:t}={}){let{matches:n}=ge(an),{pathname:i}=ln(),r=JSON.stringify(Yt(n));return pe((()=>Vt(e,JSON.parse(r),i,"path"===t)),[e,r,i,t])}function pn(e,t,n,i){xt(sn(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:r}=ge(nn),{matches:a}=ge(an),o=a[a.length-1],s=o?o.params:{},l=o?o.pathname:"/",c=o?o.pathnameBase:"/",u=o&&o.route;{let e=u&&u.path||"";wn(l,!u||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${l}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let d,h=ln();if(t){let e="string"==typeof t?Dt(t):t;xt("/"===c||e.pathname?.startsWith(c),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${c}" but pathname "${e.pathname}" was given in the \`location\` prop.`),d=e}else d=h;let p=d.pathname||"/",m=p;if("/"!==c){let e=c.replace(/^\//,"").split("/");m="/"+p.replace(/^\//,"").split("/").slice(e.length).join("/")}let g=It(e,{pathname:m});Ct(u||null!=g,`No routes matched location "${d.pathname}${d.search}${d.hash}" `),Ct(null==g||void 0!==g[g.length-1].route.element||void 0!==g[g.length-1].route.Component||void 0!==g[g.length-1].route.lazy,`Matched leaf route at location "${d.pathname}${d.search}${d.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let f=function(e,t=[],n=null){if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let i=e,r=n?.errors;if(null!=r){let e=i.findIndex((e=>e.route.id&&void 0!==r?.[e.route.id]));xt(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(r).join(",")}`),i=i.slice(0,Math.min(i.length,e+1))}let a=!1,o=-1;if(n)for(let e=0;e<i.length;e++){let t=i[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(o=e),t.route.id){let{loaderData:e,errors:r}=n,s=t.route.loader&&!e.hasOwnProperty(t.route.id)&&(!r||void 0===r[t.route.id]);if(t.route.lazy||s){a=!0,i=o>=0?i.slice(0,o+1):[i[0]];break}}}return i.reduceRight(((e,s,l)=>{let c,u=!1,d=null,h=null;n&&(c=r&&s.route.id?r[s.route.id]:void 0,d=s.route.errorElement||gn,a&&(o<0&&0===l?(wn("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),u=!0,h=null):o===l&&(u=!0,h=s.route.hydrateFallbackElement||null)));let p=t.concat(i.slice(0,l+1)),m=()=>{let t;return t=c?d:u?h:s.route.Component?w(s.route.Component,null):s.route.element?s.route.element:e,w(vn,{match:s,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(s.route.ErrorBoundary||s.route.errorElement||0===l)?w(fn,{location:n.location,revalidation:n.revalidation,component:d,error:c,children:m(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):m()}),null)}(g&&g.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:qt([c,r.encodeLocation?r.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:qt([c,r.encodeLocation?r.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),a,n,i);return t&&f?w(rn.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...d},navigationType:"POP"}},f):f}function mn(){let e=function(){let e=ge(on),t=function(e){let t=ge(en);return xt(t,_n(e)),t}("useRouteError"),n=yn("useRouteError");if(void 0!==e)return e;return t.errors?.[n]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i="rgba(200,200,200, 0.5)",r={padding:"0.5rem",backgroundColor:i},a={padding:"2px 4px",backgroundColor:i},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=w(S,null,w("p",null,"💿 Hey developer 👋"),w("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",w("code",{style:a},"ErrorBoundary")," or"," ",w("code",{style:a},"errorElement")," prop on your route.")),w(S,null,w("h2",null,"Unexpected Application Error!"),w("h3",{style:{fontStyle:"italic"}},t),n?w("pre",{style:r},n):null,o)}V(null);var gn=w(mn,null),fn=class extends x{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?w(an.Provider,{value:this.props.routeContext},w(on.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function vn({routeContext:e,match:t,children:n}){let i=ge(Qt);return i&&i.static&&i.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=t.route.id),w(an.Provider,{value:e},n)}function _n(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function yn(e){let t=function(e){let t=ge(an);return xt(t,_n(e)),t}(e),n=t.matches[t.matches.length-1];return xt(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}var bn={};function wn(e,t,n){t||bn[e]||(bn[e]=!0,Ct(!1,n))}function En({basename:e,children:t,initialEntries:n,initialIndex:i}){let r=de();null==r.current&&(r.current=St({initialEntries:n,initialIndex:i,v5Compat:!0}));let a=r.current,[o,s]=se({action:a.action,location:a.location}),l=me((e=>{Ie((()=>s(e)))}),[s]);return ue((()=>a.listen(l)),[a,l]),w(xn,{basename:e,children:t,location:o.location,navigationType:o.action,navigator:a})}function Sn(e){xt(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function xn({basename:e="/",children:t=null,location:n,navigationType:i="POP",navigator:r,static:a=!1}){xt(!sn(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let o=e.replace(/^\/*/,"/"),s=pe((()=>({basename:o,navigator:r,static:a,future:{}})),[o,r,a]);"string"==typeof n&&(n=Dt(n));let{pathname:l="/",search:c="",hash:u="",state:d=null,key:h="default"}=n,p=pe((()=>{let e=Ut(l,o);return null==e?null:{location:{pathname:e,search:c,hash:u,state:d,key:h},navigationType:i}}),[o,l,c,u,d,h,i]);return Ct(null!=p,`<Router basename="${o}"> is not able to match the URL "${l}${c}${u}" because it does not start with the basename, so the <Router> won't render anything.`),null==p?null:w(nn.Provider,{value:s},w(rn.Provider,{children:t,value:p}))}function Cn({children:e,location:t}){return pn(kn(e),t)}function kn(e,t=[]){let n=[];return Le.forEach(e,((e,i)=>{if(!pt(e))return;let r=[...t,i];if(e.type===S)return void n.push.apply(n,kn(e.props.children,r));xt(e.type===Sn,`[${"string"==typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),xt(!e.props.index||!e.props.children,"An index route cannot have child routes.");let a={id:e.props.id||r.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(a.children=kn(e.props.children,r)),n.push(a)})),n}Re((function({routes:e,future:t,state:n}){return pn(e,void 0,n,t)}));var Pn="get",Dn="application/x-www-form-urlencoded";function In(e){return null!=e&&"string"==typeof e.tagName}var An=null;var Tn=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function $n(e){return null==e||Tn.has(e)?e:(Ct(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Dn}"`),null)}function zn(e,t){let n,i,r,a,o;if(In(s=e)&&"form"===s.tagName.toLowerCase()){let o=e.getAttribute("action");i=o?Ut(o,t):null,n=e.getAttribute("method")||Pn,r=$n(e.getAttribute("enctype"))||Dn,a=new FormData(e)}else if(function(e){return In(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return In(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let o=e.form;if(null==o)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let s=e.getAttribute("formaction")||o.getAttribute("action");if(i=s?Ut(s,t):null,n=e.getAttribute("formmethod")||o.getAttribute("method")||Pn,r=$n(e.getAttribute("formenctype"))||$n(o.getAttribute("enctype"))||Dn,a=new FormData(o,e),!function(){if(null===An)try{new FormData(document.createElement("form"),0),An=!1}catch(e){An=!0}return An}()){let{name:t,type:n,value:i}=e;if("image"===n){let e=t?`${t}.`:"";a.append(`${e}x`,"0"),a.append(`${e}y`,"0")}else t&&a.append(t,i)}}else{if(In(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Pn,i=null,r=Dn,o=e}var s;return a&&"text/plain"===r&&(o=a,a=void 0),{action:i,method:n.toLowerCase(),encType:r,formData:a,body:o}}function Rn(e,t){if(!1===e||null==e)throw new Error(t)}function Nn(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}async function On(e,t,n){let i=await Promise.all(e.map((async e=>{let i=t.routes[e.route.id];if(i){let e=await async function(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(t){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(t),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}(i,n);return e.links?e.links():[]}return[]})));return function(e,t){let n=new Set;return new Set(t),e.reduce(((e,t)=>{let i=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let i of n)t[i]=e[i];return t}(t));return n.has(i)||(n.add(i),e.push({key:i,link:t})),e}),[])}(i.flat(1).filter(Nn).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"})))}function Mn(e,t,n,i,r,a){let o=(e,t)=>!n[t]||e.route.id!==n[t].route.id,s=(e,t)=>n[t].pathname!==e.pathname||n[t].route.path?.endsWith("*")&&n[t].params["*"]!==e.params["*"];return"assets"===a?t.filter(((e,t)=>o(e,t)||s(e,t))):"data"===a?t.filter(((t,a)=>{let l=i.routes[t.route.id];if(!l||!l.hasLoader)return!1;if(o(t,a)||s(t,a))return!0;if(t.route.shouldRevalidate){let i=t.route.shouldRevalidate({currentUrl:new URL(r.pathname+r.search+r.hash,window.origin),currentParams:n[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof i)return i}return!0})):[]}function Fn(e,t,{includeHydrateFallback:n}={}){return i=e.map((e=>{let i=t.routes[e.route.id];if(!i)return[];let r=[i.module];return i.clientActionModule&&(r=r.concat(i.clientActionModule)),i.clientLoaderModule&&(r=r.concat(i.clientLoaderModule)),n&&i.hydrateFallbackModule&&(r=r.concat(i.hydrateFallbackModule)),i.imports&&(r=r.concat(i.imports)),r})).flat(1),[...new Set(i)];var i}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function Ln(){let e=ge(Qt);return Rn(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}var Bn=V(void 0);function jn(){let e=ge(Bn);return Rn(e,"You must render this element inside a <HydratedRouter> element"),e}function Hn(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function Un({page:e,...t}){let{router:n}=Ln(),i=pe((()=>It(n.routes,e,n.basename)),[n.routes,e,n.basename]);return i?w(Wn,{page:e,matches:i,...t}):null}function Wn({page:e,matches:t,...n}){let i=ln(),{manifest:r,routeModules:a}=jn(),{basename:o}=Ln(),{loaderData:s,matches:l}=function(){let e=ge(en);return Rn(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}(),c=pe((()=>Mn(e,t,l,r,i,"data")),[e,t,l,r,i]),u=pe((()=>Mn(e,t,l,r,i,"assets")),[e,t,l,r,i]),d=pe((()=>{if(e===i.pathname+i.search+i.hash)return[];let n=new Set,l=!1;if(t.forEach((e=>{let t=r.routes[e.route.id];t&&t.hasLoader&&(!c.some((t=>t.route.id===e.route.id))&&e.route.id in s&&a[e.route.id]?.shouldRevalidate||t.hasClientLoader?l=!0:n.add(e.route.id))})),0===n.size)return[];let u=function(e,t){let n="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===Ut(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}(e,o);return l&&n.size>0&&u.searchParams.set("_routes",t.filter((e=>n.has(e.route.id))).map((e=>e.route.id)).join(",")),[u.pathname+u.search]}),[o,s,i,r,c,t,e,a]),h=pe((()=>Fn(u,r)),[u,r]),p=function(e){let{manifest:t,routeModules:n}=jn(),[i,r]=se([]);return ce((()=>{let i=!1;return On(e,t,n).then((e=>{i||r(e)})),()=>{i=!0}}),[e,t,n]),i}(u);return w(S,null,d.map((e=>w("link",{key:e,rel:"prefetch",as:"fetch",href:e,...n}))),h.map((e=>w("link",{key:e,rel:"modulepreload",href:e,...n}))),p.map((({key:e,link:t})=>w("link",{key:e,...t}))))}function Yn(...e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}Bn.displayName="FrameworkContext";var Vn="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{Vn&&(window.__reactRouterVersion="7.6.1")}catch(ee){}var qn=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Gn=Me((function({onClick:e,discover:t="render",prefetch:n="none",relative:i,reloadDocument:r,replace:a,state:o,target:s,to:l,preventScrollReset:c,viewTransition:u,...d},h){let p,{basename:m}=ge(nn),g="string"==typeof l&&qn.test(l),f=!1;if("string"==typeof l&&g&&(p=l,Vn))try{let e=new URL(window.location.href),t=l.startsWith("//")?new URL(e.protocol+l):new URL(l),n=Ut(t.pathname,m);t.origin===e.origin&&null!=n?l=n+t.search+t.hash:f=!0}catch(e){Ct(!1,`<Link to="${l}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let v=function(e,{relative:t}={}){xt(sn(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:i}=ge(nn),{hash:r,pathname:a,search:o}=hn(e,{relative:t}),s=a;return"/"!==n&&(s="/"===a?n:qt([n,a])),i.createHref({pathname:s,search:o,hash:r})}(l,{relative:i}),[_,y,b]=function(e,t){let n=ge(Bn),[i,r]=se(!1),[a,o]=se(!1),{onFocus:s,onBlur:l,onMouseEnter:c,onMouseLeave:u,onTouchStart:d}=t,h=de(null);ce((()=>{if("render"===e&&o(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{o(e.isIntersecting)}))}),{threshold:.5});return h.current&&e.observe(h.current),()=>{e.disconnect()}}}),[e]),ce((()=>{if(i){let e=setTimeout((()=>{o(!0)}),100);return()=>{clearTimeout(e)}}}),[i]);let p=()=>{r(!0)},m=()=>{r(!1),o(!1)};return n?"intent"!==e?[a,h,{}]:[a,h,{onFocus:Hn(s,p),onBlur:Hn(l,m),onMouseEnter:Hn(c,p),onMouseLeave:Hn(u,m),onTouchStart:Hn(d,p)}]:[!1,h,{}]}(n,d),E=function(e,{target:t,replace:n,state:i,preventScrollReset:r,relative:a,viewTransition:o}={}){let s=dn(),l=ln(),c=hn(e,{relative:a});return me((u=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(u,t)){u.preventDefault();let t=void 0!==n?n:Pt(l)===Pt(c);s(e,{replace:t,state:i,preventScrollReset:r,relative:a,viewTransition:o})}}),[l,s,c,n,i,t,e,r,a,o])}(l,{replace:a,state:o,target:s,preventScrollReset:c,relative:i,viewTransition:u});let x=w("a",{...d,...b,href:p||v,onClick:f||r?e:function(t){e&&e(t),t.defaultPrevented||E(t)},ref:Yn(h,y),target:s,"data-discover":g||"render"!==t?void 0:"true"});return _&&!g?w(S,null,x,w(Un,{page:v})):x}));Gn.displayName="Link";var Xn=Me((function({"aria-current":e="page",caseSensitive:t=!1,className:n="",end:i=!1,style:r,to:a,viewTransition:o,children:s,...l},c){let u=hn(a,{relative:l.relative}),d=ln(),h=ge(en),{navigator:p,basename:m}=ge(nn),g=null!=h&&function(e,t={}){let n=ge(tn);xt(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:i}=Jn("useViewTransitionState"),r=hn(e,{relative:t.relative});if(!n.isTransitioning)return!1;let a=Ut(n.currentLocation.pathname,i)||n.currentLocation.pathname,o=Ut(n.nextLocation.pathname,i)||n.nextLocation.pathname;return null!=jt(r.pathname,o)||null!=jt(r.pathname,a)}(u)&&!0===o,f=p.encodeLocation?p.encodeLocation(u).pathname:u.pathname,v=d.pathname,_=h&&h.navigation&&h.navigation.location?h.navigation.location.pathname:null;t||(v=v.toLowerCase(),_=_?_.toLowerCase():null,f=f.toLowerCase()),_&&m&&(_=Ut(_,m)||_);const y="/"!==f&&f.endsWith("/")?f.length-1:f.length;let b,E=v===f||!i&&v.startsWith(f)&&"/"===v.charAt(y),S=null!=_&&(_===f||!i&&_.startsWith(f)&&"/"===_.charAt(f.length)),x={isActive:E,isPending:S,isTransitioning:g},C=E?e:void 0;b="function"==typeof n?n(x):[n,E?"active":null,S?"pending":null,g?"transitioning":null].filter(Boolean).join(" ");let k="function"==typeof r?r(x):r;return w(Gn,{...l,"aria-current":C,className:b,ref:c,style:k,to:a,viewTransition:o},"function"==typeof s?s(x):s)}));Xn.displayName="NavLink";var Zn=Me((({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:i,replace:r,state:a,method:o=Pn,action:s,onSubmit:l,relative:c,preventScrollReset:u,viewTransition:d,...h},p)=>{let m=function(){let{router:e}=Jn("useSubmit"),{basename:t}=ge(nn),n=yn("useRouteId");return me((async(i,r={})=>{let{action:a,method:o,encType:s,formData:l,body:c}=zn(i,t);if(!1===r.navigate){let t=r.fetcherKey||Qn();await e.fetch(t,n,r.action||a,{preventScrollReset:r.preventScrollReset,formData:l,body:c,formMethod:r.method||o,formEncType:r.encType||s,flushSync:r.flushSync})}else await e.navigate(r.action||a,{preventScrollReset:r.preventScrollReset,formData:l,body:c,formMethod:r.method||o,formEncType:r.encType||s,replace:r.replace,state:r.state,fromRouteId:n,flushSync:r.flushSync,viewTransition:r.viewTransition})}),[e,t,n])}(),g=function(e,{relative:t}={}){let{basename:n}=ge(nn),i=ge(an);xt(i,"useFormAction must be used inside a RouteContext");let[r]=i.matches.slice(-1),a={...hn(e||".",{relative:t})},o=ln();if(null==e){a.search=o.search;let e=new URLSearchParams(a.search),t=e.getAll("index"),n=t.some((e=>""===e));if(n){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();a.search=n?`?${n}`:""}}e&&"."!==e||!r.route.index||(a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(a.pathname="/"===a.pathname?n:qt([n,a.pathname]));return Pt(a)}(s,{relative:c}),f="get"===o.toLowerCase()?"get":"post",v="string"==typeof s&&qn.test(s);return w("form",{ref:p,method:f,action:g,onSubmit:i?l:e=>{if(l&&l(e),e.defaultPrevented)return;e.preventDefault();let i=e.nativeEvent.submitter,s=i?.getAttribute("formmethod")||o;m(i||e.currentTarget,{fetcherKey:t,method:s,navigate:n,replace:r,state:a,relative:c,preventScrollReset:u,viewTransition:d})},...h,"data-discover":v||"render"!==e?void 0:"true"})}));function Jn(e){let t=ge(Qt);return xt(t,function(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}(e)),t}Zn.displayName="Form";var Kn=0,Qn=()=>`__${String(++Kn)}__`;class ei{element=null;constructor(e){e.containerEl&&(this.element=e.containerEl)}addDesc(e){if(this.element){const t=document.createElement("div");t.textContent=e,this.element.appendChild(t)}}addDescriptions(e){return this.element&&e.forEach((e=>this.addDesc(e))),this}render(){return null}}var ti=function(){return ti=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},ti.apply(this,arguments)};function ni(e,t,n){if(n||2===arguments.length)for(var i,r=0,a=t.length;r<a;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var ii="-ms-",ri="-moz-",ai="-webkit-",oi="comm",si="rule",li="decl",ci="@keyframes",ui=Math.abs,di=String.fromCharCode,hi=Object.assign;function pi(e){return e.trim()}function mi(e,t){return(e=t.exec(e))?e[0]:e}function gi(e,t,n){return e.replace(t,n)}function fi(e,t,n){return e.indexOf(t,n)}function vi(e,t){return 0|e.charCodeAt(t)}function _i(e,t,n){return e.slice(t,n)}function yi(e){return e.length}function bi(e){return e.length}function wi(e,t){return t.push(e),e}function Ei(e,t){return e.filter((function(e){return!mi(e,t)}))}var Si=1,xi=1,Ci=0,ki=0,Pi=0,Di="";function Ii(e,t,n,i,r,a,o,s){return{value:e,root:t,parent:n,type:i,props:r,children:a,line:Si,column:xi,length:o,return:"",siblings:s}}function Ai(e,t){return hi(Ii("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function Ti(e){for(;e.root;)e=Ai(e.root,{children:[e]});wi(e,e.siblings)}function $i(){return Pi=ki>0?vi(Di,--ki):0,xi--,10===Pi&&(xi=1,Si--),Pi}function zi(){return Pi=ki<Ci?vi(Di,ki++):0,xi++,10===Pi&&(xi=1,Si++),Pi}function Ri(){return vi(Di,ki)}function Ni(){return ki}function Oi(e,t){return _i(Di,e,t)}function Mi(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Fi(e){return pi(Oi(ki-1,ji(91===e?e+2:40===e?e+1:e)))}function Li(e){for(;(Pi=Ri())&&Pi<33;)zi();return Mi(e)>2||Mi(Pi)>3?"":" "}function Bi(e,t){for(;--t&&zi()&&!(Pi<48||Pi>102||Pi>57&&Pi<65||Pi>70&&Pi<97););return Oi(e,Ni()+(t<6&&32==Ri()&&32==zi()))}function ji(e){for(;zi();)switch(Pi){case e:return ki;case 34:case 39:34!==e&&39!==e&&ji(Pi);break;case 40:41===e&&ji(e);break;case 92:zi()}return ki}function Hi(e,t){for(;zi()&&e+Pi!==57&&(e+Pi!==84||47!==Ri()););return"/*"+Oi(t,ki-1)+"*"+di(47===e?e:zi())}function Ui(e){for(;!Mi(Ri());)zi();return Oi(e,ki)}function Wi(e){return function(e){return Di="",e}(Yi("",null,null,null,[""],e=function(e){return Si=xi=1,Ci=yi(Di=e),ki=0,[]}(e),0,[0],e))}function Yi(e,t,n,i,r,a,o,s,l){for(var c=0,u=0,d=o,h=0,p=0,m=0,g=1,f=1,v=1,_=0,y="",b=r,w=a,E=i,S=y;f;)switch(m=_,_=zi()){case 40:if(108!=m&&58==vi(S,d-1)){-1!=fi(S+=gi(Fi(_),"&","&\f"),"&\f",ui(c?s[c-1]:0))&&(v=-1);break}case 34:case 39:case 91:S+=Fi(_);break;case 9:case 10:case 13:case 32:S+=Li(m);break;case 92:S+=Bi(Ni()-1,7);continue;case 47:switch(Ri()){case 42:case 47:wi(qi(Hi(zi(),Ni()),t,n,l),l);break;default:S+="/"}break;case 123*g:s[c++]=yi(S)*v;case 125*g:case 59:case 0:switch(_){case 0:case 125:f=0;case 59+u:-1==v&&(S=gi(S,/\f/g,"")),p>0&&yi(S)-d&&wi(p>32?Gi(S+";",i,n,d-1,l):Gi(gi(S," ","")+";",i,n,d-2,l),l);break;case 59:S+=";";default:if(wi(E=Vi(S,t,n,c,u,r,s,y,b=[],w=[],d,a),a),123===_)if(0===u)Yi(S,t,E,E,b,a,d,s,w);else switch(99===h&&110===vi(S,3)?100:h){case 100:case 108:case 109:case 115:Yi(e,E,E,i&&wi(Vi(e,E,E,0,0,r,s,y,r,b=[],d,w),w),r,w,d,s,i?b:w);break;default:Yi(S,E,E,E,[""],w,0,s,w)}}c=u=p=0,g=v=1,y=S="",d=o;break;case 58:d=1+yi(S),p=m;default:if(g<1)if(123==_)--g;else if(125==_&&0==g++&&125==$i())continue;switch(S+=di(_),_*g){case 38:v=u>0?1:(S+="\f",-1);break;case 44:s[c++]=(yi(S)-1)*v,v=1;break;case 64:45===Ri()&&(S+=Fi(zi())),h=Ri(),u=d=yi(y=S+=Ui(Ni())),_++;break;case 45:45===m&&2==yi(S)&&(g=0)}}return a}function Vi(e,t,n,i,r,a,o,s,l,c,u,d){for(var h=r-1,p=0===r?a:[""],m=bi(p),g=0,f=0,v=0;g<i;++g)for(var _=0,y=_i(e,h+1,h=ui(f=o[g])),b=e;_<m;++_)(b=pi(f>0?p[_]+" "+y:gi(y,/&\f/g,p[_])))&&(l[v++]=b);return Ii(e,t,n,0===r?si:s,l,c,u,d)}function qi(e,t,n,i){return Ii(e,t,n,oi,di(Pi),_i(e,2,-2),0,i)}function Gi(e,t,n,i,r){return Ii(e,t,n,li,_i(e,0,i),_i(e,i+1,-1),i,r)}function Xi(e,t,n){switch(function(e,t){return 45^vi(e,0)?(((t<<2^vi(e,0))<<2^vi(e,1))<<2^vi(e,2))<<2^vi(e,3):0}(e,t)){case 5103:return ai+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return ai+e+e;case 4789:return ri+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return ai+e+ri+e+ii+e+e;case 5936:switch(vi(e,t+11)){case 114:return ai+e+ii+gi(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return ai+e+ii+gi(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return ai+e+ii+gi(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return ai+e+ii+e+e;case 6165:return ai+e+ii+"flex-"+e+e;case 5187:return ai+e+gi(e,/(\w+).+(:[^]+)/,ai+"box-$1$2"+ii+"flex-$1$2")+e;case 5443:return ai+e+ii+"flex-item-"+gi(e,/flex-|-self/g,"")+(mi(e,/flex-|baseline/)?"":ii+"grid-row-"+gi(e,/flex-|-self/g,""))+e;case 4675:return ai+e+ii+"flex-line-pack"+gi(e,/align-content|flex-|-self/g,"")+e;case 5548:return ai+e+ii+gi(e,"shrink","negative")+e;case 5292:return ai+e+ii+gi(e,"basis","preferred-size")+e;case 6060:return ai+"box-"+gi(e,"-grow","")+ai+e+ii+gi(e,"grow","positive")+e;case 4554:return ai+gi(e,/([^-])(transform)/g,"$1"+ai+"$2")+e;case 6187:return gi(gi(gi(e,/(zoom-|grab)/,ai+"$1"),/(image-set)/,ai+"$1"),e,"")+e;case 5495:case 3959:return gi(e,/(image-set\([^]*)/,ai+"$1$`$1");case 4968:return gi(gi(e,/(.+:)(flex-)?(.*)/,ai+"box-pack:$3"+ii+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+ai+e+e;case 4200:if(!mi(e,/flex-|baseline/))return ii+"grid-column-align"+_i(e,t)+e;break;case 2592:case 3360:return ii+gi(e,"template-","")+e;case 4384:case 3616:return n&&n.some((function(e,n){return t=n,mi(e.props,/grid-\w+-end/)}))?~fi(e+(n=n[t].value),"span",0)?e:ii+gi(e,"-start","")+e+ii+"grid-row-span:"+(~fi(n,"span",0)?mi(n,/\d+/):+mi(n,/\d+/)-+mi(e,/\d+/))+";":ii+gi(e,"-start","")+e;case 4896:case 4128:return n&&n.some((function(e){return mi(e.props,/grid-\w+-start/)}))?e:ii+gi(gi(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return gi(e,/(.+)-inline(.+)/,ai+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(yi(e)-1-t>6)switch(vi(e,t+1)){case 109:if(45!==vi(e,t+4))break;case 102:return gi(e,/(.+:)(.+)-([^]+)/,"$1"+ai+"$2-$3$1"+ri+(108==vi(e,t+3)?"$3":"$2-$3"))+e;case 115:return~fi(e,"stretch",0)?Xi(gi(e,"stretch","fill-available"),t,n)+e:e}break;case 5152:case 5920:return gi(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,(function(t,n,i,r,a,o,s){return ii+n+":"+i+s+(r?ii+n+"-span:"+(a?o:+o-+i)+s:"")+e}));case 4949:if(121===vi(e,t+6))return gi(e,":",":"+ai)+e;break;case 6444:switch(vi(e,45===vi(e,14)?18:11)){case 120:return gi(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+ai+(45===vi(e,14)?"inline-":"")+"box$3$1"+ai+"$2$3$1"+ii+"$2box$3")+e;case 100:return gi(e,":",":"+ii)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return gi(e,"scroll-","scroll-snap-")+e}return e}function Zi(e,t){for(var n="",i=0;i<e.length;i++)n+=t(e[i],i,e,t)||"";return n}function Ji(e,t,n,i){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case li:return e.return=e.return||e.value;case oi:return"";case ci:return e.return=e.value+"{"+Zi(e.children,i)+"}";case si:if(!yi(e.value=e.props.join(",")))return""}return yi(n=Zi(e.children,i))?e.return=e.value+"{"+n+"}":""}function Ki(e){var t=bi(e);return function(n,i,r,a){for(var o="",s=0;s<t;s++)o+=e[s](n,i,r,a)||"";return o}}function Qi(e){return function(t){t.root||(t=t.return)&&e(t)}}function er(e,t,n,i){if(e.length>-1&&!e.return)switch(e.type){case li:return void(e.return=Xi(e.value,e.length,n));case ci:return Zi([Ai(e,{value:gi(e.value,"@","@"+ai)})],i);case si:if(e.length)return function(e,t){return e.map(t).join("")}(n=e.props,(function(t){switch(mi(t,i=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":Ti(Ai(e,{props:[gi(t,/:(read-\w+)/,":-moz-$1")]})),Ti(Ai(e,{props:[t]})),hi(e,{props:Ei(n,i)});break;case"::placeholder":Ti(Ai(e,{props:[gi(t,/:(plac\w+)/,":"+ai+"input-$1")]})),Ti(Ai(e,{props:[gi(t,/:(plac\w+)/,":-moz-$1")]})),Ti(Ai(e,{props:[gi(t,/:(plac\w+)/,ii+"input-$1")]})),Ti(Ai(e,{props:[t]})),hi(e,{props:Ei(n,i)})}return""}))}}var tr={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},nr="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",ir="active",rr="data-styled-version",ar="6.1.13",or="/*!sc*/\n",sr="undefined"!=typeof window&&"HTMLElement"in window,lr=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&("false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY)),cr=Object.freeze([]),ur=Object.freeze({});var dr=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),hr=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,pr=/(^-|-$)/g;function mr(e){return e.replace(hr,"-").replace(pr,"")}var gr=/(a)(d)/gi,fr=function(e){return String.fromCharCode(e+(e>25?39:97))};function vr(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=fr(t%52)+n;return(fr(t%52)+n).replace(gr,"$1-$2")}var _r,yr=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},br=function(e){return yr(5381,e)};function wr(e){return"string"==typeof e&&!0}var Er="function"==typeof Symbol&&Symbol.for,Sr=Er?Symbol.for("react.memo"):60115,xr=Er?Symbol.for("react.forward_ref"):60112,Cr={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},kr={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Pr={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Dr=((_r={})[xr]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},_r[Sr]=Pr,_r);function Ir(e){return("type"in(t=e)&&t.type.$$typeof)===Sr?Pr:"$$typeof"in e?Dr[e.$$typeof]:Cr;var t}var Ar=Object.defineProperty,Tr=Object.getOwnPropertyNames,$r=Object.getOwnPropertySymbols,zr=Object.getOwnPropertyDescriptor,Rr=Object.getPrototypeOf,Nr=Object.prototype;function Or(e,t,n){if("string"!=typeof t){if(Nr){var i=Rr(t);i&&i!==Nr&&Or(e,i,n)}var r=Tr(t);$r&&(r=r.concat($r(t)));for(var a=Ir(e),o=Ir(t),s=0;s<r.length;++s){var l=r[s];if(!(l in kr||n&&n[l]||o&&l in o||a&&l in a)){var c=zr(t,l);try{Ar(e,l,c)}catch(e){}}}}return e}function Mr(e){return"function"==typeof e}function Fr(e){return"object"==typeof e&&"styledComponentId"in e}function Lr(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function Br(e,t){if(0===e.length)return"";for(var n=e[0],i=1;i<e.length;i++)n+=e[i];return n}function jr(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function Hr(e,t,n){if(void 0===n&&(n=!1),!n&&!jr(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var i=0;i<t.length;i++)e[i]=Hr(e[i],t[i]);else if(jr(t))for(var i in t)e[i]=Hr(e[i],t[i]);return e}function Ur(e,t){Object.defineProperty(e,"toString",{value:t})}function Wr(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var Yr=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,i=n.length,r=i;e>=r;)if((r<<=1)<0)throw Wr(16,"".concat(e));this.groupSizes=new Uint32Array(r),this.groupSizes.set(n),this.length=r;for(var a=i;a<r;a++)this.groupSizes[a]=0}for(var o=this.indexOfGroup(e+1),s=(a=0,t.length);a<s;a++)this.tag.insertRule(o,t[a])&&(this.groupSizes[e]++,o++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),i=n+t;this.groupSizes[e]=0;for(var r=n;r<i;r++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],i=this.indexOfGroup(e),r=i+n,a=i;a<r;a++)t+="".concat(this.tag.getRule(a)).concat(or);return t},e}(),Vr=new Map,qr=new Map,Gr=1,Xr=function(e){if(Vr.has(e))return Vr.get(e);for(;qr.has(Gr);)Gr++;var t=Gr++;return Vr.set(e,t),qr.set(t,e),t},Zr=function(e,t){Gr=t+1,Vr.set(e,t),qr.set(t,e)},Jr="style[".concat(nr,"][").concat(rr,'="').concat(ar,'"]'),Kr=new RegExp("^".concat(nr,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),Qr=function(e,t,n){for(var i,r=n.split(","),a=0,o=r.length;a<o;a++)(i=r[a])&&e.registerName(t,i)},ea=function(e,t){for(var n,i=(null!==(n=t.textContent)&&void 0!==n?n:"").split(or),r=[],a=0,o=i.length;a<o;a++){var s=i[a].trim();if(s){var l=s.match(Kr);if(l){var c=0|parseInt(l[1],10),u=l[2];0!==c&&(Zr(u,c),Qr(e,u,l[3]),e.getTag().insertRules(c,r)),r.length=0}else r.push(s)}}},ta=function(e){for(var t=document.querySelectorAll(Jr),n=0,i=t.length;n<i;n++){var r=t[n];r&&r.getAttribute(nr)!==ir&&(ea(e,r),r.parentNode&&r.parentNode.removeChild(r))}};var na=function(e){var t=document.head,n=e||t,i=document.createElement("style"),r=function(e){var t=Array.from(e.querySelectorAll("style[".concat(nr,"]")));return t[t.length-1]}(n),a=void 0!==r?r.nextSibling:null;i.setAttribute(nr,ir),i.setAttribute(rr,ar);var o="undefined"!=typeof __webpack_nonce__?__webpack_nonce__:null;return o&&i.setAttribute("nonce",o),n.insertBefore(i,a),i},ia=function(){function e(e){this.element=na(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,i=t.length;n<i;n++){var r=t[n];if(r.ownerNode===e)return r}throw Wr(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),ra=function(){function e(e){this.element=na(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t);return this.element.insertBefore(n,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),aa=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),oa=sr,sa={isServer:!sr,useCSSOMInjection:!lr},la=function(){function e(e,t,n){void 0===e&&(e=ur),void 0===t&&(t={});var i=this;this.options=ti(ti({},sa),e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&sr&&oa&&(oa=!1,ta(this)),Ur(this,(function(){return function(e){for(var t=e.getTag(),n=t.length,i="",r=function(n){var r=function(e){return qr.get(e)}(n);if(void 0===r)return"continue";var a=e.names.get(r),o=t.getGroup(n);if(void 0===a||!a.size||0===o.length)return"continue";var s="".concat(nr,".g").concat(n,'[id="').concat(r,'"]'),l="";void 0!==a&&a.forEach((function(e){e.length>0&&(l+="".concat(e,","))})),i+="".concat(o).concat(s,'{content:"').concat(l,'"}').concat(or)},a=0;a<n;a++)r(a);return i}(i)}))}return e.registerId=function(e){return Xr(e)},e.prototype.rehydrate=function(){!this.server&&sr&&ta(this)},e.prototype.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(ti(ti({},this.options),t),this.gs,n&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,n=e.target;return e.isServer?new aa(n):t?new ia(n):new ra(n)}(this.options),new Yr(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(Xr(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},e.prototype.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(Xr(e),n)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(Xr(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),ca=/&/g,ua=/^\s*\/\/.*$/gm;function da(e,t){return e.map((function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map((function(e){return"".concat(t," ").concat(e)}))),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=da(e.children,t)),e}))}var ha=new la,pa=function(){var e,t,n,i=ur,r=i.options,a=void 0===r?ur:r,o=i.plugins,s=void 0===o?cr:o,l=function(n,i,r){return r.startsWith(t)&&r.endsWith(t)&&r.replaceAll(t,"").length>0?".".concat(e):n},c=s.slice();c.push((function(e){e.type===si&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(ca,t).replace(n,l))})),a.prefix&&c.push(er),c.push(Ji);var u=function(i,r,o,s){void 0===r&&(r=""),void 0===o&&(o=""),void 0===s&&(s="&"),e=s,t=r,n=new RegExp("\\".concat(t,"\\b"),"g");var l=i.replace(ua,""),u=Wi(o||r?"".concat(o," ").concat(r," { ").concat(l," }"):l);a.namespace&&(u=da(u,a.namespace));var d=[];return Zi(u,Ki(c.concat(Qi((function(e){return d.push(e)}))))),d};return u.hash=s.length?s.reduce((function(e,t){return t.name||Wr(15),yr(e,t.name)}),5381).toString():"",u}(),ma=gt.createContext({shouldForwardProp:void 0,styleSheet:ha,stylis:pa});function ga(){return ge(ma)}ma.Consumer,gt.createContext(void 0);var fa=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=pa);var i=n.name+t.hash;e.hasNameForId(n.id,i)||e.insertRules(n.id,i,t(n.rules,i,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,Ur(this,(function(){throw Wr(12,String(n.name))}))}return e.prototype.getName=function(e){return void 0===e&&(e=pa),this.name+e.hash},e}(),va=function(e){return e>="A"&&e<="Z"};function _a(e){for(var t="",n=0;n<e.length;n++){var i=e[n];if(1===n&&"-"===i&&"-"===e[0])return e;va(i)?t+="-"+i.toLowerCase():t+=i}return t.startsWith("ms-")?"-"+t:t}var ya=function(e){return null==e||!1===e||""===e},ba=function(e){var t,n,i=[];for(var r in e){var a=e[r];e.hasOwnProperty(r)&&!ya(a)&&(Array.isArray(a)&&a.isCss||Mr(a)?i.push("".concat(_a(r),":"),a,";"):jr(a)?i.push.apply(i,ni(ni(["".concat(r," {")],ba(a),!1),["}"],!1)):i.push("".concat(_a(r),": ").concat((t=r,null==(n=a)||"boolean"==typeof n||""===n?"":"number"!=typeof n||0===n||t in tr||t.startsWith("--")?String(n).trim():"".concat(n,"px")),";")))}return i};function wa(e,t,n,i){return ya(e)?[]:Fr(e)?[".".concat(e.styledComponentId)]:Mr(e)?!Mr(r=e)||r.prototype&&r.prototype.isReactComponent||!t?[e]:wa(e(t),t,n,i):e instanceof fa?n?(e.inject(n,i),[e.getName(i)]):[e]:jr(e)?ba(e):Array.isArray(e)?Array.prototype.concat.apply(cr,e.map((function(e){return wa(e,t,n,i)}))):[e.toString()];var r}var Ea=br(ar),Sa=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&function(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(Mr(n)&&!Fr(n))return!1}return!0}(e),this.componentId=t,this.baseHash=yr(Ea,t),this.baseStyle=n,la.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var i=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,t,n):"";if(this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(this.componentId,this.staticRulesId))i=Lr(i,this.staticRulesId);else{var r=Br(wa(this.rules,e,t,n)),a=vr(yr(this.baseHash,r)>>>0);if(!t.hasNameForId(this.componentId,a)){var o=n(r,".".concat(a),void 0,this.componentId);t.insertRules(this.componentId,a,o)}i=Lr(i,a),this.staticRulesId=a}else{for(var s=yr(this.baseHash,n.hash),l="",c=0;c<this.rules.length;c++){var u=this.rules[c];if("string"==typeof u)l+=u;else if(u){var d=Br(wa(u,e,t,n));s=yr(s,d+c),l+=d}}if(l){var h=vr(s>>>0);t.hasNameForId(this.componentId,h)||t.insertRules(this.componentId,h,n(l,".".concat(h),void 0,this.componentId)),i=Lr(i,h)}}return i},e}(),xa=gt.createContext(void 0);xa.Consumer;var Ca={};function ka(e,t,n){var i=Fr(e),r=e,a=!wr(e),o=t.attrs,s=void 0===o?cr:o,l=t.componentId,c=void 0===l?function(e,t){var n="string"!=typeof e?"sc":mr(e);Ca[n]=(Ca[n]||0)+1;var i="".concat(n,"-").concat(function(e){return vr(br(e)>>>0)}(ar+n+Ca[n]));return t?"".concat(t,"-").concat(i):i}(t.displayName,t.parentComponentId):l,u=t.displayName,d=void 0===u?function(e){return wr(e)?"styled.".concat(e):"Styled(".concat(function(e){return e.displayName||e.name||"Component"}(e),")")}(e):u,h=t.displayName&&t.componentId?"".concat(mr(t.displayName),"-").concat(t.componentId):t.componentId||c,p=i&&r.attrs?r.attrs.concat(s).filter(Boolean):s,m=t.shouldForwardProp;if(i&&r.shouldForwardProp){var g=r.shouldForwardProp;if(t.shouldForwardProp){var f=t.shouldForwardProp;m=function(e,t){return g(e,t)&&f(e,t)}}else m=g}var v=new Sa(n,h,i?r.componentStyle:void 0);function _(e,t){return function(e,t,n){var i=e.attrs,r=e.componentStyle,a=e.defaultProps,o=e.foldedComponentIds,s=e.styledComponentId,l=e.target,c=gt.useContext(xa),u=ga(),d=e.shouldForwardProp||u.shouldForwardProp,h=function(e,t,n){return void 0===n&&(n=ur),e.theme!==n.theme&&e.theme||t||n.theme}(t,c,a)||ur,p=function(e,t,n){for(var i,r=ti(ti({},t),{className:void 0,theme:n}),a=0;a<e.length;a+=1){var o=Mr(i=e[a])?i(r):i;for(var s in o)r[s]="className"===s?Lr(r[s],o[s]):"style"===s?ti(ti({},r[s]),o[s]):o[s]}return t.className&&(r.className=Lr(r.className,t.className)),r}(i,t,h),m=p.as||l,g={};for(var f in p)void 0===p[f]||"$"===f[0]||"as"===f||"theme"===f&&p.theme===h||("forwardedAs"===f?g.as=p.forwardedAs:d&&!d(f,m)||(g[f]=p[f]));var v=function(e,t){var n=ga();return e.generateAndInjectStyles(t,n.styleSheet,n.stylis)}(r,p),_=Lr(o,s);return v&&(_+=" "+v),p.className&&(_+=" "+p.className),g[wr(m)&&!dr.has(m)?"class":"className"]=_,g.ref=n,w(m,g)}(y,e,t)}_.displayName=d;var y=gt.forwardRef(_);return y.attrs=p,y.componentStyle=v,y.displayName=d,y.shouldForwardProp=m,y.foldedComponentIds=i?Lr(r.foldedComponentIds,r.styledComponentId):"",y.styledComponentId=h,y.target=i?r.target:e,Object.defineProperty(y,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=i?function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var i=0,r=t;i<r.length;i++)Hr(e,r[i],!0);return e}({},r.defaultProps,e):e}}),Ur(y,(function(){return".".concat(y.styledComponentId)})),a&&Or(y,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),y}function Pa(e,t){for(var n=[e[0]],i=0,r=t.length;i<r;i+=1)n.push(t[i],e[i+1]);return n}var Da=function(e){return Object.assign(e,{isCss:!0})};function Ia(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(Mr(e)||jr(e))return Da(wa(Pa(cr,ni([e],t,!0))));var i=e;return 0===t.length&&1===i.length&&"string"==typeof i[0]?wa(i):Da(wa(Pa(i,t)))}function Aa(e,t,n){if(void 0===n&&(n=ur),!t)throw Wr(1,t);var i=function(i){for(var r=[],a=1;a<arguments.length;a++)r[a-1]=arguments[a];return e(t,n,Ia.apply(void 0,ni([i],r,!1)))};return i.attrs=function(i){return Aa(e,t,ti(ti({},n),{attrs:Array.prototype.concat(n.attrs,i).filter(Boolean)}))},i.withConfig=function(i){return Aa(e,t,ti(ti({},n),i))},i}var Ta=function(e){return Aa(ka,e)},$a=Ta;dr.forEach((function(e){$a[e]=Ta(e)}));const za=$a.div`
    border-bottom: ${e=>e.noBorder?"none":"1px solid var(--color-base-30)"};

    &:last-child {
        border-bottom: none;
    }
`;function Ra(e){return void 0!==e.priority}const Na=({name:e,desc:n,setHeading:i,setDisabled:r,setTooltip:a,noBorder:o,class:s,addToggles:l,addTexts:c,addTextAreas:u,addMomentFormats:d,addDropdowns:h,addSearches:p,addButtons:m,addExtraButtons:g,addSliders:f,addColorPickers:v,addProgressBars:_,addMultiDesc:y,setupSettingManually:b})=>{const w=gt.useRef(),E=gt.useRef(null),S=me((t=>{if(b&&b(t),e&&t.setName(e),n&&t.setDesc(n),a&&t.setTooltip(a),y){const e=Ra(y)?y.callback:y,n=document.createElement("div");n.addClass("setting-item-description"),t.infoEl&&t.infoEl.appendChild(n);e(new ei({containerEl:n}))}i&&t.setHeading(),s&&t.setClass(s);const o=[...l?.map(((e,t)=>({type:"toggle",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:0,originalIndex:t})))??[],...c?.map(((e,t)=>({type:"text",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:0,originalIndex:t})))??[],...u?.map(((e,t)=>({type:"textArea",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:0,originalIndex:t})))??[],...d?.map(((e,t)=>({type:"momentFormat",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:0,originalIndex:t})))??[],...h?.map(((e,t)=>({type:"dropdown",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:0,originalIndex:t})))??[],...p?.map(((e,t)=>({type:"search",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:0,originalIndex:t})))??[],...v?.map(((e,t)=>({type:"colorPicker",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:0,originalIndex:t})))??[],..._?.map(((e,t)=>({type:"progressBar",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:0,originalIndex:t})))??[],...m?.map(((e,t)=>({type:"button",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:9,originalIndex:t})))??[],...g?.map(((e,t)=>({type:"extraButton",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:10,originalIndex:t})))??[],...f?.map(((e,t)=>({type:"slider",callback:Ra(e)?e.callback:e,priority:Ra(e)?e.priority:0,originalIndex:t})))??[]].filter((e=>void 0!==e.callback&&!1!==e.callback)).sort(((e,t)=>e.priority===t.priority?e.originalIndex-t.originalIndex:e.priority-t.priority));o.forEach((e=>{switch(e.type){case"toggle":t.addToggle(e.callback);break;case"text":t.addText(e.callback);break;case"textArea":t.addTextArea(e.callback);break;case"momentFormat":t.addMomentFormat(e.callback);break;case"dropdown":t.addDropdown(e.callback);break;case"search":t.addSearch(e.callback);break;case"colorPicker":t.addColorPicker(e.callback);break;case"progressBar":t.addProgressBar(e.callback);break;case"button":t.addButton(e.callback);break;case"extraButton":t.addExtraButton(e.callback);break;case"slider":t.addSlider(e.callback)}})),t.setDisabled(!!r)}),[e,n,i,r,a,s,l,c,u,d,h,p,m,g,f,v,_,y,b]);return ue((()=>{if(E.current)return E.current.empty(),w.current=new t.Setting(E.current),S(w.current),()=>{E.current?.empty()}}),[[e,n,i,r,a,o,s,l,c,u,d,h,p,m,g,f,v,_,y,b]]),gt.createElement(za,{noBorder:o,ref:E,className:`react-obsidian-settings-item ${s??""}`})},Oa=()=>{const{plugin:e}=yt();return gt.createElement(gt.Fragment,null,gt.createElement(Na,{name:"Visit GitHub page of this plugin",addButtons:[e=>(e.setIcon("github"),e.setTooltip("Go to GitHub page of this plugin"),e.onClick((e=>{open("https://github.com/Ssentiago/diagram-zoom-drag/")})),e)]}),gt.createElement(Na,{name:"Do you have any feedback?",addButtons:[e=>(e.setIcon("message-circle-question"),e.setTooltip("Leave feedback"),e.onClick((()=>{open("https://github.com/Ssentiago/diagram-zoom-drag/issues")})),e)]}),gt.createElement("div",{style:{position:"absolute",bottom:10,left:"50%"}},gt.createElement("div",{style:{fontSize:"small",color:"gray",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"}},gt.createElement("div",null,"Current version: ",e.manifest.version),gt.createElement("div",null,"•"," ",gt.createElement("a",{href:"https://github.com/Ssentiago/diagram-zoom-drag/blob/main/LICENSE",target:"_blank"},"Apache-2.0 License")))))};var Ma;!function(e){e.Folded="folded",e.Expanded="expanded"}(Ma||(Ma={}));const Fa={px:{min:100,max:1e3,label:"px",rangeMessage:"100-1000px"},"%":{min:10,max:100,label:"%",rangeMessage:"10-100%"}},La=e=>Fa[e].rangeMessage,Ba=(e,t)=>{const n=parseInt(e,10),{min:i,max:r}=Fa[t];return n>=i&&n<=r},ja=(e,t)=>`Invalid ${e}. Please enter number in range ${La(t)}.`,Ha=e=>({min:Fa[e].min.toString(),max:Fa[e].max.toString()}),Ua=()=>{const{plugin:e}=yt(),[t,n]=se(e.settings.diagramExpanded.height),[i,r]=se(e.settings.diagramExpanded.width),[a,o]=se(e.settings.diagramExpanded.heightUnit),[s,l]=se(e.settings.diagramExpanded.widthUnit),[c,u]=se(e.settings.diagramFolded.height),[d,h]=se(e.settings.diagramFolded.width),[p,m]=se(e.settings.diagramFolded.heightUnit),[g,f]=se(e.settings.diagramFolded.widthUnit),v=e=>/^\d+$/.test(e),_=_=>{const y=_===Ma.Folded?"Folded":"Expanded",b=_===Ma.Folded?c:t,w=_===Ma.Folded?d:i,E=_===Ma.Folded?p:a,S=_===Ma.Folded?g:s,x=_===Ma.Folded?u:n,C=_===Ma.Folded?h:r,k=_===Ma.Folded?m:o,P=_===Ma.Folded?f:l;return gt.createElement(Na,{name:`${y} diagram container size`,addMultiDesc:e=>(e.addDescriptions([`Set the container dimensions for ${y.toLowerCase()} state.`,"px: 100-1000, %: 10-100","Click Save button to apply changes."]),e),addTexts:[t=>{const n=t.inputEl.parentElement;if(n){const e=document.createElement("label");e.textContent="Height:",n.insertBefore(e,t.inputEl)}t.setValue(b),t.setPlaceholder("height"),t.inputEl.type="number";const{min:i,max:r}=Ha(E);return t.inputEl.min=i,t.inputEl.max=r,t.inputEl.ariaLabel=`${y} height`,t.inputEl.onblur=()=>{v(t.getValue())?x(t.getValue()):e.showNotice("Please enter valid number")},t},t=>{const n=t.inputEl.parentElement;if(n){const e=document.createElement("label");e.textContent="Width:",n.insertBefore(e,t.inputEl)}t.setValue(w),t.setPlaceholder("width"),t.inputEl.type="number";const{min:i,max:r}=Ha(S);return t.inputEl.min=i,t.inputEl.max=r,t.inputEl.ariaLabel=`${y} width`,t.inputEl.onblur=()=>{v(t.getValue())?C(t.getValue()):e.showNotice("Please enter valid number")},t}],addDropdowns:[e=>(e.addOptions({px:"px","%":"%"}),e.setValue(E),e.onChange((e=>{"px"!==e&&"%"!==e||k(e)})),e),e=>(e.addOptions({px:"px","%":"%"}),e.setValue(S),e.onChange((e=>{"px"!==e&&"%"!==e||P(e)})),e)],addButtons:[t=>(t.setIcon("save"),t.onClick((async()=>{const t=!v(b)||!Ba(b,E),n=!v(w)||!Ba(w,S);t&&n?e.showNotice(`Invalid height and width.\nHeight must be ${La(E)}, width must be ${La(S)}.`):t?e.showNotice(ja("height",E)):n?e.showNotice(ja("width",S)):(_===Ma.Folded?(e.settings.diagramFolded.height=b,e.settings.diagramFolded.heightUnit=E,e.settings.diagramFolded.width=w,e.settings.diagramFolded.widthUnit=S):(e.settings.diagramExpanded.height=b,e.settings.diagramExpanded.heightUnit=E,e.settings.diagramExpanded.width=w,e.settings.diagramExpanded.widthUnit=S),await e.settingsManager.saveSettings(),e.showNotice("Saved successfully"))})),t)],noBorder:!0})};return gt.createElement(gt.Fragment,null,gt.createElement(Na,{name:"Diagram Size",addMultiDesc:e=>(e.addDescriptions(["Note: You need to reopen all the open Markdown views with diagrams in them to apply these settings."]),e),setHeading:!0}),_(Ma.Expanded),_(Ma.Folded))},Wa=()=>{const{plugin:e}=yt();return gt.createElement(gt.Fragment,null,gt.createElement(Na,{name:"Fold",setHeading:!0}),gt.createElement(Na,{name:"Fold diagrams by default?",addToggles:[t=>(t.setValue(e.settings.foldByDefault).onChange((async t=>{e.settings.foldByDefault=t,await e.settingsManager.saveSettings()})),t)]}),gt.createElement(Na,{name:"Automatically fold diagrams on focus change?",addToggles:[t=>(t.setValue(e.settings.automaticFoldingOnFocusChange).onChange((async t=>{e.settings.automaticFoldingOnFocusChange=t,await e.settingsManager.saveSettings()})),t)]}))},Ya=()=>{const{plugin:e}=yt();return gt.createElement(gt.Fragment,null,gt.createElement(Ua,null),gt.createElement(Wa,null))},Va=({modal:e})=>{const n=e.plugin.manifest.dir,[i,r]=se(!0),[a,o]=se(""),s=de(t.normalizePath(`${n}/assets/user-guide-video.mp4`));return ce((()=>{r(!0),e.loadVideo().then((t=>{t&&(async()=>{try{const t=await e.app.vault.adapter.readBinary(s.current),n=Buffer.from(t).toString("base64");o(`data:video/mp4;base64,${n}`)}catch(t){console.error(t),e.plugin.showNotice("Something went wrong. The video is missing.")}finally{r(!1)}})()})).catch((e=>console.error(e)))}),[e]),gt.createElement(gt.Fragment,null,gt.createElement(Na,{name:"How this plugin does work",setHeading:!0}),gt.createElement(Na,{addMultiDesc:e=>(e.addDesc("This plugin stores data related to your selected elements."),e.addDesc("When you open another Markdown file with a diagram code in it and switch to preview mode, the plugin attempts to find the corresponding diagram in preview."),e.addDesc("If a matching diagram is found, the plugin creates a container, applies CSS styles, and enables diagram movement, zooming, and adds a control panel."),e)}),gt.createElement(Na,{name:"How to find selectors in DevTool",setHeading:!0,desc:"To identify the CSS selectors for diagrams on this page, follow these steps below using your browser’s DevTools:"}),gt.createElement(Na,{name:"Steps to find selectors:",addMultiDesc:e=>(e.addDesc("1. Open the markdown file in Obsidian where the diagram is. You should switch to preview mode."),e.addDesc("2. Open the DevTools window. You can do it by pressing CTRL + SHIFT + I."),e.addDesc('3. Click the "Select an element on this page to inspect it" button (usually a arrow icon) in the top-left corner of the DevTools window. You can also press CTRL + SHIFT + C'),e.addDesc("4. Move your cursor over the diagram and click on it to select the element."),e.addDesc("5. In the Elements tab of DevTools, you will see the HTML element corresponding to the diagram highlighted."),e.addDesc("6. Look at the classes applied to this element in the DevTools panel to identify the CSS selectors you need."),e)}),i&&gt.createElement("p",null,"Loading video..."),!i&&a&&gt.createElement("video",{src:a,controls:!0,autoPlay:!1,style:{width:"100%",maxHeight:"400px"}}))};class qa extends t.Modal{plugin;root=void 0;constructor(e,t){super(e),this.plugin=t,this.setTitle("Guide")}async onOpen(){const{contentEl:e}=this;this.root=ft(e.createEl("div")),this.root.render(gt.createElement(Va,{modal:this}))}onClose(){this.root?.unmount(),this.contentEl.empty()}async loadVideo(){const e=await this.plugin.pluginStateChecker.isFirstPluginStart(),n=this.plugin.manifest.dir;if(!n)return!1;const i=t.normalizePath(`${n}/assets`),r=t.normalizePath(`${i}/user-guide-video.mp4`);if(await this.app.vault.adapter.exists(i)||await this.app.vault.adapter.mkdir(i),e)await this.downloadVideo(r);else{await this.app.vault.adapter.exists(r)||await this.downloadVideo(r)}return this.app.vault.adapter.exists(r)}async downloadVideo(e){try{const n="https://raw.githubusercontent.com/gitcpy/diagram-zoom-drag/main/assets/videos/find-class.mp4",i=await t.requestUrl(n);if(200!==i.status)throw new Error(`Error downloading video: ${i.status}`);return await this.app.vault.adapter.writeBinary(e,i.arrayBuffer),!0}catch(e){return console.error("Error downloading video:",e),!1}}}var Ga;!function(e){e.PanelsChangedVisibility="PanelsChangedVisibility",e.VisibilityOptionChanged="VisibilityOptionChanged",e.ItemsPerPageChanged="ItemsPerPageChanged",e.FoldStateChanged="FoldStateChanged"}(Ga||(Ga={}));var Xa="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",Za="active",Ja="data-styled-version",Ka="6.1.18",Qa="/*!sc*/\n",eo="undefined"!=typeof window&&"undefined"!=typeof document,to=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&("false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY)),no=Object.freeze([]),io=Object.freeze({});var ro=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),ao=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,oo=/(^-|-$)/g;function so(e){return e.replace(ao,"-").replace(oo,"")}var lo=/(a)(d)/gi,co=function(e){return String.fromCharCode(e+(e>25?39:97))};function uo(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=co(t%52)+n;return(co(t%52)+n).replace(lo,"$1-$2")}var ho,po=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},mo=function(e){return po(5381,e)};function go(e){return uo(mo(e)>>>0)}function fo(e){return"string"==typeof e&&!0}var vo="function"==typeof Symbol&&Symbol.for,_o=vo?Symbol.for("react.memo"):60115,yo=vo?Symbol.for("react.forward_ref"):60112,bo={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},wo={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Eo={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},So=((ho={})[yo]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},ho[_o]=Eo,ho);function xo(e){return("type"in(t=e)&&t.type.$$typeof)===_o?Eo:"$$typeof"in e?So[e.$$typeof]:bo;var t}var Co=Object.defineProperty,ko=Object.getOwnPropertyNames,Po=Object.getOwnPropertySymbols,Do=Object.getOwnPropertyDescriptor,Io=Object.getPrototypeOf,Ao=Object.prototype;function To(e,t,n){if("string"!=typeof t){if(Ao){var i=Io(t);i&&i!==Ao&&To(e,i,n)}var r=ko(t);Po&&(r=r.concat(Po(t)));for(var a=xo(e),o=xo(t),s=0;s<r.length;++s){var l=r[s];if(!(l in wo||n&&n[l]||o&&l in o||a&&l in a)){var c=Do(t,l);try{Co(e,l,c)}catch(e){}}}}return e}function $o(e){return"function"==typeof e}function zo(e){return"object"==typeof e&&"styledComponentId"in e}function Ro(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function No(e,t){if(0===e.length)return"";for(var n=e[0],i=1;i<e.length;i++)n+=e[i];return n}function Oo(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function Mo(e,t,n){if(void 0===n&&(n=!1),!n&&!Oo(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var i=0;i<t.length;i++)e[i]=Mo(e[i],t[i]);else if(Oo(t))for(var i in t)e[i]=Mo(e[i],t[i]);return e}function Fo(e,t){Object.defineProperty(e,"toString",{value:t})}function Lo(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var Bo=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,i=n.length,r=i;e>=r;)if((r<<=1)<0)throw Lo(16,"".concat(e));this.groupSizes=new Uint32Array(r),this.groupSizes.set(n),this.length=r;for(var a=i;a<r;a++)this.groupSizes[a]=0}for(var o=this.indexOfGroup(e+1),s=(a=0,t.length);a<s;a++)this.tag.insertRule(o,t[a])&&(this.groupSizes[e]++,o++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),i=n+t;this.groupSizes[e]=0;for(var r=n;r<i;r++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],i=this.indexOfGroup(e),r=i+n,a=i;a<r;a++)t+="".concat(this.tag.getRule(a)).concat(Qa);return t},e}(),jo=new Map,Ho=new Map,Uo=1,Wo=function(e){if(jo.has(e))return jo.get(e);for(;Ho.has(Uo);)Uo++;var t=Uo++;return jo.set(e,t),Ho.set(t,e),t},Yo=function(e,t){Uo=t+1,jo.set(e,t),Ho.set(t,e)},Vo="style[".concat(Xa,"][").concat(Ja,'="').concat(Ka,'"]'),qo=new RegExp("^".concat(Xa,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),Go=function(e,t,n){for(var i,r=n.split(","),a=0,o=r.length;a<o;a++)(i=r[a])&&e.registerName(t,i)},Xo=function(e,t){for(var n,i=(null!==(n=t.textContent)&&void 0!==n?n:"").split(Qa),r=[],a=0,o=i.length;a<o;a++){var s=i[a].trim();if(s){var l=s.match(qo);if(l){var c=0|parseInt(l[1],10),u=l[2];0!==c&&(Yo(u,c),Go(e,u,l[3]),e.getTag().insertRules(c,r)),r.length=0}else r.push(s)}}},Zo=function(e){for(var t=document.querySelectorAll(Vo),n=0,i=t.length;n<i;n++){var r=t[n];r&&r.getAttribute(Xa)!==Za&&(Xo(e,r),r.parentNode&&r.parentNode.removeChild(r))}};var Jo=function(e){var t=document.head,n=e||t,i=document.createElement("style"),r=function(e){var t=Array.from(e.querySelectorAll("style[".concat(Xa,"]")));return t[t.length-1]}(n),a=void 0!==r?r.nextSibling:null;i.setAttribute(Xa,Za),i.setAttribute(Ja,Ka);var o="undefined"!=typeof __webpack_nonce__?__webpack_nonce__:null;return o&&i.setAttribute("nonce",o),n.insertBefore(i,a),i},Ko=function(){function e(e){this.element=Jo(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,i=t.length;n<i;n++){var r=t[n];if(r.ownerNode===e)return r}throw Lo(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),Qo=function(){function e(e){this.element=Jo(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t);return this.element.insertBefore(n,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),es=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),ts=eo,ns={isServer:!eo,useCSSOMInjection:!to},is=function(){function e(e,t,n){void 0===e&&(e=io),void 0===t&&(t={});var i=this;this.options=ti(ti({},ns),e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&eo&&ts&&(ts=!1,Zo(this)),Fo(this,(function(){return function(e){for(var t=e.getTag(),n=t.length,i="",r=function(n){var r=function(e){return Ho.get(e)}(n);if(void 0===r)return"continue";var a=e.names.get(r),o=t.getGroup(n);if(void 0===a||!a.size||0===o.length)return"continue";var s="".concat(Xa,".g").concat(n,'[id="').concat(r,'"]'),l="";void 0!==a&&a.forEach((function(e){e.length>0&&(l+="".concat(e,","))})),i+="".concat(o).concat(s,'{content:"').concat(l,'"}').concat(Qa)},a=0;a<n;a++)r(a);return i}(i)}))}return e.registerId=function(e){return Wo(e)},e.prototype.rehydrate=function(){!this.server&&eo&&Zo(this)},e.prototype.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(ti(ti({},this.options),t),this.gs,n&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,n=e.target;return e.isServer?new es(n):t?new Ko(n):new Qo(n)}(this.options),new Bo(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(Wo(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},e.prototype.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(Wo(e),n)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(Wo(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),rs=/&/g,as=/^\s*\/\/.*$/gm;function os(e,t){return e.map((function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map((function(e){return"".concat(t," ").concat(e)}))),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=os(e.children,t)),e}))}var ss=new is,ls=function(){var e,t,n,i=io,r=i.options,a=void 0===r?io:r,o=i.plugins,s=void 0===o?no:o,l=function(n,i,r){return r.startsWith(t)&&r.endsWith(t)&&r.replaceAll(t,"").length>0?".".concat(e):n},c=s.slice();c.push((function(e){e.type===si&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(rs,t).replace(n,l))})),a.prefix&&c.push(er),c.push(Ji);var u=function(i,r,o,s){void 0===r&&(r=""),void 0===o&&(o=""),void 0===s&&(s="&"),e=s,t=r,n=new RegExp("\\".concat(t,"\\b"),"g");var l=i.replace(as,""),u=Wi(o||r?"".concat(o," ").concat(r," { ").concat(l," }"):l);a.namespace&&(u=os(u,a.namespace));var d=[];return Zi(u,Ki(c.concat(Qi((function(e){return d.push(e)}))))),d};return u.hash=s.length?s.reduce((function(e,t){return t.name||Lo(15),po(e,t.name)}),5381).toString():"",u}(),cs=gt.createContext({shouldForwardProp:void 0,styleSheet:ss,stylis:ls});function us(){return ge(cs)}cs.Consumer,gt.createContext(void 0);var ds=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=ls);var i=n.name+t.hash;e.hasNameForId(n.id,i)||e.insertRules(n.id,i,t(n.rules,i,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,Fo(this,(function(){throw Lo(12,String(n.name))}))}return e.prototype.getName=function(e){return void 0===e&&(e=ls),this.name+e.hash},e}(),hs=function(e){return e>="A"&&e<="Z"};function ps(e){for(var t="",n=0;n<e.length;n++){var i=e[n];if(1===n&&"-"===i&&"-"===e[0])return e;hs(i)?t+="-"+i.toLowerCase():t+=i}return t.startsWith("ms-")?"-"+t:t}var ms=function(e){return null==e||!1===e||""===e},gs=function(e){var t,n,i=[];for(var r in e){var a=e[r];e.hasOwnProperty(r)&&!ms(a)&&(Array.isArray(a)&&a.isCss||$o(a)?i.push("".concat(ps(r),":"),a,";"):Oo(a)?i.push.apply(i,ni(ni(["".concat(r," {")],gs(a),!1),["}"],!1)):i.push("".concat(ps(r),": ").concat((t=r,null==(n=a)||"boolean"==typeof n||""===n?"":"number"!=typeof n||0===n||t in tr||t.startsWith("--")?String(n).trim():"".concat(n,"px")),";")))}return i};function fs(e,t,n,i){return ms(e)?[]:zo(e)?[".".concat(e.styledComponentId)]:$o(e)?!$o(r=e)||r.prototype&&r.prototype.isReactComponent||!t?[e]:fs(e(t),t,n,i):e instanceof ds?n?(e.inject(n,i),[e.getName(i)]):[e]:Oo(e)?gs(e):Array.isArray(e)?Array.prototype.concat.apply(no,e.map((function(e){return fs(e,t,n,i)}))):[e.toString()];var r}var vs=mo(Ka),_s=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&function(e){for(var t=0;t<e.length;t+=1){var n=e[t];if($o(n)&&!zo(n))return!1}return!0}(e),this.componentId=t,this.baseHash=po(vs,t),this.baseStyle=n,is.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var i=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,t,n):"";if(this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(this.componentId,this.staticRulesId))i=Ro(i,this.staticRulesId);else{var r=No(fs(this.rules,e,t,n)),a=uo(po(this.baseHash,r)>>>0);if(!t.hasNameForId(this.componentId,a)){var o=n(r,".".concat(a),void 0,this.componentId);t.insertRules(this.componentId,a,o)}i=Ro(i,a),this.staticRulesId=a}else{for(var s=po(this.baseHash,n.hash),l="",c=0;c<this.rules.length;c++){var u=this.rules[c];if("string"==typeof u)l+=u;else if(u){var d=No(fs(u,e,t,n));s=po(s,d+c),l+=d}}if(l){var h=uo(s>>>0);t.hasNameForId(this.componentId,h)||t.insertRules(this.componentId,h,n(l,".".concat(h),void 0,this.componentId)),i=Ro(i,h)}}return i},e}(),ys=gt.createContext(void 0);ys.Consumer;var bs={};function ws(e,t,n){var i=zo(e),r=e,a=!fo(e),o=t.attrs,s=void 0===o?no:o,l=t.componentId,c=void 0===l?function(e,t){var n="string"!=typeof e?"sc":so(e);bs[n]=(bs[n]||0)+1;var i="".concat(n,"-").concat(go(Ka+n+bs[n]));return t?"".concat(t,"-").concat(i):i}(t.displayName,t.parentComponentId):l,u=t.displayName,d=void 0===u?function(e){return fo(e)?"styled.".concat(e):"Styled(".concat(function(e){return e.displayName||e.name||"Component"}(e),")")}(e):u,h=t.displayName&&t.componentId?"".concat(so(t.displayName),"-").concat(t.componentId):t.componentId||c,p=i&&r.attrs?r.attrs.concat(s).filter(Boolean):s,m=t.shouldForwardProp;if(i&&r.shouldForwardProp){var g=r.shouldForwardProp;if(t.shouldForwardProp){var f=t.shouldForwardProp;m=function(e,t){return g(e,t)&&f(e,t)}}else m=g}var v=new _s(n,h,i?r.componentStyle:void 0);function _(e,t){return function(e,t,n){var i=e.attrs,r=e.componentStyle,a=e.defaultProps,o=e.foldedComponentIds,s=e.styledComponentId,l=e.target,c=gt.useContext(ys),u=us(),d=e.shouldForwardProp||u.shouldForwardProp,h=function(e,t,n){return void 0===n&&(n=io),e.theme!==n.theme&&e.theme||t||n.theme}(t,c,a)||io,p=function(e,t,n){for(var i,r=ti(ti({},t),{className:void 0,theme:n}),a=0;a<e.length;a+=1){var o=$o(i=e[a])?i(r):i;for(var s in o)r[s]="className"===s?Ro(r[s],o[s]):"style"===s?ti(ti({},r[s]),o[s]):o[s]}return t.className&&(r.className=Ro(r.className,t.className)),r}(i,t,h),m=p.as||l,g={};for(var f in p)void 0===p[f]||"$"===f[0]||"as"===f||"theme"===f&&p.theme===h||("forwardedAs"===f?g.as=p.forwardedAs:d&&!d(f,m)||(g[f]=p[f]));var v=function(e,t){var n=us();return e.generateAndInjectStyles(t,n.styleSheet,n.stylis)}(r,p),_=Ro(o,s);return v&&(_+=" "+v),p.className&&(_+=" "+p.className),g[fo(m)&&!ro.has(m)?"class":"className"]=_,n&&(g.ref=n),w(m,g)}(y,e,t)}_.displayName=d;var y=gt.forwardRef(_);return y.attrs=p,y.componentStyle=v,y.displayName=d,y.shouldForwardProp=m,y.foldedComponentIds=i?Ro(r.foldedComponentIds,r.styledComponentId):"",y.styledComponentId=h,y.target=i?r.target:e,Object.defineProperty(y,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=i?function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var i=0,r=t;i<r.length;i++)Mo(e,r[i],!0);return e}({},r.defaultProps,e):e}}),Fo(y,(function(){return".".concat(y.styledComponentId)})),a&&To(y,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),y}function Es(e,t){for(var n=[e[0]],i=0,r=t.length;i<r;i+=1)n.push(t[i],e[i+1]);return n}var Ss=function(e){return Object.assign(e,{isCss:!0})};function xs(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if($o(e)||Oo(e))return Ss(fs(Es(no,ni([e],t,!0))));var i=e;return 0===t.length&&1===i.length&&"string"==typeof i[0]?fs(i):Ss(fs(Es(i,t)))}function Cs(e,t,n){if(void 0===n&&(n=io),!t)throw Lo(1,t);var i=function(i){for(var r=[],a=1;a<arguments.length;a++)r[a-1]=arguments[a];return e(t,n,xs.apply(void 0,ni([i],r,!1)))};return i.attrs=function(i){return Cs(e,t,ti(ti({},n),{attrs:Array.prototype.concat(n.attrs,i).filter(Boolean)}))},i.withConfig=function(i){return Cs(e,t,ti(ti({},n),i))},i}var ks=function(e){return Cs(ws,e)},Ps=ks;ro.forEach((function(e){Ps[e]=ks(e)}));const Ds=Ps.div`
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;

    &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 1px;
        background-color: var(--color-base-30);
        margin-top: 20px;
    }
`,Is=Ps.button`
    &:disabled {
        background-color: var(--color-base-50);
        cursor: not-allowed;
    }
`,As=/^[\w-]+$/,Ts=/^[.#][\w\s._>+~-]+$/;function $s(e,t,n,i){const r=function(e,t,n){const i=t.value,r=n.value;return!(!As.test(i)||!Ts.test(r))||(e.showNotice("Input is not valid!"),t.addClass("snake"),n.addClass("snake"),setTimeout((()=>{t.removeClass("shake"),n.removeClass("shake")}),500),!1)}(e,t,n),a=function(e,t,n,i){const r=t.value,a=n.value,o=i.find((e=>e.name===r||e.selector===a));return!o||(t.addClass("shake"),n.addClass("shake"),setTimeout((()=>{t.removeClass("shake"),n.removeClass("shake")}),500),e.showNotice("Diagram already exists!"),!1)}(e,t,n,i);return r&&a}function zs(e,t){const n=t.value,i=As.test(n);n?(t.toggleClass("invalid",!i),t.ariaLabel=i?"":"Incorrect input. Should be only `A-Za-z0-9-`"):(t.removeClass("invalid"),t.ariaLabel="")}function Rs(e,t){const n=t.value,i=Ts.test(n);n?(t.toggleClass("invalid",!i),t.ariaLabel=i?"":"Input incorrect. It seems to be not a valid CSS selector?"):(t.removeClass("invalid"),t.ariaLabel="")}class Ns extends t.Modal{onSubmit;constructor(e,t,n){super(e),this.onSubmit=n,this.setTitle(`Editing ${t}...`)}onOpen(){new t.Setting(this.contentEl).setName("Are you sure you want to switch the page? You will lose your unsaved changes.").setHeading().addButton((e=>{e.setButtonText("Proceed without saving"),e.onClick((e=>{this.onSubmit("Yes"),this.close()}))})).addButton((e=>{e.setButtonText("Cancel"),e.onClick((e=>{this.onSubmit("No"),this.close()}))})).addButton((e=>{e.setButtonText("Save and continue"),e.onClick((e=>{this.onSubmit("Save"),this.close()}))}))}onClose(){this.contentEl.empty()}}class Os extends t.Modal{name;initial;callback;constructor(e,t,n,i){super(e),this.name=t,this.initial=n,this.callback=i,this.setTitle(`Set diagram controls for ${this.name} diagram`)}onOpen(){const{contentEl:e}=this;new t.Setting(e).setDesc("These settings will only apply to this diagram."),new t.Setting(e).setName("Move panel").addToggle((e=>{e.setValue(this.initial.move.on),e.onChange((async e=>{await this.callback({on:e,panel:"move"})}))})),new t.Setting(e).setName("Zoom panel").addToggle((e=>{e.setValue(this.initial.zoom.on),e.onChange((async e=>{await this.callback({on:e,panel:"zoom"})}))})),new t.Setting(e).setName("Service panel").addToggle((e=>{e.setValue(this.initial.service.on),e.onChange((async e=>{await this.callback({on:e,panel:"service"})}))}))}hide(){this.contentEl.empty()}}const Ms=()=>{const{app:e,plugin:t}=yt(),[n,i]=se(void 0),[r,a]=se(t.settings.supported_diagrams),[o,s]=se(t.settings.diagramsPerPage);ce((()=>(t.observer.subscribe(e.workspace,Ga.ItemsPerPageChanged,(async()=>{s(t.settings.diagramsPerPage)})),()=>{t.observer.unsubscribeFromEvent(e.workspace,Ga.ItemsPerPageChanged)})),[e.workspace,t.settings]);const[l,c]=se(1),u=Math.ceil(r.length/o),d=(l-1)*o,h=d+o,p=async e=>{const n=document.querySelector("#editing-name-input"),o=document.querySelector("#editing-selector-input");if(!n||!o)return;const s=$s(t,n,o,r.slice(0,e).concat(r.slice(e+1)));return s&&(r[e].name=n.value,r[e].selector=o.value,a([...r]),t.settings.supported_diagrams=r,await t.settingsManager.saveSettings(),n.removeAttribute("id"),o.removeAttribute("id"),i(void 0)),s},m=e=>d+e,g=e=>{c((t=>Math.min(u,Math.max(t+e,1))))},f=a=>{void 0!==n?new Ns(e,r[m(n)].name,(async e=>{if("Yes"===e)i(void 0),g(a);else if("Save"===e){await p(m(n))||t.showNotice("Could not save diagram"),g(a)}})).open():g(a)};return gt.createElement(gt.Fragment,null,gt.createElement(Ds,null,gt.createElement(Is,{onClick:()=>f(-1),disabled:1===l},"←"),`Page ${l} of ${u} (Total diagrams: ${r.length})`,gt.createElement(Is,{onClick:()=>f(1),disabled:l===u},"→")),t.settings.supported_diagrams.slice(d,h).map(((o,s)=>{const{name:u,selector:h}=o;return n===s?gt.createElement(Na,{addTexts:[e=>(e.setValue(r[m(s)].name),e.inputEl.id="editing-name-input",e.onChange((t=>{zs(0,e.inputEl)})),e),e=>(e.setValue(r[m(s)].selector),e.inputEl.id="editing-selector-input",e.onChange((t=>{Rs(0,e.inputEl)})),e)],addButtons:[e=>(e.setIcon("circle-x"),e.setTooltip("Cancel operation? All changes will be lost."),e.onClick((e=>{i(void 0)})),e),e=>(e.setIcon("save"),e.setTooltip(`Save changes for ${r[m(s)].name}?`),e.onClick((async e=>{await p(m(s))})),e)]}):gt.createElement(Na,{name:u,desc:h,addToggles:[e=>(e.setValue(r[m(s)].on),e.setTooltip(`${r[m(s)].on?"Disable":"Enable"} ${r[m(s)].name} diagram`),e.onChange((async e=>{r[m(s)].on=e,a([...r]),t.settings.supported_diagrams=r,await t.settingsManager.saveSettings()})),e)],addButtons:["Default"!==r[m(s)].name&&(e=>(e.setIcon("edit"),e.setTooltip(`Edit ${r[m(s)].name} diagram`),e.onClick((async()=>{i(s)})),e)),"Default"!==r[m(s)].name&&(e=>(e.setIcon("trash"),e.setTooltip(`Delete ${r[m(s)].name} diagram`),e.onClick((async()=>{await(async e=>{const n=[...r];n.splice(e,1),a(n),t.settings.supported_diagrams=n,await t.settingsManager.saveSettings(),l>1&&d>=n.length&&c((e=>e-1))})(m(s))})),e))],addExtraButtons:[n=>(n.setTooltip(`Set what controls will be active for ${r[m(s)].name} diagram`),n.onClick((()=>{const n=r[m(s)].panels;new Os(e,r[m(s)].name,n,(async e=>{r[m(s)].panels[e.panel].on=e.on,await t.settingsManager.saveSettings()})).open()})),n)]})})))},Fs=()=>{const{app:e,plugin:n}=yt(),[i,r]=le((e=>e+1),0);return gt.createElement(gt.Fragment,null,gt.createElement(Na,{name:"Add new diagram",setHeading:!0,noBorder:!0,desc:"Here you can configure which diagrams will receive enhanced controls and UI.",addMultiDesc:e=>(e.addDescriptions(["Adding a Diagram Type:","1. Enter a unique name using only Latin letters, numbers and `-` (A-Z, a-z, 0-9, -)","2. Specify a valid CSS selector for your diagram","Once added, matching diagrams will get:","• Mouse and keyboard navigation","• Additional control buttons","Note: Red border indicates invalid input - hover to see details"]),e)}),gt.createElement(Na,{addTexts:[e=>(e.inputEl.id="diagram-name",e.setPlaceholder("Example Diagram"),e.onChange((t=>{e.setValue(t),zs(0,e.inputEl)})),e),e=>(e.inputEl.id="diagram-selector",e.setPlaceholder(".example-diagram"),e.onChange((t=>{e.setValue(t),Rs(0,e.inputEl)})),e)],addButtons:[e=>(e.setIcon("save"),e.setTooltip("Add this diagram"),e.onClick((async()=>{const e=document.querySelector("#diagram-name"),t=document.querySelector("#diagram-selector");e&&t&&await(async(e,t)=>{$s(n,e,t,n.settings.supported_diagrams)&&(n.settings.supported_diagrams.push({name:e.value,selector:t.value,on:!0,panels:{move:{on:!0},zoom:{on:!0},service:{on:!0}}}),await n.settingsManager.saveSettings(),n.showNotice("New diagram was added"),r())})(e,t)})),e)],addExtraButtons:[t.Platform.isDesktopApp&&(t=>(t.setIcon("info"),t.setTooltip("Click for more information on how the plugin works and how you can find diagram selectors"),t.onClick((()=>{new qa(e,n).open()})),t))]}),gt.createElement(Na,{name:"Available diagrams",setHeading:!0}),gt.createElement(Na,{name:"Diagrams per page",addSliders:[t=>(t.setValue(n.settings.diagramsPerPage),t.setDynamicTooltip(),t.setLimits(1,50,1),t.onChange((async t=>{n.settings.diagramsPerPage=t,await n.settingsManager.saveSettings(),n.publisher.publish({emitter:e.workspace,eventID:Ga.ItemsPerPageChanged,timestamp:new Date})})),t)]}),gt.createElement(Ms,null))},Ls=()=>{const e=dn(),t=ln();return gt.createElement("div",null,gt.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",borderBottom:"1px solid var(--color-base-30)"}},gt.createElement(Na,{addButtons:[n=>(n.setIcon("settings"),n.setTooltip("Settings"),n.onClick((()=>{e("/diagram-section/settings")})),"/diagram-section"!==t.pathname&&"/diagram-section/settings"!==t.pathname||n.setClass("button-active"),n),n=>(n.setIcon("folder-plus"),n.setTooltip("Diagram Management"),n.onClick((()=>{e("/diagram-section/management")})),"/diagram-section/management"===t.pathname&&n.setClass("button-active"),n)]})),gt.createElement(Cn,null,gt.createElement(Sn,{index:!0,element:gt.createElement(Ya,null)}),gt.createElement(Sn,{path:"settings",element:gt.createElement(Ya,null)}),gt.createElement(Sn,{path:"management",element:gt.createElement(Fs,null)})))},Bs=Ps.div`
    display: flex;
    flex-direction: column;
    gap: 20px;
`,js=Ps.div`
    position: relative;
    width: 400px;
    height: 300px;
    border: 2px solid var(--color-base-30);
    margin: 0 auto;
`,Hs=Ps.div`
    position: absolute;
    width: 60px;
    height: 40px; 
    padding: 8px;
    background: var(--color-base-20);
    border-radius: 4px;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    cursor: move;
    opacity: ${({dragging:e})=>e?.5:1};
    transition: ${({dragging:e})=>e?"all 0.3s ease":"none"}}
`,Us=Ps(Hs)`
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: none;
    text-align: justify;
`,Ws=Ps.div`
    display: flex;
    justify-content: center;
    gap: 20px;
`,Ys=Ps.label`
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9em;
`,Vs=()=>{const{plugin:e}=yt(),[n,i]=se(e.settings.panelsConfig),[r,a]=se(null),o=de(null);ce((()=>{i(e.settings.panelsConfig)}),[e.settings]);const s=async t=>{i(t),e.settings.panelsConfig=t,await e.settingsManager.saveSettings()},l=(e,t,n)=>{const i={},r=30,a=e,o=e+60,s=t,l=t+40,c=[{edge:"left",value:a},{edge:"right",value:n.width-o},{edge:"top",value:s},{edge:"bottom",value:n.height-l}].reduce(((e,t)=>Math.abs(e.value)<Math.abs(t.value)?e:t));if(Math.abs(c.value)<=r)switch(c.edge){case"left":i.left="0px",s<=r?i.top="0px":n.height-l<=r?i.bottom="0px":i.top=`${(s/n.height*100).toFixed(1)}%`;break;case"right":i.right="0px",s<=r?i.top="0px":n.height-l<=r?i.bottom="0px":i.top=`${(s/n.height*100).toFixed(1)}%`;break;case"top":i.top="0px",a<=r?i.left="0px":n.width-o<=r?i.right="0px":i.left=`${(a/n.width*100).toFixed(1)}%`;break;case"bottom":i.bottom="0px",a<=r?i.left="0px":n.width-o<=r?i.right="0px":i.left=`${(a/n.width*100).toFixed(1)}%`}else i.left=`${(a/n.width*100).toFixed(1)}%`,i.top=`${(s/n.height*100).toFixed(1)}%`;return i},c=e=>{const t=o.current;if(!t||!r)return;e.preventDefault();const n=e.touches[0],i=e.currentTarget,a=JSON.parse(i.dataset.dragData??"{}"),s=t.getBoundingClientRect(),c=n.clientX-s.left-a.offsetX,u=n.clientY-s.top-a.offsetY,d=l(c,u,s);i.style.left=d.left,i.style.top=d.top},u=async e=>{const t=o.current;if(!t||!r)return;const i=e.currentTarget,c=JSON.parse(i.dataset.dragData??"{}"),u=e.changedTouches[0],d=t.getBoundingClientRect(),h=u.clientX-d.left-c.offsetX,p=u.clientY-d.top-c.offsetY,m=l(h,p,d),g={...n};g[c.panelName]={...n[c.panelName],position:m},await s(g),a(null)},d=e=>t.Platform.isDesktop?{draggable:!0,onDragStart:t=>((e,t)=>{const n=e.currentTarget.getBoundingClientRect(),i=e.clientX-n.left,r=e.clientY-n.top;e.dataTransfer.setData("application/json",JSON.stringify({panelName:t,offsetX:i,offsetY:r})),a(t)})(t,e)}:{onTouchStart:t=>((e,t)=>{const n=e.touches[0],i=e.target,r=i.getBoundingClientRect(),o=n.clientX-r.left,s=n.clientY-r.top;a(t),i.dataset.dragData=JSON.stringify({panelName:t,offsetX:o,offsetY:s})})(t,e),onTouchMove:c,onTouchEnd:u};return gt.createElement(Bs,null,gt.createElement(js,{ref:o,onDragOver:e=>e.preventDefault(),onDrop:async e=>{e.preventDefault();const t=o.current;if(!t)return;const i=t.getBoundingClientRect(),r=JSON.parse(e.dataTransfer.getData("application/json")),c=e.clientX-i.left-r.offsetX,u=e.clientY-i.top-r.offsetY,d=l(c,u,i),h={...n};h[r.panelName]={...n[r.panelName],position:d},await s(h),a(null)}},Object.entries(n).map((([e,t])=>t.enabled&&gt.createElement(Hs,{key:e,dragging:r===e,style:{...t.position},...d(e)},e))),gt.createElement(Us,null,"fold")),gt.createElement(Ws,null,Object.entries(n).map((([e,t])=>gt.createElement(Ys,{key:e},gt.createElement("input",{type:"checkbox",checked:t.enabled,onChange:()=>(async e=>{const t={...n,[e]:{...n[e],enabled:!n[e].enabled}};await s(t)})(e)}),e)))))},qs=()=>gt.createElement(gt.Fragment,null,gt.createElement(Na,{name:"Panel configuration",desc:"Configure the visibility and position of control panels on your diagrams",setHeading:!0,noBorder:!0}),gt.createElement(Na,{name:"Available panels",addMultiDesc:e=>(e.addDesc("• Move Panel: By default located at bottom right - Contains 8 directional buttons for diagram movement"),e.addDesc("• Zoom Panel: By default located at center right - Features zoom in/out and reset controls"),e.addDesc("• Service Panel: By default located at upper right - Contains additional functionality buttons"),e),noBorder:!0}),gt.createElement(Na,{name:"How to customize panels",addMultiDesc:e=>(e.addDesc("1. Use checkboxes below to toggle panel visibility on/off"),e.addDesc("2. Click and drag any panel to reposition it on the diagram"),e.addDesc("3. Panel positions are saved automatically"),e.addDesc("4. Reload the view to see your changes take effect"),e),noBorder:!0}),gt.createElement(Vs,null)),Gs=()=>{const{plugin:e}=yt();return gt.createElement(gt.Fragment,null,t.Platform.isDesktopApp&&gt.createElement(gt.Fragment,null,gt.createElement(Na,{name:"Panels behavior",desc:"Configure how panels interact with mouse movement",setHeading:!0}),gt.createElement(Na,{name:"Hide panels when mouse leaves diagram?",addToggles:[t=>(t.setValue(e.settings.hideOnMouseOutDiagram),t.onChange((async t=>{e.settings.hideOnMouseOutDiagram=t,await e.settingsManager.saveSettings()})),t)]}),gt.createElement(Na,{name:"Serivce panel",setHeading:!0}),gt.createElement(Na,{name:"Add a hiding button to service panel?",addToggles:[t=>(t.setValue(e.settings.addHidingButton),t.onChange((async t=>{e.settings.addHidingButton=t,await e.settingsManager.saveSettings()})),t)]})))},Xs=()=>{const e=dn(),n=ln();return gt.createElement("div",null,gt.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",borderBottom:"1px solid var(--color-base-30)"}},t.Platform.isDesktopApp&&gt.createElement(Na,{addButtons:[t=>(t.setIcon("settings"),t.setTooltip("Panels Settings"),t.onClick((()=>{e("/panel-section/settings")})),"/panel-section/settings"!==n.pathname&&"/panel-section"!==n.pathname||t.setClass("button-active"),t),t=>(t.setIcon("layout-grid"),t.setTooltip("Panels Management"),t.onClick((()=>{e("/panel-section/management")})),"/panel-section/management"===n.pathname&&t.setClass("button-active"),t)]})),gt.createElement(Cn,null,gt.createElement(Sn,{index:!0,element:t.Platform.isDesktopApp?gt.createElement(Gs,null):gt.createElement(qs,null)}),gt.createElement(Sn,{path:"settings",element:gt.createElement(Gs,null)}),gt.createElement(Sn,{path:"management",element:gt.createElement(qs,null)})))},Zs=Ps.nav`
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px;
    background-color: var(--background-primary);
    color: var(--text-normal);
    border-bottom: 2px solid var(--background-modifier-border);
`,Js=Ps.div`
    display: flex;
    gap: 16px;
`,Ks=Ps.button`
    display: flex;
    align-items: center;
    background: none;
    border: none;
    text-decoration: none;
    color: var(--text-normal);
    font-size: 16px;
    padding: 8px 12px;
    gap: 10px;
    border-radius: 4px;
    transition:
        background-color 0.3s,
        color 0.3s;
    cursor: pointer;
    position: relative;

    &:hover {
        background-color: var(--background-modifier-hover);
        color: var(--text-accent-hover);
    }

    &.active {
        background-color: var(--background-modifier-active-hover);
        color: var(--text-accent);
    }

    &.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 3px;
        background-color: var(--text-accent);
        border-radius: 2px 2px 0 0;
    }
`,Qs=()=>gt.createElement(Zs,null,gt.createElement(Js,null,gt.createElement(Ks,{as:Xn,to:"/diagram-section"},"Diagram"),gt.createElement(Ks,{as:Xn,to:"/panel-section"},"Panel"),gt.createElement(Ks,{as:Xn,to:"/about"},"About"))),el=()=>{const{plugin:e,forceReload:n,setCurrentPath:i}=yt(),r=ln();return gt.createElement(Na,{addButtons:[a=>(a.setIcon("rotate-ccw"),a.setTooltip("Reset settings to default"),a.onClick((async()=>{i(r.pathname),await e.settingsManager.resetSettings(),n(),new t.Notice("Settings have been reset to default.")})),a)]})},tl=()=>t.Platform.isDesktopApp?gt.createElement("div",{style:{display:"grid",gridTemplateColumns:"1fr auto 1fr",alignItems:"center",width:"100%"}},gt.createElement("div",null),gt.createElement(Qs,null),gt.createElement("div",{style:{justifySelf:"end",display:"flex",alignItems:"center",marginTop:"35px"}},gt.createElement(el,null))):gt.createElement(gt.Fragment,null,gt.createElement("div",{style:{display:"flex",justifyContent:"flex-end",marginTop:"-50px",marginRight:"0px",padding:0,width:"100%",marginBottom:0}},gt.createElement(el,null)),gt.createElement(Qs,null)),nl=()=>{const{reloadCount:e,currentPath:t}=yt();return gt.createElement(En,{initialEntries:[t],key:e},gt.createElement(tl,null),gt.createElement(Cn,null,gt.createElement(Sn,{path:"/diagram-section/*",element:gt.createElement(Ls,null)}),gt.createElement(Sn,{path:"/panel-section/*",element:gt.createElement(Xs,null)}),gt.createElement(Sn,{path:"/about",element:gt.createElement(Oa,null)})))},il=(function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=No(xs.apply(void 0,ni([e],t,!1))),r=go(i);return new ds(r,i)})`
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
`,rl=Ps.div`
    animation: ${il} 0.3s cubic-bezier(0.4, 0, 0.2, 1);
`,al=({app:e,plugin:t})=>gt.createElement(_t,{app:e,plugin:t},gt.createElement(rl,null,gt.createElement(nl,null)));class ol extends t.PluginSettingTab{app;plugin;root=void 0;constructor(e,t){super(e,t),this.app=e,this.plugin=t,this.containerEl.addClass("diagram-zoom-drag-settings")}async display(){const e=this.containerEl.createDiv();this.root=ft(e),this.root.render(gt.createElement(al,{app:this.app,plugin:this.plugin}))}hide(){this.root?.unmount(),this.containerEl.empty()}}class sl{plugin;constructor(e){this.plugin=e}async isFirstPluginStart(){const e=await this.getPluginMetadata(),t=localStorage.getItem("diagram-zoom-drag-metadata");if(!t)return localStorage.setItem("diagram-zoom-drag-metadata",e.toString()),!0;const n=parseInt(t,10);return!(!isNaN(n)&&e===n)&&(localStorage.setItem("diagram-zoom-drag-metadata",e.toString()),!0)}async getPluginMetadata(){const{dir:e}=this.plugin.manifest;if(!e)throw new Error("No plugin dir found.");const t=await this.plugin.app.vault.adapter.stat(e);return t?.ctime??0}}class ll{plugin;constructor(e){this.plugin=e}}class cl extends ll{constructor(e){super(e)}publish(e){e.emitter.trigger(e.eventID,e)}}class ul{plugin;constructor(e){this.plugin=e}}class dl extends ul{subscriptions=new Map;constructor(e){super(e)}subscribe(e,t,n){const i=e.on(t,(async(...e)=>{const t=e[0];await n(t)}));this.subscriptions.has(e)||this.subscriptions.set(e,new Map);const r=this.subscriptions.get(e);r.has(t)||r.set(t,[]),r.get(t).push((()=>e.offref(i)))}subscribeForView(e,t,n,i){this.subscribe(t,n,i),e.register((()=>this.unsubscribeFromEvent(t,n)))}unsubscribeAll(){this.subscriptions.forEach(((e,t)=>{e.forEach((e=>{e.forEach((e=>e()))}))})),this.subscriptions.clear()}unsubscribeFromEmitter(e){const t=this.subscriptions.get(e);t&&(t.forEach((e=>{e.forEach((e=>e()))})),this.subscriptions.delete(e))}unsubscribeFromEvent(e,t){const n=this.subscriptions.get(e);if(n){const i=n.get(t);i&&(i.forEach((e=>e())),n.delete(t)),0===n.size&&this.subscriptions.delete(e)}}}function hl(e,n,i){n&&t.setIcon(e,n),i&&(e.ariaLabel=i)}class pl{diagram;data=new Map;constructor(e){this.diagram=e,Object.defineProperties(this.diagram,{size:{get:()=>this.size,set:e=>{this.size=e}},dx:{get:()=>this.dx,set:e=>{this.dx=e}},dy:{get:()=>this.dy,set:e=>{this.dy=e}},scale:{get:()=>this.scale,set:e=>{this.scale=e}},nativeTouchEventsEnabled:{get:()=>this.nativeTouchEventsEnabled,set:e=>{this.nativeTouchEventsEnabled=e}},source:{get:()=>this.source,set:e=>{this.source=e}},panelsData:{get:()=>this.panelsData,set:e=>{this.panelsData=e}},livePreviewObserver:{get:()=>this.livePreviewObserver,set:e=>{this.livePreviewObserver=e}}})}initializeLeafData(e){this.data.get(e)||this.data.set(e,{containers:{}})}initializeContainer(e,t,n){const i=this.diagram.plugin.context.leafID,r=this.data.get(i);r&&(r.containers[e]={size:n,dx:0,dy:0,scale:1,nativeTouchEventsEnabled:!0,panelsData:{},source:t})}initializeContainerPanels(e,t,n,i,r){this.panelsData={panels:{move:t,fold:n,zoom:i,service:r},controlPanel:e}}async cleanupContainers(){const e=this.data.get(this.diagram.plugin.context.leafID);if(!e)return;const t=this.diagram.plugin.context.view?.file?.stat.ctime,n=Object.keys(e);for(const i of n){t!==parseInt(i.split("-")[1],10)&&delete e.containers[i]}}cleanupData(e){const t=this.data.get(e);t?.livePreviewObserver?.disconnect(),this.data.delete(e)}getData(e){const t=this.diagram.activeContainer;if(!t)return;const n=this.diagram.plugin.context.leafID;if(!n)return;const i=this.data.get(n);return i?.containers[t.id]?i?.containers[t.id][e]:void 0}setData(e,t){const n=this.diagram.activeContainer;if(!n)return;const i=this.diagram.plugin.context.leafID;if(!i)return;const r=this.data.get(i);r&&r.containers[n.id]&&(r.containers[n.id][e]=t)}get size(){return this.getData("size")??{height:0,width:0}}set size(e){this.setData("size",e)}get dx(){return this.getData("dx")??0}set dx(e){this.setData("dx",e)}get dy(){return this.getData("dy")??0}set dy(e){this.setData("dy",e)}get scale(){return this.getData("scale")??1}set scale(e){this.setData("scale",e)}get nativeTouchEventsEnabled(){return this.getData("nativeTouchEventsEnabled")??!0}set nativeTouchEventsEnabled(e){this.setData("nativeTouchEventsEnabled",e)}get source(){return this.getData("source")??"No source available"}set source(e){this.setData("source",e)}get panelsData(){return this.getData("panelsData")??{}}set panelsData(e){this.setData("panelsData",e)}get livePreviewObserver(){const e=this.data.get(this.diagram.plugin.context.leafID);return e?.livePreviewObserver}set livePreviewObserver(e){const t=this.data.get(this.diagram.plugin.context.leafID);t&&(t.livePreviewObserver=e)}}class ml{diagram;diagramControlPanel;panel;constructor(e,t){this.diagram=e,this.diagramControlPanel=t}initialize(){this.panel=this.createPanel()}getButtons(e){return[{icon:"arrow-up-left",action:()=>this.diagram.actions.moveElement(e,50,50,!0),title:"Move up left"},{icon:"arrow-up",action:()=>this.diagram.actions.moveElement(e,0,50,!0),title:"Move up"},{icon:"arrow-up-right",action:()=>this.diagram.actions.moveElement(e,-50,50,!0),title:"Move up right"},{icon:"arrow-left",action:()=>this.diagram.actions.moveElement(e,50,0,!0),title:"Move left"},{icon:"",action:()=>{},title:"",active:!1,id:""},{icon:"arrow-right",action:()=>this.diagram.actions.moveElement(e,-50,0,!0),title:"Move right"},{icon:"arrow-down-left",action:()=>this.diagram.actions.moveElement(e,50,-50,!0),title:"Move down left"},{icon:"arrow-down",action:()=>this.diagram.actions.moveElement(e,0,-50,!0),title:"Move down"},{icon:"arrow-down-right",action:()=>this.diagram.actions.moveElement(e,-50,-50,!0),title:"Move down right"}]}createPanel(){const e=this.diagramControlPanel.createPanel("diagram-move-panel",{...this.diagram.plugin.settings.panelsConfig.move.position,gridTemplateColumns:"repeat(3, 1fr)",gridTemplateRows:"repeat(3, 1fr)"});return this.getButtons(this.diagram.activeContainer).forEach((t=>e.appendChild(this.diagramControlPanel.createButton(t.icon,t.action,t.title,t.active,t.id)))),e}}class gl{diagram;diagramControlPanel;panel;constructor(e,t){this.diagram=e,this.diagramControlPanel=t}initialize(){this.panel=this.createPanel()}getButtons(e){return[{icon:"zoom-in",action:()=>this.diagram.actions.zoomElement(e,1.1,!0),title:"Zoom In"},{icon:"refresh-cw",action:()=>this.diagram.actions.resetZoomAndMove(e,!0),title:"Reset Zoom and Position"},{icon:"zoom-out",action:()=>this.diagram.actions.zoomElement(e,.9,!0),title:"Zoom Out"}]}createPanel(){const e=this.diagramControlPanel.createPanel("diagram-zoom-panel",{...this.diagram.plugin.settings.panelsConfig.zoom.position,transform:"translateY(-50%)",gridTemplateColumns:"1fr"});return this.getButtons(this.diagram.activeContainer).forEach((t=>e.appendChild(this.diagramControlPanel.createButton(t.icon,t.action,t.title,!0)))),e}}class fl{diagram;diagramControlPanel;panel;container;constructor(e,t){this.diagram=e,this.diagramControlPanel=t}initialize(){this.container=this.diagram.activeContainer,this.panel=this.createPanel(),this.diagram.plugin.observer.subscribe(this.diagram.plugin.app.workspace,Ga.FoldStateChanged,(async e=>{const{containerID:t,folded:n}=e.data;t===this.container.id&&this.changeFoldState(this.container,n)}))}getButtons(e){const t="true"===this.diagram.activeContainer?.dataset.folded;return[{icon:t?"unfold-vertical":"fold-vertical",action:()=>{this.toggleFoldState(e)},title:t?"Expand diagram":"Fold diagram",id:"diagram-fold-button"}]}createPanel(){const e=this.diagramControlPanel.createPanel("diagram-fold-panel",{position:"absolute",left:"50%",bottom:"0",transform:"translateX(-50%)",gridTemplateColumns:"1fr"});return this.getButtons(this.container).forEach((t=>{const n=this.diagramControlPanel.createButton(t.icon,t.action,t.title,!0,t.id);e.appendChild(n)})),e}toggleFoldState(e){const t="true"===e.dataset.folded;e.setAttribute("data-folded",`${!t}`),this.diagram.updateDiagramSizeBasedOnStatus(e),this.handleFoldStateChange(e,!t)}changeFoldState(e,t){e.setAttribute("data-folded",`${t}`),this.diagram.updateDiagramSizeBasedOnStatus(e),this.handleFoldStateChange(e,t)}handleFoldStateChange(e,t){e.querySelectorAll(".diagram-zoom-drag-panel:not(.diagram-fold-panel)").forEach((e=>{e.toggleClass("hidden",t),e.toggleClass("visible",!t)}));const n=e.querySelector("#diagram-fold-button");n&&hl(n,t?"unfold-vertical":"fold-vertical",t?"Expand diagram":"Fold diagram")}}class vl{diagram;diagramControlPanel;panel;hiding=!1;constructor(e,t){this.diagram=e,this.diagramControlPanel=t}initialize(){this.panel=this.createPanel(),this.setupEventListeners()}getButtons(e){const n=[];return this.diagram.plugin.settings.addHidingButton&&n.push({icon:this.hiding?"eye-off":"eye",action:()=>{const e=this.diagram.state.panelsData;if(!e?.panels)return;this.hiding=!this.hiding,[e.panels.move,e.panels.zoom].forEach((e=>{e.panel&&(e.panel.toggleClass("hidden",this.hiding),e.panel.toggleClass("visible",!this.hiding))}));const t=this.panel.querySelector("#hide-show-button-diagram");t&&hl(t,this.hiding?"eye-off":"eye",(this.hiding?"Show":"Hide")+" move and zoom panels")},title:"Hide move and zoom panels",id:"hide-show-button-diagram"}),n.push({icon:"maximize",action:async()=>{const t=e.querySelector("#fullscreen-button");t&&(document.fullscreenElement?(e.removeClass("is-fullscreen"),await document.exitFullscreen(),hl(t,"maximize","Exit fullscreen mode")):(e.addClass("is-fullscreen"),await e.requestFullscreen({navigationUI:"auto"}),hl(t,"minimize","Open in fullscreen mode")))},title:"Open in fullscreen mode",id:"fullscreen-button"}),t.Platform.isMobileApp&&n.push({icon:this.diagram.nativeTouchEventsEnabled?"circle-slash-2":"hand",action:()=>{this.diagram.nativeTouchEventsEnabled=!this.diagram.nativeTouchEventsEnabled;const e=this.panel.querySelector("#native-touch-event");if(!e)return;const t=this.diagram.nativeTouchEventsEnabled;hl(e,this.diagram.nativeTouchEventsEnabled?"circle-slash-2":"hand",(t?"Enable":"Disable")+" move and pinch zoom"),this.diagram.plugin.showNotice(`Native touches are ${t?"enabled":"disabled"} now. \n            You ${t?"cannot":"can"} move and pinch zoom diagram diagram.`)},title:(this.diagram.nativeTouchEventsEnabled?"Enable":"Disable")+" move and pinch zoom",id:"native-touch-event"}),n}createPanel(){const e=this.diagramControlPanel.createPanel("diagram-service-panel",{...this.diagram.plugin.settings.panelsConfig.service.position,gridTemplateColumns:"repeat(auto-fit, minmax(24px, 1fr))",gridAutoFlow:"column"});return this.getButtons(this.diagram.activeContainer).forEach((t=>e.appendChild(this.diagramControlPanel.createButton(t.icon,t.action,t.title,!0,t.id)))),e}setupEventListeners(){const e=this.panel.querySelector("#fullscreen-button"),n=this.diagram.activeContainer;if(!e)return;this.diagram.plugin.context.view?.registerDomEvent(n,"fullscreenchange",this.onFullScreenChange.bind(this,n,e));const i=this.panel.querySelector("#hide-show-button-diagram");this.diagram.plugin.observer.subscribe(this.diagram.plugin.app.workspace,Ga.PanelsChangedVisibility,(async e=>{const n=e.data.visible;i&&(this.hiding=!n,hl(i,this.hiding?"eye-off":"eye",(this.hiding?"Show":"Hide")+" move and zoom panels"),t.setIcon(i,this.hiding?"eye-off":"eye"))}))}onFullScreenChange(e,t){document.fullscreenElement?(requestAnimationFrame((()=>{this.diagram.actions.resetZoomAndMove(e)})),hl(t,"minimize","Exit fullscreen mode")):(requestAnimationFrame((()=>{this.diagram.actions.resetZoomAndMove(e)})),hl(t,"maximize","Open in fullscreen mode"))}}class _l{diagram;constructor(e){this.diagram=e}initialize(e,t){this.diagram.activeContainer=e;const n=e.createDiv();n.addClass("diagram-zoom-drag-control-panel");const i=new ml(this.diagram,this),r=new gl(this.diagram,this),a=new fl(this.diagram,this),o=new vl(this.diagram,this);this.diagram.state.initializeContainerPanels(n,i,a,r,o),a.initialize(),this.diagram.plugin.settings.panelsConfig.move.enabled&&t.panels.move.on&&i.initialize(),this.diagram.plugin.settings.panelsConfig.zoom.enabled&&t.panels.zoom.on&&r.initialize(),this.diagram.plugin.settings.panelsConfig.service.enabled&&t.panels.service.on&&o.initialize(),(this.diagram.plugin.settings.hideOnMouseOutDiagram||"true"===this.diagram.activeContainer?.dataset.folded)&&[i,r,o].forEach((e=>{e.panel.removeClass("visible"),e.panel.addClass("hidden")})),this.diagram.activeContainer?.appendChild(n)}createPanel(e,t){const n=this.diagram.panelsData?.controlPanel,i=n.createEl("div");return i.addClass(e),i.addClass("diagram-zoom-drag-panel"),i.setCssStyles(t),i}createButton(e,t,n,i=!0,r=void 0){const a=document.createElement("button");return a.className="button",a.id=r??"",i?(a.setCssStyles({background:"transparent",border:"none",color:"var(--text-muted)",cursor:"pointer",padding:"4px",borderRadius:"3px",display:"flex",justifyContent:"center",alignItems:"center",transition:"background-color 0.2s ease"}),hl(a,e,n),this.diagram.plugin.context.view.registerDomEvent(a,"click",t),this.diagram.plugin.context.view.registerDomEvent(a,"mouseenter",(()=>{a.setCssStyles({color:"var(--interactive-accent)"})})),this.diagram.plugin.context.view.registerDomEvent(a,"mouseleave",(()=>{a.setCssStyles({color:"var(--text-muted)"})}))):a.setCssStyles({visibility:"hidden"}),a}}class yl{diagramEvents;startX;startY;initialX;initialY;isDragging=!1;constructor(e){this.diagramEvents=e}initialize(e){const t=e.querySelector(this.diagramEvents.diagram.compoundSelector);t&&this.diagramEvents.diagram.plugin.context.view&&(this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"wheel",this.wheel.bind(this,e,t),{passive:!0}),this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"mousedown",this.mouseDown.bind(this,e,t)),this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"mousemove",this.mouseMove.bind(this,e,t)),this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"mouseup",this.mouseUp.bind(this,e,t)),this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"mouseleave",this.mouseLeave.bind(this,e,t)),this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"mouseenter",this.mouseEnterOnDiagram.bind(this,e)),this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"mouseleave",this.mouseLeaveOutDiagram.bind(this,e)))}wheel(e,t,n){if(!n.ctrlKey&&document.fullscreenElement!==e)return;this.diagramEvents.diagram.activeContainer=e;const i=t.getBoundingClientRect(),r=n.clientX-i.left,a=n.clientY-i.top,o=this.diagramEvents.diagram.scale;this.diagramEvents.diagram.scale+=-.001*n.deltaY,this.diagramEvents.diagram.scale=Math.max(.125,this.diagramEvents.diagram.scale);const s=r*(1-this.diagramEvents.diagram.scale/o),l=a*(1-this.diagramEvents.diagram.scale/o);this.diagramEvents.diagram.dx+=s,this.diagramEvents.diagram.dy+=l,t.setCssStyles({transform:`translate(${this.diagramEvents.diagram.dx}px, ${this.diagramEvents.diagram.dy}px) scale(${this.diagramEvents.diagram.scale})`})}mouseDown(e,t,n){0===n.button&&(this.diagramEvents.diagram.activeContainer=e,e.focus({preventScroll:!0}),this.isDragging=!0,this.startX=n.clientX,this.startY=n.clientY,this.initialX=this.diagramEvents.diagram.dx,this.initialY=this.diagramEvents.diagram.dy,t.setCssStyles({cursor:"grabbing"}),n.preventDefault())}mouseMove(e,t,n){if(!this.isDragging)return;this.diagramEvents.diagram.activeContainer=e;const i=n.clientX-this.startX,r=n.clientY-this.startY;this.diagramEvents.diagram.dx=this.initialX+i,this.diagramEvents.diagram.dy=this.initialY+r,t.setCssStyles({transform:`translate(${this.diagramEvents.diagram.dx}px, ${this.diagramEvents.diagram.dy}px) scale(${this.diagramEvents.diagram.scale})`})}mouseUp(e,t,n){this.diagramEvents.diagram.activeContainer=e,this.isDragging=!1,t.setCssStyles({cursor:"grab"})}mouseLeave(e,t,n){this.mouseUp(e,t,n)}mouseEnterOnDiagram(e,t){if(!this.diagramEvents.diagram.plugin.settings.hideOnMouseOutDiagram)return;if("true"===e.dataset.folded)return;const n=this.diagramEvents.diagram.state.panelsData;n?.panels&&[n.panels.move.panel,n.panels.zoom.panel,n.panels.service.panel].forEach((e=>{e.removeClass("hidden"),e.addClass("visible")}))}mouseLeaveOutDiagram(e,t){if(!this.diagramEvents.diagram.plugin.settings.hideOnMouseOutDiagram)return;if("true"===e.dataset.folded)return;const n=this.diagramEvents.diagram.state.panelsData;n?.panels&&[n.panels.move.panel,n.panels.zoom.panel,n.panels.service.panel].forEach((e=>{e.removeClass("visible"),e.addClass("hidden")}))}}class bl{diagramEvents;startX;startY;initialDistance;isDragging=!1;isPinching=!1;constructor(e){this.diagramEvents=e}initialize(e){this.diagramEvents.diagram.plugin.context.view&&(this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"touchstart",this.touchStart.bind(this,e),{passive:!1}),this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"touchmove",this.touchMove.bind(this,e),{passive:!1}),this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"touchend",this.touchEnd.bind(this,e),{passive:!1}))}touchStart(e,t){if(this.diagramEvents.diagram.nativeTouchEventsEnabled)return;this.diagramEvents.diagram.activeContainer=e;t.target.closest(".diagram-zoom-drag-panel")||(t.preventDefault(),t.stopPropagation(),1===t.touches.length?(this.isDragging=!0,this.startX=t.touches[0].clientX,this.startY=t.touches[0].clientY):2===t.touches.length&&(this.isPinching=!0,this.initialDistance=this.calculateDistance(t.touches)))}touchMove(e,t){if(this.diagramEvents.diagram.nativeTouchEventsEnabled)return;this.diagramEvents.diagram.activeContainer=e,t.preventDefault(),t.stopPropagation();if(e.querySelector(this.diagramEvents.diagram.compoundSelector))if(this.isDragging&&1===t.touches.length){const n=t.touches[0].clientX-this.startX,i=t.touches[0].clientY-this.startY;this.diagramEvents.diagram.actions.moveElement(e,n,i),this.startX=t.touches[0].clientX,this.startY=t.touches[0].clientY}else if(this.isPinching&&2===t.touches.length){const n=this.calculateDistance(t.touches),i=n/this.initialDistance;this.diagramEvents.diagram.actions.zoomElement(e,i),this.initialDistance=n}}touchEnd(e,t){if(this.diagramEvents.diagram.nativeTouchEventsEnabled)return;this.diagramEvents.diagram.activeContainer=e;t.target.closest(".diagram-zoom-drag-panel")||(t.preventDefault(),t.stopPropagation(),this.isDragging=!1,this.isPinching=!1)}calculateDistance(e){const[t,n]=[e[0],e[1]],i=n.clientX-t.clientX,r=n.clientY-t.clientY;return Math.sqrt(i*i+r*r)}}class wl{diagramEvents;constructor(e){this.diagramEvents=e}initialize(e){this.diagramEvents.diagram.plugin.context.view&&this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"keydown",this.keyDown.bind(this,e))}keyDown(e,t){const n=t.code;if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","Equal","Minus","Digit0"].includes(n)){switch(t.preventDefault(),t.stopPropagation(),this.diagramEvents.diagram.activeContainer=e,n){case"ArrowUp":this.diagramEvents.diagram.actions.moveElement(e,0,50,!0);break;case"ArrowDown":this.diagramEvents.diagram.actions.moveElement(e,0,-50,!0);break;case"ArrowLeft":this.diagramEvents.diagram.actions.moveElement(e,50,0,!0);break;case"ArrowRight":this.diagramEvents.diagram.actions.moveElement(e,-50,0,!0)}if(t.ctrlKey)switch(n){case"Equal":this.diagramEvents.diagram.actions.zoomElement(e,1.1,!0);break;case"Minus":this.diagramEvents.diagram.actions.zoomElement(e,.9,!0);break;case"Digit0":this.diagramEvents.diagram.actions.resetZoomAndMove(e,!0)}}}}class El{diagramEvents;constructor(e){this.diagramEvents=e}initialize(e){this.diagramEvents.diagram.plugin.context.view&&(this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"focusin",this.focusIn.bind(this,e)),this.diagramEvents.diagram.plugin.context.view.registerDomEvent(e,"focusout",this.focusOut.bind(this,e)))}focusIn(e){this.diagramEvents.diagram.plugin.settings.automaticFoldingOnFocusChange&&(this.diagramEvents.diagram.activeContainer=e,this.publishFoldStateChange(e,!1))}focusOut(e){this.diagramEvents.diagram.plugin.settings.automaticFoldingOnFocusChange&&this.publishFoldStateChange(e,!0)}publishFoldStateChange(e,t){const n=e.id;this.diagramEvents.diagram.plugin.publisher.publish({eventID:Ga.FoldStateChanged,timestamp:new Date,emitter:this.diagramEvents.diagram.plugin.app.workspace,data:{containerID:n,folded:t}})}}class Sl{diagram;mouse;touch;keyboard;focus;constructor(e){this.diagram=e,this.mouse=new yl(this),this.touch=new bl(this),this.keyboard=new wl(this),this.focus=new El(this)}initialize(e,t){this.mouse.initialize(e),this.touch.initialize(e),this.keyboard.initialize(e),this.focus.initialize(e)}}class xl{diagram;constructor(e){this.diagram=e}moveElement(e,t,n,i){this.diagram.activeContainer=e;const r=e.querySelector(this.diagram.compoundSelector);r&&(this.diagram.dx+=t,this.diagram.dy+=n,r.setCssStyles({transition:i?"transform 0.3s ease-out":"none",transform:`translate(${this.diagram.dx}px, ${this.diagram.dy}px) scale(${this.diagram.scale})`}),i&&this.diagram.plugin.context.view.registerDomEvent(r,"transitionend",(()=>{r.setCssStyles({transition:"none"})}),{once:!0}))}zoomElement(e,t,n){this.diagram.activeContainer=e;const i=e.querySelector(this.diagram.compoundSelector);if(!i)return;const r=e.getBoundingClientRect(),a=r.width/2,o=r.height/2,s=(a-this.diagram.dx)/this.diagram.scale,l=(o-this.diagram.dy)/this.diagram.scale;this.diagram.scale*=t,this.diagram.scale=Math.max(.125,this.diagram.scale),this.diagram.dx=a-s*this.diagram.scale,this.diagram.dy=o-l*this.diagram.scale,i.setCssStyles({transition:n?"transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1)":"none",transform:`translate(${this.diagram.dx}px, ${this.diagram.dy}px) scale(${this.diagram.scale})`}),n&&this.diagram.plugin.context.view.registerDomEvent(i,"transitionend",(()=>{i.setCssStyles({transition:"none"})}),{once:!0})}resetZoomAndMove(e,t){const n=e.querySelector(this.diagram.compoundSelector);n&&this.fitToContainer(n,e,t)}fitToContainer(e,t,n){this.diagram.activeContainer=t;const i=t.clientWidth,r=t.clientHeight,a=e.clientWidth,o=e.clientHeight;this.diagram.scale=Math.min(i/a,r/o,1),this.diagram.dx=(i-a*this.diagram.scale)/2,this.diagram.dy=(r-o*this.diagram.scale)/2,e.setCssStyles({transition:n?"transform 0.3s cubic-bezier(0.42, 0, 0.58, 1)":"none",transform:`translate(${this.diagram.dx}px, ${this.diagram.dy}px) scale(${this.diagram.scale})`,transformOrigin:"top left"}),n&&this.diagram.plugin.context.view.registerDomEvent(e,"transitionend",(()=>{e.setCssStyles({transition:"none"})}),{once:!0})}}class Cl{diagramContextMenu;constructor(e){this.diagramContextMenu=e}export(e){const t=e.querySelector(this.diagramContextMenu.diagram.compoundSelector);if(!t)return;const n=t.querySelector("svg"),i=t.querySelector("img");n?this.exportSVG(n):i?this.exportIMG(i):this.diagramContextMenu.diagram.plugin.showNotice("Oops! We couldn't find any elements to export. It seems something is wrong with this diagram?.")}exportSVG(e){const t=(new XMLSerializer).serializeToString(e),n=new Blob(['<?xml version="1.0" standalone="no"?>\r\n',t],{type:"image/svg+xml;charset=utf-8"});this.downloadFile(n,"svg")}exportIMG(e){fetch(e.src).then((e=>e.blob())).then((e=>{this.downloadFile(e,"png")})).catch((e=>{this.diagramContextMenu.diagram.plugin.showNotice("Error exporting image"),console.error("Error exporting image:",e)}))}downloadFile(e,n){const{diagram:i}=this.diagramContextMenu,r=`dzg_export_${i.plugin.context.view?.file?.basename??"diagram"}_${i.activeContainer?.id??"unknown"}}_${t.moment().format("YYYYMMDDHHmmss")}.${n}`,a=URL.createObjectURL(e),o=document.createElement("a");o.href=a,o.download=r,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(a)}}class kl{diagramContextMenu;constructor(e){this.diagramContextMenu=e}async copy(e){const t=e.querySelector(this.diagramContextMenu.diagram.compoundSelector);if(!t)return;const n=t.querySelector("svg"),i=t.querySelector("img");n?(await this.copySvg(n),this.diagramContextMenu.diagram.plugin.showNotice("Copied")):i?(await this.copyImg(i),this.diagramContextMenu.diagram.plugin.showNotice("Copied")):console.error("Neither SVG nor IMG element found in the container")}async copyImg(e){fetch(e.src).then((e=>e.blob())).then((async e=>{await navigator.clipboard.write([new ClipboardItem({"image/png":e})])})).catch((e=>console.error("Error copy image:",e)))}async copySvg(e){try{e.focus();const t=(new XMLSerializer).serializeToString(e),n=new Blob([t],{type:"image/svg+xml"});await navigator.clipboard.write([new ClipboardItem({"image/svg+xml":n})])}catch(e){console.error("Failed to copy SVG:",e)}}}class Pl{diagramContextMenu;constructor(e){this.diagramContextMenu=e}async copy(e){const t=this.diagramContextMenu.diagram.source;t&&(await navigator.clipboard.writeText(t),this.diagramContextMenu.diagram.plugin.showNotice("Copied"))}}class Dl{diagram;export;copy;copySource;constructor(e){this.diagram=e,this.export=new Cl(this),this.copy=new kl(this),this.copySource=new Pl(this)}initialize(e,n){this.diagram.plugin.context.view?.registerDomEvent(e,"contextmenu",(()=>{e.addEventListener("contextmenu",(n=>{const i=n.target.closest(".diagram-container");if(!i)return;i.focus(),n.preventDefault(),n.stopPropagation();const r=new t.Menu;r.addItem((e=>{e.setTitle("Export diagram"),e.onClick((async()=>{this.export.export(this.diagram.activeContainer)}))})),r.addItem((e=>{e.setTitle("Copy diagram"),e.onClick((async()=>{await this.copy.copy(this.diagram.activeContainer)}))})),r.addItem((t=>{t.setTitle("Copy diagram source"),t.onClick((async()=>{await this.copySource.copy(e)}))})),r.showAtMouseEvent(n)}),!0)}))}}var Il,Al,Tl={},$l={};function zl(){if(Al)return Il;function e(e,t){if(!e)throw new Error(t||"Assertion failed")}return Al=1,Il=e,e.equal=function(e,t,n){if(e!=t)throw new Error(n||"Assertion failed: "+e+" != "+t)},Il}var Rl,Nl,Ol={exports:{}};function Ml(){if(Nl)return $l;Nl=1;var e=zl(),t=(Rl||(Rl=1,"function"==typeof Object.create?Ol.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:Ol.exports=function(e,t){if(t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}}),Ol.exports);function n(e,t){return 55296==(64512&e.charCodeAt(t))&&(!(t<0||t+1>=e.length)&&56320==(64512&e.charCodeAt(t+1)))}function i(e){return(e>>>24|e>>>8&65280|e<<8&16711680|(255&e)<<24)>>>0}function r(e){return 1===e.length?"0"+e:e}function a(e){return 7===e.length?"0"+e:6===e.length?"00"+e:5===e.length?"000"+e:4===e.length?"0000"+e:3===e.length?"00000"+e:2===e.length?"000000"+e:1===e.length?"0000000"+e:e}return $l.inherits=t,$l.toArray=function(e,t){if(Array.isArray(e))return e.slice();if(!e)return[];var i=[];if("string"==typeof e)if(t){if("hex"===t)for((e=e.replace(/[^a-z0-9]+/gi,"")).length%2!=0&&(e="0"+e),a=0;a<e.length;a+=2)i.push(parseInt(e[a]+e[a+1],16))}else for(var r=0,a=0;a<e.length;a++){var o=e.charCodeAt(a);o<128?i[r++]=o:o<2048?(i[r++]=o>>6|192,i[r++]=63&o|128):n(e,a)?(o=65536+((1023&o)<<10)+(1023&e.charCodeAt(++a)),i[r++]=o>>18|240,i[r++]=o>>12&63|128,i[r++]=o>>6&63|128,i[r++]=63&o|128):(i[r++]=o>>12|224,i[r++]=o>>6&63|128,i[r++]=63&o|128)}else for(a=0;a<e.length;a++)i[a]=0|e[a];return i},$l.toHex=function(e){for(var t="",n=0;n<e.length;n++)t+=r(e[n].toString(16));return t},$l.htonl=i,$l.toHex32=function(e,t){for(var n="",r=0;r<e.length;r++){var o=e[r];"little"===t&&(o=i(o)),n+=a(o.toString(16))}return n},$l.zero2=r,$l.zero8=a,$l.join32=function(t,n,i,r){var a=i-n;e(a%4==0);for(var o=new Array(a/4),s=0,l=n;s<o.length;s++,l+=4){var c;c="big"===r?t[l]<<24|t[l+1]<<16|t[l+2]<<8|t[l+3]:t[l+3]<<24|t[l+2]<<16|t[l+1]<<8|t[l],o[s]=c>>>0}return o},$l.split32=function(e,t){for(var n=new Array(4*e.length),i=0,r=0;i<e.length;i++,r+=4){var a=e[i];"big"===t?(n[r]=a>>>24,n[r+1]=a>>>16&255,n[r+2]=a>>>8&255,n[r+3]=255&a):(n[r+3]=a>>>24,n[r+2]=a>>>16&255,n[r+1]=a>>>8&255,n[r]=255&a)}return n},$l.rotr32=function(e,t){return e>>>t|e<<32-t},$l.rotl32=function(e,t){return e<<t|e>>>32-t},$l.sum32=function(e,t){return e+t>>>0},$l.sum32_3=function(e,t,n){return e+t+n>>>0},$l.sum32_4=function(e,t,n,i){return e+t+n+i>>>0},$l.sum32_5=function(e,t,n,i,r){return e+t+n+i+r>>>0},$l.sum64=function(e,t,n,i){var r=e[t],a=i+e[t+1]>>>0,o=(a<i?1:0)+n+r;e[t]=o>>>0,e[t+1]=a},$l.sum64_hi=function(e,t,n,i){return(t+i>>>0<t?1:0)+e+n>>>0},$l.sum64_lo=function(e,t,n,i){return t+i>>>0},$l.sum64_4_hi=function(e,t,n,i,r,a,o,s){var l=0,c=t;return l+=(c=c+i>>>0)<t?1:0,l+=(c=c+a>>>0)<a?1:0,e+n+r+o+(l+=(c=c+s>>>0)<s?1:0)>>>0},$l.sum64_4_lo=function(e,t,n,i,r,a,o,s){return t+i+a+s>>>0},$l.sum64_5_hi=function(e,t,n,i,r,a,o,s,l,c){var u=0,d=t;return u+=(d=d+i>>>0)<t?1:0,u+=(d=d+a>>>0)<a?1:0,u+=(d=d+s>>>0)<s?1:0,e+n+r+o+l+(u+=(d=d+c>>>0)<c?1:0)>>>0},$l.sum64_5_lo=function(e,t,n,i,r,a,o,s,l,c){return t+i+a+s+c>>>0},$l.rotr64_hi=function(e,t,n){return(t<<32-n|e>>>n)>>>0},$l.rotr64_lo=function(e,t,n){return(e<<32-n|t>>>n)>>>0},$l.shr64_hi=function(e,t,n){return e>>>n},$l.shr64_lo=function(e,t,n){return(e<<32-n|t>>>n)>>>0},$l}var Fl,Ll={};function Bl(){if(Fl)return Ll;Fl=1;var e=Ml(),t=zl();function n(){this.pending=null,this.pendingTotal=0,this.blockSize=this.constructor.blockSize,this.outSize=this.constructor.outSize,this.hmacStrength=this.constructor.hmacStrength,this.padLength=this.constructor.padLength/8,this.endian="big",this._delta8=this.blockSize/8,this._delta32=this.blockSize/32}return Ll.BlockHash=n,n.prototype.update=function(t,n){if(t=e.toArray(t,n),this.pending?this.pending=this.pending.concat(t):this.pending=t,this.pendingTotal+=t.length,this.pending.length>=this._delta8){var i=(t=this.pending).length%this._delta8;this.pending=t.slice(t.length-i,t.length),0===this.pending.length&&(this.pending=null),t=e.join32(t,0,t.length-i,this.endian);for(var r=0;r<t.length;r+=this._delta32)this._update(t,r,r+this._delta32)}return this},n.prototype.digest=function(e){return this.update(this._pad()),t(null===this.pending),this._digest(e)},n.prototype._pad=function(){var e=this.pendingTotal,t=this._delta8,n=t-(e+this.padLength)%t,i=new Array(n+this.padLength);i[0]=128;for(var r=1;r<n;r++)i[r]=0;if(e<<=3,"big"===this.endian){for(var a=8;a<this.padLength;a++)i[r++]=0;i[r++]=0,i[r++]=0,i[r++]=0,i[r++]=0,i[r++]=e>>>24&255,i[r++]=e>>>16&255,i[r++]=e>>>8&255,i[r++]=255&e}else for(i[r++]=255&e,i[r++]=e>>>8&255,i[r++]=e>>>16&255,i[r++]=e>>>24&255,i[r++]=0,i[r++]=0,i[r++]=0,i[r++]=0,a=8;a<this.padLength;a++)i[r++]=0;return i},Ll}var jl,Hl,Ul,Wl,Yl,Vl,ql,Gl,Xl,Zl,Jl,Kl,Ql={},ec={};function tc(){if(jl)return ec;jl=1;var e=Ml().rotr32;function t(e,t,n){return e&t^~e&n}function n(e,t,n){return e&t^e&n^t&n}function i(e,t,n){return e^t^n}return ec.ft_1=function(e,r,a,o){return 0===e?t(r,a,o):1===e||3===e?i(r,a,o):2===e?n(r,a,o):void 0},ec.ch32=t,ec.maj32=n,ec.p32=i,ec.s0_256=function(t){return e(t,2)^e(t,13)^e(t,22)},ec.s1_256=function(t){return e(t,6)^e(t,11)^e(t,25)},ec.g0_256=function(t){return e(t,7)^e(t,18)^t>>>3},ec.g1_256=function(t){return e(t,17)^e(t,19)^t>>>10},ec}function nc(){if(Yl)return Wl;Yl=1;var e=Ml(),t=Bl(),n=tc(),i=zl(),r=e.sum32,a=e.sum32_4,o=e.sum32_5,s=n.ch32,l=n.maj32,c=n.s0_256,u=n.s1_256,d=n.g0_256,h=n.g1_256,p=t.BlockHash,m=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];function g(){if(!(this instanceof g))return new g;p.call(this),this.h=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],this.k=m,this.W=new Array(64)}return e.inherits(g,p),Wl=g,g.blockSize=512,g.outSize=256,g.hmacStrength=192,g.padLength=64,g.prototype._update=function(e,t){for(var n=this.W,p=0;p<16;p++)n[p]=e[t+p];for(;p<n.length;p++)n[p]=a(h(n[p-2]),n[p-7],d(n[p-15]),n[p-16]);var m=this.h[0],g=this.h[1],f=this.h[2],v=this.h[3],_=this.h[4],y=this.h[5],b=this.h[6],w=this.h[7];for(i(this.k.length===n.length),p=0;p<n.length;p++){var E=o(w,u(_),s(_,y,b),this.k[p],n[p]),S=r(c(m),l(m,g,f));w=b,b=y,y=_,_=r(v,E),v=f,f=g,g=m,m=r(E,S)}this.h[0]=r(this.h[0],m),this.h[1]=r(this.h[1],g),this.h[2]=r(this.h[2],f),this.h[3]=r(this.h[3],v),this.h[4]=r(this.h[4],_),this.h[5]=r(this.h[5],y),this.h[6]=r(this.h[6],b),this.h[7]=r(this.h[7],w)},g.prototype._digest=function(t){return"hex"===t?e.toHex32(this.h,"big"):e.split32(this.h,"big")},Wl}function ic(){if(Xl)return Gl;Xl=1;var e=Ml(),t=Bl(),n=zl(),i=e.rotr64_hi,r=e.rotr64_lo,a=e.shr64_hi,o=e.shr64_lo,s=e.sum64,l=e.sum64_hi,c=e.sum64_lo,u=e.sum64_4_hi,d=e.sum64_4_lo,h=e.sum64_5_hi,p=e.sum64_5_lo,m=t.BlockHash,g=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function f(){if(!(this instanceof f))return new f;m.call(this),this.h=[1779033703,4089235720,3144134277,2227873595,1013904242,4271175723,2773480762,1595750129,1359893119,2917565137,2600822924,725511199,528734635,4215389547,1541459225,327033209],this.k=g,this.W=new Array(160)}function v(e,t,n,i,r){var a=e&n^~e&r;return a<0&&(a+=4294967296),a}function _(e,t,n,i,r,a){var o=t&i^~t&a;return o<0&&(o+=4294967296),o}function y(e,t,n,i,r){var a=e&n^e&r^n&r;return a<0&&(a+=4294967296),a}function b(e,t,n,i,r,a){var o=t&i^t&a^i&a;return o<0&&(o+=4294967296),o}function w(e,t){var n=i(e,t,28)^i(t,e,2)^i(t,e,7);return n<0&&(n+=4294967296),n}function E(e,t){var n=r(e,t,28)^r(t,e,2)^r(t,e,7);return n<0&&(n+=4294967296),n}function S(e,t){var n=i(e,t,14)^i(e,t,18)^i(t,e,9);return n<0&&(n+=4294967296),n}function x(e,t){var n=r(e,t,14)^r(e,t,18)^r(t,e,9);return n<0&&(n+=4294967296),n}function C(e,t){var n=i(e,t,1)^i(e,t,8)^a(e,t,7);return n<0&&(n+=4294967296),n}function k(e,t){var n=r(e,t,1)^r(e,t,8)^o(e,t,7);return n<0&&(n+=4294967296),n}function P(e,t){var n=i(e,t,19)^i(t,e,29)^a(e,t,6);return n<0&&(n+=4294967296),n}function D(e,t){var n=r(e,t,19)^r(t,e,29)^o(e,t,6);return n<0&&(n+=4294967296),n}return e.inherits(f,m),Gl=f,f.blockSize=1024,f.outSize=512,f.hmacStrength=192,f.padLength=128,f.prototype._prepareBlock=function(e,t){for(var n=this.W,i=0;i<32;i++)n[i]=e[t+i];for(;i<n.length;i+=2){var r=P(n[i-4],n[i-3]),a=D(n[i-4],n[i-3]),o=n[i-14],s=n[i-13],l=C(n[i-30],n[i-29]),c=k(n[i-30],n[i-29]),h=n[i-32],p=n[i-31];n[i]=u(r,a,o,s,l,c,h,p),n[i+1]=d(r,a,o,s,l,c,h,p)}},f.prototype._update=function(e,t){this._prepareBlock(e,t);var i=this.W,r=this.h[0],a=this.h[1],o=this.h[2],u=this.h[3],d=this.h[4],m=this.h[5],g=this.h[6],f=this.h[7],C=this.h[8],k=this.h[9],P=this.h[10],D=this.h[11],I=this.h[12],A=this.h[13],T=this.h[14],$=this.h[15];n(this.k.length===i.length);for(var z=0;z<i.length;z+=2){var R=T,N=$,O=S(C,k),M=x(C,k),F=v(C,k,P,D,I),L=_(C,k,P,D,I,A),B=this.k[z],j=this.k[z+1],H=i[z],U=i[z+1],W=h(R,N,O,M,F,L,B,j,H,U),Y=p(R,N,O,M,F,L,B,j,H,U);R=w(r,a),N=E(r,a),O=y(r,a,o,u,d),M=b(r,a,o,u,d,m);var V=l(R,N,O,M),q=c(R,N,O,M);T=I,$=A,I=P,A=D,P=C,D=k,C=l(g,f,W,Y),k=c(f,f,W,Y),g=d,f=m,d=o,m=u,o=r,u=a,r=l(W,Y,V,q),a=c(W,Y,V,q)}s(this.h,0,r,a),s(this.h,2,o,u),s(this.h,4,d,m),s(this.h,6,g,f),s(this.h,8,C,k),s(this.h,10,P,D),s(this.h,12,I,A),s(this.h,14,T,$)},f.prototype._digest=function(t){return"hex"===t?e.toHex32(this.h,"big"):e.split32(this.h,"big")},Gl}function rc(){return Kl||(Kl=1,Ql.sha1=function(){if(Ul)return Hl;Ul=1;var e=Ml(),t=Bl(),n=tc(),i=e.rotl32,r=e.sum32,a=e.sum32_5,o=n.ft_1,s=t.BlockHash,l=[1518500249,1859775393,2400959708,3395469782];function c(){if(!(this instanceof c))return new c;s.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.W=new Array(80)}return e.inherits(c,s),Hl=c,c.blockSize=512,c.outSize=160,c.hmacStrength=80,c.padLength=64,c.prototype._update=function(e,t){for(var n=this.W,s=0;s<16;s++)n[s]=e[t+s];for(;s<n.length;s++)n[s]=i(n[s-3]^n[s-8]^n[s-14]^n[s-16],1);var c=this.h[0],u=this.h[1],d=this.h[2],h=this.h[3],p=this.h[4];for(s=0;s<n.length;s++){var m=~~(s/20),g=a(i(c,5),o(m,u,d,h),p,n[s],l[m]);p=h,h=d,d=i(u,30),u=c,c=g}this.h[0]=r(this.h[0],c),this.h[1]=r(this.h[1],u),this.h[2]=r(this.h[2],d),this.h[3]=r(this.h[3],h),this.h[4]=r(this.h[4],p)},c.prototype._digest=function(t){return"hex"===t?e.toHex32(this.h,"big"):e.split32(this.h,"big")},Hl}(),Ql.sha224=function(){if(ql)return Vl;ql=1;var e=Ml(),t=nc();function n(){if(!(this instanceof n))return new n;t.call(this),this.h=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]}return e.inherits(n,t),Vl=n,n.blockSize=512,n.outSize=224,n.hmacStrength=192,n.padLength=64,n.prototype._digest=function(t){return"hex"===t?e.toHex32(this.h.slice(0,7),"big"):e.split32(this.h.slice(0,7),"big")},Vl}(),Ql.sha256=nc(),Ql.sha384=function(){if(Jl)return Zl;Jl=1;var e=Ml(),t=ic();function n(){if(!(this instanceof n))return new n;t.call(this),this.h=[3418070365,3238371032,1654270250,914150663,2438529370,812702999,355462360,4144912697,1731405415,4290775857,2394180231,1750603025,3675008525,1694076839,1203062813,3204075428]}return e.inherits(n,t),Zl=n,n.blockSize=1024,n.outSize=384,n.hmacStrength=192,n.padLength=128,n.prototype._digest=function(t){return"hex"===t?e.toHex32(this.h.slice(0,12),"big"):e.split32(this.h.slice(0,12),"big")},Zl}(),Ql.sha512=ic()),Ql}var ac,oc,sc,lc,cc={};var uc=(lc||(lc=1,function(){var e=Tl;e.utils=Ml(),e.common=Bl(),e.sha=rc(),e.ripemd=function(){if(ac)return cc;ac=1;var e=Ml(),t=Bl(),n=e.rotl32,i=e.sum32,r=e.sum32_3,a=e.sum32_4,o=t.BlockHash;function s(){if(!(this instanceof s))return new s;o.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.endian="little"}function l(e,t,n,i){return e<=15?t^n^i:e<=31?t&n|~t&i:e<=47?(t|~n)^i:e<=63?t&i|n&~i:t^(n|~i)}function c(e){return e<=15?0:e<=31?1518500249:e<=47?1859775393:e<=63?2400959708:2840853838}function u(e){return e<=15?1352829926:e<=31?1548603684:e<=47?1836072691:e<=63?2053994217:0}e.inherits(s,o),cc.ripemd160=s,s.blockSize=512,s.outSize=160,s.hmacStrength=192,s.padLength=64,s.prototype._update=function(e,t){for(var o=this.h[0],s=this.h[1],g=this.h[2],f=this.h[3],v=this.h[4],_=o,y=s,b=g,w=f,E=v,S=0;S<80;S++){var x=i(n(a(o,l(S,s,g,f),e[d[S]+t],c(S)),p[S]),v);o=v,v=f,f=n(g,10),g=s,s=x,x=i(n(a(_,l(79-S,y,b,w),e[h[S]+t],u(S)),m[S]),E),_=E,E=w,w=n(b,10),b=y,y=x}x=r(this.h[1],g,w),this.h[1]=r(this.h[2],f,E),this.h[2]=r(this.h[3],v,_),this.h[3]=r(this.h[4],o,y),this.h[4]=r(this.h[0],s,b),this.h[0]=x},s.prototype._digest=function(t){return"hex"===t?e.toHex32(this.h,"little"):e.split32(this.h,"little")};var d=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],h=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],p=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],m=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11];return cc}(),e.hmac=function(){if(sc)return oc;sc=1;var e=Ml(),t=zl();function n(t,i,r){if(!(this instanceof n))return new n(t,i,r);this.Hash=t,this.blockSize=t.blockSize/8,this.outSize=t.outSize/8,this.inner=null,this.outer=null,this._init(e.toArray(i,r))}return oc=n,n.prototype._init=function(e){e.length>this.blockSize&&(e=(new this.Hash).update(e).digest()),t(e.length<=this.blockSize);for(var n=e.length;n<this.blockSize;n++)e.push(0);for(n=0;n<e.length;n++)e[n]^=54;for(this.inner=(new this.Hash).update(e),n=0;n<e.length;n++)e[n]^=106;this.outer=(new this.Hash).update(e)},n.prototype.update=function(e,t){return this.inner.update(e,t),this},n.prototype.digest=function(e){return this.outer.update(this.inner.digest()),this.outer.digest(e)},oc}(),e.sha1=e.sha.sha1,e.sha256=e.sha.sha256,e.sha224=e.sha.sha224,e.sha384=e.sha.sha384,e.sha512=e.sha.sha512,e.ripemd160=e.ripemd.ripemd160}()),Tl),dc=bt(uc);class hc{diagram;constructor(e){this.diagram=e}async isThatADiagram(e){const t=this.querySelectorWithData(e),n=t?.element.querySelector("svg"),i=t?.element.querySelector("img");if(t&&(n||i))return t}querySelectorWithData(e){for(const t of this.diagram.plugin.settings.supported_diagrams){if(!t.on)continue;const n=e.closest(t.selector)||e.querySelector(t.selector);if(n)return{element:n,diagram:t}}return null}getElSize(e){const t=e.querySelector("svg"),n=e.querySelector("img");if(null===t&&null===n)return;const i=n?n.getBoundingClientRect():e.getBoundingClientRect();return{width:i.width,height:i.height}}async genID(e,t,n){const i=`${n.name}:${e}-${t}`,r=dc.sha256().update(i).digest("hex");return`id-${this.diagram.plugin.context.view?.file?.stat.ctime??0}-${r}`}sourceExtractionWithContext(e,t){const n=t.context.getSectionInfo(t.contextElement);if(!n)return{source:"No source available",lineStart:0,lineEnd:0};const{lineStart:i,lineEnd:r,text:a}=n,o=i+1,s=r-1;return{source:a.split("\n").slice(o,s+1).join("\n"),lineStart:o,lineEnd:s}}sourceExtractionWithoutContext(e){const t=e.parentElement,n=t.cmView?.deco?.widget;return n?{source:n.code,lineStart:n.lineStart,lineEnd:n.lineEnd}:{source:"No source available",lineStart:0,lineEnd:0}}initDiagramSize(e){const t=this.getElSize(e);if(t)return t}async createDiagramWrapper(e,t){const n=document.createElement("div"),i=e.element;n.addClass("diagram-container");const r=this.diagram.plugin.isInPreviewMode?"preview":"live-preview";return n.setAttribute("data-diagram-zoom-drag-rendering-mode",`${r}`),i.parentNode?.insertBefore(n,i),n.appendChild(i),n.id=await this.genID(t.lineStart,t.lineEnd,e.diagram),n.setAttribute("data-folded",this.diagram.plugin.settings.foldByDefault.toString()),n.setAttribute("tabindex","0"),n}prepareDiagramElement(e){return!!e.parentElement&&(!e.parentElement.hasClass("diagram-container")&&(!e.hasClass("diagram-content")&&(e.addClass("diagram-content"),!0)))}postInitDiagram(e,t,n,i){const r=e.element;this.diagram.state.initializeContainer(t.id,n.source,i),this.diagram.controlPanel.initialize(t,e.diagram),this.diagram.events.initialize(t,e.diagram),this.diagram.contextMenu.initialize(t,e.diagram),this.diagram.updateDiagramSizeBasedOnStatus(t),this.diagram.actions.fitToContainer(r,t)}}class pc extends hc{constructor(e){super(e)}async initialize(e,t){if(!t)return;const n=await this.isThatADiagram(e);if(n)return void await this.processDiagram(n,t);const i=new MutationObserver((async e=>{for(const n of e)if("childList"===n.type)for(const e of Array.from(n.addedNodes)){if(!(e instanceof Element))continue;const n=e,i=await this.isThatADiagram(n);i&&await this.processDiagram(i,t)}}));i.observe(e,{childList:!0,subtree:!0}),setTimeout((()=>{i.disconnect()}),5e3)}async processDiagram(e,t){if(!this.prepareDiagramElement(e.element))return;const n=this.sourceExtractionWithContext(e.element,{contextElement:e.element,context:t}),i=this.initDiagramSize(e.element);if(void 0===i)return;const r=await this.createDiagramWrapper(e,n);this.postInitDiagram(e,r,n,i)}}class mc extends hc{constructor(e){super(e)}async initialize(e,t){if(this.diagram.livePreviewObserver)return;this.diagram.livePreviewObserver=new MutationObserver((async e=>{for(const t of e)if("childList"===t.type)for(const e of Array.from(t.addedNodes)){if(!(e instanceof Element))continue;const t=e,n=await this.isThatADiagram(t);n&&await this.processDiagram(n)}}));const n=e.querySelectorAll(".cm-preview-code-block.cm-embed-block");for(const e of Array.from(n)){const t=await this.isThatADiagram(e);t&&await this.processDiagram(t)}this.diagram.livePreviewObserver.observe(e,{childList:!0,subtree:!0})}async processDiagram(e){if(!this.prepareDiagramElement(e.element))return;const t=this.sourceExtractionWithoutContext(e.element),n=this.initDiagramSize(e.element);if(void 0===n)return;e.element.parentElement?.addClass("live-preview-parent");const i=await this.createDiagramWrapper(e,t);i.addClass("live-preview"),this.postInitDiagram(e,i,t,n)}}class gc{static getSuitableAdapter(e){return e.plugin.isInPreviewMode?new pc(e):e.plugin.isInLivePreviewMode?new mc(e):void 0}}class fc{plugin;state;controlPanel;events;actions;contextMenu;activeContainer=void 0;dx;dy;scale;nativeTouchEventsEnabled;source;panelsData;livePreviewObserver;size;constructor(e){this.plugin=e,this.state=new pl(this),this.actions=new xl(this),this.events=new Sl(this),this.controlPanel=new _l(this),this.contextMenu=new Dl(this)}get compoundSelector(){return this.plugin.settings.supported_diagrams.reduce(((e,t)=>t.on?e?`${e}, ${t.selector}`:t.selector:e),"")}async initialize(e,t){const n=gc.getSuitableAdapter(this);n&&await n.initialize(e,t)}updateDiagramSizeBasedOnStatus(e){const t="true"===e.dataset.folded?this.plugin.settings.diagramFolded:this.plugin.settings.diagramExpanded,n=this.plugin.diagram.size,i=parseFloat(t.height),r=parseFloat(t.width),a="%"===t.heightUnit?i/100*n.height:i,o="%"===t.widthUnit?r/100*n.width:r;if(e.style.height=`${a}px`,e.style.width=`${o}px`,this.plugin.isInLivePreviewMode){const t=e.closest(".live-preview-parent");t.style.setProperty("height",`${a}px`,"important"),t.style.setProperty("width",`${o}px`,"important"),console.log("set parent")}}}class vc{leaf;view;get leafID(){return this.leaf&&this.leaf.id}get isValid(){return void 0!==this.leaf&&void 0!==this.view}}class _c extends t.Plugin{context;settings;settingsManager;pluginStateChecker;publisher;observer;diagram;async initializePlugin(){await this.initializeCore(),await this.initializeUI(),await this.initializeEventSystem(),await this.initializeUtils()}async initializeCore(){this.settingsManager=new n(this),await this.settingsManager.loadSettings(),this.addSettingTab(new ol(this.app,this)),this.context=new vc}async initializeEventSystem(){this.publisher=new cl(this),this.observer=new dl(this),this.registerMarkdownPostProcessor((async(e,t)=>{this.initializeView(),this.isInLivePreviewMode||await this.diagram.initialize(e,t)})),this.registerEvent(this.app.workspace.on("layout-change",(async()=>{this.cleanupView(),await this.diagram.state.cleanupContainers(),this.initializeView(),this.context.isValid&&this.isInLivePreviewMode&&await this.diagram.initialize(this.context.view.contentEl)}))),this.registerEvent(this.app.workspace.on("active-leaf-change",(async()=>{this.cleanupView(),this.initializeView(),this.context.isValid&&this.isInLivePreviewMode&&await this.diagram.initialize(this.context.view.contentEl)})))}async initializeUI(){this.diagram=new fc(this),this.addCommand({id:"diagram-zoom-drag-toggle-panels-management-state",name:"Toggle control panel visibility of current active diagram",checkCallback:e=>{if(e)return!!this.diagram.activeContainer;const t=this.diagram.panelsData.panels;if(!t)return;const n=t.zoom.panel.hasClass("hidden");var i,r;t.zoom.panel.toggleClass("hidden",!n),t.zoom.panel.toggleClass("visible",n),t.move.panel.toggleClass("hidden",!n),t.move.panel.toggleClass("visible",n),t.service.panel.toggleClass("hidden",!n),t.service.panel.toggleClass("visible",n),r=!0,(i=this).publisher.publish({eventID:Ga.PanelsChangedVisibility,timestamp:new Date,emitter:i.app.workspace,data:{visible:r}})}})}async initializeUtils(){this.pluginStateChecker=new sl(this)}async onload(){await this.initializePlugin()}async onunload(){this.observer.unsubscribeAll()}initializeView(){const e=this.app.workspace.getActiveViewOfType(t.MarkdownView);if(!e)return;const n=e.leaf;this.context.leaf=n,this.context.view=e,this.diagram.state.initializeLeafData(this.context.leafID)}cleanupView(){if(this.context?.leaf){null===this.app.workspace.getLeafById(this.context.leaf.id)&&(this.context.view=void 0,this.diagram.state.cleanupData(this.context.leafID),this.context.leaf=void 0)}}showNotice(e,n){new t.Notice(e,n)}get isInPreviewMode(){const e=this.context?.view?.getState();return"preview"===e?.mode}get isInLivePreviewMode(){const e=this.context?.view?.getState();return!e?.source&&"source"===e?.mode}}module.exports=_c;

/* nosourcemap */