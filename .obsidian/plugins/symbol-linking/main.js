/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __commonJS=(cb,mod)=>function(){return mod||(0,cb[__getOwnPropNames(cb)[0]])((mod={exports:{}}).exports,mod),mod.exports};var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var require_fuzzysort=__commonJS({"node_modules/fuzzysort/fuzzysort.js"(exports,module2){((root,UMD)=>{typeof define=="function"&&define.amd?define([],UMD):typeof module2=="object"&&module2.exports?module2.exports=UMD():root.fuzzysort=UMD()})(exports,_=>{"use strict";var single=(search,target)=>{if(!search||!target)return NULL;var preparedSearch=getPreparedSearch(search);isPrepared(target)||(target=getPrepared(target));var searchBitflags=preparedSearch.bitflags;return(searchBitflags&target._bitflags)!==searchBitflags?NULL:algorithm(preparedSearch,target)},go=(search,targets,options)=>{if(!search)return options?.all?all(targets,options):noResults;var preparedSearch=getPreparedSearch(search),searchBitflags=preparedSearch.bitflags,containsSpace=preparedSearch.containsSpace,threshold=denormalizeScore(options?.threshold||0),limit=options?.limit||INFINITY,resultsLen=0,limitedCount=0,targetsLen=targets.length;function push_result(result2){resultsLen<limit?(q.add(result2),++resultsLen):(++limitedCount,result2._score>q.peek()._score&&q.replaceTop(result2))}if(options?.key)for(var key=options.key,i=0;i<targetsLen;++i){var obj=targets[i],target=getValue(obj,key);if(target&&(isPrepared(target)||(target=getPrepared(target)),(searchBitflags&target._bitflags)===searchBitflags)){var result=algorithm(preparedSearch,target);result!==NULL&&(result._score<threshold||(result.obj=obj,push_result(result)))}}else if(options?.keys){var keys=options.keys,keysLen=keys.length;outer:for(var i=0;i<targetsLen;++i){var obj=targets[i];{for(var keysBitflags=0,keyI=0;keyI<keysLen;++keyI){var key=keys[keyI],target=getValue(obj,key);if(!target){tmpTargets[keyI]=noTarget;continue}isPrepared(target)||(target=getPrepared(target)),tmpTargets[keyI]=target,keysBitflags|=target._bitflags}if((searchBitflags&keysBitflags)!==searchBitflags)continue}if(containsSpace)for(let i2=0;i2<preparedSearch.spaceSearches.length;i2++)keysSpacesBestScores[i2]=NEGATIVE_INFINITY;for(var keyI=0;keyI<keysLen;++keyI){if(target=tmpTargets[keyI],target===noTarget){tmpResults[keyI]=noTarget;continue}if(tmpResults[keyI]=algorithm(preparedSearch,target,!1,containsSpace),tmpResults[keyI]===NULL){tmpResults[keyI]=noTarget;continue}if(containsSpace)for(let i2=0;i2<preparedSearch.spaceSearches.length;i2++){if(allowPartialMatchScores[i2]>-1e3&&keysSpacesBestScores[i2]>NEGATIVE_INFINITY){var tmp=(keysSpacesBestScores[i2]+allowPartialMatchScores[i2])/4;tmp>keysSpacesBestScores[i2]&&(keysSpacesBestScores[i2]=tmp)}allowPartialMatchScores[i2]>keysSpacesBestScores[i2]&&(keysSpacesBestScores[i2]=allowPartialMatchScores[i2])}}if(containsSpace){for(let i2=0;i2<preparedSearch.spaceSearches.length;i2++)if(keysSpacesBestScores[i2]===NEGATIVE_INFINITY)continue outer}else{var hasAtLeast1Match=!1;for(let i2=0;i2<keysLen;i2++)if(tmpResults[i2]._score!==NEGATIVE_INFINITY){hasAtLeast1Match=!0;break}if(!hasAtLeast1Match)continue}var objResults=new KeysResult(keysLen);for(let i2=0;i2<keysLen;i2++)objResults[i2]=tmpResults[i2];if(containsSpace){var score=0;for(let i2=0;i2<preparedSearch.spaceSearches.length;i2++)score+=keysSpacesBestScores[i2]}else{var score=NEGATIVE_INFINITY;for(let i2=0;i2<keysLen;i2++){var result=objResults[i2];if(result._score>-1e3&&score>NEGATIVE_INFINITY){var tmp=(score+result._score)/4;tmp>score&&(score=tmp)}result._score>score&&(score=result._score)}}if(objResults.obj=obj,objResults._score=score,options?.scoreFn){if(score=options.scoreFn(objResults),!score)continue;score=denormalizeScore(score),objResults._score=score}score<threshold||push_result(objResults)}}else for(var i=0;i<targetsLen;++i){var target=targets[i];if(target&&(isPrepared(target)||(target=getPrepared(target)),(searchBitflags&target._bitflags)===searchBitflags)){var result=algorithm(preparedSearch,target);result!==NULL&&(result._score<threshold||push_result(result))}}if(resultsLen===0)return noResults;for(var results=new Array(resultsLen),i=resultsLen-1;i>=0;--i)results[i]=q.poll();return results.total=resultsLen+limitedCount,results},highlight=(result,open="<b>",close="</b>")=>{for(var callback=typeof open=="function"?open:void 0,target=result.target,targetLen=target.length,indexes=result.indexes,highlighted="",matchI=0,indexesI=0,opened=!1,parts=[],i=0;i<targetLen;++i){var char=target[i];if(indexes[indexesI]===i){if(++indexesI,opened||(opened=!0,callback?(parts.push(highlighted),highlighted=""):highlighted+=open),indexesI===indexes.length){callback?(highlighted+=char,parts.push(callback(highlighted,matchI++)),highlighted="",parts.push(target.substr(i+1))):highlighted+=char+close+target.substr(i+1);break}}else opened&&(opened=!1,callback?(parts.push(callback(highlighted,matchI++)),highlighted=""):highlighted+=close);highlighted+=char}return callback?parts:highlighted},prepare=target=>{typeof target=="number"?target=""+target:typeof target!="string"&&(target="");var info=prepareLowerInfo(target);return new_result(target,{_targetLower:info._lower,_targetLowerCodes:info.lowerCodes,_bitflags:info.bitflags})},cleanup=()=>{preparedCache.clear(),preparedSearchCache.clear()};class Result{get indexes(){return this._indexes.slice(0,this._indexes.len).sort((a,b)=>a-b)}set indexes(indexes){return this._indexes=indexes}highlight(open,close){return highlight(this,open,close)}get score(){return normalizeScore(this._score)}set score(score){this._score=denormalizeScore(score)}}class KeysResult extends Array{get score(){return normalizeScore(this._score)}set score(score){this._score=denormalizeScore(score)}}var new_result=(target,options)=>{let result=new Result;return result.target=target,result.obj=options.obj??NULL,result._score=options._score??NEGATIVE_INFINITY,result._indexes=options._indexes??[],result._targetLower=options._targetLower??"",result._targetLowerCodes=options._targetLowerCodes??NULL,result._nextBeginningIndexes=options._nextBeginningIndexes??NULL,result._bitflags=options._bitflags??0,result},normalizeScore=score=>score===NEGATIVE_INFINITY?0:score>1?score:Math.E**(((-score+1)**.04307-1)*-2),denormalizeScore=normalizedScore=>normalizedScore===0?NEGATIVE_INFINITY:normalizedScore>1?normalizedScore:1-Math.pow(Math.log(normalizedScore)/-2+1,1/.04307),prepareSearch=search=>{typeof search=="number"?search=""+search:typeof search!="string"&&(search=""),search=search.trim();var info=prepareLowerInfo(search),spaceSearches=[];if(info.containsSpace){var searches=search.split(/\s+/);searches=[...new Set(searches)];for(var i=0;i<searches.length;i++)if(searches[i]!==""){var _info=prepareLowerInfo(searches[i]);spaceSearches.push({lowerCodes:_info.lowerCodes,_lower:searches[i].toLowerCase(),containsSpace:!1})}}return{lowerCodes:info.lowerCodes,_lower:info._lower,containsSpace:info.containsSpace,bitflags:info.bitflags,spaceSearches}},getPrepared=target=>{if(target.length>999)return prepare(target);var targetPrepared=preparedCache.get(target);return targetPrepared!==void 0||(targetPrepared=prepare(target),preparedCache.set(target,targetPrepared)),targetPrepared},getPreparedSearch=search=>{if(search.length>999)return prepareSearch(search);var searchPrepared=preparedSearchCache.get(search);return searchPrepared!==void 0||(searchPrepared=prepareSearch(search),preparedSearchCache.set(search,searchPrepared)),searchPrepared},all=(targets,options)=>{var results=[];results.total=targets.length;var limit=options?.limit||INFINITY;if(options?.key)for(var i=0;i<targets.length;i++){var obj=targets[i],target=getValue(obj,options.key);if(target!=NULL){isPrepared(target)||(target=getPrepared(target));var result=new_result(target.target,{_score:target._score,obj});if(results.push(result),results.length>=limit)return results}}else if(options?.keys)for(var i=0;i<targets.length;i++){for(var obj=targets[i],objResults=new KeysResult(options.keys.length),keyI=options.keys.length-1;keyI>=0;--keyI){var target=getValue(obj,options.keys[keyI]);if(!target){objResults[keyI]=noTarget;continue}isPrepared(target)||(target=getPrepared(target)),target._score=NEGATIVE_INFINITY,target._indexes.len=0,objResults[keyI]=target}if(objResults.obj=obj,objResults._score=NEGATIVE_INFINITY,results.push(objResults),results.length>=limit)return results}else for(var i=0;i<targets.length;i++){var target=targets[i];if(target!=NULL&&(isPrepared(target)||(target=getPrepared(target)),target._score=NEGATIVE_INFINITY,target._indexes.len=0,results.push(target),results.length>=limit))return results}return results},algorithm=(preparedSearch,prepared,allowSpaces=!1,allowPartialMatch=!1)=>{if(allowSpaces===!1&&preparedSearch.containsSpace)return algorithmSpaces(preparedSearch,prepared,allowPartialMatch);for(var searchLower=preparedSearch._lower,searchLowerCodes=preparedSearch.lowerCodes,searchLowerCode=searchLowerCodes[0],targetLowerCodes=prepared._targetLowerCodes,searchLen=searchLowerCodes.length,targetLen=targetLowerCodes.length,searchI=0,targetI=0,matchesSimpleLen=0;;){var isMatch=searchLowerCode===targetLowerCodes[targetI];if(isMatch){if(matchesSimple[matchesSimpleLen++]=targetI,++searchI,searchI===searchLen)break;searchLowerCode=searchLowerCodes[searchI]}if(++targetI,targetI>=targetLen)return NULL}var searchI=0,successStrict=!1,matchesStrictLen=0,nextBeginningIndexes=prepared._nextBeginningIndexes;nextBeginningIndexes===NULL&&(nextBeginningIndexes=prepared._nextBeginningIndexes=prepareNextBeginningIndexes(prepared.target)),targetI=matchesSimple[0]===0?0:nextBeginningIndexes[matchesSimple[0]-1];var backtrackCount=0;if(targetI!==targetLen)for(;;)if(targetI>=targetLen){if(searchI<=0||(++backtrackCount,backtrackCount>200))break;--searchI;var lastMatch=matchesStrict[--matchesStrictLen];targetI=nextBeginningIndexes[lastMatch]}else{var isMatch=searchLowerCodes[searchI]===targetLowerCodes[targetI];if(isMatch){if(matchesStrict[matchesStrictLen++]=targetI,++searchI,searchI===searchLen){successStrict=!0;break}++targetI}else targetI=nextBeginningIndexes[targetI]}var substringIndex=searchLen<=1?-1:prepared._targetLower.indexOf(searchLower,matchesSimple[0]),isSubstring=!!~substringIndex,isSubstringBeginning=isSubstring?substringIndex===0||prepared._nextBeginningIndexes[substringIndex-1]===substringIndex:!1;if(isSubstring&&!isSubstringBeginning){for(var i=0;i<nextBeginningIndexes.length;i=nextBeginningIndexes[i])if(!(i<=substringIndex)){for(var s=0;s<searchLen&&searchLowerCodes[s]===prepared._targetLowerCodes[i+s];s++);if(s===searchLen){substringIndex=i,isSubstringBeginning=!0;break}}}var calculateScore=matches=>{for(var score2=0,extraMatchGroupCount=0,i2=1;i2<searchLen;++i2)matches[i2]-matches[i2-1]!==1&&(score2-=matches[i2],++extraMatchGroupCount);var unmatchedDistance=matches[searchLen-1]-matches[0]-(searchLen-1);if(score2-=(12+unmatchedDistance)*extraMatchGroupCount,matches[0]!==0&&(score2-=matches[0]*matches[0]*.2),!successStrict)score2*=1e3;else{for(var uniqueBeginningIndexes=1,i2=nextBeginningIndexes[0];i2<targetLen;i2=nextBeginningIndexes[i2])++uniqueBeginningIndexes;uniqueBeginningIndexes>24&&(score2*=(uniqueBeginningIndexes-24)*10)}return score2-=(targetLen-searchLen)/2,isSubstring&&(score2/=1+searchLen*searchLen*1),isSubstringBeginning&&(score2/=1+searchLen*searchLen*1),score2-=(targetLen-searchLen)/2,score2};if(successStrict)if(isSubstringBeginning){for(var i=0;i<searchLen;++i)matchesSimple[i]=substringIndex+i;var matchesBest=matchesSimple,score=calculateScore(matchesSimple)}else var matchesBest=matchesStrict,score=calculateScore(matchesStrict);else{if(isSubstring)for(var i=0;i<searchLen;++i)matchesSimple[i]=substringIndex+i;var matchesBest=matchesSimple,score=calculateScore(matchesBest)}prepared._score=score;for(var i=0;i<searchLen;++i)prepared._indexes[i]=matchesBest[i];prepared._indexes.len=searchLen;let result=new Result;return result.target=prepared.target,result._score=prepared._score,result._indexes=prepared._indexes,result},algorithmSpaces=(preparedSearch,target,allowPartialMatch)=>{for(var seen_indexes=new Set,score=0,result=NULL,first_seen_index_last_search=0,searches=preparedSearch.spaceSearches,searchesLen=searches.length,changeslen=0,resetNextBeginningIndexes=()=>{for(let i2=changeslen-1;i2>=0;i2--)target._nextBeginningIndexes[nextBeginningIndexesChanges[i2*2+0]]=nextBeginningIndexesChanges[i2*2+1]},hasAtLeast1Match=!1,i=0;i<searchesLen;++i){allowPartialMatchScores[i]=NEGATIVE_INFINITY;var search=searches[i];if(result=algorithm(search,target),allowPartialMatch){if(result===NULL)continue;hasAtLeast1Match=!0}else if(result===NULL)return resetNextBeginningIndexes(),NULL;var isTheLastSearch=i===searchesLen-1;if(!isTheLastSearch){var indexes=result._indexes,indexesIsConsecutiveSubstring=!0;for(let i2=0;i2<indexes.len-1;i2++)if(indexes[i2+1]-indexes[i2]!==1){indexesIsConsecutiveSubstring=!1;break}if(indexesIsConsecutiveSubstring){var newBeginningIndex=indexes[indexes.len-1]+1,toReplace=target._nextBeginningIndexes[newBeginningIndex-1];for(let i2=newBeginningIndex-1;i2>=0&&toReplace===target._nextBeginningIndexes[i2];i2--)target._nextBeginningIndexes[i2]=newBeginningIndex,nextBeginningIndexesChanges[changeslen*2+0]=i2,nextBeginningIndexesChanges[changeslen*2+1]=toReplace,changeslen++}}score+=result._score/searchesLen,allowPartialMatchScores[i]=result._score/searchesLen,result._indexes[0]<first_seen_index_last_search&&(score-=(first_seen_index_last_search-result._indexes[0])*2),first_seen_index_last_search=result._indexes[0];for(var j=0;j<result._indexes.len;++j)seen_indexes.add(result._indexes[j])}if(allowPartialMatch&&!hasAtLeast1Match)return NULL;resetNextBeginningIndexes();var allowSpacesResult=algorithm(preparedSearch,target,!0);if(allowSpacesResult!==NULL&&allowSpacesResult._score>score){if(allowPartialMatch)for(var i=0;i<searchesLen;++i)allowPartialMatchScores[i]=allowSpacesResult._score/searchesLen;return allowSpacesResult}allowPartialMatch&&(result=target),result._score=score;var i=0;for(let index of seen_indexes)result._indexes[i++]=index;return result._indexes.len=i,result},remove_accents=str=>str.replace(/\p{Script=Latin}+/gu,match=>match.normalize("NFD")).replace(/[\u0300-\u036f]/g,""),prepareLowerInfo=str=>{str=remove_accents(str);for(var strLen=str.length,lower=str.toLowerCase(),lowerCodes=[],bitflags=0,containsSpace=!1,i=0;i<strLen;++i){var lowerCode=lowerCodes[i]=lower.charCodeAt(i);if(lowerCode===32){containsSpace=!0;continue}var bit=lowerCode>=97&&lowerCode<=122?lowerCode-97:lowerCode>=48&&lowerCode<=57?26:lowerCode<=127?30:31;bitflags|=1<<bit}return{lowerCodes,bitflags,containsSpace,_lower:lower}},prepareBeginningIndexes=target=>{for(var targetLen=target.length,beginningIndexes=[],beginningIndexesLen=0,wasUpper=!1,wasAlphanum=!1,i=0;i<targetLen;++i){var targetCode=target.charCodeAt(i),isUpper=targetCode>=65&&targetCode<=90,isAlphanum=isUpper||targetCode>=97&&targetCode<=122||targetCode>=48&&targetCode<=57,isBeginning=isUpper&&!wasUpper||!wasAlphanum||!isAlphanum;wasUpper=isUpper,wasAlphanum=isAlphanum,isBeginning&&(beginningIndexes[beginningIndexesLen++]=i)}return beginningIndexes},prepareNextBeginningIndexes=target=>{target=remove_accents(target);for(var targetLen=target.length,beginningIndexes=prepareBeginningIndexes(target),nextBeginningIndexes=[],lastIsBeginning=beginningIndexes[0],lastIsBeginningI=0,i=0;i<targetLen;++i)lastIsBeginning>i?nextBeginningIndexes[i]=lastIsBeginning:(lastIsBeginning=beginningIndexes[++lastIsBeginningI],nextBeginningIndexes[i]=lastIsBeginning===void 0?targetLen:lastIsBeginning);return nextBeginningIndexes},preparedCache=new Map,preparedSearchCache=new Map,matchesSimple=[],matchesStrict=[],nextBeginningIndexesChanges=[],keysSpacesBestScores=[],allowPartialMatchScores=[],tmpTargets=[],tmpResults=[],getValue=(obj,prop)=>{var tmp=obj[prop];if(tmp!==void 0)return tmp;if(typeof prop=="function")return prop(obj);var segs=prop;Array.isArray(prop)||(segs=prop.split("."));for(var len=segs.length,i=-1;obj&&++i<len;)obj=obj[segs[i]];return obj},isPrepared=x=>typeof x=="object"&&typeof x._bitflags=="number",INFINITY=1/0,NEGATIVE_INFINITY=-INFINITY,noResults=[];noResults.total=0;var NULL=null,noTarget=prepare(""),fastpriorityqueue=r=>{var e=[],o=0,a={},v=r2=>{for(var a2=0,v2=e[a2],c=1;c<o;){var s=c+1;a2=c,s<o&&e[s]._score<e[c]._score&&(a2=s),e[a2-1>>1]=e[a2],c=1+(a2<<1)}for(var f=a2-1>>1;a2>0&&v2._score<e[f]._score;f=(a2=f)-1>>1)e[a2]=e[f];e[a2]=v2};return a.add=r2=>{var a2=o;e[o++]=r2;for(var v2=a2-1>>1;a2>0&&r2._score<e[v2]._score;v2=(a2=v2)-1>>1)e[a2]=e[v2];e[a2]=r2},a.poll=r2=>{if(o!==0){var a2=e[0];return e[0]=e[--o],v(),a2}},a.peek=r2=>{if(o!==0)return e[0]},a.replaceTop=r2=>{e[0]=r2,v()},a},q=fastpriorityqueue();return{single,go,prepare,cleanup}})}});var main_exports={};__export(main_exports,{default:()=>AtSymbolLinking});module.exports=__toCommonJS(main_exports);var import_obsidian11=require("obsidian");var import_language=require("@codemirror/language"),import_view=require("@codemirror/view"),import_obsidian6=require("obsidian");var isValidFileNameCharacter=(char,settings)=>char===" "?!0:char==="\\"?!1:!new RegExp(settings.invalidCharacterRegex,settings.invalidCharacterRegexFlags).test(char),removeAccents=str=>str.normalize("NFD").replace(/\p{Diacritic}/gu,"");var top="top",bottom="bottom",right="right",left="left",auto="auto",basePlacements=[top,bottom,right,left],start="start",end="end",clippingParents="clippingParents",viewport="viewport",popper="popper",reference="reference",variationPlacements=basePlacements.reduce(function(acc,placement){return acc.concat([placement+"-"+start,placement+"-"+end])},[]),placements=[].concat(basePlacements,[auto]).reduce(function(acc,placement){return acc.concat([placement,placement+"-"+start,placement+"-"+end])},[]),beforeRead="beforeRead",read="read",afterRead="afterRead",beforeMain="beforeMain",main="main",afterMain="afterMain",beforeWrite="beforeWrite",write="write",afterWrite="afterWrite",modifierPhases=[beforeRead,read,afterRead,beforeMain,main,afterMain,beforeWrite,write,afterWrite];function getNodeName(element){return element?(element.nodeName||"").toLowerCase():null}function getWindow(node){if(node==null)return window;if(node.toString()!=="[object Window]"){var ownerDocument=node.ownerDocument;return ownerDocument&&ownerDocument.defaultView||window}return node}function isElement(node){var OwnElement=getWindow(node).Element;return node instanceof OwnElement||node instanceof Element}function isHTMLElement(node){var OwnElement=getWindow(node).HTMLElement;return node instanceof OwnElement||node instanceof HTMLElement}function isShadowRoot(node){if(typeof ShadowRoot>"u")return!1;var OwnElement=getWindow(node).ShadowRoot;return node instanceof OwnElement||node instanceof ShadowRoot}function applyStyles(_ref){var state=_ref.state;Object.keys(state.elements).forEach(function(name){var style=state.styles[name]||{},attributes=state.attributes[name]||{},element=state.elements[name];!isHTMLElement(element)||!getNodeName(element)||(Object.assign(element.style,style),Object.keys(attributes).forEach(function(name2){var value=attributes[name2];value===!1?element.removeAttribute(name2):element.setAttribute(name2,value===!0?"":value)}))})}function effect(_ref2){var state=_ref2.state,initialStyles={popper:{position:state.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(state.elements.popper.style,initialStyles.popper),state.styles=initialStyles,state.elements.arrow&&Object.assign(state.elements.arrow.style,initialStyles.arrow),function(){Object.keys(state.elements).forEach(function(name){var element=state.elements[name],attributes=state.attributes[name]||{},styleProperties=Object.keys(state.styles.hasOwnProperty(name)?state.styles[name]:initialStyles[name]),style=styleProperties.reduce(function(style2,property){return style2[property]="",style2},{});!isHTMLElement(element)||!getNodeName(element)||(Object.assign(element.style,style),Object.keys(attributes).forEach(function(attribute){element.removeAttribute(attribute)}))})}}var applyStyles_default={name:"applyStyles",enabled:!0,phase:"write",fn:applyStyles,effect,requires:["computeStyles"]};function getBasePlacement(placement){return placement.split("-")[0]}var max=Math.max,min=Math.min,round=Math.round;function getUAString(){var uaData=navigator.userAgentData;return uaData!=null&&uaData.brands&&Array.isArray(uaData.brands)?uaData.brands.map(function(item){return item.brand+"/"+item.version}).join(" "):navigator.userAgent}function isLayoutViewport(){return!/^((?!chrome|android).)*safari/i.test(getUAString())}function getBoundingClientRect(element,includeScale,isFixedStrategy){includeScale===void 0&&(includeScale=!1),isFixedStrategy===void 0&&(isFixedStrategy=!1);var clientRect=element.getBoundingClientRect(),scaleX=1,scaleY=1;includeScale&&isHTMLElement(element)&&(scaleX=element.offsetWidth>0&&round(clientRect.width)/element.offsetWidth||1,scaleY=element.offsetHeight>0&&round(clientRect.height)/element.offsetHeight||1);var _ref=isElement(element)?getWindow(element):window,visualViewport=_ref.visualViewport,addVisualOffsets=!isLayoutViewport()&&isFixedStrategy,x=(clientRect.left+(addVisualOffsets&&visualViewport?visualViewport.offsetLeft:0))/scaleX,y=(clientRect.top+(addVisualOffsets&&visualViewport?visualViewport.offsetTop:0))/scaleY,width=clientRect.width/scaleX,height=clientRect.height/scaleY;return{width,height,top:y,right:x+width,bottom:y+height,left:x,x,y}}function getLayoutRect(element){var clientRect=getBoundingClientRect(element),width=element.offsetWidth,height=element.offsetHeight;return Math.abs(clientRect.width-width)<=1&&(width=clientRect.width),Math.abs(clientRect.height-height)<=1&&(height=clientRect.height),{x:element.offsetLeft,y:element.offsetTop,width,height}}function contains(parent,child){var rootNode=child.getRootNode&&child.getRootNode();if(parent.contains(child))return!0;if(rootNode&&isShadowRoot(rootNode)){var next=child;do{if(next&&parent.isSameNode(next))return!0;next=next.parentNode||next.host}while(next)}return!1}function getComputedStyle(element){return getWindow(element).getComputedStyle(element)}function isTableElement(element){return["table","td","th"].indexOf(getNodeName(element))>=0}function getDocumentElement(element){return((isElement(element)?element.ownerDocument:element.document)||window.document).documentElement}function getParentNode(element){return getNodeName(element)==="html"?element:element.assignedSlot||element.parentNode||(isShadowRoot(element)?element.host:null)||getDocumentElement(element)}function getTrueOffsetParent(element){return!isHTMLElement(element)||getComputedStyle(element).position==="fixed"?null:element.offsetParent}function getContainingBlock(element){var isFirefox=/firefox/i.test(getUAString()),isIE=/Trident/i.test(getUAString());if(isIE&&isHTMLElement(element)){var elementCss=getComputedStyle(element);if(elementCss.position==="fixed")return null}var currentNode=getParentNode(element);for(isShadowRoot(currentNode)&&(currentNode=currentNode.host);isHTMLElement(currentNode)&&["html","body"].indexOf(getNodeName(currentNode))<0;){var css=getComputedStyle(currentNode);if(css.transform!=="none"||css.perspective!=="none"||css.contain==="paint"||["transform","perspective"].indexOf(css.willChange)!==-1||isFirefox&&css.willChange==="filter"||isFirefox&&css.filter&&css.filter!=="none")return currentNode;currentNode=currentNode.parentNode}return null}function getOffsetParent(element){for(var window2=getWindow(element),offsetParent=getTrueOffsetParent(element);offsetParent&&isTableElement(offsetParent)&&getComputedStyle(offsetParent).position==="static";)offsetParent=getTrueOffsetParent(offsetParent);return offsetParent&&(getNodeName(offsetParent)==="html"||getNodeName(offsetParent)==="body"&&getComputedStyle(offsetParent).position==="static")?window2:offsetParent||getContainingBlock(element)||window2}function getMainAxisFromPlacement(placement){return["top","bottom"].indexOf(placement)>=0?"x":"y"}function within(min2,value,max2){return max(min2,min(value,max2))}function withinMaxClamp(min2,value,max2){var v=within(min2,value,max2);return v>max2?max2:v}function getFreshSideObject(){return{top:0,right:0,bottom:0,left:0}}function mergePaddingObject(paddingObject){return Object.assign({},getFreshSideObject(),paddingObject)}function expandToHashMap(value,keys){return keys.reduce(function(hashMap,key){return hashMap[key]=value,hashMap},{})}var toPaddingObject=function(padding,state){return padding=typeof padding=="function"?padding(Object.assign({},state.rects,{placement:state.placement})):padding,mergePaddingObject(typeof padding!="number"?padding:expandToHashMap(padding,basePlacements))};function arrow(_ref){var _state$modifiersData$,state=_ref.state,name=_ref.name,options=_ref.options,arrowElement=state.elements.arrow,popperOffsets2=state.modifiersData.popperOffsets,basePlacement=getBasePlacement(state.placement),axis=getMainAxisFromPlacement(basePlacement),isVertical=[left,right].indexOf(basePlacement)>=0,len=isVertical?"height":"width";if(!(!arrowElement||!popperOffsets2)){var paddingObject=toPaddingObject(options.padding,state),arrowRect=getLayoutRect(arrowElement),minProp=axis==="y"?top:left,maxProp=axis==="y"?bottom:right,endDiff=state.rects.reference[len]+state.rects.reference[axis]-popperOffsets2[axis]-state.rects.popper[len],startDiff=popperOffsets2[axis]-state.rects.reference[axis],arrowOffsetParent=getOffsetParent(arrowElement),clientSize=arrowOffsetParent?axis==="y"?arrowOffsetParent.clientHeight||0:arrowOffsetParent.clientWidth||0:0,centerToReference=endDiff/2-startDiff/2,min2=paddingObject[minProp],max2=clientSize-arrowRect[len]-paddingObject[maxProp],center=clientSize/2-arrowRect[len]/2+centerToReference,offset2=within(min2,center,max2),axisProp=axis;state.modifiersData[name]=(_state$modifiersData$={},_state$modifiersData$[axisProp]=offset2,_state$modifiersData$.centerOffset=offset2-center,_state$modifiersData$)}}function effect2(_ref2){var state=_ref2.state,options=_ref2.options,_options$element=options.element,arrowElement=_options$element===void 0?"[data-popper-arrow]":_options$element;arrowElement!=null&&(typeof arrowElement=="string"&&(arrowElement=state.elements.popper.querySelector(arrowElement),!arrowElement)||contains(state.elements.popper,arrowElement)&&(state.elements.arrow=arrowElement))}var arrow_default={name:"arrow",enabled:!0,phase:"main",fn:arrow,effect:effect2,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function getVariation(placement){return placement.split("-")[1]}var unsetSides={top:"auto",right:"auto",bottom:"auto",left:"auto"};function roundOffsetsByDPR(_ref,win){var x=_ref.x,y=_ref.y,dpr=win.devicePixelRatio||1;return{x:round(x*dpr)/dpr||0,y:round(y*dpr)/dpr||0}}function mapToStyles(_ref2){var _Object$assign2,popper2=_ref2.popper,popperRect=_ref2.popperRect,placement=_ref2.placement,variation=_ref2.variation,offsets=_ref2.offsets,position=_ref2.position,gpuAcceleration=_ref2.gpuAcceleration,adaptive=_ref2.adaptive,roundOffsets=_ref2.roundOffsets,isFixed=_ref2.isFixed,_offsets$x=offsets.x,x=_offsets$x===void 0?0:_offsets$x,_offsets$y=offsets.y,y=_offsets$y===void 0?0:_offsets$y,_ref3=typeof roundOffsets=="function"?roundOffsets({x,y}):{x,y};x=_ref3.x,y=_ref3.y;var hasX=offsets.hasOwnProperty("x"),hasY=offsets.hasOwnProperty("y"),sideX=left,sideY=top,win=window;if(adaptive){var offsetParent=getOffsetParent(popper2),heightProp="clientHeight",widthProp="clientWidth";if(offsetParent===getWindow(popper2)&&(offsetParent=getDocumentElement(popper2),getComputedStyle(offsetParent).position!=="static"&&position==="absolute"&&(heightProp="scrollHeight",widthProp="scrollWidth")),offsetParent=offsetParent,placement===top||(placement===left||placement===right)&&variation===end){sideY=bottom;var offsetY=isFixed&&offsetParent===win&&win.visualViewport?win.visualViewport.height:offsetParent[heightProp];y-=offsetY-popperRect.height,y*=gpuAcceleration?1:-1}if(placement===left||(placement===top||placement===bottom)&&variation===end){sideX=right;var offsetX=isFixed&&offsetParent===win&&win.visualViewport?win.visualViewport.width:offsetParent[widthProp];x-=offsetX-popperRect.width,x*=gpuAcceleration?1:-1}}var commonStyles=Object.assign({position},adaptive&&unsetSides),_ref4=roundOffsets===!0?roundOffsetsByDPR({x,y},getWindow(popper2)):{x,y};if(x=_ref4.x,y=_ref4.y,gpuAcceleration){var _Object$assign;return Object.assign({},commonStyles,(_Object$assign={},_Object$assign[sideY]=hasY?"0":"",_Object$assign[sideX]=hasX?"0":"",_Object$assign.transform=(win.devicePixelRatio||1)<=1?"translate("+x+"px, "+y+"px)":"translate3d("+x+"px, "+y+"px, 0)",_Object$assign))}return Object.assign({},commonStyles,(_Object$assign2={},_Object$assign2[sideY]=hasY?y+"px":"",_Object$assign2[sideX]=hasX?x+"px":"",_Object$assign2.transform="",_Object$assign2))}function computeStyles(_ref5){var state=_ref5.state,options=_ref5.options,_options$gpuAccelerat=options.gpuAcceleration,gpuAcceleration=_options$gpuAccelerat===void 0?!0:_options$gpuAccelerat,_options$adaptive=options.adaptive,adaptive=_options$adaptive===void 0?!0:_options$adaptive,_options$roundOffsets=options.roundOffsets,roundOffsets=_options$roundOffsets===void 0?!0:_options$roundOffsets,commonStyles={placement:getBasePlacement(state.placement),variation:getVariation(state.placement),popper:state.elements.popper,popperRect:state.rects.popper,gpuAcceleration,isFixed:state.options.strategy==="fixed"};state.modifiersData.popperOffsets!=null&&(state.styles.popper=Object.assign({},state.styles.popper,mapToStyles(Object.assign({},commonStyles,{offsets:state.modifiersData.popperOffsets,position:state.options.strategy,adaptive,roundOffsets})))),state.modifiersData.arrow!=null&&(state.styles.arrow=Object.assign({},state.styles.arrow,mapToStyles(Object.assign({},commonStyles,{offsets:state.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets})))),state.attributes.popper=Object.assign({},state.attributes.popper,{"data-popper-placement":state.placement})}var computeStyles_default={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:computeStyles,data:{}};var passive={passive:!0};function effect3(_ref){var state=_ref.state,instance=_ref.instance,options=_ref.options,_options$scroll=options.scroll,scroll=_options$scroll===void 0?!0:_options$scroll,_options$resize=options.resize,resize=_options$resize===void 0?!0:_options$resize,window2=getWindow(state.elements.popper),scrollParents=[].concat(state.scrollParents.reference,state.scrollParents.popper);return scroll&&scrollParents.forEach(function(scrollParent){scrollParent.addEventListener("scroll",instance.update,passive)}),resize&&window2.addEventListener("resize",instance.update,passive),function(){scroll&&scrollParents.forEach(function(scrollParent){scrollParent.removeEventListener("scroll",instance.update,passive)}),resize&&window2.removeEventListener("resize",instance.update,passive)}}var eventListeners_default={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:effect3,data:{}};var hash={left:"right",right:"left",bottom:"top",top:"bottom"};function getOppositePlacement(placement){return placement.replace(/left|right|bottom|top/g,function(matched){return hash[matched]})}var hash2={start:"end",end:"start"};function getOppositeVariationPlacement(placement){return placement.replace(/start|end/g,function(matched){return hash2[matched]})}function getWindowScroll(node){var win=getWindow(node),scrollLeft=win.pageXOffset,scrollTop=win.pageYOffset;return{scrollLeft,scrollTop}}function getWindowScrollBarX(element){return getBoundingClientRect(getDocumentElement(element)).left+getWindowScroll(element).scrollLeft}function getViewportRect(element,strategy){var win=getWindow(element),html=getDocumentElement(element),visualViewport=win.visualViewport,width=html.clientWidth,height=html.clientHeight,x=0,y=0;if(visualViewport){width=visualViewport.width,height=visualViewport.height;var layoutViewport=isLayoutViewport();(layoutViewport||!layoutViewport&&strategy==="fixed")&&(x=visualViewport.offsetLeft,y=visualViewport.offsetTop)}return{width,height,x:x+getWindowScrollBarX(element),y}}function getDocumentRect(element){var _element$ownerDocumen,html=getDocumentElement(element),winScroll=getWindowScroll(element),body=(_element$ownerDocumen=element.ownerDocument)==null?void 0:_element$ownerDocumen.body,width=max(html.scrollWidth,html.clientWidth,body?body.scrollWidth:0,body?body.clientWidth:0),height=max(html.scrollHeight,html.clientHeight,body?body.scrollHeight:0,body?body.clientHeight:0),x=-winScroll.scrollLeft+getWindowScrollBarX(element),y=-winScroll.scrollTop;return getComputedStyle(body||html).direction==="rtl"&&(x+=max(html.clientWidth,body?body.clientWidth:0)-width),{width,height,x,y}}function isScrollParent(element){var _getComputedStyle=getComputedStyle(element),overflow=_getComputedStyle.overflow,overflowX=_getComputedStyle.overflowX,overflowY=_getComputedStyle.overflowY;return/auto|scroll|overlay|hidden/.test(overflow+overflowY+overflowX)}function getScrollParent(node){return["html","body","#document"].indexOf(getNodeName(node))>=0?node.ownerDocument.body:isHTMLElement(node)&&isScrollParent(node)?node:getScrollParent(getParentNode(node))}function listScrollParents(element,list){var _element$ownerDocumen;list===void 0&&(list=[]);var scrollParent=getScrollParent(element),isBody=scrollParent===((_element$ownerDocumen=element.ownerDocument)==null?void 0:_element$ownerDocumen.body),win=getWindow(scrollParent),target=isBody?[win].concat(win.visualViewport||[],isScrollParent(scrollParent)?scrollParent:[]):scrollParent,updatedList=list.concat(target);return isBody?updatedList:updatedList.concat(listScrollParents(getParentNode(target)))}function rectToClientRect(rect){return Object.assign({},rect,{left:rect.x,top:rect.y,right:rect.x+rect.width,bottom:rect.y+rect.height})}function getInnerBoundingClientRect(element,strategy){var rect=getBoundingClientRect(element,!1,strategy==="fixed");return rect.top=rect.top+element.clientTop,rect.left=rect.left+element.clientLeft,rect.bottom=rect.top+element.clientHeight,rect.right=rect.left+element.clientWidth,rect.width=element.clientWidth,rect.height=element.clientHeight,rect.x=rect.left,rect.y=rect.top,rect}function getClientRectFromMixedType(element,clippingParent,strategy){return clippingParent===viewport?rectToClientRect(getViewportRect(element,strategy)):isElement(clippingParent)?getInnerBoundingClientRect(clippingParent,strategy):rectToClientRect(getDocumentRect(getDocumentElement(element)))}function getClippingParents(element){var clippingParents2=listScrollParents(getParentNode(element)),canEscapeClipping=["absolute","fixed"].indexOf(getComputedStyle(element).position)>=0,clipperElement=canEscapeClipping&&isHTMLElement(element)?getOffsetParent(element):element;return isElement(clipperElement)?clippingParents2.filter(function(clippingParent){return isElement(clippingParent)&&contains(clippingParent,clipperElement)&&getNodeName(clippingParent)!=="body"}):[]}function getClippingRect(element,boundary,rootBoundary,strategy){var mainClippingParents=boundary==="clippingParents"?getClippingParents(element):[].concat(boundary),clippingParents2=[].concat(mainClippingParents,[rootBoundary]),firstClippingParent=clippingParents2[0],clippingRect=clippingParents2.reduce(function(accRect,clippingParent){var rect=getClientRectFromMixedType(element,clippingParent,strategy);return accRect.top=max(rect.top,accRect.top),accRect.right=min(rect.right,accRect.right),accRect.bottom=min(rect.bottom,accRect.bottom),accRect.left=max(rect.left,accRect.left),accRect},getClientRectFromMixedType(element,firstClippingParent,strategy));return clippingRect.width=clippingRect.right-clippingRect.left,clippingRect.height=clippingRect.bottom-clippingRect.top,clippingRect.x=clippingRect.left,clippingRect.y=clippingRect.top,clippingRect}function computeOffsets(_ref){var reference2=_ref.reference,element=_ref.element,placement=_ref.placement,basePlacement=placement?getBasePlacement(placement):null,variation=placement?getVariation(placement):null,commonX=reference2.x+reference2.width/2-element.width/2,commonY=reference2.y+reference2.height/2-element.height/2,offsets;switch(basePlacement){case top:offsets={x:commonX,y:reference2.y-element.height};break;case bottom:offsets={x:commonX,y:reference2.y+reference2.height};break;case right:offsets={x:reference2.x+reference2.width,y:commonY};break;case left:offsets={x:reference2.x-element.width,y:commonY};break;default:offsets={x:reference2.x,y:reference2.y}}var mainAxis=basePlacement?getMainAxisFromPlacement(basePlacement):null;if(mainAxis!=null){var len=mainAxis==="y"?"height":"width";switch(variation){case start:offsets[mainAxis]=offsets[mainAxis]-(reference2[len]/2-element[len]/2);break;case end:offsets[mainAxis]=offsets[mainAxis]+(reference2[len]/2-element[len]/2);break;default:}}return offsets}function detectOverflow(state,options){options===void 0&&(options={});var _options=options,_options$placement=_options.placement,placement=_options$placement===void 0?state.placement:_options$placement,_options$strategy=_options.strategy,strategy=_options$strategy===void 0?state.strategy:_options$strategy,_options$boundary=_options.boundary,boundary=_options$boundary===void 0?clippingParents:_options$boundary,_options$rootBoundary=_options.rootBoundary,rootBoundary=_options$rootBoundary===void 0?viewport:_options$rootBoundary,_options$elementConte=_options.elementContext,elementContext=_options$elementConte===void 0?popper:_options$elementConte,_options$altBoundary=_options.altBoundary,altBoundary=_options$altBoundary===void 0?!1:_options$altBoundary,_options$padding=_options.padding,padding=_options$padding===void 0?0:_options$padding,paddingObject=mergePaddingObject(typeof padding!="number"?padding:expandToHashMap(padding,basePlacements)),altContext=elementContext===popper?reference:popper,popperRect=state.rects.popper,element=state.elements[altBoundary?altContext:elementContext],clippingClientRect=getClippingRect(isElement(element)?element:element.contextElement||getDocumentElement(state.elements.popper),boundary,rootBoundary,strategy),referenceClientRect=getBoundingClientRect(state.elements.reference),popperOffsets2=computeOffsets({reference:referenceClientRect,element:popperRect,strategy:"absolute",placement}),popperClientRect=rectToClientRect(Object.assign({},popperRect,popperOffsets2)),elementClientRect=elementContext===popper?popperClientRect:referenceClientRect,overflowOffsets={top:clippingClientRect.top-elementClientRect.top+paddingObject.top,bottom:elementClientRect.bottom-clippingClientRect.bottom+paddingObject.bottom,left:clippingClientRect.left-elementClientRect.left+paddingObject.left,right:elementClientRect.right-clippingClientRect.right+paddingObject.right},offsetData=state.modifiersData.offset;if(elementContext===popper&&offsetData){var offset2=offsetData[placement];Object.keys(overflowOffsets).forEach(function(key){var multiply=[right,bottom].indexOf(key)>=0?1:-1,axis=[top,bottom].indexOf(key)>=0?"y":"x";overflowOffsets[key]+=offset2[axis]*multiply})}return overflowOffsets}function computeAutoPlacement(state,options){options===void 0&&(options={});var _options=options,placement=_options.placement,boundary=_options.boundary,rootBoundary=_options.rootBoundary,padding=_options.padding,flipVariations=_options.flipVariations,_options$allowedAutoP=_options.allowedAutoPlacements,allowedAutoPlacements=_options$allowedAutoP===void 0?placements:_options$allowedAutoP,variation=getVariation(placement),placements2=variation?flipVariations?variationPlacements:variationPlacements.filter(function(placement2){return getVariation(placement2)===variation}):basePlacements,allowedPlacements=placements2.filter(function(placement2){return allowedAutoPlacements.indexOf(placement2)>=0});allowedPlacements.length===0&&(allowedPlacements=placements2);var overflows=allowedPlacements.reduce(function(acc,placement2){return acc[placement2]=detectOverflow(state,{placement:placement2,boundary,rootBoundary,padding})[getBasePlacement(placement2)],acc},{});return Object.keys(overflows).sort(function(a,b){return overflows[a]-overflows[b]})}function getExpandedFallbackPlacements(placement){if(getBasePlacement(placement)===auto)return[];var oppositePlacement=getOppositePlacement(placement);return[getOppositeVariationPlacement(placement),oppositePlacement,getOppositeVariationPlacement(oppositePlacement)]}function flip(_ref){var state=_ref.state,options=_ref.options,name=_ref.name;if(!state.modifiersData[name]._skip){for(var _options$mainAxis=options.mainAxis,checkMainAxis=_options$mainAxis===void 0?!0:_options$mainAxis,_options$altAxis=options.altAxis,checkAltAxis=_options$altAxis===void 0?!0:_options$altAxis,specifiedFallbackPlacements=options.fallbackPlacements,padding=options.padding,boundary=options.boundary,rootBoundary=options.rootBoundary,altBoundary=options.altBoundary,_options$flipVariatio=options.flipVariations,flipVariations=_options$flipVariatio===void 0?!0:_options$flipVariatio,allowedAutoPlacements=options.allowedAutoPlacements,preferredPlacement=state.options.placement,basePlacement=getBasePlacement(preferredPlacement),isBasePlacement=basePlacement===preferredPlacement,fallbackPlacements=specifiedFallbackPlacements||(isBasePlacement||!flipVariations?[getOppositePlacement(preferredPlacement)]:getExpandedFallbackPlacements(preferredPlacement)),placements2=[preferredPlacement].concat(fallbackPlacements).reduce(function(acc,placement2){return acc.concat(getBasePlacement(placement2)===auto?computeAutoPlacement(state,{placement:placement2,boundary,rootBoundary,padding,flipVariations,allowedAutoPlacements}):placement2)},[]),referenceRect=state.rects.reference,popperRect=state.rects.popper,checksMap=new Map,makeFallbackChecks=!0,firstFittingPlacement=placements2[0],i=0;i<placements2.length;i++){var placement=placements2[i],_basePlacement=getBasePlacement(placement),isStartVariation=getVariation(placement)===start,isVertical=[top,bottom].indexOf(_basePlacement)>=0,len=isVertical?"width":"height",overflow=detectOverflow(state,{placement,boundary,rootBoundary,altBoundary,padding}),mainVariationSide=isVertical?isStartVariation?right:left:isStartVariation?bottom:top;referenceRect[len]>popperRect[len]&&(mainVariationSide=getOppositePlacement(mainVariationSide));var altVariationSide=getOppositePlacement(mainVariationSide),checks=[];if(checkMainAxis&&checks.push(overflow[_basePlacement]<=0),checkAltAxis&&checks.push(overflow[mainVariationSide]<=0,overflow[altVariationSide]<=0),checks.every(function(check){return check})){firstFittingPlacement=placement,makeFallbackChecks=!1;break}checksMap.set(placement,checks)}if(makeFallbackChecks)for(var numberOfChecks=flipVariations?3:1,_loop=function(_i2){var fittingPlacement=placements2.find(function(placement2){var checks2=checksMap.get(placement2);if(checks2)return checks2.slice(0,_i2).every(function(check){return check})});if(fittingPlacement)return firstFittingPlacement=fittingPlacement,"break"},_i=numberOfChecks;_i>0;_i--){var _ret=_loop(_i);if(_ret==="break")break}state.placement!==firstFittingPlacement&&(state.modifiersData[name]._skip=!0,state.placement=firstFittingPlacement,state.reset=!0)}}var flip_default={name:"flip",enabled:!0,phase:"main",fn:flip,requiresIfExists:["offset"],data:{_skip:!1}};function getSideOffsets(overflow,rect,preventedOffsets){return preventedOffsets===void 0&&(preventedOffsets={x:0,y:0}),{top:overflow.top-rect.height-preventedOffsets.y,right:overflow.right-rect.width+preventedOffsets.x,bottom:overflow.bottom-rect.height+preventedOffsets.y,left:overflow.left-rect.width-preventedOffsets.x}}function isAnySideFullyClipped(overflow){return[top,right,bottom,left].some(function(side){return overflow[side]>=0})}function hide(_ref){var state=_ref.state,name=_ref.name,referenceRect=state.rects.reference,popperRect=state.rects.popper,preventedOffsets=state.modifiersData.preventOverflow,referenceOverflow=detectOverflow(state,{elementContext:"reference"}),popperAltOverflow=detectOverflow(state,{altBoundary:!0}),referenceClippingOffsets=getSideOffsets(referenceOverflow,referenceRect),popperEscapeOffsets=getSideOffsets(popperAltOverflow,popperRect,preventedOffsets),isReferenceHidden=isAnySideFullyClipped(referenceClippingOffsets),hasPopperEscaped=isAnySideFullyClipped(popperEscapeOffsets);state.modifiersData[name]={referenceClippingOffsets,popperEscapeOffsets,isReferenceHidden,hasPopperEscaped},state.attributes.popper=Object.assign({},state.attributes.popper,{"data-popper-reference-hidden":isReferenceHidden,"data-popper-escaped":hasPopperEscaped})}var hide_default={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:hide};function distanceAndSkiddingToXY(placement,rects,offset2){var basePlacement=getBasePlacement(placement),invertDistance=[left,top].indexOf(basePlacement)>=0?-1:1,_ref=typeof offset2=="function"?offset2(Object.assign({},rects,{placement})):offset2,skidding=_ref[0],distance=_ref[1];return skidding=skidding||0,distance=(distance||0)*invertDistance,[left,right].indexOf(basePlacement)>=0?{x:distance,y:skidding}:{x:skidding,y:distance}}function offset(_ref2){var state=_ref2.state,options=_ref2.options,name=_ref2.name,_options$offset=options.offset,offset2=_options$offset===void 0?[0,0]:_options$offset,data=placements.reduce(function(acc,placement){return acc[placement]=distanceAndSkiddingToXY(placement,state.rects,offset2),acc},{}),_data$state$placement=data[state.placement],x=_data$state$placement.x,y=_data$state$placement.y;state.modifiersData.popperOffsets!=null&&(state.modifiersData.popperOffsets.x+=x,state.modifiersData.popperOffsets.y+=y),state.modifiersData[name]=data}var offset_default={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:offset};function popperOffsets(_ref){var state=_ref.state,name=_ref.name;state.modifiersData[name]=computeOffsets({reference:state.rects.reference,element:state.rects.popper,strategy:"absolute",placement:state.placement})}var popperOffsets_default={name:"popperOffsets",enabled:!0,phase:"read",fn:popperOffsets,data:{}};function getAltAxis(axis){return axis==="x"?"y":"x"}function preventOverflow(_ref){var state=_ref.state,options=_ref.options,name=_ref.name,_options$mainAxis=options.mainAxis,checkMainAxis=_options$mainAxis===void 0?!0:_options$mainAxis,_options$altAxis=options.altAxis,checkAltAxis=_options$altAxis===void 0?!1:_options$altAxis,boundary=options.boundary,rootBoundary=options.rootBoundary,altBoundary=options.altBoundary,padding=options.padding,_options$tether=options.tether,tether=_options$tether===void 0?!0:_options$tether,_options$tetherOffset=options.tetherOffset,tetherOffset=_options$tetherOffset===void 0?0:_options$tetherOffset,overflow=detectOverflow(state,{boundary,rootBoundary,padding,altBoundary}),basePlacement=getBasePlacement(state.placement),variation=getVariation(state.placement),isBasePlacement=!variation,mainAxis=getMainAxisFromPlacement(basePlacement),altAxis=getAltAxis(mainAxis),popperOffsets2=state.modifiersData.popperOffsets,referenceRect=state.rects.reference,popperRect=state.rects.popper,tetherOffsetValue=typeof tetherOffset=="function"?tetherOffset(Object.assign({},state.rects,{placement:state.placement})):tetherOffset,normalizedTetherOffsetValue=typeof tetherOffsetValue=="number"?{mainAxis:tetherOffsetValue,altAxis:tetherOffsetValue}:Object.assign({mainAxis:0,altAxis:0},tetherOffsetValue),offsetModifierState=state.modifiersData.offset?state.modifiersData.offset[state.placement]:null,data={x:0,y:0};if(popperOffsets2){if(checkMainAxis){var _offsetModifierState$,mainSide=mainAxis==="y"?top:left,altSide=mainAxis==="y"?bottom:right,len=mainAxis==="y"?"height":"width",offset2=popperOffsets2[mainAxis],min2=offset2+overflow[mainSide],max2=offset2-overflow[altSide],additive=tether?-popperRect[len]/2:0,minLen=variation===start?referenceRect[len]:popperRect[len],maxLen=variation===start?-popperRect[len]:-referenceRect[len],arrowElement=state.elements.arrow,arrowRect=tether&&arrowElement?getLayoutRect(arrowElement):{width:0,height:0},arrowPaddingObject=state.modifiersData["arrow#persistent"]?state.modifiersData["arrow#persistent"].padding:getFreshSideObject(),arrowPaddingMin=arrowPaddingObject[mainSide],arrowPaddingMax=arrowPaddingObject[altSide],arrowLen=within(0,referenceRect[len],arrowRect[len]),minOffset=isBasePlacement?referenceRect[len]/2-additive-arrowLen-arrowPaddingMin-normalizedTetherOffsetValue.mainAxis:minLen-arrowLen-arrowPaddingMin-normalizedTetherOffsetValue.mainAxis,maxOffset=isBasePlacement?-referenceRect[len]/2+additive+arrowLen+arrowPaddingMax+normalizedTetherOffsetValue.mainAxis:maxLen+arrowLen+arrowPaddingMax+normalizedTetherOffsetValue.mainAxis,arrowOffsetParent=state.elements.arrow&&getOffsetParent(state.elements.arrow),clientOffset=arrowOffsetParent?mainAxis==="y"?arrowOffsetParent.clientTop||0:arrowOffsetParent.clientLeft||0:0,offsetModifierValue=(_offsetModifierState$=offsetModifierState?.[mainAxis])!=null?_offsetModifierState$:0,tetherMin=offset2+minOffset-offsetModifierValue-clientOffset,tetherMax=offset2+maxOffset-offsetModifierValue,preventedOffset=within(tether?min(min2,tetherMin):min2,offset2,tether?max(max2,tetherMax):max2);popperOffsets2[mainAxis]=preventedOffset,data[mainAxis]=preventedOffset-offset2}if(checkAltAxis){var _offsetModifierState$2,_mainSide=mainAxis==="x"?top:left,_altSide=mainAxis==="x"?bottom:right,_offset=popperOffsets2[altAxis],_len=altAxis==="y"?"height":"width",_min=_offset+overflow[_mainSide],_max=_offset-overflow[_altSide],isOriginSide=[top,left].indexOf(basePlacement)!==-1,_offsetModifierValue=(_offsetModifierState$2=offsetModifierState?.[altAxis])!=null?_offsetModifierState$2:0,_tetherMin=isOriginSide?_min:_offset-referenceRect[_len]-popperRect[_len]-_offsetModifierValue+normalizedTetherOffsetValue.altAxis,_tetherMax=isOriginSide?_offset+referenceRect[_len]+popperRect[_len]-_offsetModifierValue-normalizedTetherOffsetValue.altAxis:_max,_preventedOffset=tether&&isOriginSide?withinMaxClamp(_tetherMin,_offset,_tetherMax):within(tether?_tetherMin:_min,_offset,tether?_tetherMax:_max);popperOffsets2[altAxis]=_preventedOffset,data[altAxis]=_preventedOffset-_offset}state.modifiersData[name]=data}}var preventOverflow_default={name:"preventOverflow",enabled:!0,phase:"main",fn:preventOverflow,requiresIfExists:["offset"]};function getHTMLElementScroll(element){return{scrollLeft:element.scrollLeft,scrollTop:element.scrollTop}}function getNodeScroll(node){return node===getWindow(node)||!isHTMLElement(node)?getWindowScroll(node):getHTMLElementScroll(node)}function isElementScaled(element){var rect=element.getBoundingClientRect(),scaleX=round(rect.width)/element.offsetWidth||1,scaleY=round(rect.height)/element.offsetHeight||1;return scaleX!==1||scaleY!==1}function getCompositeRect(elementOrVirtualElement,offsetParent,isFixed){isFixed===void 0&&(isFixed=!1);var isOffsetParentAnElement=isHTMLElement(offsetParent),offsetParentIsScaled=isHTMLElement(offsetParent)&&isElementScaled(offsetParent),documentElement=getDocumentElement(offsetParent),rect=getBoundingClientRect(elementOrVirtualElement,offsetParentIsScaled,isFixed),scroll={scrollLeft:0,scrollTop:0},offsets={x:0,y:0};return(isOffsetParentAnElement||!isOffsetParentAnElement&&!isFixed)&&((getNodeName(offsetParent)!=="body"||isScrollParent(documentElement))&&(scroll=getNodeScroll(offsetParent)),isHTMLElement(offsetParent)?(offsets=getBoundingClientRect(offsetParent,!0),offsets.x+=offsetParent.clientLeft,offsets.y+=offsetParent.clientTop):documentElement&&(offsets.x=getWindowScrollBarX(documentElement))),{x:rect.left+scroll.scrollLeft-offsets.x,y:rect.top+scroll.scrollTop-offsets.y,width:rect.width,height:rect.height}}function order(modifiers){var map=new Map,visited=new Set,result=[];modifiers.forEach(function(modifier){map.set(modifier.name,modifier)});function sort(modifier){visited.add(modifier.name);var requires=[].concat(modifier.requires||[],modifier.requiresIfExists||[]);requires.forEach(function(dep){if(!visited.has(dep)){var depModifier=map.get(dep);depModifier&&sort(depModifier)}}),result.push(modifier)}return modifiers.forEach(function(modifier){visited.has(modifier.name)||sort(modifier)}),result}function orderModifiers(modifiers){var orderedModifiers=order(modifiers);return modifierPhases.reduce(function(acc,phase){return acc.concat(orderedModifiers.filter(function(modifier){return modifier.phase===phase}))},[])}function debounce(fn2){var pending;return function(){return pending||(pending=new Promise(function(resolve){Promise.resolve().then(function(){pending=void 0,resolve(fn2())})})),pending}}function mergeByName(modifiers){var merged=modifiers.reduce(function(merged2,current){var existing=merged2[current.name];return merged2[current.name]=existing?Object.assign({},existing,current,{options:Object.assign({},existing.options,current.options),data:Object.assign({},existing.data,current.data)}):current,merged2},{});return Object.keys(merged).map(function(key){return merged[key]})}var DEFAULT_OPTIONS={placement:"bottom",modifiers:[],strategy:"absolute"};function areValidElements(){for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++)args[_key]=arguments[_key];return!args.some(function(element){return!(element&&typeof element.getBoundingClientRect=="function")})}function popperGenerator(generatorOptions){generatorOptions===void 0&&(generatorOptions={});var _generatorOptions=generatorOptions,_generatorOptions$def=_generatorOptions.defaultModifiers,defaultModifiers2=_generatorOptions$def===void 0?[]:_generatorOptions$def,_generatorOptions$def2=_generatorOptions.defaultOptions,defaultOptions=_generatorOptions$def2===void 0?DEFAULT_OPTIONS:_generatorOptions$def2;return function(reference2,popper2,options){options===void 0&&(options=defaultOptions);var state={placement:"bottom",orderedModifiers:[],options:Object.assign({},DEFAULT_OPTIONS,defaultOptions),modifiersData:{},elements:{reference:reference2,popper:popper2},attributes:{},styles:{}},effectCleanupFns=[],isDestroyed=!1,instance={state,setOptions:function(setOptionsAction){var options2=typeof setOptionsAction=="function"?setOptionsAction(state.options):setOptionsAction;cleanupModifierEffects(),state.options=Object.assign({},defaultOptions,state.options,options2),state.scrollParents={reference:isElement(reference2)?listScrollParents(reference2):reference2.contextElement?listScrollParents(reference2.contextElement):[],popper:listScrollParents(popper2)};var orderedModifiers=orderModifiers(mergeByName([].concat(defaultModifiers2,state.options.modifiers)));return state.orderedModifiers=orderedModifiers.filter(function(m){return m.enabled}),runModifierEffects(),instance.update()},forceUpdate:function(){if(!isDestroyed){var _state$elements=state.elements,reference3=_state$elements.reference,popper3=_state$elements.popper;if(areValidElements(reference3,popper3)){state.rects={reference:getCompositeRect(reference3,getOffsetParent(popper3),state.options.strategy==="fixed"),popper:getLayoutRect(popper3)},state.reset=!1,state.placement=state.options.placement,state.orderedModifiers.forEach(function(modifier){return state.modifiersData[modifier.name]=Object.assign({},modifier.data)});for(var index=0;index<state.orderedModifiers.length;index++){if(state.reset===!0){state.reset=!1,index=-1;continue}var _state$orderedModifie=state.orderedModifiers[index],fn2=_state$orderedModifie.fn,_state$orderedModifie2=_state$orderedModifie.options,_options=_state$orderedModifie2===void 0?{}:_state$orderedModifie2,name=_state$orderedModifie.name;typeof fn2=="function"&&(state=fn2({state,options:_options,name,instance})||state)}}}},update:debounce(function(){return new Promise(function(resolve){instance.forceUpdate(),resolve(state)})}),destroy:function(){cleanupModifierEffects(),isDestroyed=!0}};if(!areValidElements(reference2,popper2))return instance;instance.setOptions(options).then(function(state2){!isDestroyed&&options.onFirstUpdate&&options.onFirstUpdate(state2)});function runModifierEffects(){state.orderedModifiers.forEach(function(_ref){var name=_ref.name,_ref$options=_ref.options,options2=_ref$options===void 0?{}:_ref$options,effect4=_ref.effect;if(typeof effect4=="function"){var cleanupFn=effect4({state,name,instance,options:options2}),noopFn=function(){};effectCleanupFns.push(cleanupFn||noopFn)}})}function cleanupModifierEffects(){effectCleanupFns.forEach(function(fn2){return fn2()}),effectCleanupFns=[]}return instance}}var defaultModifiers=[eventListeners_default,popperOffsets_default,computeStyles_default,applyStyles_default,offset_default,flip_default,preventOverflow_default,arrow_default,hide_default],createPopper=popperGenerator({defaultModifiers});var import_obsidian5=require("obsidian");var import_fuzzysort=__toESM(require_fuzzysort()),import_obsidian=require("obsidian");function fileNameNoExtension(path){return path&&path.split("/")?.pop()?.slice(0,-3)}function isAllowedExtension(file,extension){return extension.includes(file.extension)}function sharedGetSuggestions(files,query,settings,app,typedChar,originalQuery){let options=[],newNoteDirectory=(0,import_obsidian.normalizePath)(`${settings.addNewNoteDirectory.trim()}/`),newNoteDirectories=new Set;settings.addNewNoteDirectory.trim().length>0&&newNoteDirectories.add(newNoteDirectory);for(let file of files){if(settings.limitToDirectories.length>0){let isAllowed=!1;for(let folder of settings.limitToDirectories)if(typedChar===folder.triggerSymbol&&(newNoteDirectories.add(folder.path),file.parent?.path.startsWith(folder.path)&&isAllowedExtension(file,folder.extensions??["md"]))){isAllowed=!0;break}if(!isAllowed)continue}let meta=app.metadataCache.getFileCache(file),fileName=settings.removeAccents?removeAccents(file.basename):file.basename;if(meta?.frontmatter?.alias)options.push({fileName,filePath:file.path,alias:settings.removeAccents?removeAccents(meta.frontmatter.alias):meta.frontmatter.alias,originalAlias:settings.removeAccents?meta.frontmatter.alias:void 0});else if(meta?.frontmatter?.aliases){let aliases=meta.frontmatter.aliases;typeof meta.frontmatter.aliases=="string"&&(aliases=meta.frontmatter.aliases.split(",").map(s=>s.trim()));for(let alias of aliases)options.push({fileName,filePath:file.path,alias:settings.removeAccents?removeAccents(alias):alias,originalAlias:settings.removeAccents?alias:void 0})}options.push({fileName,filePath:file.path})}let results=[];if(query?results=import_fuzzysort.default.go(query,options,{keys:["alias","fileName"]}):results=options.map(option=>({obj:option})).reverse(),settings.showAddNewNote&&query&&!results.some(result=>result?.obj?.fileName.toLocaleLowerCase()===query?.toLocaleLowerCase())){results=results.filter(result=>!result.obj?.isCreateNewOption);for(let folder of newNoteDirectories)results.push({obj:{isCreateNewOption:!0,query,fileName:originalQuery,filePath:(0,import_obsidian.normalizePath)(`${folder}/${originalQuery.trim()}.md`)}})}return results}function sharedGetMonoFileSuggestion(query,settings,app,typedChar,originalQuery){if(settings.limitToFile.length===0)return[];let files=settings.limitToFile.filter(x=>x.triggerSymbol==typedChar).map(path=>{let file=app.vault.getAbstractFileByPath(path.path);return file&&file instanceof import_obsidian.TFile?file:null}).filter(file=>file!==null);if(files.length===0)return[];let options=[];for(let file of files){let meta=app.metadataCache.getFileCache(file);if(!meta||!meta.headings){options.push({isCreateNewOption:!0,query,fileName:originalQuery,filePath:file.path,alias:query,originalAlias:originalQuery});continue}let option=(settings.headerLevelForContact===0?meta.headings:meta.headings.filter(heading2=>heading2.level===settings.headerLevelForContact)).map(heading2=>({fileName:heading2.heading,filePath:file.path,originalAlias:heading2.heading,alias:settings.removeAccents?removeAccents(heading2.heading):heading2.heading}));options.push(...option)}if(options.length===0)return[];let results=[];if(query?results=import_fuzzysort.default.go(query,options,{keys:["fileName"]}):results=options.map(option=>({obj:option})),settings.appendAsHeader&&query&&!results.some(result=>result?.obj?.fileName.toLocaleLowerCase()===query?.toLocaleLowerCase())){results=results.filter(result=>!result.obj?.isCreateNewOption);for(let file of files)results.push({obj:{isCreateNewOption:!0,query,fileName:originalQuery,filePath:file.path,alias:query,originalAlias:originalQuery}})}return results}var import_obsidian2=require("obsidian");function highlightSearch(element,result,file,uniformize){if(result===null)return;let matchesIndex=0,opened=!1,target=result.target;if(file&&uniformize){let alias=file.alias,fileName=file.fileName;alias===target?target=file.originalAlias??alias:removeAccents(fileName)===target&&(target=file.filePath.split("/").pop()?.split(".")[0]??fileName)}let targetLen=target.length,indexes=result._indexes;indexes=indexes.slice(0,indexes.len).sort((a,b)=>a-b);let strongElement;for(let i=0;i<targetLen;++i){let char=target[i];if(indexes[matchesIndex]===i){if(++matchesIndex,opened||(opened=!0,strongElement=document.createElement("strong")),matchesIndex===indexes.length){strongElement.appendChild(document.createTextNode(char)),element.appendChild(strongElement),element.appendChild(document.createTextNode(target.substring(i+1)));break}}else opened&&(opened=!1,element.appendChild(strongElement),strongElement=void 0);strongElement?strongElement.appendChild(document.createTextNode(char)):element.appendChild(document.createTextNode(char))}}function sharedRenderSuggestion(value,el,limitToOneFile=0,uniformize){el.addClass("at-symbol-linking-suggestion");let context=el.doc.createElement("div");context.addClass("suggestion-context"),context.id="at-symbol-suggestion-context";let title=el.doc.createElement("div");if(title.addClass("suggestion-title"),value[0]?.target?highlightSearch(title,value[0],value.obj,uniformize):value.obj?.originalAlias?title.setText(value.obj?.originalAlias):value.obj?.alias?title.setText(value.obj?.alias):value[1]?highlightSearch(title,value[1],value.obj,uniformize):value.obj?.fileName?title.setText(value.obj?.fileName.split(".")[0]):title.setText(""),context.appendChild(title),limitToOneFile===0||limitToOneFile>1){let path=el.doc.createElement("div");path.addClass("suggestion-path");let pathText="";value.obj?.isCreateNewOption&&(pathText+=`Create a new ${limitToOneFile>1?"header":"note"} in `),pathText+=value.obj?.filePath,path.setText(pathText),context.appendChild(path)}else if(limitToOneFile===1&&value.obj?.isCreateNewOption){let path=el.doc.createElement("div");path.addClass("suggestion-path"),path.setText("Create a new header"),context.appendChild(path)}let aux=el.doc.createElement("div");if(aux.addClass("suggestion-aux"),value?.obj?.alias){let alias=el.doc.createElement("span");alias.addClass("suggestion-flair"),alias.ariaLabel="Alias",(0,import_obsidian2.setIcon)(alias,"forward"),aux.appendChild(alias)}el.appendChild(context),el.appendChild(aux)}var import_obsidian4=require("obsidian");var import_obsidian3=require("obsidian");async function replaceNewFileVars(app,templateContent,title){if(!templateContent)return templateContent;let coreTemplatesConfigPath=`${app.vault.configDir}/templates.json`,coreTemplatesConfig;try{coreTemplatesConfig=await app.vault.adapter.read(coreTemplatesConfigPath),coreTemplatesConfig=JSON.parse(coreTemplatesConfig)}catch(error){return console.error(`@ Symbol Linking: Unable to read core plugin templates config at path: ${coreTemplatesConfigPath}`),console.log(error),templateContent}let dateFormat="YYYY-MM-DD",timeFormat="HH:mm";return coreTemplatesConfig?.dateFormat&&(dateFormat=coreTemplatesConfig.dateFormat),coreTemplatesConfig?.timeFormat&&(timeFormat=coreTemplatesConfig.timeFormat),templateContent=templateContent.replace(/{{date}}/g,(0,import_obsidian3.moment)().format(dateFormat)),templateContent=templateContent.replace(/{{time}}/g,(0,import_obsidian3.moment)().format(timeFormat)),templateContent=templateContent.replace(/{{title}}/g,title),templateContent}async function sharedSelectSuggestion(app,settings,typedChar,originalQuery,value){let linkFile;if(value?.obj?.isCreateNewOption)if(settings.limitToFile.length>0){let file=app.vault.getAbstractFileByPath(value.obj?.filePath);file||new import_obsidian4.Notice(`Unable to get the file at path: ${value.obj.filePath}. Please open an issue on GitHub, as this should not happen.`,0);let newContent=`${"#".repeat(settings.headerLevelForContact<=1?1:settings.headerLevelForContact)} ${originalQuery}
`;await app.vault.process(file,content=>content.endsWith(`
`)?`${content}${newContent}`:`${content}
${newContent}`)}else{let newNoteContents="";if(settings.addNewNoteTemplateFile){let fileTemplate=app.vault.getAbstractFileByPath(`${settings.addNewNoteTemplateFile}.md`);if(!fileTemplate||!(fileTemplate instanceof import_obsidian4.TFile))throw new import_obsidian4.Notice(`Unable to get the file at path: ${settings.addNewNoteTemplateFile}. Verify the path in the settings.`),new Error("Template file not found");newNoteContents=await app.vault.read(fileTemplate)||"",newNoteContents=await replaceNewFileVars(app,newNoteContents,fileNameNoExtension(value.obj?.filePath))}try{linkFile=await app.vault.create(value.obj?.filePath,newNoteContents),value.obj.alias=originalQuery}catch(error){throw new import_obsidian4.Notice(`Unable to create new note at path: ${value.obj?.filePath}. Please open an issue on GitHub, https://github.com/Mara-Li/symbol-linking/issues`,0),error}}let currentFile=app.workspace.getActiveFile();if(linkFile||(linkFile=app.vault.getAbstractFileByPath(value.obj?.filePath)),!linkFile||!(linkFile instanceof import_obsidian4.TFile))throw new import_obsidian4.Notice(`Unable to get the file at path: ${value.obj?.filePath}. Please open an issue on GitHub, as this should not happen.`,0),new Error("File not found");let alias=value.obj?.originalAlias||"",aliasFallBack=settings.limitToFile.length>0?originalQuery??value.obj?.fileName:value.obj?.fileName;settings.includeSymbol&&(alias=`${typedChar}${alias||aliasFallBack}`);let linkText=settings.limitToFile.length>0?app.fileManager.generateMarkdownLink(linkFile,currentFile?.path||"",`#${originalQuery||value.obj?.fileName}`,alias):app.fileManager.generateMarkdownLink(linkFile,currentFile?.path||"",void 0,alias);return linkText.includes(`
`)?linkText.replace(/\n/g,""):linkText}var Suggest=class{owner;values;suggestions;selectedItem;containerEl;constructor(owner,containerEl,scope){this.owner=owner,this.containerEl=containerEl,containerEl.on("click",".suggestion-item",this.onSuggestionClick.bind(this)),containerEl.on("mousemove",".suggestion-item",this.onSuggestionMouseover.bind(this)),scope.register([],"ArrowUp",event=>{if(!event.isComposing)return this.setSelectedItem(this.selectedItem-1,!0),!1}),scope.register([],"ArrowDown",event=>{if(!event.isComposing)return this.setSelectedItem(this.selectedItem+1,!0),!1}),scope.register([],"Enter",event=>{if(!event.isComposing)return this.useSelectedItem(event),!1})}onSuggestionClick(event,el){event.preventDefault();let item=this.suggestions.indexOf(el);this.setSelectedItem(item,!1),this.useSelectedItem(event)}onSuggestionMouseover(_event,el){let item=this.suggestions.indexOf(el);this.setSelectedItem(item,!1)}setSuggestions(values){this.containerEl.empty();let suggestionEls=[];values.forEach(value=>{let suggestionEl=this.containerEl.createDiv("suggestion-item");this.owner.renderSuggestion(value,suggestionEl),suggestionEls.push(suggestionEl)}),this.values=values,this.suggestions=suggestionEls,this.setSelectedItem(0,!1)}useSelectedItem(event){let currentValue=this.values[this.selectedItem];currentValue&&this.owner.selectSuggestion(currentValue,event)}setSelectedItem(selectedIndex,scrollIntoView){let normalizedIndex=wrapAround(selectedIndex,this.suggestions.length),prevSelectedSuggestion=this.suggestions[this.selectedItem],selectedSuggestion=this.suggestions[normalizedIndex];prevSelectedSuggestion?.removeClass("is-selected"),selectedSuggestion?.addClass("is-selected"),this.selectedItem=normalizedIndex,scrollIntoView&&selectedSuggestion.scrollIntoView(!1)}},LinkSuggest=class{app;inputEl;settings;triggerSymbol;popper;scope;suggestEl;suggest;onSelect;originalQuery;constructor(app,inputEl,settings,triggerSymbol,onSelect){this.app=app,this.inputEl=inputEl,this.settings=settings,this.scope=new import_obsidian5.Scope,this.onSelect=onSelect,this.triggerSymbol=triggerSymbol,this.suggestEl=createDiv("suggestion-container"),import_obsidian5.Platform.isMobile?this.suggestEl.style.padding="0":this.suggestEl.addClass("extension-container-at-symbol-linking"),this.suggestEl.style.zIndex="1000";let suggestion=this.suggestEl.createDiv("suggestion");suggestion.id="at-symbol-suggestion-container",this.suggest=new Suggest(this,suggestion,this.scope),this.scope.register([],"Escape",this.close.bind(this)),this.inputEl.addEventListener("focus",this.onInputChanged.bind(this)),this.inputEl.addEventListener("blur",this.close.bind(this)),this.suggestEl.on("mousedown",".suggestion-container",event=>{event.preventDefault()})}onInputChanged(inputStr){let suggestions=this.getSuggestions(inputStr);suggestions.length>0&&(this.suggest.setSuggestions(suggestions),this.open(this.app.dom.appContainerEl,this.inputEl))}open(container,inputEl){this.app.keymap.pushScope(this.scope),container.appendChild(this.suggestEl),this.popper=createPopper(inputEl,this.suggestEl,{placement:import_obsidian5.Platform.isMobile?"top":"bottom-start",modifiers:[{name:"flip",options:{flipVariations:!1,fallbackPlacements:[import_obsidian5.Platform.isMobile?"top":"right"]}},{name:"sameWidth",enabled:!0,fn:({state,instance})=>{let targetWidth=import_obsidian5.Platform.isMobile?"100vw":`${state.rects.reference.width}px`;state.styles.popper.width!==targetWidth&&(state.styles.popper.width=targetWidth,instance.update())},phase:"beforeWrite",requires:["computeStyles"]}]})}close(){this.app.keymap.popScope(this.scope),this.suggest.setSuggestions([]),this.popper?.destroy(),this.suggestEl.detach(),this.inputEl.removeEventListener("focus",this.onInputChanged.bind(this)),this.inputEl.removeEventListener("blur",this.close.bind(this))}getSuggestions(query){if(this.originalQuery=query,query=removeAccents(query),this.settings.limitToFile.length>0)return sharedGetMonoFileSuggestion(query,this.settings,this.app,this.triggerSymbol,this.originalQuery);{let files=this.app.vault.getFiles();return sharedGetSuggestions(files,query,this.settings,this.app,this.triggerSymbol,this.originalQuery)}}renderSuggestion(value,el){sharedRenderSuggestion(value,el,this.settings.limitToFile.length,this.settings.removeAccents)}async selectSuggestion(value){let linkText=await sharedSelectSuggestion(this.app,this.settings,this.triggerSymbol,this.originalQuery,value);this.onSelect(linkText)}},wrapAround=(value,size)=>(value%size+size)%size;var maxParentDepth=5;function atSymbolTriggerExtension(app,settings){return import_view.ViewPlugin.fromClass(class{view;firstOpenedCursor=null;openQuery="";isOpen=!1;suggestionEl=null;suggestionPopup=null;constructor(view){this.view=view,this.handleKeyEvent=this.handleKeyEvent.bind(this),this.handleClickEvent=this.handleClickEvent.bind(this),window.addEventListener("click",this.handleClickEvent),this.view.dom.addEventListener("keydown",this.handleKeyEvent)}destroy(){this.view.dom.removeEventListener("keydown",this.handleKeyEvent),window.removeEventListener("click",this.handleClickEvent)}closeSuggestion(){return this.isOpen=!1,this.firstOpenedCursor=null,this.openQuery="",this.suggestionPopup?.close(),this.suggestionEl?.remove(),this.suggestionPopup=null,this.suggestionEl=null,!0}openSuggestion(){return this.isOpen=!0,this.firstOpenedCursor=this.getCursor(),!0}handleKeyEvent(event){let isInTitle=!1;if((event.target?.classList||[]).forEach(className=>{isInTitle=isInTitle||className==="inline-title"}),isInTitle)return!1;let typedChar=event.key;if(this.isOpen&&(typedChar===`
`||typedChar==="	"))return this.closeSuggestion();let isInValidContext=!0,cursor=this.view?.viewState.state?.selection?.main;if((0,import_language.syntaxTree)(this.view?.viewState?.state).iterate({from:cursor.from,to:cursor.to,enter(node){(node.type.name==="hmd-frontmatter"||node.type.name==="inline-code"||node.type.name?.includes("codeblock"))&&(isInValidContext=!1)}}),!isInValidContext)return!1;let triggerFileSymbol=settings.limitToFile.map(file=>file.triggerSymbol),triggerFolderSymbol=settings.limitToDirectories.map(dir=>dir.triggerSymbol),justOpened=!1;if(!this.isOpen&&typedChar===settings.triggerSymbol||triggerFileSymbol.includes(typedChar)||triggerFolderSymbol.includes(typedChar))justOpened=!0,this.openSuggestion();else if(!this.isOpen)return!1;let key=event.key.toLocaleLowerCase(),excludeFrom=["backspace","shift","arrow","tab","capslock","alt"].find(excluded=>key.includes(excluded));if(typedChar==="Backspace"){if(this.openQuery.length===0)return this.closeSuggestion();this.openQuery=this.openQuery.slice(0,-1)}else if(typedChar==="Escape")this.closeSuggestion();else{if(!isValidFileNameCharacter(typedChar,settings)||event.altKey||event.metaKey||event.ctrlKey||excludeFrom)return!1;justOpened||(this.openQuery+=typedChar)}if(this.openQuery.split(" ").length-1>settings.leavePopupOpenForXSpaces||this.openQuery.startsWith(" "))return this.closeSuggestion();if(!this.suggestionEl&&this.firstOpenedCursor&&this.view){let container=app.dom.appContainerEl;if(this.suggestionEl=createDiv(),this.suggestionEl.style.position="absolute",this.suggestionEl.style.zIndex="1000",this.suggestionEl.id="at-symbol-suggestion-root",this.suggestionEl.style.width="0px",this.suggestionEl.style.height="0px",import_obsidian6.Platform.isDesktop){let{left:leftOffset,top:topOffset}=this.view.coordsAtPos(this.firstOpenedCursor?.ch);this.suggestionEl.style.left=`${leftOffset}px`;let lineElementHeight=24;this.suggestionEl.style.top=`${topOffset+lineElementHeight}px`}else this.suggestionEl.style.bottom="var(--mobile-toolbar-height)",this.suggestionEl.style.left="0px";container.appendChild(this.suggestionEl),this.suggestionPopup=new LinkSuggest(app,this.suggestionEl,settings,typedChar,this.onSelect.bind(this)),this.suggestionPopup.onInputChanged(this.openQuery)}return this.suggestionPopup&&this.suggestionPopup.onInputChanged(this.openQuery),!0}handleClickEvent(event){if(this.isOpen){let currentDepth=0,parent=event.target,shouldClose=!0;for(;event.target&&currentDepth<maxParentDepth;){if(currentDepth++,parent?.id==="at-symbol-suggestion-context"){shouldClose=!1;break}parent=parent?.parentNode}shouldClose&&this.closeSuggestion()}}onSelect(linkText){let cursor=this.getCursor();try{this.view.dispatch(this.view.state.update({changes:{from:this.firstOpenedCursor?.ch,to:cursor.ch,insert:linkText}}))}catch(error){console.log("Symbol Linking: Error creating first link",error),this.view.dispatch(this.view.state.update({changes:{from:this.firstOpenedCursor?.ch,insert:linkText}}))}this.closeSuggestion()}getCursor(){let ch=this.view.state.selection.ranges[0].head,line=this.view.state.doc.lineAt(ch).number;return{ch,line}}})}var import_language2=require("@codemirror/language"),import_obsidian7=require("obsidian");var SuggestionPopup=class extends import_obsidian7.EditorSuggest{settings;typedChar;originalQuery;firstOpenedCursor=null;focused=!1;app;name="Symbol linking suggest";constructor(app,settings){super(app),this.app=app,this.settings=settings,this.typedChar=settings.triggerSymbol;let self=this;self.scope.keys=[]}open(){super.open(),this.focused=!0}close(){super.close(),this.focused=!1}getSuggestions(context){if(this.settings.limitToFile.length>0)return sharedGetMonoFileSuggestion(context.query,this.settings,this.app,this.typedChar,this.originalQuery);let files=this.app.vault.getFiles();return sharedGetSuggestions(files,context.query,this.settings,this.app,this.typedChar,this.originalQuery)}onTrigger(cursor,editor){let query="",typedChar=editor.getRange({...cursor,ch:cursor.ch-1},{...cursor,ch:cursor.ch});if(this.firstOpenedCursor&&(typedChar===`
`||typedChar==="	"))return this.closeSuggestion();let isInCodeBlock=!1;if(editor?.cm){let cm=editor.cm,cursor2=cm.state?.selection?.main;(0,import_language2.syntaxTree)(cm.state).iterate({from:cursor2.from,to:cursor2.to,enter(node){(node.type.name==="inline-code"||node.type.name?.includes("codeblock"))&&(isInCodeBlock=!0)}})}if(isInCodeBlock&&!this.firstOpenedCursor)return null;let triggerFileSymbol=this.settings.limitToFile.map(file=>file.triggerSymbol),triggerFolderSymbol=this.settings.limitToDirectories.map(dir=>dir.triggerSymbol);if(typedChar===this.settings.triggerSymbol||triggerFileSymbol.includes(typedChar)||triggerFolderSymbol.includes(typedChar))return this.typedChar=typedChar,this.firstOpenedCursor=cursor,this.originalQuery=query,{start:{...cursor,ch:cursor.ch-1},end:cursor,query:this.settings.removeAccents?removeAccents(query):query};if(this.firstOpenedCursor)query=editor.getRange(this.firstOpenedCursor,{...cursor,ch:cursor.ch});else return null;return query.split(" ").length-1>this.settings.leavePopupOpenForXSpaces||query.startsWith(" ")?this.closeSuggestion():!query||!isValidFileNameCharacter(typedChar,this.settings)?this.closeSuggestion():(this.originalQuery=query,{start:{...cursor,ch:cursor.ch-1},end:cursor,query:this.settings.removeAccents?removeAccents(query):query})}renderSuggestion(value,el){sharedRenderSuggestion(value,el,this.settings.limitToFile.length,this.settings.removeAccents)}async selectSuggestion(value){let line=this.context?.editor.getRange({line:this.context.start.line,ch:0},this.context.end)||"",linkText=await sharedSelectSuggestion(this.app,this.settings,this.typedChar,this.originalQuery,value);this.context?.editor.replaceRange(linkText,{line:this.context.start.line,ch:line.lastIndexOf(this.typedChar)},this.context.end),this.closeSuggestion()}selectNextItem(dir){this.focused||(this.focused=!0,dir=dir===-1?dir:0);let self=this;self.suggestions.setSelectedItem(self.suggestions.selectedItem+dir,new KeyboardEvent("keydown"))}closeSuggestion(){return this.firstOpenedCursor=null,this.close(),null}getSelectedItem(){let self=this;return self.suggestions.values[self.suggestions.selectedItem]}applySelectedItem(){this.suggestions.useSelectedItem()}isVisible(){return this.isOpen}isFocused(){return this.focused}};function applyHotKeyHack(_this,app){app.scope.keys=[];let isHotkeyMatch=(hotkey,context,isBypassCommand)=>{let modifiers=hotkey.modifiers,key=hotkey.key;return modifiers!==null&&(isBypassCommand?!context?.modifiers?.contains(modifiers):modifiers!==context.modifiers)?!1:!key||key===context.vkey||!(!context.key||key.toLocaleLowerCase()!==context.key.toLocaleLowerCase())};_this.app.scope.register(null,null,(e,t)=>{let hotkeyManager=app.hotkeyManager;hotkeyManager.bake();for(let bakedHotkeys=hotkeyManager.bakedHotkeys,bakedIds=hotkeyManager.bakedIds,r=0;r<bakedHotkeys.length;r++){let hotkey=bakedHotkeys[r],id=bakedIds[r],command=app.commands.findCommand(id),isBypassCommand=command?.isBypassCommand?.();if(isHotkeyMatch(hotkey,t,isBypassCommand)){if(!command||e.repeat&&!command.repeatable)continue;if(command.isVisible&&!command.isVisible())continue;if(isBypassCommand){_this._suggestionPopup.close();let validMods=t.modifiers.replace(new RegExp(`${hotkey.modifiers},*`),"").split(","),event=new KeyboardEvent("keydown",{key:hotkeyManager.defaultKeys[id][0].key,ctrlKey:validMods?.contains("Ctrl"),shiftKey:validMods?.contains("Shift"),altKey:validMods?.contains("Alt"),metaKey:validMods?.contains("Meta")});return e?.target?.dispatchEvent(event),!1}if(app.commands.executeCommandById(id))return!1}}}),_this.addCommand({id:"select-next-suggestion",name:"Select next suggestion",hotkeys:[{key:"ArrowDown",modifiers:[]}],repeatable:!0,editorCallback:()=>{_this._suggestionPopup.selectNextItem(1)},isVisible:()=>_this._suggestionPopup?.isVisible()}),_this.addCommand({id:"select-previous-suggestion",name:"Select previous suggestion",hotkeys:[{key:"ArrowUp",modifiers:[]}],repeatable:!0,editorCallback:()=>{_this._suggestionPopup.selectNextItem(-1)},isVisible:()=>_this._suggestionPopup?.isVisible()}),_this.addCommand({id:"insert-selected-suggestion",name:"Insert selected suggestion",hotkeys:[{key:"Enter",modifiers:[]}],editorCallback:()=>_this._suggestionPopup.applySelectedItem(),isVisible:()=>_this._suggestionPopup?.isVisible()}),_this.addCommand({id:"exit-suggestion",name:"Exit suggestions",hotkeys:[{key:"Escape",modifiers:[]}],editorCallback:()=>_this._suggestionPopup.closeSuggestion(),isVisible:()=>_this._suggestionPopup?.isVisible()})}var DEFAULT={triggerSymbol:"@",includeSymbol:!0,appendAsHeader:!1,headerLevelForContact:1,_enableOneFile:!1,showAddNewNote:!1,addNewNoteTemplateFile:"",addNewNoteDirectory:"",useCompatibilityMode:!1,leavePopupOpenForXSpaces:0,invalidCharacterRegex:"[[]^|#]",invalidCharacterRegexFlags:"i",removeAccents:!0},DEFAULT_SETTINGS={...DEFAULT,limitToDirectories:[],limitToFile:[]};var import_obsidian10=require("obsidian");var import_obsidian8=require("obsidian"),FileSuggestWithPath=class extends import_obsidian8.AbstractInputSuggest{constructor(inputEl,app,removeExt=!1,onSubmit){super(app,inputEl);this.inputEl=inputEl;this.onSubmit=onSubmit;this.removeExt=removeExt}removeExt=!1;renderSuggestion(value,el){el.setText(this.removeExt?value.path.replace(/\.md$/,""):value.path)}getItems(){return this.app.vault.getMarkdownFiles()}getSuggestions(query){return this.getItems().filter(file=>file.path.toLowerCase().contains(query.toLowerCase()))}selectSuggestion(value,_evt){this.onSubmit(value),this.inputEl.value=this.removeExt?value.path.replace(/\.md$/,""):value.path,this.inputEl.focus(),this.inputEl.trigger("input"),this.close()}};var import_obsidian9=require("obsidian"),FolderSuggester=class extends import_obsidian9.AbstractInputSuggest{constructor(inputEl,app,onSubmit){super(app,inputEl);this.inputEl=inputEl;this.onSubmit=onSubmit}renderSuggestion(value,el){el.setText(value)}getSuggestions(query){return this.app.vault.getAllFolders().filter(folder=>folder.path.toLowerCase().contains(query.toLowerCase())).map(folder=>folder.path)}selectSuggestion(value,_evt){this.inputEl.value=value,this.onSubmit(value),this.inputEl.focus(),this.inputEl.trigger("input"),this.close()}};var arrayMove=(array,fromIndex,toIndex)=>{if(toIndex<0||toIndex===array.length)return;let temp=array[fromIndex];array[fromIndex]=array[toIndex],array[toIndex]=temp},SettingsTab=class extends import_obsidian10.PluginSettingTab{plugin;shouldReset;constructor(app,plugin){super(app,plugin),this.plugin=plugin,this.shouldReset=!1}hide(){this.plugin.reloadPlugin(this.shouldReset),this.shouldReset=!1}display(){this.containerEl.empty(),this.containerEl.addClass("at-symbol-linking");let triggerSymbolDesc=document.createDocumentFragment();triggerSymbolDesc.append("Type this symbol to trigger the popup."),new import_obsidian10.Setting(this.containerEl).setName("Default symbol (trigger)").setDesc(triggerSymbolDesc).addText(text=>{text.setPlaceholder("@").setValue(this.plugin.settings.triggerSymbol).onChange(async value=>{this.plugin.settings.triggerSymbol=value,await this.plugin.saveSettings()}),text.inputEl.onblur=async()=>{this.display(),await this.validate()}});let includeSymbolDesc=document.createDocumentFragment();includeSymbolDesc.append(`Include the trigger symbol (default: ${this.plugin.settings.triggerSymbol}) prefixing the final link text`,includeSymbolDesc.createEl("br"),includeSymbolDesc.createEl("em",{text:`E.g. [${this.plugin.settings.includeSymbol?this.plugin.settings.triggerSymbol:""}evan](./evan)`})),new import_obsidian10.Setting(this.containerEl).setName("Include the trigger symbol").setDesc(includeSymbolDesc).addToggle(toggle=>toggle.setValue(this.plugin.settings.includeSymbol).onChange(async value=>{this.plugin.settings.includeSymbol=value,await this.plugin.saveSettings(),this.display()}));let messageAboutLevel=this.plugin.settings.headerLevelForContact===0?"include all headers":`current heading level: ${this.plugin.settings.headerLevelForContact}`;if(this.plugin.settings.limitToDirectories.length===0&&new import_obsidian10.Setting(this.containerEl).setName("Limit links to files").setHeading().setDesc(`Limit to one files for contact linking, using the header (${messageAboutLevel}) as the contact name.
				Leave empty to use the directories structure instead.`).addButton(button=>{button.setTooltip("Add a file").setButtonText("+").setCta().onClick(async()=>(this.plugin.settings.limitToFile.push({path:"",triggerSymbol:this.plugin.settings.triggerSymbol}),this.plugin.settings._enableOneFile=!0,await this.plugin.saveSettings(),this.display()))}),this.plugin.settings.limitToFile.forEach((file,index)=>{let newFileSetting=new import_obsidian10.Setting(this.containerEl).setClass("at-symbol-linking-folder-container").addSearch(cb=>{cb.setValue(file.path),new FileSuggestWithPath(cb.inputEl,this.app,!1,async newFile=>{this.plugin.settings.limitToFile[index].path=newFile.path,await this.plugin.saveSettings()}),cb.inputEl.onblur=async()=>{await this.validate()}}).addText(text=>{text.setPlaceholder("Trigger symbol").setValue(file.triggerSymbol??this.plugin.settings.triggerSymbol).onChange(async value=>{this.plugin.settings.limitToFile[index].triggerSymbol=value,await this.plugin.saveSettings()}).inputEl.addClass("min-width")}).addExtraButton(cb=>{cb.setIcon("up-chevron-glyph").setTooltip("Move up").onClick(async()=>{arrayMove(this.plugin.settings.limitToFile,index,index-1),await this.plugin.saveSettings(),this.display()})}).addExtraButton(cb=>{cb.setIcon("down-chevron-glyph").setTooltip("Move down").onClick(async()=>{arrayMove(this.plugin.settings.limitToFile,index,index+1),await this.plugin.saveSettings(),this.display()})}).addExtraButton(cb=>{cb.setIcon("cross").setTooltip("Delete").onClick(async()=>{this.plugin.settings.limitToFile.splice(index,1),await this.plugin.saveSettings(),this.display()})});newFileSetting.controlEl.addClass("at-symbol-linking-folder-setting"),newFileSetting.infoEl.remove()}),this.plugin.settings.limitToFile.length===0&&this.plugin.settings.limitToDirectories.length===0&&(this.plugin.settings._enableOneFile=!1),this.plugin.settings._enableOneFile){let snRdTh=this.plugin.settings.headerLevelForContact===2?"nd":this.plugin.settings.headerLevelForContact===3?"rd":this.plugin.settings.headerLevelForContact===1?"st":"th",atTheLevel=this.plugin.settings.headerLevelForContact===0?"at the 1st level":`at the ${this.plugin.settings.headerLevelForContact}${snRdTh} level`;new import_obsidian10.Setting(this.containerEl).setName("Header level for contact name").addSlider(slider=>{slider.setLimits(0,6,1).setValue(this.plugin.settings.headerLevelForContact).onChange(async value=>{this.plugin.settings.headerLevelForContact=value,await this.plugin.saveSettings()}),slider.sliderEl.onmouseleave=()=>{this.display()},slider.sliderEl.ariaLabel=`Header level for contact name
Current: ${this.plugin.settings.headerLevelForContact}`}),new import_obsidian10.Setting(this.containerEl).setName("Add new header").setHeading(),new import_obsidian10.Setting(this.containerEl).setName("Append as header if it doesn't exist").setDesc(`If the header doesn't exist when ${this.plugin.settings.triggerSymbol} linking, add an option to create the header ${atTheLevel} in the end of the file.`).addToggle(toggle=>toggle.setValue(this.plugin.settings.appendAsHeader).onChange(async value=>{this.plugin.settings.appendAsHeader=value,await this.plugin.saveSettings()}))}else{let ruleDesc=document.createDocumentFragment();if(ruleDesc.append(`${this.plugin.settings.triggerSymbol} linking will only source links from the following folders.`,ruleDesc.createEl("br"),`For example, you might only want contacts in the Contacts/ folder to be linked when you type ${this.plugin.settings.triggerSymbol}.`,ruleDesc.createEl("br"),ruleDesc.createEl("em",{text:"If no folders are added, links will be sourced from all folders."})),new import_obsidian10.Setting(this.containerEl).setName("Limit links to folders").setHeading().setDesc(ruleDesc).addButton(button=>{button.setTooltip("Add limit folder").setButtonText("+").setCta().onClick(async()=>(this.plugin.settings.limitToDirectories.push({path:"",triggerSymbol:this.plugin.settings.triggerSymbol,extensions:["md"]}),this.plugin.settings._enableOneFile=!1,await this.plugin.saveSettings(),this.display()))}),this.plugin.settings.limitToDirectories.forEach((directory,index)=>{let newDirectorySetting=new import_obsidian10.Setting(this.containerEl).setClass("at-symbol-linking-folder-container").addSearch(cb=>{cb.setPlaceholder("Folder").setValue(directory.path),new FolderSuggester(cb.inputEl,this.app,async result=>{this.plugin.settings.limitToDirectories[index].path=result.trim(),await this.plugin.saveSettings()}),cb.inputEl.onblur=async()=>{await this.validate()}}).addText(text=>{text.setPlaceholder("Trigger symbol").setValue(directory.triggerSymbol??this.plugin.settings.triggerSymbol).onChange(async value=>{this.plugin.settings.limitToDirectories[index].triggerSymbol=value,await this.plugin.saveSettings()}).inputEl.addClass("min-width")}).addText(text=>{text.setPlaceholder("Extensions"),text.setValue(directory.extensions?.join(", ")??"md"),text.onChange(async value=>{let res=value.split(/[\n ,]+/).map(ext=>ext.trim().replace(/^\./,""));value.length===0&&(res=["md"]),this.plugin.settings.limitToDirectories[index].extensions=res,await this.plugin.saveSettings()}),text.inputEl.onblur=()=>{this.display()}}).addExtraButton(cb=>{cb.setIcon("up-chevron-glyph").setTooltip("Move up").onClick(async()=>{arrayMove(this.plugin.settings.limitToDirectories,index,index-1),await this.plugin.saveSettings(),this.display()})}).addExtraButton(cb=>{cb.setIcon("down-chevron-glyph").setTooltip("Move down").onClick(async()=>{arrayMove(this.plugin.settings.limitToDirectories,index,index+1),await this.plugin.saveSettings(),this.display()})}).addExtraButton(cb=>{cb.setIcon("cross").setTooltip("Delete").onClick(async()=>{this.plugin.settings.limitToDirectories.splice(index,1),await this.plugin.saveSettings(),this.display()})});newDirectorySetting.controlEl.addClass("at-symbol-linking-folder-setting"),newDirectorySetting.infoEl.remove()}),new import_obsidian10.Setting(this.containerEl).setName("Add new note").setHeading(),new import_obsidian10.Setting(this.containerEl).setName("Add new note if it doesn't exist").setDesc(`If the note doesn't exist when ${this.plugin.settings.triggerSymbol} linking, add an option to create the note.`).addToggle(toggle=>toggle.setValue(this.plugin.settings.showAddNewNote).onChange(async value=>{this.plugin.settings.showAddNewNote=value,await this.plugin.saveSettings(),this.display()})),this.plugin.settings.showAddNewNote){let newNoteTemplateDesc=document.createDocumentFragment();newNoteTemplateDesc.append(`Template to use when creating a new note from ${this.plugin.settings.triggerSymbol} link.`,newNoteTemplateDesc.createEl("br"),"Uses formats from the ",newNoteTemplateDesc.createEl("a",{text:"core templates plugin",href:"https://help.obsidian.md/Plugins/Templates"})," to replace the following variables in the template:",newNoteTemplateDesc.createEl("br"),newNoteTemplateDesc.createEl("code",{text:"{{title}}"})," - The title of the new file",newNoteTemplateDesc.createEl("br"),newNoteTemplateDesc.createEl("code",{text:"{{date}}"})," - The current date",newNoteTemplateDesc.createEl("br"),newNoteTemplateDesc.createEl("code",{text:"{{time}}"})," - The current time"),new import_obsidian10.Setting(this.containerEl).setName("Add new note template").setDesc(newNoteTemplateDesc).addSearch(cb=>{cb.setPlaceholder("No template (blank note)").setValue(this.plugin.settings.addNewNoteTemplateFile),new FileSuggestWithPath(cb.inputEl,this.app,!0,async newFile=>{this.plugin.settings.addNewNoteTemplateFile=newFile.path.replace(/\.md$/,""),await this.plugin.saveSettings()}),cb.inputEl.onblur=async()=>{await this.validate()}}),new import_obsidian10.Setting(this.containerEl).setName("Add new note folder").setDesc(`Folder to create new notes in when using ${this.plugin.settings.triggerSymbol} linking. If the limit directories is used, the folder will be the limited directory selected by the trigger.`).addSearch(cb=>{cb.setPlaceholder("No folder (root)").setValue(this.plugin.settings.addNewNoteDirectory),new FolderSuggester(cb.inputEl,this.app,async result=>{this.plugin.settings.addNewNoteDirectory=result.trim(),await this.plugin.saveSettings()}),cb.inputEl.onblur=async()=>{await this.validate()}})}}new import_obsidian10.Setting(this.containerEl).setName("Suggestion popup behavior").setHeading();let useCompatibilityModeDesc=document.createDocumentFragment();useCompatibilityModeDesc.append(useCompatibilityModeDesc.createEl("br"),"Renders an HTML popup in place of the native Obsidian popup.",useCompatibilityModeDesc.createEl("br"),"Useful if you other plugins are interfering with the popup (e.g. the Tasks plugin).",useCompatibilityModeDesc.createEl("br"),useCompatibilityModeDesc.createEl("em",{text:"May be slower than the native popup."})),new import_obsidian10.Setting(this.containerEl).setName("Use compatibility mode").setDesc(useCompatibilityModeDesc).addToggle(toggle=>toggle.setValue(this.plugin.settings.useCompatibilityMode).onChange(async value=>{this.shouldReset=!0,this.plugin.settings.useCompatibilityMode=value,await this.plugin.saveSettings(),this.plugin.registerPopup(),this.display()}));let leavePopupOpenDesc=document.createDocumentFragment();leavePopupOpenDesc.append(`When ${this.plugin.settings.triggerSymbol} linking, you might want to type a full name e.g. "Brandon Sanderson" without the popup closing.`,leavePopupOpenDesc.createEl("br"),leavePopupOpenDesc.createEl("em",{text:"When set above 0, you'll need to press escape, return/enter, or type over X spaces to close the popup."})),new import_obsidian10.Setting(this.containerEl).setName("Leave popup open for X spaces").setDesc(leavePopupOpenDesc).addText(text=>{text.setPlaceholder("0").setValue(this.plugin.settings.leavePopupOpenForXSpaces?.toString()).onChange(async value=>{this.plugin.settings.leavePopupOpenForXSpaces=parseInt(value,10),await this.plugin.saveSettings()}),text.inputEl.onblur=async()=>{await this.validate()}}),new import_obsidian10.Setting(this.containerEl).setName("Advanced").setHeading();let invalidCharacterRegexDesc=document.createDocumentFragment();invalidCharacterRegexDesc.append(invalidCharacterRegexDesc.createEl("br"),"Characters typed that match this regex will not be included in the final search query in compatibility mode.",invalidCharacterRegexDesc.createEl("br"),"In normal mode, the popup will close when an invalid character is typed."),new import_obsidian10.Setting(this.containerEl).setName("Invalid character Regex").setDesc(invalidCharacterRegexDesc).addText(text=>{text.setPlaceholder(this.plugin.settings.invalidCharacterRegex).setValue(this.plugin.settings.invalidCharacterRegex).onChange(async value=>{this.plugin.settings.invalidCharacterRegex=value,await this.plugin.saveSettings()}),text.inputEl.onblur=async()=>{await this.validate("invalidCharacterRegex")}});let invalidCharacterRegexFlagsDesc=document.createDocumentFragment();invalidCharacterRegexFlagsDesc.append("Flags to use with the invalid character regex."),new import_obsidian10.Setting(this.containerEl).setName("Invalid character Regex flags").setDesc(invalidCharacterRegexFlagsDesc).addText(text=>{text.setPlaceholder(this.plugin.settings.invalidCharacterRegexFlags).setValue(this.plugin.settings.invalidCharacterRegexFlags).onChange(async value=>{this.plugin.settings.invalidCharacterRegexFlags=value,await this.plugin.saveSettings()}),text.inputEl.onblur=async()=>{await this.validate("invalidCharacterRegexFlags")}}),new import_obsidian10.Setting(this.containerEl).setName("Remove accents from search query").setDesc("e.g. \xE9 -> e when searching or creating links via the popup.").addToggle(toggle=>toggle.setValue(this.plugin.settings.removeAccents).onChange(async value=>{this.plugin.settings.removeAccents=value,await this.plugin.saveSettings()}))}async validate(editedSetting){let settings=this.plugin.settings,updateSetting=async(setting,value)=>(this.plugin.settings[setting]=value,await this.plugin.saveSettings(),this.display());settings.triggerSymbol.length!==1&&(new import_obsidian10.Notice("Trigger symbol must be a single character."),await updateSetting("triggerSymbol",settings.triggerSymbol.length?settings.triggerSymbol[0]:"@"));for(let i=0;i<settings.limitToDirectories.length;i++){let folder=settings.limitToDirectories[i];if(folder.path==="")continue;if(!this.app.vault.getAbstractFileByPath(folder.path)){new import_obsidian10.Notice(`Unable to find folder at path: ${folder.path}. Please add it if you want to limit links to this folder.`);let newFolders=[...settings.limitToDirectories];newFolders[i].path="",await updateSetting("limitToDirectories",newFolders)}}settings.showAddNewNote&&settings.addNewNoteTemplateFile&&(this.app.vault.getAbstractFileByPath(`${settings.addNewNoteTemplateFile}.md`)||(new import_obsidian10.Notice(`Unable to find template file at path: ${settings.addNewNoteTemplateFile}.md`),await updateSetting("addNewNoteTemplateFile",""))),settings.showAddNewNote&&settings.addNewNoteDirectory&&(this.app.vault.getAbstractFileByPath(`${settings.addNewNoteDirectory}`)||(new import_obsidian10.Notice(`Unable to find folder for new notes at path: ${settings.addNewNoteDirectory}. Please add it if you want to create new notes in this folder.`),await updateSetting("addNewNoteDirectory",""))),(isNaN(parseInt(settings.leavePopupOpenForXSpaces.toString()))||settings.leavePopupOpenForXSpaces<0)&&await updateSetting("leavePopupOpenForXSpaces",0),settings.invalidCharacterRegex?.trim()===""&&await updateSetting("invalidCharacterRegex",DEFAULT_SETTINGS.invalidCharacterRegex);try{new RegExp(settings.invalidCharacterRegex,settings.invalidCharacterRegexFlags)}catch{new import_obsidian10.Notice("Invalid regex or flags"),editedSetting==="invalidCharacterRegex"?await updateSetting("invalidCharacterRegex",DEFAULT_SETTINGS.invalidCharacterRegex):editedSetting==="invalidCharacterRegexFlags"&&await updateSetting("invalidCharacterRegexFlags",DEFAULT_SETTINGS.invalidCharacterRegexFlags)}}};var AtSymbolLinking=class extends import_obsidian11.Plugin{settings;reloadingPlugins=!1;activeExtensions;_suggestionPopup;async onload(){console.log(`[${this.manifest.id}] Loading plugin`),await this.loadSettings(),this.addSettingTab(new SettingsTab(this.app,this)),this.registerPopup()}onunload(){console.log(`[${this.manifest.id}] Unloading plugin`)}registerPopup(){this.settings.useCompatibilityMode?(this.activeExtensions=[atSymbolTriggerExtension(this.app,this.settings)],this.registerEditorExtension(this.activeExtensions),this.registerEvent(this.app.workspace.on("active-leaf-change",this.updateEditorProcessors.bind(this)))):(this._suggestionPopup=new SuggestionPopup(this.app,this.settings),this.registerEditorSuggest(this._suggestionPopup),applyHotKeyHack(this,this.app))}async reloadPlugin(shouldReset){if(!shouldReset||this.reloadingPlugins)return;this.reloadingPlugins=!0;let plugins=this.app.plugins;if(plugins?.enabledPlugins?.has(this.manifest.id)){await plugins.disablePlugin(this.manifest.id);try{await new Promise(resolve=>setTimeout(resolve,100)),await plugins.enablePlugin(this.manifest.id)}catch{}this.reloadingPlugins=!1}}async convertOldSettings(){let oldManifest=this.app.plugins.manifests["at-symbol-linking"];if(oldManifest&&!this.settings._converted){let dataFile=`${oldManifest.dir}/data.json`,settings=await this.app.vault.adapter.read(dataFile),oldSettings=JSON.parse(settings);if(oldSettings){new import_obsidian11.Notice("Convertir the settings of @ symbol linking to custom suggester");let directories=oldSettings?.limitLinkDirectories,files=oldSettings?.limitToOneFile,directoriesTrigger=oldSettings?.limitLinkDirectoriesWithTrigger;delete oldSettings.limitToOneFile;let filesTrigger=oldSettings?.limitToOneFileWithTrigger;delete oldSettings.limitToOneFileWithTrigger,delete oldSettings.limitLinkDirectories,delete oldSettings.limitToOneFile,directories&&directories.length>0?this.settings.limitToDirectories=directories.map(path=>({path,triggerSymbol:oldSettings.triggerSymbol??"@"})):directoriesTrigger&&directoriesTrigger.length>0&&(this.settings.limitToDirectories=directoriesTrigger,delete oldSettings.limitLinkDirectoriesWithTrigger),files&&files.length>0?this.settings.limitToFile=files.map(path=>({path,triggerSymbol:oldSettings.triggerSymbol??"@"})):filesTrigger&&filesTrigger.length>0&&(this.settings.limitToFile=filesTrigger,delete oldSettings.limitToOneFileWithTrigger),this.settings=Object.assign(this.settings,oldSettings)}}this.settings._converted=!0,await this.saveSettings()}updateEditorProcessors(){this.activeExtensions?.length&&(this.activeExtensions.forEach(extension=>{typeof extension?.destroy=="function"&&extension.destroy()}),this.activeExtensions.length=0,this.activeExtensions.push(atSymbolTriggerExtension(this.app,this.settings)),this.app.workspace.updateOptions())}async loadSettings(){this.settings=Object.assign({},DEFAULT_SETTINGS,await this.loadData()),await this.convertOldSettings()}async saveSettings(){await this.saveData(this.settings)}};

/* nosourcemap */