/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  default: () => CrossPlatformFullScreenPlugin
});
module.exports = __toCommonJS(main_exports);

// i18n.ts
var TRANSLATION_EN = /* @__PURE__ */ new Map([
  ["toggleMouseShortcuts", "Fullscreen toggle shortcut"],
  ["toggleMouseShortcutsDes", "The number of consecutive mouse or touchscreen clicks, with a time interval between two clicks less than 300ms."],
  ["doubleClick", "double click"],
  ["tripleClick", "triple click"],
  ["noClick", "command|hotkey only"]
]);
var TRANSLATION_CH = /* @__PURE__ */ new Map([
  ["toggleMouseShortcuts", "\u5168\u5C4F\u5207\u6362\u5FEB\u6377\u952E"],
  ["toggleMouseShortcutsDes", "\u9F20\u6807\u6216\u89E6\u5C4F\u8FDE\u7EED\u70B9\u51FB\u6B21\u6570\uFF0C\u4E24\u6B21\u70B9\u51FB\u65F6\u95F4\u95F4\u9694\u9700\u5C0F\u4E8E300ms\u3002"],
  ["doubleClick", "\u53CC\u51FB"],
  ["tripleClick", "\u4E09\u8FDE\u51FB"],
  ["noClick", "\u4EC5\u547D\u4EE4\u884C\u6216\u5FEB\u6377\u952E"]
]);
var TRANSLATION_JA = /* @__PURE__ */ new Map([
  ["toggleMouseShortcuts", "\u30D5\u30EB\u30B9\u30AF\u30EA\u30FC\u30F3\u5207\u308A\u66FF\u3048\u30B7\u30E7\u30FC\u30C8\u30AB\u30C3\u30C8"],
  ["toggleMouseShortcutsDes", "\u30DE\u30A6\u30B9\u307E\u305F\u306F\u30BF\u30C3\u30C1\u30B9\u30AF\u30EA\u30FC\u30F3\u306E\u9023\u7D9A\u30AF\u30EA\u30C3\u30AF\u56DE\u6570\u30012\u56DE\u306E\u30AF\u30EA\u30C3\u30AF\u9593\u9694\u306F300ms\u672A\u6E80\u3067\u3042\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059"],
  ["doubleClick", "\u30C0\u30D6\u30EB\u30AF\u30EA\u30C3\u30AF"],
  ["tripleClick", "\u30C8\u30EA\u30D7\u30EB\u30AF\u30EA\u30C3\u30AF"],
  ["noClick", "\u30B3\u30DE\u30F3\u30C9\u30E9\u30A4\u30F3\u307E\u305F\u306F\u30B7\u30E7\u30FC\u30C8\u30AB\u30C3\u30C8\u30AD\u30FC\u306E\u307F"]
]);
var TRANSLATION_KO = /* @__PURE__ */ new Map([
  ["toggleMouseShortcuts", "\uC804\uCCB4 \uD654\uBA74 \uC804\uD658 \uBC14\uB85C \uAC00\uAE30"],
  ["toggleMouseShortcutsDes", "\uB9C8\uC6B0\uC2A4 \uB610\uB294 \uD130\uCE58\uC2A4\uD06C\uB9B0 \uC5F0\uC18D \uD074\uB9AD \uD69F\uC218, 2\uD68C \uD074\uB9AD \uAC04\uACA9\uC740 300ms \uBBF8\uB9CC\uC774\uC5B4\uC57C \uD569\uB2C8\uB2E4"],
  ["doubleClick", "\uB354\uBE14 \uD074\uB9AD"],
  ["tripleClick", "\uD2B8\uB9AC\uD50C \uD074\uB9AD"],
  ["noClick", "\uBA85\uB839\uC904 \uB610\uB294 \uB2E8\uCD95\uD0A4\uB9CC"]
]);
var TRANSLATION_UK = /* @__PURE__ */ new Map([
  ["toggleMouseShortcuts", "\u0413\u043E\u0440\u044F\u0447\u0430\u044F \u043A\u043B\u0430\u0432\u0438\u0448\u0430 \u043F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F \u043F\u043E\u043B\u043D\u043E\u044D\u043A\u0440\u0430\u043D\u043D\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0430"],
  ["toggleMouseShortcutsDes", "\u043E\u043B\u0438\u0447\u0435\u0441\u0442\u0432\u043E \u043F\u043E\u0441\u043B\u0435\u0434\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0445 \u043A\u043B\u0438\u043A\u043E\u0432 \u043C\u044B\u0448\u0438 \u0438\u043B\u0438 \u0441\u0435\u043D\u0441\u043E\u0440\u043D\u043E\u0433\u043E \u044D\u043A\u0440\u0430\u043D\u0430 \u0441 \u0438\u043D\u0442\u0435\u0440\u0432\u0430\u043B\u043E\u043C \u0432\u0440\u0435\u043C\u0435\u043D\u0438 \u043C\u0435\u043D\u0435\u0435 300 \u043C\u0441"],
  ["doubleClick", "\u0414\u0432\u043E\u0439\u043D\u043E\u0439 \u0449\u0435\u043B\u0447\u043E\u043A"],
  ["tripleClick", "\u0422\u0440\u043E\u0439\u043D\u043E\u0439 \u0449\u0435\u043B\u0447\u043E\u043A"],
  ["noClick", "\u0422\u043E\u043B\u044C\u043A\u043E \u043A\u043E\u043C\u0430\u043D\u0434\u043D\u0430\u044F \u0441\u0442\u0440\u043E\u043A\u0430 \u0438\u043B\u0438 \u0441\u043E\u0447\u0435\u0442\u0430\u043D\u0438\u044F \u043A\u043B\u0430\u0432\u0438\u0448"]
]);
var TRANSLATION_TW = /* @__PURE__ */ new Map([
  ["toggleMouseShortcuts", "\u5168\u87A2\u5E55\u5207\u63DB\u5FEB\u901F\u9375"],
  ["toggleMouseShortcutsDes", "\u9F20\u6A19\u6216\u89F8\u63A7\u87A2\u5E55\u9023\u9EDE\u6B21\u6578\uFF0C\u5169\u6B21\u9EDE\u64CA\u6642\u9593\u9593\u9694\u9700\u5C0F\u65BC300\u6BEB\u79D2"],
  ["doubleClick", "\u96D9\u64CA"],
  ["tripleClick", "\u4E09\u64CA"],
  ["noClick", "\u50C5\u63A7\u5236\u53F0\u6216\u9375\u76E4"]
]);
var TRANSLATION = /* @__PURE__ */ new Map([
  ["en", TRANSLATION_EN],
  ["zh-cn", TRANSLATION_CH],
  ["ja", TRANSLATION_JA],
  ["ko", TRANSLATION_KO],
  ["uk", TRANSLATION_UK],
  ["zh-tw", TRANSLATION_TW]
]);
var I18n = class {
  constructor(lang) {
    this.lang = lang;
  }
  t(key) {
    const langPack = TRANSLATION.get(this.lang);
    const langBackendPack = TRANSLATION_EN;
    let value = null;
    if (langPack && langPack.has(key)) {
      value = langPack.get(key);
    } else if (langBackendPack && langBackendPack.has(key)) {
      value = langBackendPack.get(key);
    }
    if (value) {
      return value;
    }
    return key;
  }
};

// main.ts
var import_obsidian = require("obsidian");
var import_obsidian2 = require("obsidian");
var DEFAULT_SETTINGS = {
  consecutiveClickTimes: 3
};
var CrossPlatformFullScreenPlugin = class extends import_obsidian.Plugin {
  constructor() {
    super(...arguments);
    this.elementsStatusMap = /* @__PURE__ */ new Map();
  }
  async onload() {
    await this.loadSettings();
    this.addCommand({
      id: "toggle-full-screen",
      name: "toggle",
      callback: async () => {
        this.toggleFullScreen();
      }
    });
    this.i18n = new I18n(import_obsidian.moment.locale());
    this.addTouchEventListener();
    this.addSettingTab(new SettingTab(this.app, this));
  }
  addTouchEventListener() {
    const clickEventName = import_obsidian2.Platform.isMobile ? "touchend" : "click";
    if (this.handleClickFunc) {
      document.removeEventListener(clickEventName, this.handleClickFunc);
    }
    const waitTime = 300;
    const maxCount = this.settings.consecutiveClickTimes;
    let lastTouchEnd = 0;
    let touchCount = 0;
    this.handleClickFunc = async (evt) => {
      const now = new Date().getTime();
      touchCount = now - lastTouchEnd < waitTime ? touchCount + 1 : 1;
      lastTouchEnd = now;
      if (touchCount === maxCount) {
        await this.toggleFullScreen();
      }
    };
    document.addEventListener(clickEventName, this.handleClickFunc);
  }
  async toggle(toggleClassList) {
    toggleClassList.forEach((cls) => {
      const elements = document.querySelectorAll(cls);
      if (cls && elements) {
        elements.forEach((element, i) => {
          const cname = "fullscreen-cross-platform-hidden";
          if (element.classList.contains(cname)) {
            element.removeClass(cname);
          } else {
            element.addClass(cname);
          }
        });
      }
    });
  }
  async toggleFullScreen() {
    if (import_obsidian2.Platform.isMobile) {
      const clsArr = [".mobile-navbar", ".view-header", ".workspace-tab-header-inner"];
      if (import_obsidian2.Platform.isTablet) {
        clsArr.push(".workspace-tab-header-container");
      }
      this.toggle(clsArr);
    } else if (import_obsidian2.Platform.isDesktop) {
      this.toggle([
        ".workspace-ribbon.side-dock-ribbon.mod-left",
        ".workspace-split.mod-horizontal.mod-left-split",
        ".workspace-tab-header-container",
        ".workspace-split.mod-horizontal.mod-right-split"
      ]);
    }
  }
  onunload() {
  }
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
  }
  async saveSettings() {
    await this.saveData(this.settings);
    this.addTouchEventListener();
  }
};
var SettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    const i18n = this.plugin.i18n;
    new import_obsidian.Setting(containerEl).setName(i18n.t("toggleMouseShortcuts")).setDesc(i18n.t("toggleMouseShortcutsDes")).addDropdown((component) => {
      component.addOption("2", i18n.t("doubleClick")).addOption("3", i18n.t("tripleClick")).addOption("-1", i18n.t("noClick")).setValue(this.plugin.settings.consecutiveClickTimes.toString()).onChange((value) => {
        this.plugin.settings.consecutiveClickTimes = parseInt(value);
        this.plugin.saveSettings();
      });
    });
  }
};


/* nosourcemap */