name: ecam-project

services:
  app:
    build: .
    platform: linux/amd64
    container_name: ecam_app
    volumes:
      - .:/app
    depends_on:
      db:
        condition: service_healthy
    networks:
      - ecam_network_v2
    tty: true  # 保持容器运行
    stdin_open: true  # 保持标准输入打开
    command: tail -f /dev/null  # 使容器持续运行
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  db:
    image: mysql:5.7
    platform: linux/amd64
    container_name: ecam_city_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: ecam_city
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --default-authentication-plugin=mysql_native_password
    ports:
      - "9999:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./dlkst_prd_mysql57-ecam_city.sql:/docker-entrypoint-initdb.d/dlkst_prd_mysql57-ecam_city.sql
    networks:
      - ecam_network_v2
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  mysql_data:

networks:
  ecam_network_v2:
    driver: bridge
