2025-09-03 08:02:32,178 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 08:02:32,178 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 08:02:32,178 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 08:02:32,178 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 08:02:32,178 - __main__ - INFO - ================================================================================
2025-09-03 08:02:32,178 - __main__ - INFO - 
正在处理: 年度省级能源消费数据 (ecam_in_y_pro_ind_ene_off)
2025-09-03 08:02:32,178 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 08:02:32,239 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:02:32,390 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 08:02:32,393 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 08:02:32,884 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 08:02:32,885 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 08:02:32,885 - __main__ - INFO - 
================================================================================
2025-09-03 08:02:32,885 - __main__ - INFO - 表名: ecam_in_y_pro_ind_ene_off
2025-09-03 08:02:32,885 - __main__ - INFO - ================================================================================
2025-09-03 08:02:32,885 - __main__ - INFO - 原始数据行数: 25760
2025-09-03 08:02:32,885 - __main__ - INFO - 标准化后行数: 25760
2025-09-03 08:02:32,885 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:02:32,885 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 08:02:32,885 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 08:02:32,885 - __main__ - INFO - 新增标准化列: ['standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 08:02:32,885 - __main__ - INFO - 
--- standard_province 列分析 ---
2025-09-03 08:02:32,887 - __main__ - INFO - 覆盖率: 25760/25760 (100.0%)
2025-09-03 08:02:32,888 - __main__ - INFO - 唯一值数量: 1
2025-09-03 08:02:32,890 - __main__ - INFO - 值分布 (前10个):
2025-09-03 08:02:32,890 - __main__ - INFO -   山西: 25760 条 (100.0%)
2025-09-03 08:02:32,891 - __main__ - INFO - 
--- macro_province 列分析 ---
2025-09-03 08:02:32,892 - __main__ - INFO - 覆盖率: 25760/25760 (100.0%)
2025-09-03 08:02:32,893 - __main__ - INFO - 唯一值数量: 1
2025-09-03 08:02:32,894 - __main__ - INFO - 值分布 (前10个):
2025-09-03 08:02:32,894 - __main__ - INFO -   山西: 25760 条 (100.0%)
2025-09-03 08:02:32,895 - __main__ - INFO - 
--- standard_item 列分析 ---
2025-09-03 08:02:32,896 - __main__ - INFO - 覆盖率: 7360/25760 (28.6%)
2025-09-03 08:02:32,896 - __main__ - INFO - 唯一值数量: 3
2025-09-03 08:02:32,897 - __main__ - INFO - 值分布 (前10个):
2025-09-03 08:02:32,897 - __main__ - INFO -   能源行业: 5888 条 (22.9%)
2025-09-03 08:02:32,897 - __main__ - INFO -   工业: 736 条 (2.9%)
2025-09-03 08:02:32,897 - __main__ - INFO -   建筑业: 736 条 (2.9%)
2025-09-03 08:02:32,898 - __main__ - INFO - 空值数量: 18400 (71.4%)
2025-09-03 08:02:32,898 - __main__ - INFO - 
--- macro_item 列分析 ---
2025-09-03 08:02:32,898 - __main__ - INFO - 覆盖率: 7360/25760 (28.6%)
2025-09-03 08:02:32,899 - __main__ - INFO - 唯一值数量: 3
2025-09-03 08:02:32,899 - __main__ - INFO - 值分布 (前10个):
2025-09-03 08:02:32,899 - __main__ - INFO -   能源行业: 5888 条 (22.9%)
2025-09-03 08:02:32,899 - __main__ - INFO -   工业: 736 条 (2.9%)
2025-09-03 08:02:32,899 - __main__ - INFO -   建筑业: 736 条 (2.9%)
2025-09-03 08:02:32,900 - __main__ - INFO - 空值数量: 18400 (71.4%)
2025-09-03 08:02:32,900 - __main__ - INFO - 
--- standard_energy_type 列分析 ---
2025-09-03 08:02:32,901 - __main__ - INFO - 覆盖率: 12075/25760 (46.9%)
2025-09-03 08:02:32,901 - __main__ - INFO - 唯一值数量: 4
2025-09-03 08:02:32,902 - __main__ - INFO - 值分布 (前10个):
2025-09-03 08:02:32,902 - __main__ - INFO -   原煤: 8855 条 (34.4%)
2025-09-03 08:02:32,902 - __main__ - INFO -   天然气: 1610 条 (6.2%)
2025-09-03 08:02:32,902 - __main__ - INFO -   焦炭: 805 条 (3.1%)
2025-09-03 08:02:32,902 - __main__ - INFO -   电力: 805 条 (3.1%)
2025-09-03 08:02:32,903 - __main__ - INFO - 空值数量: 13685 (53.1%)
2025-09-03 08:02:32,903 - __main__ - INFO - 
--- macro_energy_type 列分析 ---
2025-09-03 08:02:32,905 - __main__ - INFO - 覆盖率: 12075/25760 (46.9%)
2025-09-03 08:02:32,906 - __main__ - INFO - 唯一值数量: 4
2025-09-03 08:02:32,907 - __main__ - INFO - 值分布 (前10个):
2025-09-03 08:02:32,907 - __main__ - INFO -   原煤: 8855 条 (34.4%)
2025-09-03 08:02:32,907 - __main__ - INFO -   天然气: 1610 条 (6.2%)
2025-09-03 08:02:32,907 - __main__ - INFO -   焦炭: 805 条 (3.1%)
2025-09-03 08:02:32,907 - __main__ - INFO -   电力: 805 条 (3.1%)
2025-09-03 08:02:32,908 - __main__ - INFO - 空值数量: 13685 (53.1%)
2025-09-03 08:02:32,908 - __main__ - INFO - 
--- 数据质量检查 ---
2025-09-03 08:02:32,928 - __main__ - INFO - 重复行数: 0
2025-09-03 08:02:32,942 - __main__ - INFO - 存在空值的列:
2025-09-03 08:02:32,942 - __main__ - INFO -   value: 19205 个空值 (74.6%)
2025-09-03 08:02:32,942 - __main__ - INFO -   standard_item: 18400 个空值 (71.4%)
2025-09-03 08:02:32,942 - __main__ - INFO -   macro_item: 18400 个空值 (71.4%)
2025-09-03 08:02:32,943 - __main__ - INFO -   standard_energy_type: 13685 个空值 (53.1%)
2025-09-03 08:02:32,943 - __main__ - INFO -   macro_energy_type: 13685 个空值 (53.1%)
2025-09-03 08:02:32,943 - __main__ - INFO - 
--- 数据样本 ---
2025-09-03 08:02:32,951 - __main__ - INFO -    year province item convert method energy_type  value unit standard_province macro_province standard_item macro_item standard_energy_type macro_energy_type
0  2000       山西   乡村     实物量                 原油    NaN   万吨                山西             山西           NaN        NaN                  NaN               NaN
1  2000       山西   乡村     实物量                 原煤  442.0   万吨                山西             山西           NaN        NaN                   原煤                原煤
2  2000       山西   乡村     实物量                 柴油    NaN   万吨                山西             山西           NaN        NaN                  NaN               NaN
2025-09-03 08:02:32,951 - __main__ - INFO - 
正在处理: 月度分行业用电量数据 (ecam_in_m_pro_ind_ele_off)
2025-09-03 08:02:32,951 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 08:02:32,976 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:02:34,004 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 08:02:34,008 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 08:02:34,568 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 08:02:34,568 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 08:02:34,573 - __main__ - INFO - 
================================================================================
2025-09-03 08:02:34,573 - __main__ - INFO - 表名: ecam_in_m_pro_ind_ele_off
2025-09-03 08:02:34,573 - __main__ - INFO - ================================================================================
2025-09-03 08:02:34,573 - __main__ - INFO - 原始数据行数: 74496
2025-09-03 08:02:34,573 - __main__ - INFO - 标准化后行数: 74496
2025-09-03 08:02:34,573 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:02:34,573 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 08:02:34,573 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area']
2025-09-03 08:02:34,573 - __main__ - INFO - 新增标准化列: ['standard_area', 'macro_area']
2025-09-03 08:02:34,573 - __main__ - INFO - 
--- standard_area 列分析 ---
2025-09-03 08:02:34,575 - __main__ - INFO - 覆盖率: 74496/74496 (100.0%)
2025-09-03 08:02:34,577 - __main__ - INFO - 唯一值数量: 11
2025-09-03 08:02:34,580 - __main__ - INFO - 值分布 (前10个):
2025-09-03 08:02:34,580 - __main__ - INFO -   运城: 6826 条 (9.2%)
2025-09-03 08:02:34,580 - __main__ - INFO -   大同: 6800 条 (9.1%)
2025-09-03 08:02:34,580 - __main__ - INFO -   太原: 6798 条 (9.1%)
2025-09-03 08:02:34,580 - __main__ - INFO -   忻州: 6798 条 (9.1%)
2025-09-03 08:02:34,580 - __main__ - INFO -   晋中: 6798 条 (9.1%)
2025-09-03 08:02:34,580 - __main__ - INFO -   临汾: 6765 条 (9.1%)
2025-09-03 08:02:34,580 - __main__ - INFO -   长治: 6765 条 (9.1%)
2025-09-03 08:02:34,580 - __main__ - INFO -   吕梁: 6752 条 (9.1%)
2025-09-03 08:02:34,580 - __main__ - INFO -   朔州: 6750 条 (9.1%)
2025-09-03 08:02:34,580 - __main__ - INFO -   晋城: 6732 条 (9.0%)
2025-09-03 08:02:34,582 - __main__ - INFO - 
--- macro_area 列分析 ---
2025-09-03 08:02:34,584 - __main__ - INFO - 覆盖率: 74496/74496 (100.0%)
2025-09-03 08:02:34,586 - __main__ - INFO - 唯一值数量: 1
2025-09-03 08:02:34,589 - __main__ - INFO - 值分布 (前10个):
2025-09-03 08:02:34,589 - __main__ - INFO -   山西: 74496 条 (100.0%)
2025-09-03 08:02:34,591 - __main__ - INFO - 
--- 数据质量检查 ---
2025-09-03 08:02:34,614 - __main__ - INFO - 重复行数: 0
2025-09-03 08:02:34,626 - __main__ - INFO - 
--- 数据样本 ---
2025-09-03 08:02:34,634 - __main__ - INFO -         month area       industry  electricity standard_area macro_area
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西
2  2020-01-01   临汾        二、工业用电量  102837.0401            临汾         山西
2025-09-03 08:02:34,634 - __main__ - INFO - 
正在处理: 年度GDP数据 (fct_y_gdp)
2025-09-03 08:02:34,634 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 08:02:34,656 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:02:34,709 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 08:02:34,711 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 08:02:34,711 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 08:02:34,711 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 08:02:34,715 - __main__ - INFO - 
================================================================================
2025-09-03 08:02:34,716 - __main__ - INFO - 表名: fct_y_gdp
2025-09-03 08:02:34,716 - __main__ - INFO - ================================================================================
2025-09-03 08:02:34,716 - __main__ - INFO - 原始数据行数: 3360
2025-09-03 08:02:34,716 - __main__ - INFO - 标准化后行数: 3360
2025-09-03 08:02:34,716 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:02:34,716 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 08:02:34,716 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 08:02:34,716 - __main__ - INFO - 新增标准化列: []
2025-09-03 08:02:34,716 - __main__ - INFO - 
--- 数据质量检查 ---
2025-09-03 08:02:34,717 - __main__ - INFO - 重复行数: 0
2025-09-03 08:02:34,718 - __main__ - INFO - 存在空值的列:
2025-09-03 08:02:34,718 - __main__ - INFO -   area: 252 个空值 (7.5%)
2025-09-03 08:02:34,718 - __main__ - INFO -   record: 1030 个空值 (30.7%)
2025-09-03 08:02:34,718 - __main__ - INFO - 
--- 数据样本 ---
2025-09-03 08:02:34,719 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 08:02:34,719 - __main__ - INFO - 
正在处理: 年度能耗强度数据 (fct_y_all_ene_intsty)
2025-09-03 08:02:34,719 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 08:02:34,742 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:02:34,751 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 08:02:34,752 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 08:02:34,752 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 08:02:34,752 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 08:02:34,753 - __main__ - INFO - 
================================================================================
2025-09-03 08:02:34,753 - __main__ - INFO - 表名: fct_y_all_ene_intsty
2025-09-03 08:02:34,753 - __main__ - INFO - ================================================================================
2025-09-03 08:02:34,753 - __main__ - INFO - 原始数据行数: 204
2025-09-03 08:02:34,753 - __main__ - INFO - 标准化后行数: 204
2025-09-03 08:02:34,753 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:02:34,753 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 08:02:34,753 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 08:02:34,753 - __main__ - INFO - 新增标准化列: []
2025-09-03 08:02:34,753 - __main__ - INFO - 
--- 数据质量检查 ---
2025-09-03 08:02:34,754 - __main__ - INFO - 重复行数: 0
2025-09-03 08:02:34,754 - __main__ - INFO - 
--- 数据样本 ---
2025-09-03 08:02:34,755 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 08:02:34,755 - __main__ - INFO - 
正在处理: 年度工业产品产量数据 (fct_y_prd_output)
2025-09-03 08:02:34,755 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 08:02:34,790 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:02:34,822 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 08:02:34,823 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 08:02:34,824 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 08:02:34,824 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 08:02:34,824 - __main__ - INFO - 
================================================================================
2025-09-03 08:02:34,824 - __main__ - INFO - 表名: fct_y_prd_output
2025-09-03 08:02:34,824 - __main__ - INFO - ================================================================================
2025-09-03 08:02:34,824 - __main__ - INFO - 原始数据行数: 1812
2025-09-03 08:02:34,824 - __main__ - INFO - 标准化后行数: 1812
2025-09-03 08:02:34,824 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:02:34,824 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 08:02:34,824 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 08:02:34,824 - __main__ - INFO - 新增标准化列: ['industry']
2025-09-03 08:02:34,824 - __main__ - INFO - 
--- industry 列分析 ---
2025-09-03 08:02:34,825 - __main__ - INFO - 覆盖率: 1812/1812 (100.0%)
2025-09-03 08:02:34,825 - __main__ - INFO - 唯一值数量: 5
2025-09-03 08:02:34,825 - __main__ - INFO - 值分布 (前10个):
2025-09-03 08:02:34,825 - __main__ - INFO -   钢铁: 540 条 (29.8%)
2025-09-03 08:02:34,825 - __main__ - INFO -   其他工业: 480 条 (26.5%)
2025-09-03 08:02:34,825 - __main__ - INFO -   建材: 360 条 (19.9%)
2025-09-03 08:02:34,825 - __main__ - INFO -   未知: 252 条 (13.9%)
2025-09-03 08:02:34,825 - __main__ - INFO -   石化: 180 条 (9.9%)
2025-09-03 08:02:34,825 - __main__ - INFO - 
--- 数据质量检查 ---
2025-09-03 08:02:34,826 - __main__ - INFO - 重复行数: 0
2025-09-03 08:02:34,828 - __main__ - INFO - 
--- 数据样本 ---
2025-09-03 08:02:34,830 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴     其他工业
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴     其他工业
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴       钢铁
2025-09-03 08:02:34,830 - __main__ - INFO - 
================================================================================
2025-09-03 08:02:34,830 - __main__ - INFO - 验证总结报告
2025-09-03 08:02:34,830 - __main__ - INFO - ================================================================================
2025-09-03 08:02:34,830 - __main__ - INFO - ✓ ecam_in_y_pro_ind_ene_off: 成功
2025-09-03 08:02:34,830 - __main__ - INFO -   原始行数: 25760
2025-09-03 08:02:34,830 - __main__ - INFO -   标准化后行数: 25760
2025-09-03 08:02:34,830 - __main__ - INFO -   新增列数: 6
2025-09-03 08:02:34,830 - __main__ - INFO -   新增列: ['standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 08:02:34,830 - __main__ - INFO - ✓ ecam_in_m_pro_ind_ele_off: 成功
2025-09-03 08:02:34,830 - __main__ - INFO -   原始行数: 74496
2025-09-03 08:02:34,831 - __main__ - INFO -   标准化后行数: 74496
2025-09-03 08:02:34,831 - __main__ - INFO -   新增列数: 2
2025-09-03 08:02:34,831 - __main__ - INFO -   新增列: ['standard_area', 'macro_area']
2025-09-03 08:02:34,831 - __main__ - INFO - ✓ fct_y_gdp: 成功
2025-09-03 08:02:34,831 - __main__ - INFO -   原始行数: 3360
2025-09-03 08:02:34,831 - __main__ - INFO -   标准化后行数: 3360
2025-09-03 08:02:34,831 - __main__ - INFO -   新增列数: 0
2025-09-03 08:02:34,831 - __main__ - INFO -   新增列: []
2025-09-03 08:02:34,831 - __main__ - INFO - ✓ fct_y_all_ene_intsty: 成功
2025-09-03 08:02:34,831 - __main__ - INFO -   原始行数: 204
2025-09-03 08:02:34,831 - __main__ - INFO -   标准化后行数: 204
2025-09-03 08:02:34,831 - __main__ - INFO -   新增列数: 0
2025-09-03 08:02:34,831 - __main__ - INFO -   新增列: []
2025-09-03 08:02:34,831 - __main__ - INFO - ✓ fct_y_prd_output: 成功
2025-09-03 08:02:34,831 - __main__ - INFO -   原始行数: 1812
2025-09-03 08:02:34,831 - __main__ - INFO -   标准化后行数: 1812
2025-09-03 08:02:34,831 - __main__ - INFO -   新增列数: 1
2025-09-03 08:02:34,831 - __main__ - INFO -   新增列: ['industry']
2025-09-03 08:02:34,831 - __main__ - INFO - 
总体结果: 5/5 个表验证成功
2025-09-03 08:02:34,831 - __main__ - INFO - 🎉 所有表验证通过！
2025-09-03 08:14:55,811 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 08:14:55,812 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 08:14:55,812 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 08:14:55,812 - __main__ - INFO - ================================================================================
2025-09-03 08:14:55,812 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 08:14:55,812 - __main__ - INFO - ================================================================================
2025-09-03 08:14:55,812 - __main__ - INFO - 
============================================================
2025-09-03 08:14:55,812 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 08:14:55,812 - __main__ - INFO - ============================================================
2025-09-03 08:14:55,812 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 08:14:55,812 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 08:14:55,891 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:14:55,911 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 08:14:55,912 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 08:14:55,912 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 08:14:55,912 - __main__ - INFO - 原始数据样本:
2025-09-03 08:14:55,915 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 08:14:55,915 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 08:14:55,915 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 08:14:55,915 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 08:14:55,915 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 08:14:55,915 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 08:14:55,915 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 08:14:55,915 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 08:14:55,915 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 08:14:55,919 - __main__ - INFO - 
============================================================
2025-09-03 08:14:55,919 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 08:14:55,919 - __main__ - INFO - ============================================================
2025-09-03 08:14:55,919 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 08:14:55,919 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 08:14:55,942 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:14:56,084 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 08:14:56,085 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 08:14:56,085 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 08:14:56,085 - __main__ - INFO - 原始数据样本:
2025-09-03 08:14:56,086 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 08:14:56,086 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 08:14:56,088 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 08:14:56,562 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 08:14:56,562 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 08:14:56,562 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 08:14:56,562 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 08:14:56,562 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 08:14:56,564 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 08:14:56,588 - __main__ - INFO - 
============================================================
2025-09-03 08:14:56,588 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 08:14:56,588 - __main__ - INFO - ============================================================
2025-09-03 08:14:56,588 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 08:14:56,588 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 08:14:56,610 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:14:57,649 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 08:14:57,650 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 08:14:57,650 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 08:14:57,650 - __main__ - INFO - 原始数据样本:
2025-09-03 08:14:57,651 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 08:14:57,651 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 08:14:57,654 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 08:14:58,177 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 08:14:58,177 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 08:14:58,177 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 08:14:58,177 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area']
2025-09-03 08:14:58,177 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 08:14:58,179 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 08:14:58,206 - __main__ - INFO - 
============================================================
2025-09-03 08:14:58,206 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 08:14:58,206 - __main__ - INFO - ============================================================
2025-09-03 08:14:58,207 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 08:14:58,207 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 08:14:58,234 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:14:58,283 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 08:14:58,284 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 08:14:58,284 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 08:14:58,284 - __main__ - INFO - 原始数据样本:
2025-09-03 08:14:58,285 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 08:14:58,285 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 08:14:58,285 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 08:14:58,285 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 08:14:58,285 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 08:14:58,285 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 08:14:58,285 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 08:14:58,286 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 08:14:58,286 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 08:14:58,287 - __main__ - INFO - 
============================================================
2025-09-03 08:14:58,287 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 08:14:58,287 - __main__ - INFO - ============================================================
2025-09-03 08:14:58,287 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 08:14:58,287 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 08:14:58,309 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:14:58,314 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 08:14:58,315 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 08:14:58,315 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 08:14:58,315 - __main__ - INFO - 原始数据样本:
2025-09-03 08:14:58,316 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 08:14:58,316 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 08:14:58,316 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 08:14:58,316 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 08:14:58,316 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 08:14:58,316 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 08:14:58,316 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 08:14:58,316 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 08:14:58,316 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 08:14:58,317 - __main__ - INFO - 
============================================================
2025-09-03 08:14:58,317 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 08:14:58,317 - __main__ - INFO - ============================================================
2025-09-03 08:14:58,317 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 08:14:58,317 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 08:14:58,339 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 08:14:58,363 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 08:14:58,364 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 08:14:58,364 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 08:14:58,364 - __main__ - INFO - 原始数据样本:
2025-09-03 08:14:58,365 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 08:14:58,365 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 08:14:58,366 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 08:14:58,366 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 08:14:58,366 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 08:14:58,366 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 08:14:58,366 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 08:14:58,366 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 08:14:58,367 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 08:14:58,368 - __main__ - INFO - 
================================================================================
2025-09-03 08:14:58,368 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 08:14:58,368 - __main__ - INFO - ================================================================================
2025-09-03 08:14:58,368 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 08:14:58,368 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 08:14:58,368 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:14:58,368 - __main__ - INFO - 标准化列数: 0
2025-09-03 08:14:58,368 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 08:14:58,368 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 08:14:58,368 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:14:58,368 - __main__ - INFO - 标准化列数: 6
2025-09-03 08:14:58,368 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 08:14:58,368 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 08:14:58,368 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 08:14:58,368 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 08:14:58,368 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 08:14:58,368 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:14:58,368 - __main__ - INFO - 标准化列数: 2
2025-09-03 08:14:58,369 - __main__ - INFO -   standard_area: 74496/74496 (100.0%)
2025-09-03 08:14:58,369 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 08:14:58,369 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 08:14:58,369 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:14:58,369 - __main__ - INFO - 标准化列数: 0
2025-09-03 08:14:58,369 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 08:14:58,369 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 08:14:58,369 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:14:58,369 - __main__ - INFO - 标准化列数: 0
2025-09-03 08:14:58,369 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 08:14:58,369 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 08:14:58,369 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 08:14:58,369 - __main__ - INFO - 标准化列数: 0
2025-09-03 08:14:58,369 - __main__ - INFO - 
总体统计:
2025-09-03 08:14:58,369 - __main__ - INFO - 成功表数: 6/6
2025-09-03 08:14:58,369 - __main__ - INFO - 成功率: 100.0%
2025-09-03 08:14:58,369 - __main__ - INFO - 
================================================================================
2025-09-03 08:14:58,369 - __main__ - INFO - 标准化数据模型
2025-09-03 08:14:58,369 - __main__ - INFO - ================================================================================
2025-09-03 08:14:58,369 - __main__ - INFO - 
标准化列模型:
2025-09-03 08:14:58,369 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 08:14:58,369 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 08:14:58,369 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 08:14:58,369 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 08:14:58,369 - __main__ - INFO - 
层次关系列模型:
2025-09-03 08:14:58,369 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 08:14:58,369 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 08:14:58,369 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 08:14:58,369 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 08:14:58,369 - __main__ - INFO - 
各表标准化模型:
2025-09-03 08:14:58,369 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 08:14:58,370 - __main__ - INFO - 列数: 9
2025-09-03 08:14:58,370 - __main__ - INFO - 行数: 273
2025-09-03 08:14:58,370 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 08:14:58,370 - __main__ - INFO - 数据样本:
2025-09-03 08:14:58,373 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 08:14:58,373 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 08:14:58,373 - __main__ - INFO - 列数: 14
2025-09-03 08:14:58,373 - __main__ - INFO - 行数: 25760
2025-09-03 08:14:58,373 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 08:14:58,373 - __main__ - INFO - 数据样本:
2025-09-03 08:14:58,378 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 08:14:58,378 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 08:14:58,379 - __main__ - INFO - 列数: 6
2025-09-03 08:14:58,379 - __main__ - INFO - 行数: 74496
2025-09-03 08:14:58,379 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area']
2025-09-03 08:14:58,379 - __main__ - INFO - 数据样本:
2025-09-03 08:14:58,387 - __main__ - INFO -         month area       industry  electricity standard_area macro_area
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西
2025-09-03 08:14:58,387 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 08:14:58,387 - __main__ - INFO - 列数: 4
2025-09-03 08:14:58,388 - __main__ - INFO - 行数: 3360
2025-09-03 08:14:58,388 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 08:14:58,388 - __main__ - INFO - 数据样本:
2025-09-03 08:14:58,389 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 08:14:58,389 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 08:14:58,389 - __main__ - INFO - 列数: 4
2025-09-03 08:14:58,389 - __main__ - INFO - 行数: 204
2025-09-03 08:14:58,389 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 08:14:58,389 - __main__ - INFO - 数据样本:
2025-09-03 08:14:58,390 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 08:14:58,391 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 08:14:58,391 - __main__ - INFO - 列数: 7
2025-09-03 08:14:58,391 - __main__ - INFO - 行数: 1812
2025-09-03 08:14:58,391 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 08:14:58,391 - __main__ - INFO - 数据样本:
2025-09-03 08:14:58,393 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴     其他工业
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴     其他工业
2025-09-03 09:10:14,370 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:10:14,372 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:10:14,372 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:10:14,372 - __main__ - INFO - ================================================================================
2025-09-03 09:10:14,372 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 09:10:14,372 - __main__ - INFO - ================================================================================
2025-09-03 09:10:14,372 - __main__ - INFO - 
============================================================
2025-09-03 09:10:14,372 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 09:10:14,372 - __main__ - INFO - ============================================================
2025-09-03 09:10:14,372 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:10:14,372 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:10:14,430 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:10:14,443 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:10:14,444 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 09:10:14,444 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:10:14,444 - __main__ - INFO - 原始数据样本:
2025-09-03 09:10:14,447 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 09:10:14,447 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:10:14,447 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:10:14,447 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:10:14,447 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:10:14,447 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 09:10:14,447 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:10:14,447 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:10:14,447 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:10:14,450 - __main__ - INFO - 
============================================================
2025-09-03 09:10:14,450 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 09:10:14,450 - __main__ - INFO - ============================================================
2025-09-03 09:10:14,450 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:10:14,450 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:10:14,468 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:10:14,585 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:10:14,586 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 09:10:14,586 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:10:14,586 - __main__ - INFO - 原始数据样本:
2025-09-03 09:10:14,587 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 09:10:14,587 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:10:14,589 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:10:14,971 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:10:14,971 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:10:14,971 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 09:10:14,971 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:10:14,971 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:10:14,973 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:10:14,990 - __main__ - INFO - 
============================================================
2025-09-03 09:10:14,990 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene2_off (地市工业能源消费)
2025-09-03 09:10:14,990 - __main__ - INFO - ============================================================
2025-09-03 09:10:14,990 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:10:14,990 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:10:15,009 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:10:15,012 - ecam_calculator.infrastructure.database_reader - ERROR - 读取地市工业能源消费数据失败: Execution failed on sql '
                SELECT 
                    YEAR(year) as year, 
                    area, 
                    item, 
                    `convert`, 
                    method, 
                    energy_type, 
                    value, 
                    unit 
                FROM ecam_in_y_pro_ind_ene2_off 
                ORDER BY year, area
                ': 1054 (42S22): Unknown column 'item' in 'field list'
2025-09-03 09:10:15,012 - __main__ - ERROR - 数据读取失败: 读取地市工业能源消费数据失败: Execution failed on sql '
                SELECT 
                    YEAR(year) as year, 
                    area, 
                    item, 
                    `convert`, 
                    method, 
                    energy_type, 
                    value, 
                    unit 
                FROM ecam_in_y_pro_ind_ene2_off 
                ORDER BY year, area
                ': 1054 (42S22): Unknown column 'item' in 'field list'
2025-09-03 09:10:15,012 - __main__ - INFO - 
============================================================
2025-09-03 09:10:15,012 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 09:10:15,012 - __main__ - INFO - ============================================================
2025-09-03 09:10:15,012 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:10:15,012 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:10:15,030 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:10:15,853 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:10:15,854 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 09:10:15,854 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 09:10:15,854 - __main__ - INFO - 原始数据样本:
2025-09-03 09:10:15,856 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 09:10:15,856 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:10:15,858 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:10:16,569 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:10:16,569 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:10:16,569 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 09:10:16,569 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:10:16,569 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:10:16,572 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:10:16,598 - __main__ - INFO - 
============================================================
2025-09-03 09:10:16,598 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 09:10:16,598 - __main__ - INFO - ============================================================
2025-09-03 09:10:16,598 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:10:16,598 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:10:16,617 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:10:16,657 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:10:16,658 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 09:10:16,658 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:10:16,658 - __main__ - INFO - 原始数据样本:
2025-09-03 09:10:16,659 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 09:10:16,659 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:10:16,659 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:10:16,659 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:10:16,659 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:10:16,659 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 09:10:16,659 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:10:16,659 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:10:16,659 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:10:16,660 - __main__ - INFO - 
============================================================
2025-09-03 09:10:16,660 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 09:10:16,660 - __main__ - INFO - ============================================================
2025-09-03 09:10:16,660 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:10:16,660 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:10:16,678 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:10:16,683 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:10:16,684 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 09:10:16,684 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:10:16,684 - __main__ - INFO - 原始数据样本:
2025-09-03 09:10:16,685 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 09:10:16,685 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:10:16,685 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:10:16,685 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:10:16,685 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:10:16,685 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 09:10:16,685 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:10:16,685 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:10:16,685 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:10:16,686 - __main__ - INFO - 
============================================================
2025-09-03 09:10:16,686 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 09:10:16,686 - __main__ - INFO - ============================================================
2025-09-03 09:10:16,686 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:10:16,686 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:10:16,704 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:10:16,724 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:10:16,725 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 09:10:16,725 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 09:10:16,725 - __main__ - INFO - 原始数据样本:
2025-09-03 09:10:16,726 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 09:10:16,726 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:10:16,726 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:10:16,727 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:10:16,727 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:10:16,727 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 09:10:16,727 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:10:16,727 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:10:16,727 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:10:16,728 - __main__ - INFO - 
================================================================================
2025-09-03 09:10:16,728 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 09:10:16,728 - __main__ - INFO - ================================================================================
2025-09-03 09:10:16,728 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 09:10:16,728 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:10:16,728 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:10:16,728 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:10:16,728 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 09:10:16,728 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:10:16,728 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:10:16,728 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:10:16,728 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 09:10:16,728 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 09:10:16,728 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 09:10:16,728 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:10:16,728 - __main__ - INFO - 状态: ✗ 失败
2025-09-03 09:10:16,728 - __main__ - INFO - 错误: 读取地市工业能源消费数据失败: Execution failed on sql '
                SELECT 
                    YEAR(year) as year, 
                    area, 
                    item, 
                    `convert`, 
                    method, 
                    energy_type, 
                    value, 
                    unit 
                FROM ecam_in_y_pro_ind_ene2_off 
                ORDER BY year, area
                ': 1054 (42S22): Unknown column 'item' in 'field list'
2025-09-03 09:10:16,728 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 09:10:16,729 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:10:16,729 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:10:16,729 - __main__ - INFO - 标准化列数: 4
2025-09-03 09:10:16,729 - __main__ - INFO -   standard_area: 74496/74496 (100.0%)
2025-09-03 09:10:16,729 - __main__ - INFO -   standard_industry: 6457/74496 (8.7%)
2025-09-03 09:10:16,729 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 09:10:16,729 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:10:16,729 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:10:16,729 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:10:16,729 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 09:10:16,729 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:10:16,729 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:10:16,729 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:10:16,729 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 09:10:16,729 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:10:16,729 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:10:16,729 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:10:16,729 - __main__ - INFO - 
总体统计:
2025-09-03 09:10:16,729 - __main__ - INFO - 成功表数: 6/7
2025-09-03 09:10:16,729 - __main__ - INFO - 成功率: 85.7%
2025-09-03 09:10:16,729 - __main__ - INFO - 
================================================================================
2025-09-03 09:10:16,729 - __main__ - INFO - 标准化数据模型
2025-09-03 09:10:16,729 - __main__ - INFO - ================================================================================
2025-09-03 09:10:16,729 - __main__ - INFO - 
标准化列模型:
2025-09-03 09:10:16,729 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 09:10:16,729 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 09:10:16,729 - __main__ - INFO -   standard_industry: 标准化后的标准值
2025-09-03 09:10:16,729 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 09:10:16,729 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 09:10:16,729 - __main__ - INFO - 
层次关系列模型:
2025-09-03 09:10:16,729 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 09:10:16,729 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 09:10:16,729 - __main__ - INFO -   macro_industry: 层次关系值
2025-09-03 09:10:16,729 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 09:10:16,729 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 09:10:16,730 - __main__ - INFO - 
各表标准化模型:
2025-09-03 09:10:16,730 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 09:10:16,730 - __main__ - INFO - 列数: 9
2025-09-03 09:10:16,730 - __main__ - INFO - 行数: 273
2025-09-03 09:10:16,730 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:10:16,730 - __main__ - INFO - 数据样本:
2025-09-03 09:10:16,732 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 09:10:16,732 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:10:16,732 - __main__ - INFO - 列数: 14
2025-09-03 09:10:16,732 - __main__ - INFO - 行数: 25760
2025-09-03 09:10:16,732 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:10:16,732 - __main__ - INFO - 数据样本:
2025-09-03 09:10:16,736 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 09:10:16,736 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:10:16,736 - __main__ - INFO - 列数: 8
2025-09-03 09:10:16,736 - __main__ - INFO - 行数: 74496
2025-09-03 09:10:16,736 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:10:16,736 - __main__ - INFO - 数据样本:
2025-09-03 09:10:16,744 - __main__ - INFO -         month area       industry  electricity standard_area macro_area standard_industry macro_industry
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西               NaN            NaN
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西               NaN            NaN
2025-09-03 09:10:16,744 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 09:10:16,744 - __main__ - INFO - 列数: 4
2025-09-03 09:10:16,744 - __main__ - INFO - 行数: 3360
2025-09-03 09:10:16,744 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:10:16,745 - __main__ - INFO - 数据样本:
2025-09-03 09:10:16,746 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 09:10:16,746 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 09:10:16,746 - __main__ - INFO - 列数: 4
2025-09-03 09:10:16,746 - __main__ - INFO - 行数: 204
2025-09-03 09:10:16,746 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:10:16,746 - __main__ - INFO - 数据样本:
2025-09-03 09:10:16,746 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 09:10:16,746 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 09:10:16,746 - __main__ - INFO - 列数: 7
2025-09-03 09:10:16,747 - __main__ - INFO - 行数: 1812
2025-09-03 09:10:16,747 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:10:16,747 - __main__ - INFO - 数据样本:
2025-09-03 09:10:16,748 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴     其他工业
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴     其他工业
2025-09-03 09:12:12,853 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:12:12,853 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:12:12,853 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:12:12,853 - __main__ - INFO - ================================================================================
2025-09-03 09:12:12,853 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 09:12:12,853 - __main__ - INFO - ================================================================================
2025-09-03 09:12:12,853 - __main__ - INFO - 
============================================================
2025-09-03 09:12:12,853 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 09:12:12,853 - __main__ - INFO - ============================================================
2025-09-03 09:12:12,853 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:12:12,853 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:12:12,891 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:12:12,901 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:12:12,902 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 09:12:12,902 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:12:12,902 - __main__ - INFO - 原始数据样本:
2025-09-03 09:12:12,903 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 09:12:12,903 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:12:12,903 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:12:12,904 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:12:12,904 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:12:12,904 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 09:12:12,904 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:12:12,904 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:12:12,904 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:12:12,905 - __main__ - INFO - 
============================================================
2025-09-03 09:12:12,905 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 09:12:12,905 - __main__ - INFO - ============================================================
2025-09-03 09:12:12,905 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:12:12,905 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:12:12,923 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:12:13,041 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:12:13,042 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 09:12:13,042 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:12:13,042 - __main__ - INFO - 原始数据样本:
2025-09-03 09:12:13,043 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 09:12:13,043 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:12:13,045 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:12:13,423 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:12:13,423 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:12:13,423 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 09:12:13,423 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:12:13,423 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:12:13,425 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:12:13,441 - __main__ - INFO - 
============================================================
2025-09-03 09:12:13,441 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene2_off (地市工业能源消费)
2025-09-03 09:12:13,441 - __main__ - INFO - ============================================================
2025-09-03 09:12:13,441 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:12:13,441 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:12:13,459 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:12:13,977 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:12:13,979 - __main__ - INFO - ✓ 原始数据读取成功: 46535 行
2025-09-03 09:12:13,979 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:12:13,979 - __main__ - INFO - 原始数据样本:
2025-09-03 09:12:13,980 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2  None   大同       全部     实物量                 煤炭  2567.22   万吨
2025-09-03 09:12:13,980 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:12:13,983 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:12:14,520 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:12:14,520 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:12:14,520 - __main__ - INFO - ✓ 标准化完成: 46535 行
2025-09-03 09:12:14,520 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:12:14,520 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:12:14,522 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:12:14,554 - __main__ - INFO - 
============================================================
2025-09-03 09:12:14,554 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 09:12:14,554 - __main__ - INFO - ============================================================
2025-09-03 09:12:14,554 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:12:14,554 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:12:14,572 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:12:15,386 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:12:15,387 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 09:12:15,387 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 09:12:15,387 - __main__ - INFO - 原始数据样本:
2025-09-03 09:12:15,388 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 09:12:15,388 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:12:15,390 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:12:16,074 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:12:16,074 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:12:16,074 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 09:12:16,074 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:12:16,074 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:12:16,077 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:12:16,102 - __main__ - INFO - 
============================================================
2025-09-03 09:12:16,102 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 09:12:16,102 - __main__ - INFO - ============================================================
2025-09-03 09:12:16,102 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:12:16,102 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:12:16,124 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:12:16,165 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:12:16,166 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 09:12:16,166 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:12:16,166 - __main__ - INFO - 原始数据样本:
2025-09-03 09:12:16,167 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 09:12:16,167 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:12:16,167 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:12:16,167 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:12:16,167 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:12:16,167 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 09:12:16,167 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:12:16,167 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:12:16,167 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:12:16,168 - __main__ - INFO - 
============================================================
2025-09-03 09:12:16,168 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 09:12:16,168 - __main__ - INFO - ============================================================
2025-09-03 09:12:16,168 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:12:16,168 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:12:16,188 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:12:16,192 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:12:16,193 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 09:12:16,193 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:12:16,193 - __main__ - INFO - 原始数据样本:
2025-09-03 09:12:16,193 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 09:12:16,193 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:12:16,194 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:12:16,194 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:12:16,194 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:12:16,194 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 09:12:16,194 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:12:16,194 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:12:16,194 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:12:16,194 - __main__ - INFO - 
============================================================
2025-09-03 09:12:16,194 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 09:12:16,194 - __main__ - INFO - ============================================================
2025-09-03 09:12:16,194 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:12:16,194 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:12:16,213 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:12:16,234 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:12:16,234 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 09:12:16,234 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 09:12:16,234 - __main__ - INFO - 原始数据样本:
2025-09-03 09:12:16,235 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 09:12:16,235 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:12:16,235 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:12:16,236 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:12:16,236 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:12:16,236 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 09:12:16,236 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:12:16,236 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:12:16,236 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:12:16,237 - __main__ - INFO - 
================================================================================
2025-09-03 09:12:16,237 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 09:12:16,237 - __main__ - INFO - ================================================================================
2025-09-03 09:12:16,237 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 09:12:16,237 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:12:16,237 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:12:16,237 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:12:16,237 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 09:12:16,238 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:12:16,238 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:12:16,238 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:12:16,238 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 09:12:16,238 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 09:12:16,238 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 09:12:16,238 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:12:16,238 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:12:16,238 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:12:16,238 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:12:16,238 - __main__ - INFO -   standard_area: 46535/46535 (100.0%)
2025-09-03 09:12:16,238 - __main__ - INFO -   standard_industry: 779/46535 (1.7%)
2025-09-03 09:12:16,238 - __main__ - INFO -   standard_energy_type: 26735/46535 (57.5%)
2025-09-03 09:12:16,238 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 09:12:16,238 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:12:16,238 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:12:16,238 - __main__ - INFO - 标准化列数: 4
2025-09-03 09:12:16,238 - __main__ - INFO -   standard_area: 74496/74496 (100.0%)
2025-09-03 09:12:16,238 - __main__ - INFO -   standard_industry: 6457/74496 (8.7%)
2025-09-03 09:12:16,238 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 09:12:16,238 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:12:16,238 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:12:16,238 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:12:16,238 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 09:12:16,238 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:12:16,238 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:12:16,238 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:12:16,238 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 09:12:16,238 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:12:16,239 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:12:16,239 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:12:16,239 - __main__ - INFO - 
总体统计:
2025-09-03 09:12:16,239 - __main__ - INFO - 成功表数: 7/7
2025-09-03 09:12:16,239 - __main__ - INFO - 成功率: 100.0%
2025-09-03 09:12:16,239 - __main__ - INFO - 
================================================================================
2025-09-03 09:12:16,239 - __main__ - INFO - 标准化数据模型
2025-09-03 09:12:16,239 - __main__ - INFO - ================================================================================
2025-09-03 09:12:16,239 - __main__ - INFO - 
标准化列模型:
2025-09-03 09:12:16,239 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 09:12:16,239 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 09:12:16,239 - __main__ - INFO -   standard_industry: 标准化后的标准值
2025-09-03 09:12:16,239 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 09:12:16,239 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 09:12:16,239 - __main__ - INFO - 
层次关系列模型:
2025-09-03 09:12:16,239 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 09:12:16,239 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 09:12:16,239 - __main__ - INFO -   macro_industry: 层次关系值
2025-09-03 09:12:16,239 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 09:12:16,239 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 09:12:16,239 - __main__ - INFO - 
各表标准化模型:
2025-09-03 09:12:16,239 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 09:12:16,239 - __main__ - INFO - 列数: 9
2025-09-03 09:12:16,239 - __main__ - INFO - 行数: 273
2025-09-03 09:12:16,239 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:12:16,239 - __main__ - INFO - 数据样本:
2025-09-03 09:12:16,240 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 09:12:16,241 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:12:16,241 - __main__ - INFO - 列数: 14
2025-09-03 09:12:16,241 - __main__ - INFO - 行数: 25760
2025-09-03 09:12:16,241 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:12:16,241 - __main__ - INFO - 数据样本:
2025-09-03 09:12:16,244 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 09:12:16,244 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:12:16,244 - __main__ - INFO - 列数: 14
2025-09-03 09:12:16,244 - __main__ - INFO - 行数: 46535
2025-09-03 09:12:16,245 - __main__ - INFO - 列名: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:12:16,245 - __main__ - INFO - 数据样本:
2025-09-03 09:12:16,253 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2025-09-03 09:12:16,254 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:12:16,254 - __main__ - INFO - 列数: 8
2025-09-03 09:12:16,254 - __main__ - INFO - 行数: 74496
2025-09-03 09:12:16,254 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:12:16,254 - __main__ - INFO - 数据样本:
2025-09-03 09:12:16,262 - __main__ - INFO -         month area       industry  electricity standard_area macro_area standard_industry macro_industry
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西               NaN            NaN
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西               NaN            NaN
2025-09-03 09:12:16,262 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 09:12:16,262 - __main__ - INFO - 列数: 4
2025-09-03 09:12:16,263 - __main__ - INFO - 行数: 3360
2025-09-03 09:12:16,263 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:12:16,263 - __main__ - INFO - 数据样本:
2025-09-03 09:12:16,264 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 09:12:16,264 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 09:12:16,264 - __main__ - INFO - 列数: 4
2025-09-03 09:12:16,264 - __main__ - INFO - 行数: 204
2025-09-03 09:12:16,264 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:12:16,264 - __main__ - INFO - 数据样本:
2025-09-03 09:12:16,265 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 09:12:16,265 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 09:12:16,265 - __main__ - INFO - 列数: 7
2025-09-03 09:12:16,265 - __main__ - INFO - 行数: 1812
2025-09-03 09:12:16,265 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:12:16,265 - __main__ - INFO - 数据样本:
2025-09-03 09:12:16,266 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴     其他工业
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴     其他工业
2025-09-03 09:19:15,871 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 36 个列标准化器
2025-09-03 09:19:15,871 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:19:15,871 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:19:15,871 - __main__ - INFO - ================================================================================
2025-09-03 09:19:15,871 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 09:19:15,871 - __main__ - INFO - ================================================================================
2025-09-03 09:19:15,871 - __main__ - INFO - 
============================================================
2025-09-03 09:19:15,871 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 09:19:15,871 - __main__ - INFO - ============================================================
2025-09-03 09:19:15,872 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:19:15,872 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:19:15,901 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:19:15,910 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:19:15,911 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 09:19:15,911 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:19:15,911 - __main__ - INFO - 原始数据样本:
2025-09-03 09:19:15,913 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 09:19:15,913 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:19:15,913 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:19:15,913 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:19:15,913 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:19:15,913 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 09:19:15,913 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:19:15,913 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:19:15,913 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:19:15,914 - __main__ - INFO - 
============================================================
2025-09-03 09:19:15,914 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 09:19:15,915 - __main__ - INFO - ============================================================
2025-09-03 09:19:15,915 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:19:15,915 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:19:15,933 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:19:16,064 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:19:16,065 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 09:19:16,065 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:19:16,065 - __main__ - INFO - 原始数据样本:
2025-09-03 09:19:16,067 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 09:19:16,067 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:19:16,068 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:19:16,517 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:19:16,517 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:19:16,517 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 09:19:16,517 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:19:16,517 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:19:16,519 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:19:16,537 - __main__ - INFO - 
============================================================
2025-09-03 09:19:16,537 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene2_off (地市工业能源消费)
2025-09-03 09:19:16,537 - __main__ - INFO - ============================================================
2025-09-03 09:19:16,537 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:19:16,537 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:19:16,558 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:19:17,070 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:19:17,071 - __main__ - INFO - ✓ 原始数据读取成功: 46535 行
2025-09-03 09:19:17,071 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:19:17,071 - __main__ - INFO - 原始数据样本:
2025-09-03 09:19:17,072 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2  None   大同       全部     实物量                 煤炭  2567.22   万吨
2025-09-03 09:19:17,072 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:19:17,076 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:19:17,989 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:19:17,989 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:19:17,989 - __main__ - INFO - ✓ 标准化完成: 46535 行
2025-09-03 09:19:17,989 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:19:17,989 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:19:17,992 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:19:18,028 - __main__ - INFO - 
============================================================
2025-09-03 09:19:18,028 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 09:19:18,028 - __main__ - INFO - ============================================================
2025-09-03 09:19:18,029 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:19:18,029 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:19:18,050 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:19:18,878 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:19:18,879 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 09:19:18,879 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 09:19:18,879 - __main__ - INFO - 原始数据样本:
2025-09-03 09:19:18,880 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 09:19:18,880 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:19:18,883 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:19:20,042 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:19:20,043 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:19:20,043 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 09:19:20,043 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:19:20,043 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:19:20,046 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:19:20,074 - __main__ - INFO - 
============================================================
2025-09-03 09:19:20,074 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 09:19:20,074 - __main__ - INFO - ============================================================
2025-09-03 09:19:20,074 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:19:20,074 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:19:20,095 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:19:20,137 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:19:20,137 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 09:19:20,138 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:19:20,138 - __main__ - INFO - 原始数据样本:
2025-09-03 09:19:20,138 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 09:19:20,138 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:19:20,139 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:19:20,139 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:19:20,139 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:19:20,139 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 09:19:20,139 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:19:20,139 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:19:20,139 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:19:20,140 - __main__ - INFO - 
============================================================
2025-09-03 09:19:20,140 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 09:19:20,140 - __main__ - INFO - ============================================================
2025-09-03 09:19:20,140 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:19:20,140 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:19:20,159 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:19:20,163 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:19:20,163 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 09:19:20,164 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:19:20,164 - __main__ - INFO - 原始数据样本:
2025-09-03 09:19:20,164 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 09:19:20,164 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:19:20,164 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:19:20,165 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:19:20,165 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:19:20,165 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 09:19:20,165 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:19:20,165 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:19:20,165 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:19:20,166 - __main__ - INFO - 
============================================================
2025-09-03 09:19:20,166 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 09:19:20,166 - __main__ - INFO - ============================================================
2025-09-03 09:19:20,166 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:19:20,166 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:19:20,185 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:19:20,206 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:19:20,207 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 09:19:20,208 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 09:19:20,208 - __main__ - INFO - 原始数据样本:
2025-09-03 09:19:20,209 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 09:19:20,209 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:19:20,209 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:19:20,210 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:19:20,210 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:19:20,210 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 09:19:20,210 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:19:20,210 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:19:20,210 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:19:20,211 - __main__ - INFO - 
================================================================================
2025-09-03 09:19:20,211 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 09:19:20,211 - __main__ - INFO - ================================================================================
2025-09-03 09:19:20,211 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 09:19:20,211 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:19:20,211 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:19:20,211 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:19:20,211 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 09:19:20,211 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:19:20,211 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:19:20,211 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:19:20,211 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 09:19:20,211 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 09:19:20,211 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 09:19:20,211 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:19:20,211 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:19:20,211 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:19:20,211 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:19:20,211 - __main__ - INFO -   standard_area: 46535/46535 (100.0%)
2025-09-03 09:19:20,212 - __main__ - INFO -   standard_industry: 32793/46535 (70.5%)
2025-09-03 09:19:20,212 - __main__ - INFO -   standard_energy_type: 26735/46535 (57.5%)
2025-09-03 09:19:20,212 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 09:19:20,212 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:19:20,212 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:19:20,212 - __main__ - INFO - 标准化列数: 4
2025-09-03 09:19:20,212 - __main__ - INFO -   standard_area: 74496/74496 (100.0%)
2025-09-03 09:19:20,212 - __main__ - INFO -   standard_industry: 22618/74496 (30.4%)
2025-09-03 09:19:20,212 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 09:19:20,212 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:19:20,212 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:19:20,212 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:19:20,212 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 09:19:20,212 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:19:20,212 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:19:20,212 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:19:20,212 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 09:19:20,212 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:19:20,212 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:19:20,212 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:19:20,212 - __main__ - INFO - 
总体统计:
2025-09-03 09:19:20,212 - __main__ - INFO - 成功表数: 7/7
2025-09-03 09:19:20,212 - __main__ - INFO - 成功率: 100.0%
2025-09-03 09:19:20,212 - __main__ - INFO - 
================================================================================
2025-09-03 09:19:20,212 - __main__ - INFO - 标准化数据模型
2025-09-03 09:19:20,212 - __main__ - INFO - ================================================================================
2025-09-03 09:19:20,212 - __main__ - INFO - 
标准化列模型:
2025-09-03 09:19:20,212 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 09:19:20,212 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 09:19:20,212 - __main__ - INFO -   standard_industry: 标准化后的标准值
2025-09-03 09:19:20,212 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 09:19:20,212 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 09:19:20,212 - __main__ - INFO - 
层次关系列模型:
2025-09-03 09:19:20,212 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 09:19:20,212 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 09:19:20,212 - __main__ - INFO -   macro_industry: 层次关系值
2025-09-03 09:19:20,212 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 09:19:20,212 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 09:19:20,212 - __main__ - INFO - 
各表标准化模型:
2025-09-03 09:19:20,212 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 09:19:20,213 - __main__ - INFO - 列数: 9
2025-09-03 09:19:20,213 - __main__ - INFO - 行数: 273
2025-09-03 09:19:20,213 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:19:20,213 - __main__ - INFO - 数据样本:
2025-09-03 09:19:20,214 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 09:19:20,214 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:19:20,214 - __main__ - INFO - 列数: 14
2025-09-03 09:19:20,214 - __main__ - INFO - 行数: 25760
2025-09-03 09:19:20,214 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:19:20,214 - __main__ - INFO - 数据样本:
2025-09-03 09:19:20,218 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 09:19:20,218 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:19:20,218 - __main__ - INFO - 列数: 14
2025-09-03 09:19:20,218 - __main__ - INFO - 行数: 46535
2025-09-03 09:19:20,218 - __main__ - INFO - 列名: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:19:20,218 - __main__ - INFO - 数据样本:
2025-09-03 09:19:20,226 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2025-09-03 09:19:20,226 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:19:20,226 - __main__ - INFO - 列数: 8
2025-09-03 09:19:20,226 - __main__ - INFO - 行数: 74496
2025-09-03 09:19:20,226 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:19:20,226 - __main__ - INFO - 数据样本:
2025-09-03 09:19:20,235 - __main__ - INFO -         month area       industry  electricity standard_area macro_area standard_industry macro_industry
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西               NaN            NaN
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西               NaN            NaN
2025-09-03 09:19:20,235 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 09:19:20,235 - __main__ - INFO - 列数: 4
2025-09-03 09:19:20,235 - __main__ - INFO - 行数: 3360
2025-09-03 09:19:20,235 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:19:20,235 - __main__ - INFO - 数据样本:
2025-09-03 09:19:20,236 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 09:19:20,236 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 09:19:20,237 - __main__ - INFO - 列数: 4
2025-09-03 09:19:20,237 - __main__ - INFO - 行数: 204
2025-09-03 09:19:20,237 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:19:20,237 - __main__ - INFO - 数据样本:
2025-09-03 09:19:20,238 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 09:19:20,238 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 09:19:20,238 - __main__ - INFO - 列数: 7
2025-09-03 09:19:20,238 - __main__ - INFO - 行数: 1812
2025-09-03 09:19:20,238 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:19:20,238 - __main__ - INFO - 数据样本:
2025-09-03 09:19:20,239 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴     煤炭行业
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴     电力行业
2025-09-03 09:29:39,520 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:29:39,520 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:29:39,520 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:29:39,521 - __main__ - INFO - ================================================================================
2025-09-03 09:29:39,521 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 09:29:39,521 - __main__ - INFO - ================================================================================
2025-09-03 09:29:39,521 - __main__ - INFO - 
============================================================
2025-09-03 09:29:39,521 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 09:29:39,521 - __main__ - INFO - ============================================================
2025-09-03 09:29:39,521 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:29:39,521 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:29:39,575 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:29:39,587 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:29:39,588 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 09:29:39,588 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:29:39,588 - __main__ - INFO - 原始数据样本:
2025-09-03 09:29:39,592 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 09:29:39,592 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:29:39,592 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:29:39,592 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:29:39,592 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:29:39,592 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 09:29:39,592 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:29:39,592 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:29:39,592 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:29:39,595 - __main__ - INFO - 
============================================================
2025-09-03 09:29:39,595 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 09:29:39,595 - __main__ - INFO - ============================================================
2025-09-03 09:29:39,595 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:29:39,595 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:29:39,617 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:29:39,739 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:29:39,740 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 09:29:39,740 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:29:39,740 - __main__ - INFO - 原始数据样本:
2025-09-03 09:29:39,741 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 09:29:39,741 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:29:39,743 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:29:40,126 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:29:40,126 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:29:40,126 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 09:29:40,126 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:29:40,126 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:29:40,128 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:29:40,145 - __main__ - INFO - 
============================================================
2025-09-03 09:29:40,145 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene2_off (地市工业能源消费)
2025-09-03 09:29:40,145 - __main__ - INFO - ============================================================
2025-09-03 09:29:40,145 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:29:40,146 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:29:40,169 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:29:40,676 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:29:40,677 - __main__ - INFO - ✓ 原始数据读取成功: 46535 行
2025-09-03 09:29:40,677 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:29:40,677 - __main__ - INFO - 原始数据样本:
2025-09-03 09:29:40,678 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2  None   大同       全部     实物量                 煤炭  2567.22   万吨
2025-09-03 09:29:40,678 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:29:40,682 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:29:41,228 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:29:41,228 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:29:41,228 - __main__ - INFO - ✓ 标准化完成: 46535 行
2025-09-03 09:29:41,228 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:29:41,228 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:29:41,230 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:29:41,260 - __main__ - INFO - 
============================================================
2025-09-03 09:29:41,261 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 09:29:41,261 - __main__ - INFO - ============================================================
2025-09-03 09:29:41,261 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:29:41,261 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:29:41,283 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:29:42,110 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:29:42,111 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 09:29:42,111 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 09:29:42,111 - __main__ - INFO - 原始数据样本:
2025-09-03 09:29:42,113 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 09:29:42,113 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:29:42,116 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:29:42,819 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:29:42,819 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:29:42,819 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 09:29:42,819 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:29:42,819 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:29:42,822 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:29:42,850 - __main__ - INFO - 
============================================================
2025-09-03 09:29:42,851 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 09:29:42,851 - __main__ - INFO - ============================================================
2025-09-03 09:29:42,851 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:29:42,851 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:29:42,876 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:29:42,917 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:29:42,917 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 09:29:42,917 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:29:42,917 - __main__ - INFO - 原始数据样本:
2025-09-03 09:29:42,918 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 09:29:42,918 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:29:42,918 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:29:42,918 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:29:42,918 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:29:42,918 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 09:29:42,918 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:29:42,918 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:29:42,918 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:29:42,919 - __main__ - INFO - 
============================================================
2025-09-03 09:29:42,919 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 09:29:42,919 - __main__ - INFO - ============================================================
2025-09-03 09:29:42,919 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:29:42,919 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:29:42,938 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:29:42,943 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:29:42,943 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 09:29:42,943 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:29:42,943 - __main__ - INFO - 原始数据样本:
2025-09-03 09:29:42,944 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 09:29:42,944 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:29:42,944 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:29:42,944 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:29:42,944 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:29:42,945 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 09:29:42,945 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:29:42,945 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:29:42,945 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:29:42,946 - __main__ - INFO - 
============================================================
2025-09-03 09:29:42,946 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 09:29:42,946 - __main__ - INFO - ============================================================
2025-09-03 09:29:42,946 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:29:42,946 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:29:42,965 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:29:42,986 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:29:42,987 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 09:29:42,987 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 09:29:42,987 - __main__ - INFO - 原始数据样本:
2025-09-03 09:29:42,988 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 09:29:42,988 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:29:42,989 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:29:42,989 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:29:42,989 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:29:42,989 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 09:29:42,989 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:29:42,989 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:29:42,989 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:29:42,990 - __main__ - INFO - 
================================================================================
2025-09-03 09:29:42,990 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 09:29:42,990 - __main__ - INFO - ================================================================================
2025-09-03 09:29:42,991 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 09:29:42,991 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:29:42,991 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:29:42,991 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:29:42,991 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 09:29:42,991 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:29:42,991 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:29:42,991 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:29:42,991 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 09:29:42,991 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 09:29:42,991 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 09:29:42,991 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:29:42,991 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:29:42,991 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:29:42,991 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:29:42,991 - __main__ - INFO -   standard_area: 46535/46535 (100.0%)
2025-09-03 09:29:42,991 - __main__ - INFO -   standard_industry: 779/46535 (1.7%)
2025-09-03 09:29:42,991 - __main__ - INFO -   standard_energy_type: 26735/46535 (57.5%)
2025-09-03 09:29:42,991 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 09:29:42,991 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:29:42,991 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:29:42,991 - __main__ - INFO - 标准化列数: 4
2025-09-03 09:29:42,991 - __main__ - INFO -   standard_area: 74496/74496 (100.0%)
2025-09-03 09:29:42,991 - __main__ - INFO -   standard_industry: 6457/74496 (8.7%)
2025-09-03 09:29:42,991 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 09:29:42,991 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:29:42,991 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:29:42,991 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:29:42,991 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 09:29:42,991 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:29:42,991 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:29:42,991 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:29:42,992 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 09:29:42,992 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:29:42,992 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:29:42,992 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:29:42,992 - __main__ - INFO - 
总体统计:
2025-09-03 09:29:42,992 - __main__ - INFO - 成功表数: 7/7
2025-09-03 09:29:42,992 - __main__ - INFO - 成功率: 100.0%
2025-09-03 09:29:42,992 - __main__ - INFO - 
================================================================================
2025-09-03 09:29:42,992 - __main__ - INFO - 标准化数据模型
2025-09-03 09:29:42,992 - __main__ - INFO - ================================================================================
2025-09-03 09:29:42,992 - __main__ - INFO - 
标准化列模型:
2025-09-03 09:29:42,992 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 09:29:42,992 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 09:29:42,992 - __main__ - INFO -   standard_industry: 标准化后的标准值
2025-09-03 09:29:42,992 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 09:29:42,992 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 09:29:42,992 - __main__ - INFO - 
层次关系列模型:
2025-09-03 09:29:42,992 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 09:29:42,992 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 09:29:42,992 - __main__ - INFO -   macro_industry: 层次关系值
2025-09-03 09:29:42,992 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 09:29:42,992 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 09:29:42,992 - __main__ - INFO - 
各表标准化模型:
2025-09-03 09:29:42,992 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 09:29:42,992 - __main__ - INFO - 列数: 9
2025-09-03 09:29:42,992 - __main__ - INFO - 行数: 273
2025-09-03 09:29:42,992 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:29:42,992 - __main__ - INFO - 数据样本:
2025-09-03 09:29:42,995 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 09:29:42,995 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:29:42,995 - __main__ - INFO - 列数: 14
2025-09-03 09:29:42,995 - __main__ - INFO - 行数: 25760
2025-09-03 09:29:42,995 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:29:42,995 - __main__ - INFO - 数据样本:
2025-09-03 09:29:42,999 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 09:29:43,000 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:29:43,000 - __main__ - INFO - 列数: 14
2025-09-03 09:29:43,000 - __main__ - INFO - 行数: 46535
2025-09-03 09:29:43,000 - __main__ - INFO - 列名: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:29:43,000 - __main__ - INFO - 数据样本:
2025-09-03 09:29:43,008 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2025-09-03 09:29:43,008 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:29:43,008 - __main__ - INFO - 列数: 8
2025-09-03 09:29:43,008 - __main__ - INFO - 行数: 74496
2025-09-03 09:29:43,008 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:29:43,008 - __main__ - INFO - 数据样本:
2025-09-03 09:29:43,017 - __main__ - INFO -         month area       industry  electricity standard_area macro_area standard_industry macro_industry
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西               NaN            NaN
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西               NaN            NaN
2025-09-03 09:29:43,017 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 09:29:43,017 - __main__ - INFO - 列数: 4
2025-09-03 09:29:43,017 - __main__ - INFO - 行数: 3360
2025-09-03 09:29:43,017 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:29:43,017 - __main__ - INFO - 数据样本:
2025-09-03 09:29:43,018 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 09:29:43,018 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 09:29:43,018 - __main__ - INFO - 列数: 4
2025-09-03 09:29:43,018 - __main__ - INFO - 行数: 204
2025-09-03 09:29:43,018 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:29:43,018 - __main__ - INFO - 数据样本:
2025-09-03 09:29:43,019 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 09:29:43,019 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 09:29:43,019 - __main__ - INFO - 列数: 7
2025-09-03 09:29:43,019 - __main__ - INFO - 行数: 1812
2025-09-03 09:29:43,019 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:29:43,019 - __main__ - INFO - 数据样本:
2025-09-03 09:29:43,020 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴     其他工业
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴     其他工业
2025-09-03 09:32:23,377 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 33 个列标准化器
2025-09-03 09:32:23,377 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:32:23,378 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:32:23,378 - __main__ - INFO - ================================================================================
2025-09-03 09:32:23,378 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 09:32:23,378 - __main__ - INFO - ================================================================================
2025-09-03 09:32:23,378 - __main__ - INFO - 
============================================================
2025-09-03 09:32:23,378 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 09:32:23,378 - __main__ - INFO - ============================================================
2025-09-03 09:32:23,378 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:32:23,378 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:32:23,404 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:32:23,413 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:32:23,414 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 09:32:23,414 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:32:23,414 - __main__ - INFO - 原始数据样本:
2025-09-03 09:32:23,417 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 09:32:23,417 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:32:23,417 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:32:23,417 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:32:23,417 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:32:23,417 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 09:32:23,417 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:32:23,417 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:32:23,417 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:32:23,418 - __main__ - INFO - 
============================================================
2025-09-03 09:32:23,418 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 09:32:23,418 - __main__ - INFO - ============================================================
2025-09-03 09:32:23,418 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:32:23,418 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:32:23,494 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:32:23,744 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:32:23,745 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 09:32:23,745 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:32:23,745 - __main__ - INFO - 原始数据样本:
2025-09-03 09:32:23,746 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 09:32:23,746 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:32:23,748 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:32:24,153 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:32:24,154 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:32:24,154 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 09:32:24,154 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:32:24,154 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:32:24,156 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:32:24,172 - __main__ - INFO - 
============================================================
2025-09-03 09:32:24,172 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene2_off (地市工业能源消费)
2025-09-03 09:32:24,172 - __main__ - INFO - ============================================================
2025-09-03 09:32:24,172 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:32:24,172 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:32:24,191 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:32:24,683 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:32:24,684 - __main__ - INFO - ✓ 原始数据读取成功: 46535 行
2025-09-03 09:32:24,684 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:32:24,684 - __main__ - INFO - 原始数据样本:
2025-09-03 09:32:24,685 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2  None   大同       全部     实物量                 煤炭  2567.22   万吨
2025-09-03 09:32:24,685 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:32:24,689 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:32:25,215 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:32:25,216 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:32:25,216 - __main__ - INFO - ✓ 标准化完成: 46535 行
2025-09-03 09:32:25,216 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:32:25,216 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:32:25,218 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:32:25,248 - __main__ - INFO - 
============================================================
2025-09-03 09:32:25,248 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 09:32:25,249 - __main__ - INFO - ============================================================
2025-09-03 09:32:25,249 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:32:25,249 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:32:25,270 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:32:26,073 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:32:26,074 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 09:32:26,074 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 09:32:26,074 - __main__ - INFO - 原始数据样本:
2025-09-03 09:32:26,075 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 09:32:26,075 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:32:26,077 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:32:26,771 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:32:26,771 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:32:26,771 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 09:32:26,771 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:32:26,771 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:32:26,773 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:32:26,801 - __main__ - INFO - 
============================================================
2025-09-03 09:32:26,802 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 09:32:26,802 - __main__ - INFO - ============================================================
2025-09-03 09:32:26,802 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:32:26,802 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:32:26,820 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:32:26,858 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:32:26,859 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 09:32:26,859 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:32:26,859 - __main__ - INFO - 原始数据样本:
2025-09-03 09:32:26,860 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 09:32:26,860 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:32:26,860 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:32:26,860 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:32:26,860 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:32:26,860 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 09:32:26,860 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:32:26,860 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:32:26,860 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:32:26,861 - __main__ - INFO - 
============================================================
2025-09-03 09:32:26,861 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 09:32:26,861 - __main__ - INFO - ============================================================
2025-09-03 09:32:26,861 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:32:26,861 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:32:26,880 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:32:26,884 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:32:26,885 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 09:32:26,885 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:32:26,885 - __main__ - INFO - 原始数据样本:
2025-09-03 09:32:26,886 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 09:32:26,886 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:32:26,886 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:32:26,886 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:32:26,886 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:32:26,886 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 09:32:26,886 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:32:26,886 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:32:26,886 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:32:26,886 - __main__ - INFO - 
============================================================
2025-09-03 09:32:26,886 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 09:32:26,886 - __main__ - INFO - ============================================================
2025-09-03 09:32:26,886 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:32:26,886 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:32:26,905 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:32:26,925 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:32:26,926 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 09:32:26,926 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 09:32:26,926 - __main__ - INFO - 原始数据样本:
2025-09-03 09:32:26,927 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 09:32:26,927 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:32:26,927 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:32:26,927 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:32:26,928 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:32:26,928 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 09:32:26,928 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:32:26,928 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:32:26,928 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:32:26,929 - __main__ - INFO - 
================================================================================
2025-09-03 09:32:26,929 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 09:32:26,929 - __main__ - INFO - ================================================================================
2025-09-03 09:32:26,929 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 09:32:26,929 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:32:26,929 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:32:26,929 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:32:26,929 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 09:32:26,929 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:32:26,929 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:32:26,929 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:32:26,929 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 09:32:26,929 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 09:32:26,929 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 09:32:26,929 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:32:26,929 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:32:26,929 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:32:26,929 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:32:26,929 - __main__ - INFO -   standard_area: 46535/46535 (100.0%)
2025-09-03 09:32:26,929 - __main__ - INFO -   standard_industry: 779/46535 (1.7%)
2025-09-03 09:32:26,929 - __main__ - INFO -   standard_energy_type: 26735/46535 (57.5%)
2025-09-03 09:32:26,929 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 09:32:26,929 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:32:26,929 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:32:26,929 - __main__ - INFO - 标准化列数: 4
2025-09-03 09:32:26,929 - __main__ - INFO -   standard_area: 74496/74496 (100.0%)
2025-09-03 09:32:26,929 - __main__ - INFO -   standard_industry: 6457/74496 (8.7%)
2025-09-03 09:32:26,929 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 09:32:26,929 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:32:26,929 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:32:26,929 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:32:26,929 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 09:32:26,929 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:32:26,930 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:32:26,930 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:32:26,930 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 09:32:26,930 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:32:26,930 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:32:26,930 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:32:26,930 - __main__ - INFO - 
总体统计:
2025-09-03 09:32:26,930 - __main__ - INFO - 成功表数: 7/7
2025-09-03 09:32:26,930 - __main__ - INFO - 成功率: 100.0%
2025-09-03 09:32:26,930 - __main__ - INFO - 
================================================================================
2025-09-03 09:32:26,930 - __main__ - INFO - 标准化数据模型
2025-09-03 09:32:26,930 - __main__ - INFO - ================================================================================
2025-09-03 09:32:26,930 - __main__ - INFO - 
标准化列模型:
2025-09-03 09:32:26,930 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 09:32:26,930 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 09:32:26,930 - __main__ - INFO -   standard_industry: 标准化后的标准值
2025-09-03 09:32:26,930 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 09:32:26,930 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 09:32:26,930 - __main__ - INFO - 
层次关系列模型:
2025-09-03 09:32:26,930 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 09:32:26,930 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 09:32:26,930 - __main__ - INFO -   macro_industry: 层次关系值
2025-09-03 09:32:26,930 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 09:32:26,930 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 09:32:26,930 - __main__ - INFO - 
各表标准化模型:
2025-09-03 09:32:26,930 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 09:32:26,930 - __main__ - INFO - 列数: 9
2025-09-03 09:32:26,930 - __main__ - INFO - 行数: 273
2025-09-03 09:32:26,930 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:32:26,930 - __main__ - INFO - 数据样本:
2025-09-03 09:32:26,932 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 09:32:26,932 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:32:26,932 - __main__ - INFO - 列数: 14
2025-09-03 09:32:26,932 - __main__ - INFO - 行数: 25760
2025-09-03 09:32:26,932 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:32:26,932 - __main__ - INFO - 数据样本:
2025-09-03 09:32:26,936 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 09:32:26,936 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:32:26,936 - __main__ - INFO - 列数: 14
2025-09-03 09:32:26,936 - __main__ - INFO - 行数: 46535
2025-09-03 09:32:26,936 - __main__ - INFO - 列名: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:32:26,936 - __main__ - INFO - 数据样本:
2025-09-03 09:32:26,945 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2025-09-03 09:32:26,945 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:32:26,945 - __main__ - INFO - 列数: 8
2025-09-03 09:32:26,946 - __main__ - INFO - 行数: 74496
2025-09-03 09:32:26,946 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:32:26,946 - __main__ - INFO - 数据样本:
2025-09-03 09:32:26,955 - __main__ - INFO -         month area       industry  electricity standard_area macro_area standard_industry macro_industry
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西               NaN            NaN
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西               NaN            NaN
2025-09-03 09:32:26,955 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 09:32:26,955 - __main__ - INFO - 列数: 4
2025-09-03 09:32:26,955 - __main__ - INFO - 行数: 3360
2025-09-03 09:32:26,955 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:32:26,955 - __main__ - INFO - 数据样本:
2025-09-03 09:32:26,956 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 09:32:26,956 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 09:32:26,956 - __main__ - INFO - 列数: 4
2025-09-03 09:32:26,956 - __main__ - INFO - 行数: 204
2025-09-03 09:32:26,956 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:32:26,957 - __main__ - INFO - 数据样本:
2025-09-03 09:32:26,958 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 09:32:26,958 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 09:32:26,958 - __main__ - INFO - 列数: 7
2025-09-03 09:32:26,958 - __main__ - INFO - 行数: 1812
2025-09-03 09:32:26,958 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:32:26,958 - __main__ - INFO - 数据样本:
2025-09-03 09:32:26,960 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴       煤炭
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴       电力
2025-09-03 09:36:03,861 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:36:03,862 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:36:03,862 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:36:03,862 - __main__ - INFO - ================================================================================
2025-09-03 09:36:03,862 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 09:36:03,862 - __main__ - INFO - ================================================================================
2025-09-03 09:36:03,862 - __main__ - INFO - 
============================================================
2025-09-03 09:36:03,862 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 09:36:03,862 - __main__ - INFO - ============================================================
2025-09-03 09:36:03,862 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:36:03,862 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:36:03,915 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:36:03,928 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:36:03,929 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 09:36:03,929 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:36:03,929 - __main__ - INFO - 原始数据样本:
2025-09-03 09:36:03,932 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 09:36:03,932 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:36:03,932 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:36:03,932 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:36:03,932 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:36:03,932 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 09:36:03,932 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:36:03,932 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:36:03,932 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:36:03,935 - __main__ - INFO - 
============================================================
2025-09-03 09:36:03,935 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 09:36:03,935 - __main__ - INFO - ============================================================
2025-09-03 09:36:03,935 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:36:03,935 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:36:03,955 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:36:04,088 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:36:04,089 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 09:36:04,089 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:36:04,090 - __main__ - INFO - 原始数据样本:
2025-09-03 09:36:04,091 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 09:36:04,091 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:36:04,092 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:36:04,476 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:36:04,476 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:36:04,476 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 09:36:04,476 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:36:04,476 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:36:04,478 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:36:04,509 - __main__ - INFO - 
============================================================
2025-09-03 09:36:04,509 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene2_off (地市工业能源消费)
2025-09-03 09:36:04,509 - __main__ - INFO - ============================================================
2025-09-03 09:36:04,509 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:36:04,509 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:36:04,529 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:36:05,033 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:36:05,034 - __main__ - INFO - ✓ 原始数据读取成功: 46535 行
2025-09-03 09:36:05,034 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:36:05,035 - __main__ - INFO - 原始数据样本:
2025-09-03 09:36:05,036 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2  None   大同       全部     实物量                 煤炭  2567.22   万吨
2025-09-03 09:36:05,036 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:36:05,040 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:36:05,611 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:36:05,611 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:36:05,611 - __main__ - INFO - ✓ 标准化完成: 46535 行
2025-09-03 09:36:05,611 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:36:05,611 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:36:05,614 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:36:05,646 - __main__ - INFO - 
============================================================
2025-09-03 09:36:05,646 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 09:36:05,646 - __main__ - INFO - ============================================================
2025-09-03 09:36:05,646 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:36:05,646 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:36:05,667 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:36:06,501 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:36:06,502 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 09:36:06,502 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 09:36:06,502 - __main__ - INFO - 原始数据样本:
2025-09-03 09:36:06,503 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 09:36:06,503 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:36:06,505 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:36:07,284 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:36:07,284 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:36:07,284 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 09:36:07,284 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:36:07,284 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:36:07,287 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:36:07,315 - __main__ - INFO - 
============================================================
2025-09-03 09:36:07,315 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 09:36:07,315 - __main__ - INFO - ============================================================
2025-09-03 09:36:07,315 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:36:07,315 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:36:07,333 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:36:07,371 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:36:07,372 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 09:36:07,372 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:36:07,372 - __main__ - INFO - 原始数据样本:
2025-09-03 09:36:07,372 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 09:36:07,373 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:36:07,373 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:36:07,373 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:36:07,373 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:36:07,373 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 09:36:07,373 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:36:07,373 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:36:07,373 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:36:07,374 - __main__ - INFO - 
============================================================
2025-09-03 09:36:07,374 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 09:36:07,374 - __main__ - INFO - ============================================================
2025-09-03 09:36:07,374 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:36:07,374 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:36:07,392 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:36:07,396 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:36:07,396 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 09:36:07,396 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:36:07,396 - __main__ - INFO - 原始数据样本:
2025-09-03 09:36:07,397 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 09:36:07,397 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:36:07,397 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:36:07,397 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:36:07,397 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:36:07,397 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 09:36:07,397 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:36:07,397 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:36:07,397 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:36:07,398 - __main__ - INFO - 
============================================================
2025-09-03 09:36:07,398 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 09:36:07,398 - __main__ - INFO - ============================================================
2025-09-03 09:36:07,398 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:36:07,398 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:36:07,415 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:36:07,435 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:36:07,436 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 09:36:07,436 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 09:36:07,436 - __main__ - INFO - 原始数据样本:
2025-09-03 09:36:07,437 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 09:36:07,437 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:36:07,437 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:36:07,438 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:36:07,438 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:36:07,438 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 09:36:07,438 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:36:07,438 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:36:07,438 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:36:07,439 - __main__ - INFO - 
================================================================================
2025-09-03 09:36:07,439 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 09:36:07,439 - __main__ - INFO - ================================================================================
2025-09-03 09:36:07,439 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 09:36:07,439 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:36:07,439 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:36:07,439 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:36:07,439 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 09:36:07,439 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:36:07,439 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:36:07,439 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:36:07,439 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 09:36:07,439 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 09:36:07,439 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 09:36:07,439 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:36:07,439 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:36:07,439 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:36:07,439 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:36:07,439 - __main__ - INFO -   standard_area: 46535/46535 (100.0%)
2025-09-03 09:36:07,439 - __main__ - INFO -   standard_industry: 17565/46535 (37.7%)
2025-09-03 09:36:07,439 - __main__ - INFO -   standard_energy_type: 26735/46535 (57.5%)
2025-09-03 09:36:07,439 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 09:36:07,439 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:36:07,439 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:36:07,439 - __main__ - INFO - 标准化列数: 4
2025-09-03 09:36:07,439 - __main__ - INFO -   standard_area: 74496/74496 (100.0%)
2025-09-03 09:36:07,439 - __main__ - INFO -   standard_industry: 20097/74496 (27.0%)
2025-09-03 09:36:07,439 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 09:36:07,439 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:36:07,439 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:36:07,440 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:36:07,440 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 09:36:07,440 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:36:07,440 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:36:07,440 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:36:07,440 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 09:36:07,440 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:36:07,440 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:36:07,440 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:36:07,440 - __main__ - INFO - 
总体统计:
2025-09-03 09:36:07,440 - __main__ - INFO - 成功表数: 7/7
2025-09-03 09:36:07,440 - __main__ - INFO - 成功率: 100.0%
2025-09-03 09:36:07,440 - __main__ - INFO - 
================================================================================
2025-09-03 09:36:07,440 - __main__ - INFO - 标准化数据模型
2025-09-03 09:36:07,440 - __main__ - INFO - ================================================================================
2025-09-03 09:36:07,440 - __main__ - INFO - 
标准化列模型:
2025-09-03 09:36:07,440 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 09:36:07,440 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 09:36:07,440 - __main__ - INFO -   standard_industry: 标准化后的标准值
2025-09-03 09:36:07,440 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 09:36:07,440 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 09:36:07,440 - __main__ - INFO - 
层次关系列模型:
2025-09-03 09:36:07,440 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 09:36:07,440 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 09:36:07,440 - __main__ - INFO -   macro_industry: 层次关系值
2025-09-03 09:36:07,440 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 09:36:07,440 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 09:36:07,440 - __main__ - INFO - 
各表标准化模型:
2025-09-03 09:36:07,440 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 09:36:07,440 - __main__ - INFO - 列数: 9
2025-09-03 09:36:07,440 - __main__ - INFO - 行数: 273
2025-09-03 09:36:07,440 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:36:07,440 - __main__ - INFO - 数据样本:
2025-09-03 09:36:07,443 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 09:36:07,443 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:36:07,443 - __main__ - INFO - 列数: 14
2025-09-03 09:36:07,443 - __main__ - INFO - 行数: 25760
2025-09-03 09:36:07,443 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:36:07,443 - __main__ - INFO - 数据样本:
2025-09-03 09:36:07,446 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 09:36:07,446 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:36:07,446 - __main__ - INFO - 列数: 14
2025-09-03 09:36:07,446 - __main__ - INFO - 行数: 46535
2025-09-03 09:36:07,446 - __main__ - INFO - 列名: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:36:07,447 - __main__ - INFO - 数据样本:
2025-09-03 09:36:07,453 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2025-09-03 09:36:07,454 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:36:07,454 - __main__ - INFO - 列数: 8
2025-09-03 09:36:07,454 - __main__ - INFO - 行数: 74496
2025-09-03 09:36:07,454 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:36:07,454 - __main__ - INFO - 数据样本:
2025-09-03 09:36:07,462 - __main__ - INFO -         month area       industry  electricity standard_area macro_area standard_industry macro_industry
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西               NaN            NaN
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西               NaN            NaN
2025-09-03 09:36:07,462 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 09:36:07,462 - __main__ - INFO - 列数: 4
2025-09-03 09:36:07,462 - __main__ - INFO - 行数: 3360
2025-09-03 09:36:07,462 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:36:07,462 - __main__ - INFO - 数据样本:
2025-09-03 09:36:07,463 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 09:36:07,463 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 09:36:07,463 - __main__ - INFO - 列数: 4
2025-09-03 09:36:07,463 - __main__ - INFO - 行数: 204
2025-09-03 09:36:07,463 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:36:07,463 - __main__ - INFO - 数据样本:
2025-09-03 09:36:07,464 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 09:36:07,464 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 09:36:07,464 - __main__ - INFO - 列数: 7
2025-09-03 09:36:07,464 - __main__ - INFO - 行数: 1812
2025-09-03 09:36:07,464 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:36:07,464 - __main__ - INFO - 数据样本:
2025-09-03 09:36:07,465 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴     其他工业
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴     其他工业
2025-09-03 09:39:43,713 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:39:43,714 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:39:43,714 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:39:43,714 - __main__ - INFO - ================================================================================
2025-09-03 09:39:43,714 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 09:39:43,714 - __main__ - INFO - ================================================================================
2025-09-03 09:39:43,714 - __main__ - INFO - 
============================================================
2025-09-03 09:39:43,714 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 09:39:43,714 - __main__ - INFO - ============================================================
2025-09-03 09:39:43,714 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:39:43,714 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:39:43,764 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:39:43,776 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:39:43,777 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 09:39:43,777 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:39:43,777 - __main__ - INFO - 原始数据样本:
2025-09-03 09:39:43,779 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 09:39:43,779 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:39:43,780 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:39:43,780 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:39:43,780 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:39:43,780 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 09:39:43,780 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:39:43,780 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:39:43,780 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:39:43,783 - __main__ - INFO - 
============================================================
2025-09-03 09:39:43,783 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 09:39:43,783 - __main__ - INFO - ============================================================
2025-09-03 09:39:43,783 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:39:43,783 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:39:43,806 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:39:43,917 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:39:43,918 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 09:39:43,918 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:39:43,918 - __main__ - INFO - 原始数据样本:
2025-09-03 09:39:43,919 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 09:39:43,919 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:39:43,921 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:39:44,292 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:39:44,292 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:39:44,292 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 09:39:44,292 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:39:44,292 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:39:44,294 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:39:44,311 - __main__ - INFO - 
============================================================
2025-09-03 09:39:44,311 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene2_off (地市工业能源消费)
2025-09-03 09:39:44,311 - __main__ - INFO - ============================================================
2025-09-03 09:39:44,311 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:39:44,311 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:39:44,341 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:39:44,853 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:39:44,855 - __main__ - INFO - ✓ 原始数据读取成功: 46535 行
2025-09-03 09:39:44,855 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:39:44,855 - __main__ - INFO - 原始数据样本:
2025-09-03 09:39:44,856 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2  None   大同       全部     实物量                 煤炭  2567.22   万吨
2025-09-03 09:39:44,856 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:39:44,859 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:39:45,506 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:39:45,506 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:39:45,506 - __main__ - INFO - ✓ 标准化完成: 46535 行
2025-09-03 09:39:45,506 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:39:45,506 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:39:45,509 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:39:45,540 - __main__ - INFO - 
============================================================
2025-09-03 09:39:45,540 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 09:39:45,540 - __main__ - INFO - ============================================================
2025-09-03 09:39:45,540 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:39:45,540 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:39:45,561 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:39:46,390 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:39:46,392 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 09:39:46,392 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 09:39:46,392 - __main__ - INFO - 原始数据样本:
2025-09-03 09:39:46,393 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 09:39:46,393 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:39:46,396 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:39:47,247 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:39:47,247 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:39:47,247 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 09:39:47,247 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:39:47,247 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:39:47,250 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:39:47,278 - __main__ - INFO - 
============================================================
2025-09-03 09:39:47,278 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 09:39:47,278 - __main__ - INFO - ============================================================
2025-09-03 09:39:47,278 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:39:47,278 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:39:47,297 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:39:47,335 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:39:47,336 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 09:39:47,336 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:39:47,336 - __main__ - INFO - 原始数据样本:
2025-09-03 09:39:47,337 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 09:39:47,337 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:39:47,337 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:39:47,337 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:39:47,337 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:39:47,337 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 09:39:47,337 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:39:47,337 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:39:47,337 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:39:47,338 - __main__ - INFO - 
============================================================
2025-09-03 09:39:47,338 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 09:39:47,338 - __main__ - INFO - ============================================================
2025-09-03 09:39:47,338 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:39:47,338 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:39:47,356 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:39:47,360 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:39:47,361 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 09:39:47,361 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:39:47,361 - __main__ - INFO - 原始数据样本:
2025-09-03 09:39:47,361 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 09:39:47,361 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:39:47,361 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:39:47,361 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:39:47,362 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:39:47,362 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 09:39:47,362 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:39:47,362 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:39:47,362 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:39:47,362 - __main__ - INFO - 
============================================================
2025-09-03 09:39:47,362 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 09:39:47,362 - __main__ - INFO - ============================================================
2025-09-03 09:39:47,362 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:39:47,362 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:39:47,380 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:39:47,400 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:39:47,400 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 09:39:47,400 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 09:39:47,401 - __main__ - INFO - 原始数据样本:
2025-09-03 09:39:47,401 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 09:39:47,402 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:39:47,402 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:39:47,402 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:39:47,402 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:39:47,402 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 09:39:47,402 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:39:47,402 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:39:47,402 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:39:47,403 - __main__ - INFO - 
================================================================================
2025-09-03 09:39:47,403 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 09:39:47,404 - __main__ - INFO - ================================================================================
2025-09-03 09:39:47,404 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 09:39:47,404 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:39:47,404 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:39:47,404 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:39:47,404 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 09:39:47,404 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:39:47,404 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:39:47,404 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:39:47,404 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 09:39:47,404 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 09:39:47,404 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 09:39:47,404 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:39:47,404 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:39:47,404 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:39:47,404 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:39:47,404 - __main__ - INFO -   standard_area: 46535/46535 (100.0%)
2025-09-03 09:39:47,404 - __main__ - INFO -   standard_industry: 16665/46535 (35.8%)
2025-09-03 09:39:47,404 - __main__ - INFO -   standard_energy_type: 26735/46535 (57.5%)
2025-09-03 09:39:47,404 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 09:39:47,404 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:39:47,404 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:39:47,404 - __main__ - INFO - 标准化列数: 4
2025-09-03 09:39:47,404 - __main__ - INFO -   standard_area: 74496/74496 (100.0%)
2025-09-03 09:39:47,404 - __main__ - INFO -   standard_industry: 20097/74496 (27.0%)
2025-09-03 09:39:47,404 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 09:39:47,404 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:39:47,404 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:39:47,404 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:39:47,404 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 09:39:47,404 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:39:47,404 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:39:47,404 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:39:47,404 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 09:39:47,404 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:39:47,404 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:39:47,404 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:39:47,404 - __main__ - INFO - 
总体统计:
2025-09-03 09:39:47,404 - __main__ - INFO - 成功表数: 7/7
2025-09-03 09:39:47,404 - __main__ - INFO - 成功率: 100.0%
2025-09-03 09:39:47,404 - __main__ - INFO - 
================================================================================
2025-09-03 09:39:47,405 - __main__ - INFO - 标准化数据模型
2025-09-03 09:39:47,405 - __main__ - INFO - ================================================================================
2025-09-03 09:39:47,405 - __main__ - INFO - 
标准化列模型:
2025-09-03 09:39:47,405 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 09:39:47,405 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 09:39:47,405 - __main__ - INFO -   standard_industry: 标准化后的标准值
2025-09-03 09:39:47,405 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 09:39:47,405 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 09:39:47,405 - __main__ - INFO - 
层次关系列模型:
2025-09-03 09:39:47,405 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 09:39:47,405 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 09:39:47,405 - __main__ - INFO -   macro_industry: 层次关系值
2025-09-03 09:39:47,405 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 09:39:47,405 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 09:39:47,405 - __main__ - INFO - 
各表标准化模型:
2025-09-03 09:39:47,405 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 09:39:47,405 - __main__ - INFO - 列数: 9
2025-09-03 09:39:47,405 - __main__ - INFO - 行数: 273
2025-09-03 09:39:47,405 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:39:47,405 - __main__ - INFO - 数据样本:
2025-09-03 09:39:47,408 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 09:39:47,408 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:39:47,408 - __main__ - INFO - 列数: 14
2025-09-03 09:39:47,408 - __main__ - INFO - 行数: 25760
2025-09-03 09:39:47,408 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:39:47,408 - __main__ - INFO - 数据样本:
2025-09-03 09:39:47,412 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 09:39:47,412 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:39:47,412 - __main__ - INFO - 列数: 14
2025-09-03 09:39:47,412 - __main__ - INFO - 行数: 46535
2025-09-03 09:39:47,412 - __main__ - INFO - 列名: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:39:47,413 - __main__ - INFO - 数据样本:
2025-09-03 09:39:47,420 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2025-09-03 09:39:47,421 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:39:47,421 - __main__ - INFO - 列数: 8
2025-09-03 09:39:47,421 - __main__ - INFO - 行数: 74496
2025-09-03 09:39:47,421 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:39:47,421 - __main__ - INFO - 数据样本:
2025-09-03 09:39:47,429 - __main__ - INFO -         month area       industry  electricity standard_area macro_area standard_industry macro_industry
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西               NaN            NaN
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西               NaN            NaN
2025-09-03 09:39:47,429 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 09:39:47,429 - __main__ - INFO - 列数: 4
2025-09-03 09:39:47,429 - __main__ - INFO - 行数: 3360
2025-09-03 09:39:47,429 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:39:47,429 - __main__ - INFO - 数据样本:
2025-09-03 09:39:47,430 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 09:39:47,430 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 09:39:47,430 - __main__ - INFO - 列数: 4
2025-09-03 09:39:47,430 - __main__ - INFO - 行数: 204
2025-09-03 09:39:47,431 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:39:47,431 - __main__ - INFO - 数据样本:
2025-09-03 09:39:47,431 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 09:39:47,431 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 09:39:47,431 - __main__ - INFO - 列数: 7
2025-09-03 09:39:47,431 - __main__ - INFO - 行数: 1812
2025-09-03 09:39:47,431 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:39:47,431 - __main__ - INFO - 数据样本:
2025-09-03 09:39:47,432 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴     其他工业
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴     其他工业
2025-09-03 09:41:03,438 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:41:03,439 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:41:03,439 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:41:03,439 - __main__ - INFO - ================================================================================
2025-09-03 09:41:03,439 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 09:41:03,439 - __main__ - INFO - ================================================================================
2025-09-03 09:41:03,439 - __main__ - INFO - 
============================================================
2025-09-03 09:41:03,439 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 09:41:03,439 - __main__ - INFO - ============================================================
2025-09-03 09:41:03,439 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:41:03,439 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:41:03,491 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:41:03,505 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:41:03,506 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 09:41:03,506 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:41:03,506 - __main__ - INFO - 原始数据样本:
2025-09-03 09:41:03,508 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 09:41:03,508 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:41:03,508 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:41:03,508 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:41:03,508 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:41:03,508 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 09:41:03,509 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:41:03,509 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:41:03,509 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:41:03,511 - __main__ - INFO - 
============================================================
2025-09-03 09:41:03,511 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 09:41:03,511 - __main__ - INFO - ============================================================
2025-09-03 09:41:03,511 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:41:03,511 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:41:03,533 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:41:03,647 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:41:03,648 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 09:41:03,649 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:41:03,649 - __main__ - INFO - 原始数据样本:
2025-09-03 09:41:03,650 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 09:41:03,650 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:41:03,651 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:41:04,020 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:41:04,021 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:41:04,021 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 09:41:04,021 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:41:04,021 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:41:04,022 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:41:04,039 - __main__ - INFO - 
============================================================
2025-09-03 09:41:04,040 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene2_off (地市工业能源消费)
2025-09-03 09:41:04,040 - __main__ - INFO - ============================================================
2025-09-03 09:41:04,040 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:41:04,040 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:41:04,062 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:41:04,560 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:41:04,561 - __main__ - INFO - ✓ 原始数据读取成功: 46535 行
2025-09-03 09:41:04,562 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:41:04,562 - __main__ - INFO - 原始数据样本:
2025-09-03 09:41:04,563 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2  None   大同       全部     实物量                 煤炭  2567.22   万吨
2025-09-03 09:41:04,563 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:41:04,566 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:41:05,267 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:41:05,267 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:41:05,268 - __main__ - INFO - ✓ 标准化完成: 46535 行
2025-09-03 09:41:05,268 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:41:05,268 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:41:05,270 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:41:05,303 - __main__ - INFO - 
============================================================
2025-09-03 09:41:05,303 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 09:41:05,303 - __main__ - INFO - ============================================================
2025-09-03 09:41:05,303 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:41:05,303 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:41:05,326 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:41:06,141 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:41:06,142 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 09:41:06,142 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 09:41:06,142 - __main__ - INFO - 原始数据样本:
2025-09-03 09:41:06,143 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 09:41:06,143 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:41:06,146 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:41:07,108 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:41:07,108 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:41:07,108 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 09:41:07,108 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:41:07,108 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:41:07,111 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:41:07,139 - __main__ - INFO - 
============================================================
2025-09-03 09:41:07,139 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 09:41:07,139 - __main__ - INFO - ============================================================
2025-09-03 09:41:07,139 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:41:07,139 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:41:07,163 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:41:07,225 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:41:07,226 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 09:41:07,226 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:41:07,226 - __main__ - INFO - 原始数据样本:
2025-09-03 09:41:07,227 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 09:41:07,227 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:41:07,227 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:41:07,227 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:41:07,227 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:41:07,227 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 09:41:07,227 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:41:07,227 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:41:07,228 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:41:07,228 - __main__ - INFO - 
============================================================
2025-09-03 09:41:07,229 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 09:41:07,229 - __main__ - INFO - ============================================================
2025-09-03 09:41:07,229 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:41:07,229 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:41:07,246 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:41:07,251 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:41:07,251 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 09:41:07,252 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:41:07,252 - __main__ - INFO - 原始数据样本:
2025-09-03 09:41:07,252 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 09:41:07,252 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:41:07,253 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:41:07,253 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:41:07,253 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:41:07,253 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 09:41:07,253 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:41:07,253 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:41:07,253 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:41:07,253 - __main__ - INFO - 
============================================================
2025-09-03 09:41:07,253 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 09:41:07,253 - __main__ - INFO - ============================================================
2025-09-03 09:41:07,253 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:41:07,253 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:41:07,271 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:41:07,290 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:41:07,291 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 09:41:07,291 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 09:41:07,291 - __main__ - INFO - 原始数据样本:
2025-09-03 09:41:07,292 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 09:41:07,292 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:41:07,292 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:41:07,293 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:41:07,293 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:41:07,293 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 09:41:07,293 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:41:07,293 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:41:07,293 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:41:07,294 - __main__ - INFO - 
================================================================================
2025-09-03 09:41:07,294 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 09:41:07,294 - __main__ - INFO - ================================================================================
2025-09-03 09:41:07,294 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 09:41:07,294 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:41:07,294 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:41:07,294 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:41:07,294 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 09:41:07,294 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:41:07,294 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:41:07,294 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:41:07,294 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 09:41:07,294 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 09:41:07,294 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 09:41:07,294 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:41:07,294 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:41:07,294 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:41:07,295 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:41:07,295 - __main__ - INFO -   standard_area: 46535/46535 (100.0%)
2025-09-03 09:41:07,295 - __main__ - INFO -   standard_industry: 14749/46535 (31.7%)
2025-09-03 09:41:07,295 - __main__ - INFO -   standard_energy_type: 26735/46535 (57.5%)
2025-09-03 09:41:07,295 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 09:41:07,295 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:41:07,295 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:41:07,295 - __main__ - INFO - 标准化列数: 4
2025-09-03 09:41:07,295 - __main__ - INFO -   standard_area: 74496/74496 (100.0%)
2025-09-03 09:41:07,295 - __main__ - INFO -   standard_industry: 20064/74496 (26.9%)
2025-09-03 09:41:07,295 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 09:41:07,295 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:41:07,295 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:41:07,295 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:41:07,295 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 09:41:07,295 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:41:07,295 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:41:07,295 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:41:07,295 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 09:41:07,295 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:41:07,295 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:41:07,295 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:41:07,295 - __main__ - INFO - 
总体统计:
2025-09-03 09:41:07,295 - __main__ - INFO - 成功表数: 7/7
2025-09-03 09:41:07,295 - __main__ - INFO - 成功率: 100.0%
2025-09-03 09:41:07,295 - __main__ - INFO - 
================================================================================
2025-09-03 09:41:07,295 - __main__ - INFO - 标准化数据模型
2025-09-03 09:41:07,295 - __main__ - INFO - ================================================================================
2025-09-03 09:41:07,295 - __main__ - INFO - 
标准化列模型:
2025-09-03 09:41:07,295 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 09:41:07,295 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 09:41:07,295 - __main__ - INFO -   standard_industry: 标准化后的标准值
2025-09-03 09:41:07,295 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 09:41:07,295 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 09:41:07,295 - __main__ - INFO - 
层次关系列模型:
2025-09-03 09:41:07,295 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 09:41:07,295 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 09:41:07,295 - __main__ - INFO -   macro_industry: 层次关系值
2025-09-03 09:41:07,295 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 09:41:07,295 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 09:41:07,296 - __main__ - INFO - 
各表标准化模型:
2025-09-03 09:41:07,296 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 09:41:07,296 - __main__ - INFO - 列数: 9
2025-09-03 09:41:07,296 - __main__ - INFO - 行数: 273
2025-09-03 09:41:07,296 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:41:07,296 - __main__ - INFO - 数据样本:
2025-09-03 09:41:07,298 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 09:41:07,298 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:41:07,298 - __main__ - INFO - 列数: 14
2025-09-03 09:41:07,298 - __main__ - INFO - 行数: 25760
2025-09-03 09:41:07,298 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:41:07,298 - __main__ - INFO - 数据样本:
2025-09-03 09:41:07,302 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 09:41:07,303 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:41:07,303 - __main__ - INFO - 列数: 14
2025-09-03 09:41:07,303 - __main__ - INFO - 行数: 46535
2025-09-03 09:41:07,303 - __main__ - INFO - 列名: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:41:07,303 - __main__ - INFO - 数据样本:
2025-09-03 09:41:07,310 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2025-09-03 09:41:07,311 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:41:07,311 - __main__ - INFO - 列数: 8
2025-09-03 09:41:07,311 - __main__ - INFO - 行数: 74496
2025-09-03 09:41:07,311 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:41:07,311 - __main__ - INFO - 数据样本:
2025-09-03 09:41:07,319 - __main__ - INFO -         month area       industry  electricity standard_area macro_area standard_industry macro_industry
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西               NaN            NaN
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西               NaN            NaN
2025-09-03 09:41:07,319 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 09:41:07,319 - __main__ - INFO - 列数: 4
2025-09-03 09:41:07,319 - __main__ - INFO - 行数: 3360
2025-09-03 09:41:07,319 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:41:07,319 - __main__ - INFO - 数据样本:
2025-09-03 09:41:07,320 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 09:41:07,320 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 09:41:07,320 - __main__ - INFO - 列数: 4
2025-09-03 09:41:07,321 - __main__ - INFO - 行数: 204
2025-09-03 09:41:07,321 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:41:07,321 - __main__ - INFO - 数据样本:
2025-09-03 09:41:07,321 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 09:41:07,321 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 09:41:07,321 - __main__ - INFO - 列数: 7
2025-09-03 09:41:07,322 - __main__ - INFO - 行数: 1812
2025-09-03 09:41:07,322 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:41:07,322 - __main__ - INFO - 数据样本:
2025-09-03 09:41:07,323 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴     其他工业
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴     其他工业
2025-09-03 09:46:23,707 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:46:23,709 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:46:23,709 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:46:23,709 - __main__ - INFO - ================================================================================
2025-09-03 09:46:23,709 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 09:46:23,709 - __main__ - INFO - ================================================================================
2025-09-03 09:46:23,709 - __main__ - INFO - 
============================================================
2025-09-03 09:46:23,709 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 09:46:23,709 - __main__ - INFO - ============================================================
2025-09-03 09:46:23,709 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:46:23,709 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:46:23,761 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:46:23,773 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:46:23,774 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 09:46:23,774 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:46:23,774 - __main__ - INFO - 原始数据样本:
2025-09-03 09:46:23,776 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 09:46:23,776 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:46:23,777 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:46:23,777 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:46:23,777 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:46:23,777 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 09:46:23,777 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:46:23,777 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:46:23,777 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:46:23,780 - __main__ - INFO - 
============================================================
2025-09-03 09:46:23,780 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 09:46:23,780 - __main__ - INFO - ============================================================
2025-09-03 09:46:23,780 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:46:23,780 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:46:23,802 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:46:23,920 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:46:23,921 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 09:46:23,921 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:46:23,921 - __main__ - INFO - 原始数据样本:
2025-09-03 09:46:23,923 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 09:46:23,923 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:46:23,924 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:46:24,164 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:46:24,164 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:46:24,164 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 09:46:24,164 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:46:24,164 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:46:24,165 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:46:24,182 - __main__ - INFO - 
============================================================
2025-09-03 09:46:24,182 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene2_off (地市工业能源消费)
2025-09-03 09:46:24,182 - __main__ - INFO - ============================================================
2025-09-03 09:46:24,182 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:46:24,182 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:46:24,202 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:46:24,699 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:46:24,700 - __main__ - INFO - ✓ 原始数据读取成功: 46535 行
2025-09-03 09:46:24,700 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:46:24,700 - __main__ - INFO - 原始数据样本:
2025-09-03 09:46:24,702 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2  None   大同       全部     实物量                 煤炭  2567.22   万吨
2025-09-03 09:46:24,702 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:46:24,706 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:46:25,326 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:46:25,326 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:46:25,326 - __main__ - INFO - ✓ 标准化完成: 46535 行
2025-09-03 09:46:25,326 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:46:25,326 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:46:25,329 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:46:25,361 - __main__ - INFO - 
============================================================
2025-09-03 09:46:25,361 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 09:46:25,361 - __main__ - INFO - ============================================================
2025-09-03 09:46:25,361 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:46:25,361 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:46:25,381 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:46:26,204 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:46:26,205 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 09:46:26,205 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 09:46:26,205 - __main__ - INFO - 原始数据样本:
2025-09-03 09:46:26,206 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 09:46:26,206 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:46:26,208 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:46:27,142 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:46:27,143 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:46:27,143 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 09:46:27,143 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:46:27,143 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:46:27,146 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:46:27,173 - __main__ - INFO - 
============================================================
2025-09-03 09:46:27,174 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 09:46:27,174 - __main__ - INFO - ============================================================
2025-09-03 09:46:27,174 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:46:27,174 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:46:27,195 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:46:27,233 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:46:27,234 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 09:46:27,234 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:46:27,234 - __main__ - INFO - 原始数据样本:
2025-09-03 09:46:27,235 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 09:46:27,235 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:46:27,235 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:46:27,236 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:46:27,236 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:46:27,236 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 09:46:27,236 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:46:27,236 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:46:27,236 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:46:27,237 - __main__ - INFO - 
============================================================
2025-09-03 09:46:27,237 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 09:46:27,237 - __main__ - INFO - ============================================================
2025-09-03 09:46:27,237 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:46:27,237 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:46:27,255 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:46:27,260 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:46:27,261 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 09:46:27,261 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:46:27,261 - __main__ - INFO - 原始数据样本:
2025-09-03 09:46:27,262 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 09:46:27,262 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:46:27,262 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:46:27,262 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:46:27,262 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:46:27,262 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 09:46:27,263 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:46:27,263 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:46:27,263 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:46:27,263 - __main__ - INFO - 
============================================================
2025-09-03 09:46:27,263 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 09:46:27,264 - __main__ - INFO - ============================================================
2025-09-03 09:46:27,264 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:46:27,266 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:46:27,286 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:46:27,341 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:46:27,342 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 09:46:27,342 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 09:46:27,342 - __main__ - INFO - 原始数据样本:
2025-09-03 09:46:27,344 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 09:46:27,345 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:46:27,345 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:46:27,346 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:46:27,346 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:46:27,346 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 09:46:27,346 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:46:27,346 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:46:27,346 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:46:27,347 - __main__ - INFO - 
================================================================================
2025-09-03 09:46:27,347 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 09:46:27,347 - __main__ - INFO - ================================================================================
2025-09-03 09:46:27,347 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 09:46:27,347 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:46:27,347 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:46:27,347 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:46:27,347 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 09:46:27,347 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:46:27,347 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:46:27,347 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:46:27,347 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 09:46:27,347 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 09:46:27,347 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 09:46:27,348 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:46:27,348 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:46:27,348 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:46:27,348 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:46:27,348 - __main__ - INFO -   standard_area: 46535/46535 (100.0%)
2025-09-03 09:46:27,348 - __main__ - INFO -   standard_industry: 12487/46535 (26.8%)
2025-09-03 09:46:27,348 - __main__ - INFO -   standard_energy_type: 26735/46535 (57.5%)
2025-09-03 09:46:27,348 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 09:46:27,348 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:46:27,348 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:46:27,348 - __main__ - INFO - 标准化列数: 4
2025-09-03 09:46:27,348 - __main__ - INFO -   standard_area: 67784/74496 (91.0%)
2025-09-03 09:46:27,348 - __main__ - INFO -   standard_industry: 19382/74496 (26.0%)
2025-09-03 09:46:27,348 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 09:46:27,348 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:46:27,348 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:46:27,348 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:46:27,348 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 09:46:27,348 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:46:27,348 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:46:27,348 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:46:27,348 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 09:46:27,348 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:46:27,348 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:46:27,348 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:46:27,348 - __main__ - INFO - 
总体统计:
2025-09-03 09:46:27,348 - __main__ - INFO - 成功表数: 7/7
2025-09-03 09:46:27,348 - __main__ - INFO - 成功率: 100.0%
2025-09-03 09:46:27,348 - __main__ - INFO - 
================================================================================
2025-09-03 09:46:27,348 - __main__ - INFO - 标准化数据模型
2025-09-03 09:46:27,348 - __main__ - INFO - ================================================================================
2025-09-03 09:46:27,348 - __main__ - INFO - 
标准化列模型:
2025-09-03 09:46:27,348 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 09:46:27,348 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 09:46:27,348 - __main__ - INFO -   standard_industry: 标准化后的标准值
2025-09-03 09:46:27,348 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 09:46:27,348 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 09:46:27,349 - __main__ - INFO - 
层次关系列模型:
2025-09-03 09:46:27,349 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 09:46:27,349 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 09:46:27,349 - __main__ - INFO -   macro_industry: 层次关系值
2025-09-03 09:46:27,349 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 09:46:27,349 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 09:46:27,349 - __main__ - INFO - 
各表标准化模型:
2025-09-03 09:46:27,349 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 09:46:27,349 - __main__ - INFO - 列数: 9
2025-09-03 09:46:27,349 - __main__ - INFO - 行数: 273
2025-09-03 09:46:27,349 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:46:27,349 - __main__ - INFO - 数据样本:
2025-09-03 09:46:27,352 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 09:46:27,352 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:46:27,352 - __main__ - INFO - 列数: 14
2025-09-03 09:46:27,352 - __main__ - INFO - 行数: 25760
2025-09-03 09:46:27,352 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:46:27,352 - __main__ - INFO - 数据样本:
2025-09-03 09:46:27,357 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 09:46:27,358 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:46:27,358 - __main__ - INFO - 列数: 14
2025-09-03 09:46:27,358 - __main__ - INFO - 行数: 46535
2025-09-03 09:46:27,358 - __main__ - INFO - 列名: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:46:27,358 - __main__ - INFO - 数据样本:
2025-09-03 09:46:27,369 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2025-09-03 09:46:27,369 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:46:27,370 - __main__ - INFO - 列数: 8
2025-09-03 09:46:27,370 - __main__ - INFO - 行数: 74496
2025-09-03 09:46:27,370 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:46:27,370 - __main__ - INFO - 数据样本:
2025-09-03 09:46:27,380 - __main__ - INFO -         month area       industry  electricity standard_area macro_area standard_industry macro_industry
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西               NaN            NaN
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西               NaN            NaN
2025-09-03 09:46:27,380 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 09:46:27,380 - __main__ - INFO - 列数: 4
2025-09-03 09:46:27,380 - __main__ - INFO - 行数: 3360
2025-09-03 09:46:27,380 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:46:27,380 - __main__ - INFO - 数据样本:
2025-09-03 09:46:27,381 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 09:46:27,382 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 09:46:27,382 - __main__ - INFO - 列数: 4
2025-09-03 09:46:27,382 - __main__ - INFO - 行数: 204
2025-09-03 09:46:27,382 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:46:27,382 - __main__ - INFO - 数据样本:
2025-09-03 09:46:27,382 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 09:46:27,383 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 09:46:27,383 - __main__ - INFO - 列数: 7
2025-09-03 09:46:27,383 - __main__ - INFO - 行数: 1812
2025-09-03 09:46:27,383 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:46:27,383 - __main__ - INFO - 数据样本:
2025-09-03 09:46:27,384 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴       未知
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴       未知
2025-09-03 09:48:24,157 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:48:24,158 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:48:24,158 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:48:24,158 - __main__ - INFO - ================================================================================
2025-09-03 09:48:24,158 - __main__ - INFO - 开始全面数据标准化验证
2025-09-03 09:48:24,158 - __main__ - INFO - ================================================================================
2025-09-03 09:48:24,158 - __main__ - INFO - 
============================================================
2025-09-03 09:48:24,158 - __main__ - INFO - 验证表: ecam_in_energy_factor (排放因子)
2025-09-03 09:48:24,158 - __main__ - INFO - ============================================================
2025-09-03 09:48:24,158 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:48:24,158 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:48:24,189 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:48:24,197 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:48:24,198 - __main__ - INFO - ✓ 原始数据读取成功: 273 行
2025-09-03 09:48:24,198 - __main__ - INFO - 原始列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:48:24,198 - __main__ - INFO - 原始数据样本:
2025-09-03 09:48:24,200 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
2025-09-03 09:48:24,200 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:48:24,200 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_energy_factor
2025-09-03 09:48:24,200 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 273 行数据
2025-09-03 09:48:24,200 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_energy_factor' 标准化完成，输出 273 行数据
2025-09-03 09:48:24,200 - __main__ - INFO - ✓ 标准化完成: 273 行
2025-09-03 09:48:24,200 - __main__ - INFO - 标准化后列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:48:24,200 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:48:24,200 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:48:24,201 - __main__ - INFO - 
============================================================
2025-09-03 09:48:24,201 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene_off (能源消费)
2025-09-03 09:48:24,201 - __main__ - INFO - ============================================================
2025-09-03 09:48:24,201 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:48:24,201 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 09:48:24,220 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:48:24,338 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 09:48:24,339 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 09:48:24,339 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:48:24,339 - __main__ - INFO - 原始数据样本:
2025-09-03 09:48:24,340 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 09:48:24,340 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:48:24,341 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:48:24,583 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 09:48:24,584 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 09:48:24,584 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 09:48:24,584 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:48:24,584 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:48:24,585 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:48:24,602 - __main__ - INFO - 
============================================================
2025-09-03 09:48:24,602 - __main__ - INFO - 验证表: ecam_in_y_pro_ind_ene2_off (地市工业能源消费)
2025-09-03 09:48:24,602 - __main__ - INFO - ============================================================
2025-09-03 09:48:24,602 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:48:24,602 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:48:24,624 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:48:25,142 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:48:25,143 - __main__ - INFO - ✓ 原始数据读取成功: 46535 行
2025-09-03 09:48:25,143 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:48:25,143 - __main__ - INFO - 原始数据样本:
2025-09-03 09:48:25,144 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2  None   大同       全部     实物量                 煤炭  2567.22   万吨
2025-09-03 09:48:25,145 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:48:25,148 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:48:25,850 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:48:25,850 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:48:25,850 - __main__ - INFO - ✓ 标准化完成: 46535 行
2025-09-03 09:48:25,850 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:48:25,850 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:48:25,852 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:48:25,886 - __main__ - INFO - 
============================================================
2025-09-03 09:48:25,886 - __main__ - INFO - 验证表: ecam_in_m_pro_ind_ele_off (用电量)
2025-09-03 09:48:25,886 - __main__ - INFO - ============================================================
2025-09-03 09:48:25,886 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:48:25,886 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 09:48:25,908 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:48:26,898 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 09:48:26,900 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 09:48:26,900 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 09:48:26,900 - __main__ - INFO - 原始数据样本:
2025-09-03 09:48:26,901 - __main__ - INFO -         month area       industry  electricity
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457
1  2020-01-01   临汾       八、金融业用电量     481.3453
2  2020-01-01   临汾        二、工业用电量  102837.0401
2025-09-03 09:48:26,901 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:48:26,903 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:48:27,858 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 09:48:27,858 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 09:48:27,858 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 09:48:27,858 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:48:27,858 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:48:27,861 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:48:27,888 - __main__ - INFO - 
============================================================
2025-09-03 09:48:27,888 - __main__ - INFO - 验证表: fct_y_gdp (GDP数据)
2025-09-03 09:48:27,888 - __main__ - INFO - ============================================================
2025-09-03 09:48:27,888 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:48:27,888 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度GDP数据...
2025-09-03 09:48:27,910 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:48:27,947 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 3360 条GDP记录
2025-09-03 09:48:27,948 - __main__ - INFO - ✓ 原始数据读取成功: 3360 行
2025-09-03 09:48:27,948 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:48:27,948 - __main__ - INFO - 原始数据样本:
2025-09-03 09:48:27,949 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2  2005  None  第二产业增加值（亿元）   2389.5
2025-09-03 09:48:27,949 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:48:27,949 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_gdp
2025-09-03 09:48:27,949 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 3360 行数据
2025-09-03 09:48:27,949 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_gdp' 标准化完成，输出 3360 行数据
2025-09-03 09:48:27,949 - __main__ - INFO - ✓ 标准化完成: 3360 行
2025-09-03 09:48:27,949 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:48:27,949 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:48:27,949 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:48:27,950 - __main__ - INFO - 
============================================================
2025-09-03 09:48:27,950 - __main__ - INFO - 验证表: fct_y_all_ene_intsty (能耗强度)
2025-09-03 09:48:27,950 - __main__ - INFO - ============================================================
2025-09-03 09:48:27,950 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:48:27,950 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度能耗强度数据...
2025-09-03 09:48:27,969 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:48:27,973 - ecam_calculator.infrastructure.database_reader - INFO - 成功获取到 204 条能耗强度记录
2025-09-03 09:48:27,974 - __main__ - INFO - ✓ 原始数据读取成功: 204 行
2025-09-03 09:48:27,974 - __main__ - INFO - 原始列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:48:27,974 - __main__ - INFO - 原始数据样本:
2025-09-03 09:48:27,975 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2  2007   大同  单位地区生产总值能源消耗(等价值)    2.32
2025-09-03 09:48:27,975 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:48:27,975 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_all_ene_intsty
2025-09-03 09:48:27,975 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 204 行数据
2025-09-03 09:48:27,975 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_all_ene_intsty' 标准化完成，输出 204 行数据
2025-09-03 09:48:27,975 - __main__ - INFO - ✓ 标准化完成: 204 行
2025-09-03 09:48:27,975 - __main__ - INFO - 标准化后列: ['year', 'area', 'indicator', 'record']
2025-09-03 09:48:27,975 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:48:27,975 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:48:27,976 - __main__ - INFO - 
============================================================
2025-09-03 09:48:27,976 - __main__ - INFO - 验证表: fct_y_prd_output (工业产品产量)
2025-09-03 09:48:27,976 - __main__ - INFO - ============================================================
2025-09-03 09:48:27,976 - __main__ - INFO - 步骤1: 读取原始数据
2025-09-03 09:48:27,976 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度工业产品产量数据...
2025-09-03 09:48:27,995 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:48:28,014 - ecam_calculator.infrastructure.database_reader - INFO - 获取到1812条工业产品产量记录
2025-09-03 09:48:28,015 - __main__ - INFO - ✓ 原始数据读取成功: 1812 行
2025-09-03 09:48:28,015 - __main__ - INFO - 原始列: ['year', 'area', 'product_name', 'record', 'unit', 'source']
2025-09-03 09:48:28,015 - __main__ - INFO - 原始数据样本:
2025-09-03 09:48:28,016 - __main__ - INFO -    year area product_name       record  unit  source
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴
2  2009   临汾           粗钢   605.797700    万吨  山西统计年鉴
2025-09-03 09:48:28,016 - __main__ - INFO - 步骤2: 执行标准化
2025-09-03 09:48:28,016 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: fct_y_prd_output
2025-09-03 09:48:28,017 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 1812 行数据
2025-09-03 09:48:28,017 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'fct_y_prd_output' 标准化完成，输出 1812 行数据
2025-09-03 09:48:28,017 - __main__ - INFO - ✓ 标准化完成: 1812 行
2025-09-03 09:48:28,017 - __main__ - INFO - 标准化后列: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:48:28,017 - __main__ - INFO - 步骤3: 验证标准化结果
2025-09-03 09:48:28,017 - __main__ - INFO - 步骤4: 分析数据分布
2025-09-03 09:48:28,018 - __main__ - INFO - 
================================================================================
2025-09-03 09:48:28,018 - __main__ - INFO - 标准化验证汇总报告
2025-09-03 09:48:28,018 - __main__ - INFO - ================================================================================
2025-09-03 09:48:28,018 - __main__ - INFO - 
表名: ecam_in_energy_factor
2025-09-03 09:48:28,018 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:48:28,019 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:48:28,019 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:48:28,019 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene_off
2025-09-03 09:48:28,019 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:48:28,019 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:48:28,019 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:48:28,019 - __main__ - INFO -   standard_province: 25760/25760 (100.0%)
2025-09-03 09:48:28,019 - __main__ - INFO -   standard_item: 7360/25760 (28.6%)
2025-09-03 09:48:28,019 - __main__ - INFO -   standard_energy_type: 12075/25760 (46.9%)
2025-09-03 09:48:28,019 - __main__ - INFO - 
表名: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:48:28,019 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:48:28,019 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:48:28,019 - __main__ - INFO - 标准化列数: 6
2025-09-03 09:48:28,019 - __main__ - INFO -   standard_area: 46535/46535 (100.0%)
2025-09-03 09:48:28,019 - __main__ - INFO -   standard_industry: 12487/46535 (26.8%)
2025-09-03 09:48:28,019 - __main__ - INFO -   standard_energy_type: 26735/46535 (57.5%)
2025-09-03 09:48:28,019 - __main__ - INFO - 
表名: ecam_in_m_pro_ind_ele_off
2025-09-03 09:48:28,019 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:48:28,019 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:48:28,019 - __main__ - INFO - 标准化列数: 4
2025-09-03 09:48:28,019 - __main__ - INFO -   standard_area: 74496/74496 (100.0%)
2025-09-03 09:48:28,019 - __main__ - INFO -   standard_industry: 19382/74496 (26.0%)
2025-09-03 09:48:28,019 - __main__ - INFO - 
表名: fct_y_gdp
2025-09-03 09:48:28,019 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:48:28,019 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:48:28,019 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:48:28,019 - __main__ - INFO - 
表名: fct_y_all_ene_intsty
2025-09-03 09:48:28,019 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:48:28,019 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:48:28,019 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:48:28,019 - __main__ - INFO - 
表名: fct_y_prd_output
2025-09-03 09:48:28,019 - __main__ - INFO - 状态: ✓ 成功
2025-09-03 09:48:28,019 - __main__ - INFO - 数据完整性: ✓ 通过
2025-09-03 09:48:28,019 - __main__ - INFO - 标准化列数: 0
2025-09-03 09:48:28,019 - __main__ - INFO - 
总体统计:
2025-09-03 09:48:28,020 - __main__ - INFO - 成功表数: 7/7
2025-09-03 09:48:28,020 - __main__ - INFO - 成功率: 100.0%
2025-09-03 09:48:28,020 - __main__ - INFO - 
================================================================================
2025-09-03 09:48:28,020 - __main__ - INFO - 标准化数据模型
2025-09-03 09:48:28,020 - __main__ - INFO - ================================================================================
2025-09-03 09:48:28,020 - __main__ - INFO - 
标准化列模型:
2025-09-03 09:48:28,020 - __main__ - INFO -   standard_area: 标准化后的标准值
2025-09-03 09:48:28,020 - __main__ - INFO -   standard_energy_type: 标准化后的标准值
2025-09-03 09:48:28,020 - __main__ - INFO -   standard_industry: 标准化后的标准值
2025-09-03 09:48:28,020 - __main__ - INFO -   standard_item: 标准化后的标准值
2025-09-03 09:48:28,020 - __main__ - INFO -   standard_province: 标准化后的标准值
2025-09-03 09:48:28,020 - __main__ - INFO - 
层次关系列模型:
2025-09-03 09:48:28,020 - __main__ - INFO -   macro_area: 层次关系值
2025-09-03 09:48:28,020 - __main__ - INFO -   macro_energy_type: 层次关系值
2025-09-03 09:48:28,020 - __main__ - INFO -   macro_industry: 层次关系值
2025-09-03 09:48:28,020 - __main__ - INFO -   macro_item: 层次关系值
2025-09-03 09:48:28,020 - __main__ - INFO -   macro_province: 层次关系值
2025-09-03 09:48:28,020 - __main__ - INFO - 
各表标准化模型:
2025-09-03 09:48:28,020 - __main__ - INFO - 
表: ecam_in_energy_factor
2025-09-03 09:48:28,020 - __main__ - INFO - 列数: 9
2025-09-03 09:48:28,020 - __main__ - INFO - 行数: 273
2025-09-03 09:48:28,020 - __main__ - INFO - 列名: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:48:28,020 - __main__ - INFO - 数据样本:
2025-09-03 09:48:28,021 - __main__ - INFO -          year source area factor    method industry energy_type       unit
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨
2025-09-03 09:48:28,023 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene_off
2025-09-03 09:48:28,023 - __main__ - INFO - 列数: 14
2025-09-03 09:48:28,023 - __main__ - INFO - 行数: 25760
2025-09-03 09:48:28,023 - __main__ - INFO - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:48:28,023 - __main__ - INFO - 数据样本:
2025-09-03 09:48:28,028 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2025-09-03 09:48:28,031 - __main__ - INFO - 
表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:48:28,031 - __main__ - INFO - 列数: 14
2025-09-03 09:48:28,031 - __main__ - INFO - 行数: 46535
2025-09-03 09:48:28,031 - __main__ - INFO - 列名: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:48:28,031 - __main__ - INFO - 数据样本:
2025-09-03 09:48:28,039 - __main__ - INFO -    year area industry convert method energy_type    value unit
0  None   大同       全部     实物量                 煤炭  2202.05   万吨
1  None   大同       全部     实物量                 煤炭  2171.17   万吨
2025-09-03 09:48:28,039 - __main__ - INFO - 
表: ecam_in_m_pro_ind_ele_off
2025-09-03 09:48:28,039 - __main__ - INFO - 列数: 8
2025-09-03 09:48:28,040 - __main__ - INFO - 行数: 74496
2025-09-03 09:48:28,040 - __main__ - INFO - 列名: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry']
2025-09-03 09:48:28,040 - __main__ - INFO - 数据样本:
2025-09-03 09:48:28,048 - __main__ - INFO -         month area       industry  electricity standard_area macro_area standard_industry macro_industry
0  2020-01-01   临汾  B、城乡居民生活用电量合计   24440.7457            临汾         山西               NaN            NaN
1  2020-01-01   临汾       八、金融业用电量     481.3453            临汾         山西               NaN            NaN
2025-09-03 09:48:28,048 - __main__ - INFO - 
表: fct_y_gdp
2025-09-03 09:48:28,048 - __main__ - INFO - 列数: 4
2025-09-03 09:48:28,048 - __main__ - INFO - 行数: 3360
2025-09-03 09:48:28,048 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:48:28,048 - __main__ - INFO - 数据样本:
2025-09-03 09:48:28,049 - __main__ - INFO -    year  area    indicator   record
0  2005  None   地区生产总值（亿元）  4179.52
1  2005  None  第一产业增加值（亿元）    247.8
2025-09-03 09:48:28,049 - __main__ - INFO - 
表: fct_y_all_ene_intsty
2025-09-03 09:48:28,049 - __main__ - INFO - 列数: 4
2025-09-03 09:48:28,049 - __main__ - INFO - 行数: 204
2025-09-03 09:48:28,049 - __main__ - INFO - 列名: ['year', 'area', 'indicator', 'record']
2025-09-03 09:48:28,049 - __main__ - INFO - 数据样本:
2025-09-03 09:48:28,050 - __main__ - INFO -    year area          indicator  record
0  2007   临汾  单位地区生产总值能源消耗(等价值)    4.01
1  2007   吕梁  单位地区生产总值能源消耗(等价值)    3.68
2025-09-03 09:48:28,050 - __main__ - INFO - 
表: fct_y_prd_output
2025-09-03 09:48:28,050 - __main__ - INFO - 列数: 7
2025-09-03 09:48:28,050 - __main__ - INFO - 行数: 1812
2025-09-03 09:48:28,050 - __main__ - INFO - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'industry']
2025-09-03 09:48:28,051 - __main__ - INFO - 数据样本:
2025-09-03 09:48:28,052 - __main__ - INFO -    year area product_name       record  unit  source industry
0  2009   临汾           原煤  2493.900000    万吨  山西统计年鉴       未知
1  2009   临汾          发电量    62.132263  亿千瓦时  山西统计年鉴       未知
