2025-09-03 09:52:00,831 - __main__ - INFO - ================================================================================
2025-09-03 09:52:00,831 - __main__ - INFO - 开始能源转换端到端测试
2025-09-03 09:52:00,831 - __main__ - INFO - ================================================================================
2025-09-03 09:52:00,831 - __main__ - INFO - 步骤1: 初始化组件
2025-09-03 09:52:00,895 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:52:00,895 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:52:00,895 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:52:00,895 - __main__ - INFO - 步骤2: 读取转换系数数据
2025-09-03 09:52:00,895 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:52:00,925 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:52:00,934 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:52:00,935 - __main__ - INFO - ✓ 转换系数数据读取成功: 273 条记录
2025-09-03 09:52:00,935 - __main__ - INFO - 转换系数数据列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:52:00,935 - __main__ - INFO - 转换系数数据样本:
2025-09-03 09:52:00,935 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
3  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          煤炭  吨CO2/标准煤吨  2.837
4  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          褐煤  吨CO2/标准煤吨  2.837
2025-09-03 09:52:00,942 - __main__ - INFO - 步骤3: 读取并标准化能源消费数据
2025-09-03 09:52:00,943 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:52:00,961 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:52:01,454 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:52:01,455 - __main__ - INFO - ✓ 能源消费数据读取成功: 46535 条记录
2025-09-03 09:52:01,455 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:52:01,458 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:52:02,147 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:52:02,147 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:52:02,147 - __main__ - INFO - ✓ 数据标准化完成: 46535 条记录
2025-09-03 09:52:02,147 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:52:02,147 - __main__ - INFO - 步骤4: 执行能源转换
2025-09-03 09:52:02,147 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 开始能源转换编排
2025-09-03 09:52:02,147 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 步骤1: 加载转换系数
2025-09-03 09:52:02,153 - ecam_calculator.domain.service.energy_conversion_service - INFO - 成功加载 109 个转换系数
2025-09-03 09:52:02,153 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 步骤2: 验证转换系数
2025-09-03 09:52:02,153 - ecam_calculator.domain.service.energy_conversion_orchestrator - WARNING - 转换系数验证发现问题: ['缺少 原煤 (万吨) 的转换系数', '缺少 电力 (亿千瓦时) 的转换系数', '缺少 天然气 (亿立方米) 的转换系数', '缺少 柴油 (万吨) 的转换系数', '缺少 汽油 (万吨) 的转换系数', '缺少 焦炭 (万吨) 的转换系数']
2025-09-03 09:52:02,153 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 步骤3: 执行能源转换
2025-09-03 09:52:02,178 - ecam_calculator.domain.service.energy_conversion_service - ERROR - 能源转换失败: '<=' not supported between instances of 'str' and 'int'
2025-09-03 09:52:02,178 - ecam_calculator.domain.service.energy_conversion_orchestrator - ERROR - 能源转换编排失败: '<=' not supported between instances of 'str' and 'int'
2025-09-03 09:52:02,178 - __main__ - ERROR - 能源转换端到端测试失败: '<=' not supported between instances of 'str' and 'int'
2025-09-03 09:52:16,094 - __main__ - INFO - ================================================================================
2025-09-03 09:52:16,094 - __main__ - INFO - 开始能源转换端到端测试
2025-09-03 09:52:16,094 - __main__ - INFO - ================================================================================
2025-09-03 09:52:16,094 - __main__ - INFO - 步骤1: 初始化组件
2025-09-03 09:52:16,157 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 09:52:16,157 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 09:52:16,157 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 09:52:16,157 - __main__ - INFO - 步骤2: 读取转换系数数据
2025-09-03 09:52:16,157 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有能源排放因子数据...
2025-09-03 09:52:16,187 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:52:16,195 - ecam_calculator.infrastructure.database_reader - INFO - 获取到273条能源因子记录
2025-09-03 09:52:16,195 - __main__ - INFO - ✓ 转换系数数据读取成功: 273 条记录
2025-09-03 09:52:16,195 - __main__ - INFO - 转换系数数据列: ['year', 'source', 'area', 'factor', 'method', 'industry', 'energy_type', 'unit', 'value']
2025-09-03 09:52:16,195 - __main__ - INFO - 转换系数数据样本:
2025-09-03 09:52:16,195 - __main__ - INFO -          year source area factor    method industry energy_type       unit  value
0  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          原煤  吨CO2/标准煤吨  2.837
1  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          柴油  吨CO2/标准煤吨  2.168
2  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          焦炭  吨CO2/标准煤吨  2.837
3  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          煤炭  吨CO2/标准煤吨  2.837
4  2023-01-01     国家   全国    co2  标煤折碳排放因子       供热          褐煤  吨CO2/标准煤吨  2.837
2025-09-03 09:52:16,203 - __main__ - INFO - 步骤3: 读取并标准化能源消费数据
2025-09-03 09:52:16,203 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取地市工业能源消费数据...
2025-09-03 09:52:16,221 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 09:52:16,706 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 46535 条地市工业能源消费记录
2025-09-03 09:52:16,707 - __main__ - INFO - ✓ 能源消费数据读取成功: 46535 条记录
2025-09-03 09:52:16,707 - __main__ - INFO - 原始列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 09:52:16,710 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene2_off
2025-09-03 09:52:17,405 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 46535 行数据
2025-09-03 09:52:17,405 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene2_off' 标准化完成，输出 46535 行数据
2025-09-03 09:52:17,405 - __main__ - INFO - ✓ 数据标准化完成: 46535 条记录
2025-09-03 09:52:17,405 - __main__ - INFO - 标准化后列: ['year', 'area', 'industry', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_area', 'macro_area', 'standard_industry', 'macro_industry', 'standard_energy_type', 'macro_energy_type']
2025-09-03 09:52:17,405 - __main__ - INFO - 步骤4: 执行能源转换
2025-09-03 09:52:17,405 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 开始能源转换编排
2025-09-03 09:52:17,405 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 步骤1: 加载转换系数
2025-09-03 09:52:17,425 - ecam_calculator.domain.service.energy_conversion_service - INFO - 成功加载 109 个转换系数
2025-09-03 09:52:17,425 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 步骤2: 验证转换系数
2025-09-03 09:52:17,426 - ecam_calculator.domain.service.energy_conversion_orchestrator - WARNING - 转换系数验证发现问题: ['缺少 原煤 (万吨) 的转换系数', '缺少 电力 (亿千瓦时) 的转换系数', '缺少 天然气 (亿立方米) 的转换系数', '缺少 柴油 (万吨) 的转换系数', '缺少 汽油 (万吨) 的转换系数', '缺少 焦炭 (万吨) 的转换系数']
2025-09-03 09:52:17,426 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 步骤3: 执行能源转换
2025-09-03 09:52:18,661 - ecam_calculator.domain.service.energy_conversion_service - INFO - 能源转换完成: 6940/46535 (14.9%)
2025-09-03 09:52:18,663 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 步骤4: 生成转换摘要
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - === 能源转换摘要 ===
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 总记录数: 46535
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 已转换记录数: 6940
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 未转换记录数: 39595
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 转换率: 14.9%
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 总标准煤当量: 64124307229.01
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 能源品种分布:
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   原煤: 19428
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   天然气: 3198
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   电力: 2212
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   焦炭: 1897
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 单位分布:
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   吨: 28274
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   万立方米: 4482
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   百万千焦: 3041
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   : 2435
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   万千瓦时: 1953
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   用于燃料: 683
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   吨标准煤: 631
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   气态: 534
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   煤田: 497
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO -   用于炼焦: 363
2025-09-03 09:52:18,680 - ecam_calculator.domain.service.energy_conversion_orchestrator - INFO - 能源转换编排完成
2025-09-03 09:52:18,680 - __main__ - INFO - ✓ 能源转换完成: 46535 条记录
2025-09-03 09:52:18,681 - __main__ - INFO - 步骤5: 验证转换结果
2025-09-03 09:52:18,681 - __main__ - INFO - ✓ 所有预期列都存在
2025-09-03 09:52:18,685 - __main__ - INFO - 转换统计:
2025-09-03 09:52:18,685 - __main__ - INFO -   总标准煤当量: 64124307229.01
2025-09-03 09:52:18,685 - __main__ - INFO -   平均转换系数: 0.5277
2025-09-03 09:52:18,685 - __main__ - INFO -   转换率: 14.9%
2025-09-03 09:52:18,685 - __main__ - INFO -   能源品种数: 4
2025-09-03 09:52:18,685 - __main__ - INFO -   单位数: 21
2025-09-03 09:52:18,685 - __main__ - INFO - 步骤6: 输出转换结果样本
2025-09-03 09:52:18,686 - __main__ - INFO - 转换结果样本:
2025-09-03 09:52:18,689 - __main__ - INFO -   area industry standard_energy_type  ... standard_coal_equivalent conversion_factor  conversion_status
0   大同       全部                   原煤  ...              6263.070610            2.8442                已转换
1   大同       全部                   原煤  ...              6175.241714            2.8442                已转换
2   大同       全部                   原煤  ...              7301.687124            2.8442                已转换
3   大同       全部                   原煤  ...              7610.965432            2.8442                已转换
4   大同       全部                   原煤  ...              7472.168472            2.8442                已转换

[5 rows x 8 columns]
2025-09-03 09:52:18,695 - __main__ - INFO - 步骤7: 数据完整性检查
2025-09-03 09:52:18,695 - __main__ - INFO - ✓ 原始数据完整性保持
2025-09-03 09:52:18,695 - __main__ - INFO - ✓ 标准化数据完整性保持
2025-09-03 09:52:18,695 - __main__ - INFO - 步骤8: 保存转换结果
2025-09-03 09:52:18,808 - __main__ - INFO - ✓ 转换结果已保存到: energy_conversion_results.csv
2025-09-03 09:52:18,808 - __main__ - INFO - ================================================================================
2025-09-03 09:52:18,808 - __main__ - INFO - 能源转换端到端测试总结
2025-09-03 09:52:18,808 - __main__ - INFO - ================================================================================
2025-09-03 09:52:18,808 - __main__ - INFO - ✓ 转换系数加载: 成功
2025-09-03 09:52:18,808 - __main__ - INFO - ✓ 数据标准化: 成功
2025-09-03 09:52:18,808 - __main__ - INFO - ✓ 能源转换: 成功
2025-09-03 09:52:18,808 - __main__ - INFO - ✓ 结果验证: 成功
2025-09-03 09:52:18,808 - __main__ - INFO - ✓ 数据完整性: 保持
2025-09-03 09:52:18,808 - __main__ - INFO - ✓ 结果保存: 成功
2025-09-03 09:52:18,811 - __main__ - INFO - 最终结果:
2025-09-03 09:52:18,811 - __main__ - INFO -   总记录数: 46535
2025-09-03 09:52:18,811 - __main__ - INFO -   转换率: 14.9%
2025-09-03 09:52:18,811 - __main__ - INFO -   总标准煤当量: 64124307229.01
2025-09-03 09:52:18,811 - __main__ - INFO - 能源转换端到端测试完成！
