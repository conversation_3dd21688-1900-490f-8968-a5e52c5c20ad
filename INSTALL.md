# 区域"电-能-碳"监测分析系统 安装指南

本文档详细说明如何在Linux环境中安装和配置区域"电-能-碳"监测分析系统。

## 一、环境准备

### 1. 安装MySQL

```bash
# CentOS系统
sudo yum install -y mysql-server mysql-devel

# Ubuntu系统
sudo apt-get update
sudo apt-get install -y mysql-server libmysqlclient-dev
```

启动MySQL服务：

```bash
# CentOS系统
sudo systemctl start mysqld
sudo systemctl enable mysqld

# Ubuntu系统
sudo systemctl start mysql
sudo systemctl enable mysql
```

设置MySQL root密码：

```bash
sudo mysql_secure_installation
```

### 2. 安装Python 3.7

```bash
# CentOS系统
sudo yum install -y python3 python3-devel

# Ubuntu系统
sudo apt-get install -y python3 python3-dev
```

### 3. 安装pip

```bash
# CentOS系统
sudo yum install -y python3-pip

# Ubuntu系统
sudo apt-get install -y python3-pip

# 升级pip
pip3 install --upgrade pip
```

## 二、数据库配置

### 1. 创建数据库

```bash
# 登录MySQL
mysql -u root -p

# 在MySQL中执行以下命令
CREATE DATABASE ecam_city CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'ecam_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON ecam_city.* TO 'ecam_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 2. 导入数据库结构

```bash
# 导入SQL脚本
mysql -u root -p ecam_city < dlkst_prd_mysql57-ecam_city.sql
```

## 三、安装ECAM计算器

### 使用pip安装whl包

```bash
# 安装whl包
pip3 install ecam-calculator-1.0.0-cp37-cp37m-linux_x86_64.whl
```

## 四、配置系统

### 1. 设置环境变量

将以下内容添加到`~/.bashrc`或`~/.bash_profile`文件中：

```bash
export ECAM_DB_HOST=localhost
export ECAM_DB_PORT=3306
export ECAM_DB_USER=ecam_user
export ECAM_DB_PASSWORD=your_password
export ECAM_DB_NAME=ecam_city
```

应用更改：

```bash
source ~/.bashrc  # 或 source ~/.bash_profile
```

### 2. 验证安装

```bash
# 检查命令是否可用
ecam-calculator --help

# 测试连接数据库
ecam-calculator --start-year 2022 --province 山西 --log-level DEBUG
```

## 五、使用示例

### 1. 基本用法

```bash
# 计算单一年份数据
ecam-calculator --start-year 2022 --province 山西

# 计算多年数据
ecam-calculator --start-year 2020 --end-year 2022 --province 山西
```

### 2. 高级用法

```bash
# 指定数据库连接参数
ecam-calculator --db-host localhost --db-port 3306 --db-user ecam_user --db-password your_password --db-name ecam_city --start-year 2022 --province 山西

# 调整日志级别
ecam-calculator --start-year 2022 --province 山西 --log-level DEBUG
```

## 六、常见问题排查

### 1. 数据库连接问题

如果遇到数据库连接错误：

```bash
# 检查MySQL服务是否运行
sudo systemctl status mysql  # 或 mysqld

# 检查数据库用户权限
mysql -u ecam_user -p -e "SHOW GRANTS;"

# 检查数据库是否存在
mysql -u ecam_user -p -e "SHOW DATABASES;"
```

### 2. 依赖问题

如果遇到依赖相关错误：

```bash
# 重新安装依赖
pip3 install -r requirements.txt --force-reinstall

# 检查依赖版本
pip3 freeze | grep -E "pandas|numpy|mysql"
```