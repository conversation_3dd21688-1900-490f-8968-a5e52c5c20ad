#!/bin/bash

# 区域"电-能-碳"监测分析系统开发环境设置脚本

echo "🚀 设置区域电-能-碳监测分析系统开发环境..."

# 检查uv是否安装
if ! command -v uv &> /dev/null; then
    echo "❌ uv未安装，请先安装uv: https://docs.astral.sh/uv/getting-started/installation/"
    exit 1
fi

# 检查Python 3.8是否可用
if ! uv python list | grep -q "3.8.20"; then
    echo "📦 安装Python 3.8.20..."
    uv python install 3.8.20
fi

# 创建虚拟环境（如果不存在）
if [ ! -d ".venv" ]; then
    echo "🔧 创建虚拟环境..."
    uv venv --python 3.8.20
fi

# 激活虚拟环境
echo "🔌 激活虚拟环境..."
source .venv/bin/activate

# 安装依赖
echo "📚 安装项目依赖..."
uv pip install -e .

# 安装开发依赖（可选）
echo "🔧 安装开发依赖..."
uv pip install pytest black flake8 mypy

echo "✅ 开发环境设置完成！"
echo ""
echo "使用方法："
echo "1. 激活虚拟环境: source .venv/bin/activate"
echo "2. 运行主程序: python main.py"
echo "3. 运行测试: pytest"
echo "4. 代码格式化: black ."
echo "5. 代码检查: flake8 ."
echo "6. 类型检查: mypy ."
echo ""
echo "当前Python版本: $(python --version)"
echo "虚拟环境路径: $(pwd)/.venv"
