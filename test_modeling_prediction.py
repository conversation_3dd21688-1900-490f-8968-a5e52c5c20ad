#!/usr/bin/env python3
"""
建模预测服务测试脚本
"""

import sys
import os
import pandas as pd
import numpy as np
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ecam_calculator.domain.service.modeling_prediction_service import ModelingPredictionService

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def create_test_data():
    """创建测试数据"""
    # 创建模拟的能源消费数据（标准煤当量）
    np.random.seed(42)
    n_months = 24
    
    energy_data = []
    electricity_data = []
    
    areas = ['太原', '大同', '阳泉']
    industries = ['制造业', '电力热力燃气及水生产和供应业']
    
    for area in areas:
        for industry in industries:
            for year in [2019, 2020]:
                for month in range(1, 13):
                    # 模拟能源消费数据（标准煤当量）
                    energy_consumption = 100 + np.random.normal(0, 10)
                    
                    # 模拟电力消费数据
                    electricity_consumption = 50 + np.random.normal(0, 5)
                    
                    energy_data.append({
                        'year': year,
                        'month': month,
                        'standard_area': area,
                        'standard_industry': industry,
                        'standard_coal_equivalent': max(0, energy_consumption),
                        'data_source': 'test'
                    })
                    
                    electricity_data.append({
                        'year': year,
                        'month': month,
                        'standard_area': area,
                        'standard_industry': industry,
                        'value': max(0, electricity_consumption),
                        'unit': '万千瓦时',
                        'data_source': 'test'
                    })
    
    return pd.DataFrame(energy_data), pd.DataFrame(electricity_data)

def test_modeling_prediction_service():
    """测试建模预测服务"""
    logger = logging.getLogger(__name__)
    
    try:
        # 1. 创建服务实例
        logger.info("创建建模预测服务实例...")
        service = ModelingPredictionService()
        
        # 2. 创建测试数据
        logger.info("创建测试数据...")
        energy_data, electricity_data = create_test_data()
        
        logger.info(f"能源数据: {len(energy_data)} 条记录")
        logger.info(f"电力数据: {len(electricity_data)} 条记录")
        
        # 3. 测试ARDL模型构建
        logger.info("测试ARDL模型构建...")
        ardl_models = service.build_ardl_models(energy_data, electricity_data)
        
        logger.info(f"构建了 {len(ardl_models)} 个ARDL模型")
        
        # 打印模型信息
        for model_key, model_info in ardl_models.items():
            logger.info(f"模型 {model_key}: R²={model_info['r_squared']:.4f}, AIC={model_info['aic']:.2f}")
        
        # 4. 测试预测功能
        logger.info("测试预测功能...")
        
        # 创建模拟的最新电力数据（2021年1-6月）
        latest_electricity_data = []
        for area in ['太原', '大同', '阳泉']:
            for industry in ['制造业', '电力热力燃气及水生产和供应业']:
                for month in range(1, 7):  # 2021年1-6月
                    latest_electricity_data.append({
                        'year': 2021,
                        'month': month,
                        'standard_area': area,
                        'standard_industry': industry,
                        'value': 60 + np.random.normal(0, 5),  # 模拟2021年数据
                        'unit': '万千瓦时',
                        'data_source': 'test'
                    })
        
        latest_electricity_df = pd.DataFrame(latest_electricity_data)
        
        # 执行预测
        prediction_results = service.predict_with_latest_electricity(ardl_models, latest_electricity_df)
        
        if not prediction_results.empty:
            logger.info(f"生成了 {len(prediction_results)} 条预测结果")
            logger.info("预测结果示例:")
            logger.info(prediction_results.head())
        else:
            logger.warning("未生成预测结果")
        
        logger.info("建模预测服务测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=== 建模预测服务测试 ===")
    
    success = test_modeling_prediction_service()
    
    if success:
        logger.info("=== 测试成功 ===")
        return 0
    else:
        logger.error("=== 测试失败 ===")
        return 1

if __name__ == "__main__":
    sys.exit(main())
