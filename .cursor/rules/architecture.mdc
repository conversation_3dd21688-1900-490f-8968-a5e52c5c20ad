---
description: 编码以及编写文档架构相关部分
alwaysApply: false
---
# 架构约束模块

## 绝对禁止的依赖关系

AI助手必须严格检查以下依赖方向：

```
External → Infrastructure → Application → Domain
```

违反此规则的代码建议将被拒绝。

### 领域层禁止引入
- 任何Web框架、ORM、日志库、序列化库
- 只能引入：业务规则注解、领域事件接口、聚合根基类

### 应用层约束
- 特定数据库、Web控制器
- 可以引入：领域仓储接口、事件总线接口

## DDD聚合设计强制约束

AI助手在建议聚合根设计时必须：

1. 严格维护聚合边界
   - 禁止直接修改其他聚合状态
   - 通过领域事件实现聚合间通信
   - 使用应用服务协调跨聚合操作

2. 强制验证物理定律
   - 所有数据修改必须触发物理守恒验证
   - 统计平衡约束必须在领域层实现
   - 使用`@BusinessRule("BR-XXX")`注解标记验证方法

## 数据关系验证强制要求

AI助手必须确保以下验证逻辑：

```typescript
// 能源平衡：投入 = 产出 + 损耗
@BusinessRule("BR-100")
validateEnergyBalance(input, output, losses)

// 统计平衡：总量 = Σ(分项)
@BusinessRule("BR-013")
validateStatisticalBalance(total, items)
```

## 错误处理架构规范

AI助手必须遵循：

1. Result模式优先：避免过度使用异常
2. 分层异常处理：领域异常、应用异常、基础设施异常
3. 业务规则违反：使用`BusinessRuleViolationException`

## 性能架构约束

AI助手必须实现：

1. 命令查询分离：`ReportCommandService` vs `ReportQueryService`
2. 缓存策略隔离：只在基础设施层处理缓存
3. 聚合边界保持：避免N+1查询问题

## 安全架构约束

AI助手必须遵循：

1. 权限验证位置：应用层验证权限，领域层不处理
2. 注解驱动权限：使用`@RequiresPermission("XXX")`

## 架构违规检查清单

AI助手在提供代码建议前必须确认：

- 依赖方向是否正确？
- 领域层是否纯净无框架依赖？
- 聚合边界是否保持？
- 数据关系验证是否到位？
- 错误处理是否分层？
- 命令查询是否分离？

违反任何一项的代码建议将被拒绝。

## 核心架构原则

1. 分层依赖：严格遵循依赖方向，不允许反向依赖
2. 聚合边界：维护业务一致性边界，避免跨聚合直接操作
3. 物理定律：所有业务操作必须验证物理守恒和统计平衡
4. 职责分离：每层只处理自己的职责，不越界
5. 错误分层：不同层使用不同的错误处理策略

## 架构决策记录

重要架构决策应记录在ADR文档中，包括：
- 决策背景和问题描述
- 考虑的备选方案
- 最终选择的原因
- 实施计划和风险
- 后续评估标准
# 架构约束模块

## 绝对禁止的依赖关系

AI助手必须严格检查以下依赖方向：

```
External → Infrastructure → Application → Domain
```

违反此规则的代码建议将被拒绝。

### 领域层禁止引入
- 任何Web框架、ORM、日志库、序列化库
- 只能引入：业务规则注解、领域事件接口、聚合根基类

### 应用层约束
- 特定数据库、Web控制器
- 可以引入：领域仓储接口、事件总线接口

## DDD聚合设计强制约束

AI助手在建议聚合根设计时必须：

1. 严格维护聚合边界
   - 禁止直接修改其他聚合状态
   - 通过领域事件实现聚合间通信
   - 使用应用服务协调跨聚合操作

2. 强制验证物理定律
   - 所有数据修改必须触发物理守恒验证
   - 统计平衡约束必须在领域层实现
   - 使用`@BusinessRule("BR-XXX")`注解标记验证方法

## 数据关系验证强制要求

AI助手必须确保以下验证逻辑：

```typescript
// 能源平衡：投入 = 产出 + 损耗
@BusinessRule("BR-100")
validateEnergyBalance(input, output, losses)

// 统计平衡：总量 = Σ(分项)
@BusinessRule("BR-013")
validateStatisticalBalance(total, items)
```

## 错误处理架构规范

AI助手必须遵循：

1. Result模式优先：避免过度使用异常
2. 分层异常处理：领域异常、应用异常、基础设施异常
3. 业务规则违反：使用`BusinessRuleViolationException`

## 性能架构约束

AI助手必须实现：

1. 命令查询分离：`ReportCommandService` vs `ReportQueryService`
2. 缓存策略隔离：只在基础设施层处理缓存
3. 聚合边界保持：避免N+1查询问题

## 安全架构约束

AI助手必须遵循：

1. 权限验证位置：应用层验证权限，领域层不处理
2. 注解驱动权限：使用`@RequiresPermission("XXX")`

## 架构违规检查清单

AI助手在提供代码建议前必须确认：

- 依赖方向是否正确？
- 领域层是否纯净无框架依赖？
- 聚合边界是否保持？
- 数据关系验证是否到位？
- 错误处理是否分层？
- 命令查询是否分离？

违反任何一项的代码建议将被拒绝。

## 核心架构原则

1. 分层依赖：严格遵循依赖方向，不允许反向依赖
2. 聚合边界：维护业务一致性边界，避免跨聚合直接操作
3. 物理定律：所有业务操作必须验证物理守恒和统计平衡
4. 职责分离：每层只处理自己的职责，不越界
5. 错误分层：不同层使用不同的错误处理策略

## 架构决策记录

重要架构决策应记录在ADR文档中，包括：
- 决策背景和问题描述
- 考虑的备选方案
- 最终选择的原因
- 实施计划和风险
- 后续评估标准
