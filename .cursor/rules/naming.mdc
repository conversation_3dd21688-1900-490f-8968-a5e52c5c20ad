# 命名规范模块

## 绝对禁止的命名模式

AI助手必须拒绝以下命名建议：

- `DataMgr`、`Utils`、`Helper` - 无意义缩写
- `能源Report`、`电表Data` - 中英文混合
- `calc()`、`process()` - 动词过于通用
- `data`、`result`、`temp` - 变量名过于通用

## 强制术语查找流程

AI助手在建议类名时必须：

1. 优先查找统一语言表 (`1.3 统一语言.md`)
   - 搜索对应的英文术语ID (T-XXX格式)
   - 使用准确的英文术语命名

2. 参考聚合根设计 (`3. 战术设计/`)
   - 确认聚合边界和职责
   - 遵循已有的命名模式

3. 业务规则引用 (`1.4 业务规则.md`)
   - 使用BR-XXX格式引用业务规则
   - 在代码中体现规则约束

## 强制命名模式

AI助手必须使用以下命名：

### 领域对象命名
- 统计报表 → `StatisticalReport` (T-001)
- 质量评估作业 → `QualityAssessmentJob` (T-004)
- 能源时间序列 → `EnergyTimeSeries` (聚合根)
- 数据转换器 → `DataTransformer` (T-002)

### 方法命名模式
```typescript
// 验证方法 - 必须使用@BusinessRule注解
@BusinessRule("BR-010")
validateDataQuality(): ValidationResult

// 业务操作 - 必须体现业务意图
createStatisticalReport(): ReportId
publishEnergyReport(): void
archiveTimeSeries(): void

// 查询方法 - 必须明确查询条件
findReportsByPeriod(): List<Report>
getTimeSeriesAnalysis(): AnalysisResult
```

### 事件命名规范
AI助手必须使用格式：`{实体名}{动作过去式}Event`
- `ReportPublishedEvent`
- `DataQualityValidatedEvent`
- `TimeSeriesFrozenEvent`
- `EnergyBalanceCalculatedEvent`

## 变量命名强制要求

AI助手必须使用以下命名模式：

### 业务变量
```typescript
// 必须使用专业术语
const energyConsumptionData: EnergyData
const carbonEmissionFactor: EmissionFactor
const regionalEnergyReport: StatisticalReport
const qualityAssessmentResult: ValidationResult

// 禁止使用
const data: any
const result: Object
const temp: number
const flag: boolean
```

### 集合变量
```typescript
// 必须明确集合内容
const energyTimeSeriesList: List<EnergyTimeSeries>
const validationIssues: List<ValidationIssue>
const businessRuleViolations: Set<BusinessRule>

// 禁止使用
const items: List<any>
const records: Array<Object>
```

## 常量命名强制规范

AI助手必须使用以下常量命名：

### 业务规则常量
```typescript
// 必须使用BR-XXX格式
const BR_DATA_QUALITY_MANDATORY = "BR-010";
const BR_ENERGY_BALANCE_PRINCIPLE = "BR-013";
const BR_COVERAGE_RATE_MINIMUM = "BR-015";

// 必须使用T-XXX格式
const TERM_STATISTICAL_REPORT = "T-001";
const TERM_QUALITY_ASSESSMENT_JOB = "T-004";
```

### 配置常量
```typescript
// 必须使用描述性命名
const ENERGY_DATA_COVERAGE_THRESHOLD = 0.95;
const DATA_CONSISTENCY_ERROR_TOLERANCE = 0.02;
const REPORT_GENERATION_TIMEOUT_SECONDS = 30;
```

## 包结构命名规范

AI助手必须建议以下包结构：

```
com.energy.system.domain
├── aggregate/
│   ├── StatisticalReport
│   └── EnergyTimeSeries  
├── valueobject/
│   ├── EnergyUnit
│   └── ValidationResult
├── service/
│   ├── QualityAssessmentService
│   └── EnergyCalculationService
└── event/
    ├── ReportPublishedEvent
    └── DataValidatedEvent
```

## 命名违规检查清单

AI助手在提供命名建议前必须确认：

- 类名来自统一语言表？
- 方法名体现业务意图？
- 业务规则有正确注解？
- 变量名清晰表达含义？
- 事件命名符合格式？

违反任何一项的命名建议将被拒绝。

## 核心原则

AI助手必须记住：
1. 业务优先：命名必须体现领域专业性
2. 统一语言：严格遵循项目术语标准
3. 规则驱动：使用BR-XXX和T-XXX格式
4. 意图明确：避免含糊不清的缩写和通用术语

## 命名模式总结

### 类命名模式
- 聚合根：`{业务概念}Aggregate` 或直接使用业务概念名
- 实体：`{业务概念}Entity` 或直接使用业务概念名
- 值对象：`{业务概念}Value` 或 `{业务概念}`
- 领域服务：`{业务能力}Service`
- 应用服务：`{业务用例}ApplicationService`

### 方法命名模式
- 验证方法：`validate{验证内容}()`
- 业务操作：`{业务动作}{业务对象}()`
- 查询方法：`find{查询条件}()` 或 `get{查询内容}()`
- 事件处理：`handle{事件名}()`

### 变量命名模式
- 业务数据：`{业务概念}{数据类型}`
- 计算结果：`{计算内容}Result`
- 配置参数：`{配置项}_{配置类型}`
- 状态标志：`is{状态描述}` 或 `has{属性描述}`
