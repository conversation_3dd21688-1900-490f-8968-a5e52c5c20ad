# Cursor规则重构说明

## 重构目标

本次重构将原有的项目规则进行了以下改进：

1. **分离用户规则**: 将适用于Cursor用户的核心规则独立出来
2. **发挥mdc特性**: 充分利用mdc格式的结构化特性
3. **大模型友好**: 移除emoji，使用更清晰的文本结构，适合大模型理解

## 新的规则文件结构

### 主规则文件
- **`main.mdc`** - 主规则文件，整合所有模块化规则
- **`cursor-user-rules.mdc`** - Cursor用户规则，包含核心设计原则和交互指南

### 模块化规则文件
- **`architecture.mdc`** - 架构约束模块，定义分层依赖和聚合边界
- **`naming.mdc`** - 命名规范模块，定义术语使用和命名模式
- **`workflow.mdc`** - 工作流程模块，定义开发流程和协作规范
- **`documentation.mdc`** - 文档操作模块，定义文档查找和引用策略

## mdc特性使用

### 结构化标题
使用清晰的标题层级组织内容，便于大模型理解和导航：
```markdown
# 一级标题
## 二级标题
### 三级标题
```

### 代码块
使用语言特定的代码块，提供语法高亮和上下文：
```typescript
@BusinessRule("BR-010")
validateDataQuality(): ValidationResult
```

### 列表和检查点
使用结构化的列表格式，便于大模型识别和验证：
- 检查点1
- 检查点2
- 检查点3

### 引用和链接
使用明确的文件路径引用，便于大模型查找相关信息：
- 参考 `1.3 统一语言.md`
- 查看 `3. 战术设计/聚合根设计/`

## 规则应用方式

### 自动应用
- **`main.mdc`**: 作为主规则，自动应用到所有文件
- **`cursor-user-rules.mdc`**: 用户交互规则，自动应用

### 按需应用
- **模块化规则**: 根据具体需求选择应用相应的规则模块
- **文件类型匹配**: 使用glob模式匹配特定类型的文件

### 规则优先级
1. 架构约束（最高优先级）
2. 业务规则（必须遵循）
3. 命名规范（强烈建议）
4. 工作流程（指导原则）

## 使用建议

### 新用户
1. 首先阅读 `cursor-user-rules.mdc` 了解核心原则
2. 查看 `main.mdc` 了解整体规则结构
3. 根据具体需求查阅相应的模块化规则

### 开发过程中
1. 使用 `workflow.mdc` 指导开发流程
2. 参考 `naming.mdc` 确保命名规范
3. 遵循 `architecture.mdc` 维护架构约束
4. 使用 `documentation.mdc` 查找相关信息

### 代码审查
1. 使用规则检查清单验证代码质量
2. 确保业务规则正确实现
3. 验证架构分层和聚合边界
4. 检查命名是否符合统一语言

## 规则维护

### 更新规则
- 重大变更需要更新主规则文件
- 模块化规则可以独立更新
- 保持规则之间的一致性

### 版本控制
- 规则变更需要记录变更历史
- 重大变更需要版本号升级
- 保持向后兼容性

### 反馈机制
- 欢迎提出规则改进建议
- 规则问题会及时解答
- 规则优化会持续进行

## 注意事项

1. **规则一致性**: 确保所有规则文件之间没有冲突
2. **文档同步**: 规则变更需要同步更新相关文档
3. **团队协作**: 规则变更需要团队共识和培训
4. **持续改进**: 根据实际使用情况不断优化规则

---

**重要提醒**: 这些规则是项目成功的基础，必须严格遵守。如有疑问，请查阅相关模块的详细规则说明。
