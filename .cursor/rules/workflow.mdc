# 工作流程模块

## 新功能开发工作流

### 1. 业务需求理解阶段
```
必须完成的步骤：
1. 查找业务规则 (`1.4 业务规则.md`)
   - 搜索相关的 BR-XXX 编号
   - 确认适用的业务约束
   
2. 确认统一语言术语 (`1.3 统一语言.md`)  
   - 查找对应的 T-XXX 术语ID
   - 使用准确的英文术语命名
   
3. 识别领域概念 (`1.1 领域概述.md`)
   - 确认业务对象归属
   - 理解业务活动流程
```

### 2. 设计决策阶段
```
设计检查清单：
1. 聚合根设计 (`3. 战术设计/聚合根设计/`)
   - 确认聚合边界
   - 识别值对象
   - 定义不变式
   
2. 事件风暴对照 (`1.2 事件风暴.md`)
   - 确认命令-事件映射
   - 验证业务流程完整性
   
3. 数据关系验证 (`附录/数据关系.md`)
   - 物理守恒关系
   - 统计平衡关系
```

### 3. 代码实现阶段
```
实现顺序：
1. 领域层 (Domain)
   - 定义聚合根和实体
   - 实现值对象  
   - 编写业务规则验证
   - 定义领域事件
   
2. 应用层 (Application)
   - 命令处理器
   - 查询处理器
   - 应用服务
   - 事件处理器
   
3. 基础设施层 (Infrastructure)
   - 仓储实现
   - 外部服务适配
   - 数据库映射
```

### 4. 质量保证阶段
```
质量检查：
1. 业务规则覆盖测试
   - 每个 BR-XXX 规则有对应测试
   - 异常路径测试覆盖
   
2. 聚合边界验证测试
   - 跨聚合操作检测
   - 事务边界验证
   
3. 数据关系一致性测试  
   - 物理守恒定律验证
   - 统计平衡关系检查
```

## 代码审查工作流

### 业务逻辑审查（优先级1）
```typescript
审查检查点：
1. 业务规则是否正确实现？
   @BusinessRule("BR-010") // 必须有注解
   validateDataQuality(): ValidationResult {
     // 业务逻辑是否符合规则定义？
   }
   
2. 聚合不变式是否保持？
   class StatisticalReport {
     // 状态变更是否维护不变式？
     publish(): void {
       this.ensureDataQuality(); // 必须检查
     }
   }
   
3. 物理定律是否遵循？
   // 能源平衡：投入 = 产出 + 损耗
   validateEnergyBalance(input, output, losses)
```

### 架构合规审查（优先级2）
```typescript
架构检查点：
1. 依赖方向是否正确？
   // 领域层不能依赖基础设施
   import { Database } from '../infrastructure' // 违规
   
2. 聚合边界是否清晰？
   // 不能直接修改其他聚合
   report.updateTimeSeries() // 违反边界
   
3. 分层职责是否明确？
   // 领域层不处理HTTP请求
   class Report {
     handleHttpRequest() {} // 职责混乱
   }
```

### 命名规范审查（优先级3）
```typescript
命名检查点：
1. 类名是否来自统一语言表？
   class StatisticalReport {} // 来自 T-001
   class DataMgr {}          // 无意义缩写
   
2. 方法名是否体现业务意图？
   validateDataCompleteness() // 明确意图
   process()                  // 过于模糊
   
3. 变量名是否专业准确？
   energyConsumptionData      // 专业术语
   data                       // 过于通用
```

## 文档同步工作流

### 代码变更引起的文档更新
```
文档影响评估：
1. 新增业务规则 → 更新 `1.4 业务规则.md`
2. 修改聚合根 → 更新 `3. 战术设计/聚合根设计/`
3. 新增统一语言 → 更新 `1.3 统一语言.md`  
4. 变更事件流程 → 更新 `1.2 事件风暴.md`
```

### 文档一致性检查
```
一致性验证：
1. 代码中的 @BusinessRule 注解与业务规则文档匹配
2. 类名与统一语言表中的术语ID匹配
3. 聚合根设计与聚合根文档匹配
4. 事件命名与事件风暴文档匹配
```

## 问题排查工作流

### 业务逻辑问题排查
```
排查步骤：
1. 确认业务规则理解是否正确
   - 查看 `1.4 业务规则.md` 中的规则定义
   - 对比代码实现是否匹配
   
2. 检查数据关系验证
   - 物理守恒是否满足？
   - 统计平衡是否达成？
   
3. 验证聚合状态一致性
   - 不变式是否被破坏？
   - 状态转换是否合法？
```

### 架构问题排查
```
排查步骤：
1. 依赖关系检查
   - 是否违反分层架构？
   - 是否存在循环依赖？
   
2. 聚合边界验证
   - 是否跨聚合修改状态？
   - 事务边界是否正确？
   
3. 性能问题分析
   - 是否存在N+1查询？
   - 缓存策略是否合理？
```

## 自动化工作流

### 代码生成前的自动检查
```
自动检查清单：
- 统一语言表已查阅？
- 相关业务规则已确认？
- 聚合边界设计已完成？
- 架构分层职责已明确？
- 错误处理策略已考虑？
```

### 提交前的自动验证
```
验证清单：
- 所有业务规则有测试覆盖？
- 代码命名符合统一语言？
- 聚合不变式测试通过？
- 数据关系验证有效？
- 架构约束检查通过？
```

## 迭代改进工作流

### 重构决策流程
```
重构判断：
1. 业务需求变更 → 重新评估聚合设计
2. 性能问题 → 考虑CQRS分离
3. 复杂度增加 → 评估拆分聚合
4. 技术债务 → 重构基础设施层
```

### 架构演进策略
```
演进原则：
1. 业务优先：先保证业务逻辑正确
2. 渐进式：逐步重构，避免大爆炸
3. 向后兼容：保持API稳定性
4. 测试保护：重构前确保测试覆盖
```

## 团队协作工作流

### 代码评审分工
```
评审角色：
- 业务专家：审查业务规则正确性
- 架构师：审查架构合规性  
- 开发者：审查代码质量
- 测试工程师：审查测试覆盖度
```

### 知识分享机制
```
分享内容：
1. 新发现的业务规则 → 更新文档
2. 架构决策理由 → 记录ADR
3. 重构经验 → 团队分享
4. 性能优化 → 最佳实践积累
```

## 工作流执行原则

1. 文档驱动：所有决策必须基于项目文档
2. 业务优先：技术实现必须服务于业务需求
3. 质量保证：每个阶段都有明确的检查点
4. 持续改进：定期回顾和优化工作流程
5. 团队协作：明确角色分工和协作机制

## 工作流检查清单

### 开发前检查
- 业务需求是否已理解？
- 相关文档是否已查阅？
- 设计决策是否已完成？
- 技术方案是否已确定？

### 开发中检查
- 是否遵循架构约束？
- 是否实现业务规则？
- 是否维护聚合边界？
- 是否使用正确命名？

### 开发后检查
- 测试是否覆盖完整？
- 文档是否同步更新？
- 代码是否通过审查？
- 部署是否成功？

记住：好的工作流程是团队效率和代码质量的保障，必须严格执行。
