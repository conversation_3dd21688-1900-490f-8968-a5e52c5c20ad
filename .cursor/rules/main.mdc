# DDD能源系统开发主规则

## 规则概述

本规则集定义了DDD能源统计系统开发的完整约束体系，包括架构设计、命名规范、工作流程和文档操作等核心模块。

## 模块化规则结构

### 1. 架构约束模块
- **文件路径**: `.cursor/rules/architecture.mdc`
- **适用范围**: 所有代码文件
- **核心约束**: 分层依赖、聚合边界、物理定律验证
- **强制要求**: 严格遵循依赖方向，维护业务一致性边界

### 2. 命名规范模块  
- **文件路径**: `.cursor/rules/naming.mdc`
- **适用范围**: 所有代码文件
- **核心约束**: 统一语言、业务术语、命名模式
- **强制要求**: 使用项目术语标准，避免无意义缩写

### 3. 工作流程模块
- **文件路径**: `.cursor/rules/workflow.mdc`
- **适用范围**: 开发流程和协作规范
- **核心约束**: 开发流程、代码审查、质量保证
- **强制要求**: 文档驱动决策，业务优先原则

### 4. 文档操作模块
- **文件路径**: `.cursor/rules/documentation.mdc`
- **适用范围**: 文档查找和引用策略
- **核心约束**: 文档权威性、引用准确性、一致性维护
- **强制要求**: 基于项目文档决策，禁止假设和猜测

## 规则应用优先级

### 最高优先级（必须遵循）
1. **架构约束**: 违反将导致系统架构问题
2. **业务规则**: 违反将导致业务逻辑错误
3. **统一语言**: 违反将导致概念混乱

### 高优先级（强烈建议）
1. **命名规范**: 影响代码可读性和维护性
2. **工作流程**: 影响开发效率和代码质量
3. **文档操作**: 影响知识传递和决策准确性

## 规则执行检查点

### 代码生成前检查
- 是否遵循架构分层约束？
- 是否使用正确的业务术语？
- 是否实现了必要的业务规则？
- 是否维护了聚合边界？

### 代码审查时检查
- 是否体现了领域专业性？
- 命名是否来自统一语言？
- 业务规则是否正确实现？
- 架构分层是否清晰？

### 文档更新时检查
- 是否同步更新了相关文档？
- 术语使用是否一致？
- 业务规则是否完整？
- 引用是否准确？

## 规则违反处理

### 轻微违反
- 提醒用户注意规则要求
- 建议按照规则进行修改
- 提供具体的修改建议

### 严重违反
- 拒绝提供代码建议
- 明确说明违反的具体规则
- 要求重新设计或修改

### 架构违规
- 立即停止并提供警告
- 解释违规的潜在风险
- 建议重新审视设计方案

## 规则学习和适应

### 新用户引导
1. 首先阅读主规则文件
2. 理解核心设计原则
3. 熟悉工作流程要求
4. 掌握文档查找方法

### 规则更新通知
- 重大规则变更会提前通知
- 规则解释会提供具体示例
- 规则冲突会及时澄清

### 规则反馈机制
- 欢迎提出规则改进建议
- 规则问题会及时解答
- 规则优化会持续进行

## 核心原则总结

1. **业务优先**: 技术实现必须服务于业务需求
2. **文档驱动**: 所有决策基于项目文档，禁止假设
3. **架构约束**: 严格遵循分层依赖和聚合边界
4. **统一语言**: 使用项目术语标准，保持概念一致
5. **质量保证**: 每个阶段都有明确的检查点

## 规则文件引用

```
架构约束: .cursor/rules/architecture.mdc
命名规范: .cursor/rules/naming.mdc  
工作流程: .cursor/rules/workflow.mdc
文档操作: .cursor/rules/documentation.mdc
用户规则: .cursor/rules/cursor-user-rules.mdc
```

---

**重要提醒**: 这些规则是项目成功的基础，必须严格遵守。如有疑问，请查阅相关模块的详细规则说明。
description:
globs:
alwaysApply: false
---
