# 文档操作模块

## 文档优先级策略

### 查找顺序（强制执行）
```
业务概念相关 → 查找 `1.3 统一语言.md`
业务规则相关 → 查找 `1.4 业务规则.md`  
聚合根设计 → 查找 `3. 战术设计/聚合根设计/`
事件流程相关 → 查找 `1.2 事件风暴.md`
数据关系相关 → 查找 `附录/数据关系.md`
不确定概念 → 查找 `1.1 领域概述.md`
```

### 文档引用规范
```
术语引用格式：T-001 Statistical Report
业务规则格式：BR-010 数据质量评估强制性
代码引用格式：```12:15:path/to/file.md```
聚合根引用：参考《统计报表聚合根.md》第X节
```

## 主动搜索策略

### 遇到业务概念时
```typescript
// 用户提到：报表、统计、质量评估
→ 立即搜索 `1.3 统一语言.md` 
→ 查找对应的英文术语ID
→ 在回答中明确引用：
   "根据统一语言表，统计报表对应 T-001 Statistical Report"
```

### 涉及业务约束时
```typescript
// 用户提到：验证、检查、规则、约束
→ 立即搜索 `1.4 业务规则.md`
→ 查找相关的 BR-XXX 编号  
→ 在代码中体现：
   @BusinessRule("BR-010")
   validateDataQuality(): ValidationResult
```

### 设计聚合根时
```typescript
// 用户提到：实体、聚合、领域对象
→ 搜索 `3. 战术设计/聚合根设计/` 目录
→ 参考已有聚合根设计模式
→ 确保聚合边界合理性
```

## 文档更新策略

### 新增术语处理
```
发现新的业务术语时：
1. 检查 `1.3 统一语言.md` 是否已存在
2. 如果不存在，建议用户添加到统一语言表
3. 暂时使用描述性命名，等待正式术语确认
4. 记录临时命名到待优化列表
```

### 新增业务规则处理
```
发现新的业务约束时：
1. 检查 `1.4 业务规则.md` 是否涵盖
2. 如果不涵盖，提醒用户更新业务规则文档
3. 在代码中使用临时注解：@BusinessRule("PENDING-XXX")
4. 建议具体的业务规则描述文本
```

### 聚合根设计变更
```
修改聚合根时：
1. 检查是否影响现有聚合根文档
2. 建议同步更新 `3. 战术设计/` 中对应文档
3. 验证聚合边界变更的合理性
4. 提醒检查其他聚合根的影响
```

## 引用准确性要求

### 必须精确引用
```markdown
正确引用：
- "根据业务规则 BR-010，数据质量评估是强制性的"
- "使用统一语言 T-001 Statistical Report 作为类名"
- "参考```45:67:3. 战术设计/聚合根设计/统计报表聚合根.md```"

错误引用：
- "根据业务规则，需要验证数据" // 没有具体规则编号
- "使用报表类" // 没有引用统一语言
- "看看聚合根文档" // 引用不具体
```

### 引用验证检查
```
提供建议前必须确认：
- 术语ID引用准确？
- 业务规则编号正确？
- 文档路径引用精确？
- 所引用内容确实存在？
```

## 文档导向决策

### 业务优先原则
```
决策顺序：
1. 业务规则要求 (来自 1.4 业务规则.md)
2. 领域概念定义 (来自 1.1 领域概述.md)  
3. 统一语言约定 (来自 1.3 统一语言.md)
4. 技术实现考虑 (架构约束)
5. 性能优化需求 (最后考虑)
```

### 文档缺失处理
```
当文档信息不足时：
1. 明确告知用户信息缺失
2. 建议具体需要补充的文档内容
3. 提供基于现有信息的临时方案
4. 强调方案的局限性和风险
5. 不基于假设提供建议
```

## 文档一致性维护

### 跨文档一致性检查
```
检查要点：
1. 统一语言表 ↔ 业务规则文档
   - 术语使用是否一致？
   - 新规则是否引用正确术语？
   
2. 事件风暴 ↔ 聚合根设计  
   - 事件与聚合根对应关系
   - 命令处理与聚合根方法匹配
   
3. 领域概述 ↔ 具体实现
   - 业务对象是否准确建模？
   - 业务活动是否完整覆盖？
```

### 文档版本管理建议
```
版本控制策略：
1. 重大业务规则变更 → 版本号升级
2. 术语新增/修改 → 记录变更历史
3. 聚合根重构 → 保留设计决策记录
4. 架构调整 → ADR (Architecture Decision Record)
```

## 文档操作禁忌

### 绝对禁止的行为
```
- 基于常识而非项目文档给建议
- 假设业务需求而不查阅文档  
- 忽略已有的术语定义
- 绕过业务规则约束
- 提供与文档冲突的方案
```

### 避免的表达方式
```
错误表达：
- "一般来说..."
- "通常情况下..."  
- "按照常规做法..."
- "我觉得应该..."

正确表达：
- "根据业务规则 BR-010..."
- "统一语言表 T-001 定义为..."
- "聚合根设计文档建议..."
- "数据关系文档要求..."
```

## 文档操作检查清单

### 回答问题前检查
```
- 相关文档已查找？
- 术语ID已确认？
- 业务规则已核实？
- 引用格式正确？
- 信息来源明确？
```

### 提供建议前验证
```
- 建议基于项目文档？
- 没有假设或猜测？
- 符合业务规则约束？
- 与统一语言一致？
- 注明信息来源？
```

## 文档学习建议

### 优先熟悉的文档
```
核心文档（必须掌握）：
1. `1.3 统一语言.md` - 术语标准
2. `1.4 业务规则.md` - 约束规范
3. `1.1 领域概述.md` - 业务理解

重要文档（经常引用）：
4. `1.2 事件风暴.md` - 流程理解  
5. `3. 战术设计/` - 设计参考
6. `附录/数据关系.md` - 数据约束
```

### 文档使用技巧
```
高效查找：
1. 关键词搜索 → 快速定位
2. 术语ID索引 → 精确引用
3. 业务规则编号 → 约束验证
4. 交叉引用 → 一致性检查
```

## 核心原则总结

1. 文档权威性：项目文档是知识的唯一权威来源
2. 引用精确性：所有引用必须准确到具体位置和编号
3. 一致性维护：跨文档信息必须保持一致
4. 假设禁止：绝不基于假设或常识提供建议
5. 业务优先：技术决策必须基于业务规则和领域概念

记住：文档是项目知识的权威来源，必须严格遵循，绝不能偏离。
