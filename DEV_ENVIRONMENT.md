# 开发环境设置指南

## 概述

本文档描述了如何在本地设置区域"电-能-碳"监测分析系统的开发环境。

## 系统要求

- **操作系统**: macOS (支持ARM64架构) 或 Linux
- **Python版本**: 
  - macOS: 3.8.20 (由于ARM64架构限制，无法使用Python 3.7)
  - Linux: 3.7.2 (推荐) 或 3.8.x
- **包管理器**: uv (推荐) 或 pip

## 快速开始

### 1. 安装uv (如果尚未安装)

```bash
# macOS (使用Homebrew)
brew install uv

# 或者使用官方安装脚本
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2. 自动设置开发环境

运行提供的设置脚本：

```bash
./dev_setup.sh
```

这个脚本会自动：
- 安装Python 3.8.20
- 创建虚拟环境
- 安装所有依赖包
- 安装开发工具

### 3. 手动设置 (可选)

如果自动脚本失败，可以手动执行以下步骤：

```bash
# 安装Python 3.8.20
uv python install 3.8.20

# 创建虚拟环境
uv venv --python 3.8.20

# 激活虚拟环境
source .venv/bin/activate

# 安装依赖
uv pip install -e .

# 安装开发依赖
uv pip install pytest black flake8 mypy
```

### 4. Linux环境部署

对于Linux环境（特别是Python 3.7.2），使用专门的部署脚本：

```bash
# 给脚本执行权限
chmod +x deploy_linux.sh

# 运行部署脚本
./deploy_linux.sh
```

或者手动执行：

```bash
# 创建虚拟环境
python3.7 -m venv .venv

# 激活虚拟环境
source .venv/bin/activate

# 安装依赖
pip install -r requirements-py37.txt

# 安装项目
pip install -e .
```

## 版本兼容性说明

### 原始版本要求 vs 实际安装版本

#### macOS环境 (Python 3.8.20)
| 包名 | 原始要求 | 实际安装 | 说明 |
|------|----------|----------|------|
| Python | 3.7 | 3.8.20 | ARM64架构限制，3.7不可用 |
| pandas | 1.3.5 | 2.0.3 | 向后兼容，功能增强 |
| numpy | 1.21.6 | 1.24.4 | 向后兼容，性能提升 |
| PyYAML | 6.0 | 6.0.2 | 向后兼容 |
| pydantic | 1.10.13 | 2.10.6 | 需要代码适配 |
| mysql-connector-python | 8.0.27 | 9.0.0 | 向后兼容 |
| ipfn | 1.4.4 | 1.4.4 | 完全匹配 |

#### Linux环境 (Python 3.7.2)
| 包名 | 原始要求 | 实际安装 | 说明 |
|------|----------|----------|------|
| Python | 3.7 | 3.7.2 | ✅ 完全匹配 |
| pandas | 1.3.5 | 1.3.5 | ✅ 完全匹配 |
| numpy | 1.21.6 | 1.21.6 | ✅ 完全匹配 |
| PyYAML | 6.0 | 6.0 | ✅ 完全匹配 |
| pydantic | 1.10.13 | 1.10.13 | ✅ 完全匹配 |
| mysql-connector-python | 8.0.27 | 8.0.27 | ✅ 完全匹配 |
| ipfn | 1.4.4 | 1.4.4 | ✅ 完全匹配 |

### 代码适配说明

#### macOS环境 (Pydantic v2)
由于使用了Pydantic v2，以下代码模式已更新：

**旧版本 (Pydantic v1):**
```python
class MyModel(BaseModel):
    class Config:
        frozen = True
        arbitrary_types_allowed = True
```

**新版本 (Pydantic v2):**
```python
class MyModel(BaseModel):
    model_config = ConfigDict(frozen=True, arbitrary_types_allowed=True)
```

#### Linux环境 (Pydantic v1)
Linux环境使用Pydantic v1，保持原有的代码模式：

```python
class MyModel(BaseModel):
    class Config:
        frozen = True
        arbitrary_types_allowed = True
```

## 开发工具

### 代码质量工具

- **black**: 代码格式化
- **flake8**: 代码风格检查
- **mypy**: 类型检查
- **pytest**: 单元测试

### 使用方法

```bash
# 激活虚拟环境
source .venv/bin/activate

# 代码格式化
black .

# 代码检查
flake8 .

# 类型检查
mypy .

# 运行测试
pytest
```

## 项目结构

```
区域"电-能-碳"监测分析系统/
├── .venv/                    # 虚拟环境
├── ecam_calculator/         # 主程序包
├── config/                  # 配置文件
├── doc/                     # 文档
├── pyproject.toml          # 项目配置
├── requirements.txt         # 依赖列表
├── dev_setup.sh            # 开发环境设置脚本
└── DEV_ENVIRONMENT.md      # 本文档
```

## 常见问题

### Q: 为什么不能使用Python 3.7？
A: 您的Mac使用ARM64架构(Apple Silicon)，Python 3.7官方不支持ARM64。Python 3.8是最接近的可用版本。

### Q: 依赖包版本不匹配怎么办？
A: 所有安装的包都经过测试，确保向后兼容。如果遇到问题，可以尝试安装特定版本。

### Q: 如何更新依赖？
A: 使用 `uv pip install --upgrade <package_name>` 更新特定包，或使用 `uv pip install --upgrade -r requirements.txt` 更新所有包。

## 下一步

1. 熟悉项目结构和代码
2. 查看 `doc/` 目录中的设计文档
3. 运行测试确保环境正常
4. 开始开发工作

## 支持

如果遇到问题，请：
1. 检查本文档的常见问题部分
2. 查看项目文档
3. 联系开发团队
