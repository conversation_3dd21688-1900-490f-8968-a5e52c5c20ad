# 能源标准化后折算为标准煤并加总的调用流程修改说明

## 修改概述

根据您的建议，我们对能源标准化后折算为标准煤并加总的调用流程进行了重构，确保能源转换逻辑在正确的阶段执行，而不是在数据标准化阶段。

## 修改内容

### 1. 数据标准化阶段修改

**文件**: `ecam_calculator/domain/service/data_standardization_service.py`

**修改内容**:
- 移除了 `_standardize_energy` 方法中的能源转换调用
- 将 `_convert_to_standard_units` 方法改为占位符，不再执行实际转换
- 标准化阶段只负责行业和能源品种的映射，不涉及能源转换

**修改前**:
```python
# 4. 单位换算（折标）
df = self._convert_to_standard_units(df, table_config)
```

**修改后**:
```python
# 注意：能源转换（折标）不在此阶段进行，应在后续的能源转换服务中处理
```

### 2. 应用服务流程修改

**文件**: `ecam_calculator/application/calculation_job_service.py`

**修改内容**:
- 在计算流程中添加了新的 `_step_energy_conversion` 步骤
- 在数据标准化完成后，清单构建前执行能源转换
- 添加了能源转换服务的依赖注入

**新的流程顺序**:
1. 获取原始数据
2. 数据质量检查
3. 数据标准化
4. **能源转换（折标）** ← 新增步骤
5. 约束计算
6. 数据平衡
7. 清单构建
8. 结果输出
9. 完成

### 3. 能源转换服务集成

**新增方法**: `_step_energy_conversion`

**功能**:
- 收集所有能源类型的标准化数据
- 调用 `EnergyConversionService.convert_to_standard_coal` 进行批量转换
- 将转换后的数据重新封装为 `StandardizedData` 对象
- 更新上下文中的能源数据

## 调用流程

### 修改后的完整调用链路

```
计算任务服务 (CalculationJobService)
    ↓
数据标准化服务 (DataStandardizationServiceImpl)
    - 行业名称标准化
    - 能源品种标准化
    - 不进行能源转换
    ↓
能源转换服务 (EnergyConversionService) ← 新增步骤
    - 批量预加载转换因子
    - 向量化能源转换
    - 生成标准煤当量
    ↓
清单构建服务 (InventoryConstructionServiceImpl)
    - 数据平衡
    - 能源加总
    - 生成最终清单
```

### 数据流转

1. **原始数据** → `RawData` 对象
2. **标准化数据** → `StandardizedData` 对象（保持原始值和单位）
3. **转换后数据** → `StandardizedData` 对象（包含标准煤当量）
4. **清单数据** → `EnergyConsumptionInventory` 对象（包含加总结果）

## 数据结构适合性

### ✅ 优势

1. **职责分离清晰**: 
   - 数据标准化：负责映射和分类
   - 能源转换：负责折标计算
   - 清单构建：负责加总和平衡

2. **配置驱动**: 
   - 标准化映射通过 `parameters_refactor.yaml` 配置
   - 转换系数从数据库动态获取

3. **性能优化**: 
   - 批量预加载转换因子
   - 向量化处理

4. **错误处理**: 
   - 每个阶段都有独立的错误处理
   - 转换失败时有回退机制

### 🔧 改进点

1. **数据验证**: 在能源转换前增加数据完整性检查
2. **缓存策略**: 对频繁使用的转换系数进行更有效的缓存
3. **性能监控**: 添加转换过程的性能指标监控

## 测试验证

创建了测试脚本 `test_energy_conversion_flow.py` 来验证修改后的流程：

1. **数据标准化测试**: 验证行业和能源品种的标准化映射
2. **能源转换测试**: 验证标准煤当量的计算
3. **清单构建测试**: 验证数据的加总和汇总

## 总结

通过这次修改，我们实现了：

1. **清晰的职责分离**: 数据标准化和能源转换各司其职
2. **正确的调用顺序**: 能源转换在数据标准化之后，清单构建之前
3. **可维护的代码结构**: 每个阶段都有明确的输入输出
4. **配置驱动的设计**: 通过配置文件管理标准化映射规则

这样的设计使得能源标准化后折算为标准煤并加总的流程更加清晰、可维护，并且符合单一职责原则。


