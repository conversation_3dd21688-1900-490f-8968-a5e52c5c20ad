/* D2引用样式 */
.d2-ref {
  max-width: 100%;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  &:hover {
    border-color: #007acc;
    transform: translateY(-2px);
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }
}

/* D2文件链接样式 */
a[href$=".d2"] {
  &:before {
    content: "📊 ";
  }
  
  color: #0066cc;
  text-decoration: none;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(0, 102, 204, 0.1);
  
  &:hover {
    background: rgba(0, 102, 204, 0.2);
    transform: translateY(-1px);
    transition: all 0.2s ease;
  }
}

/* 架构图特殊样式 */
img[alt*="架构"], img[alt*="architecture"] {
  border: 2px solid #4caf50;
  border-radius: 8px;
  padding: 8px;
  background: white;
}

/* 流程图特殊样式 */
img[alt*="流程"], img[alt*="flow"] {
  border: 2px solid #ff9800;
  border-radius: 8px;
  padding: 8px;
  background: white;
}

/* 领域模型特殊样式 */
img[alt*="领域"], img[alt*="domain"] {
  border: 2px solid #9c27b0;
  border-radius: 8px;
  padding: 8px;
  background: white;
}

/* ========== 主题自适应样式 ========== */

/* 方案一：CSS媒体查询主题切换 */
.theme-adaptive-diagram {
  max-width: 100%;
  
  img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
    border-radius: 8px;
    transition: opacity 0.3s ease;
  }
  
  /* 浅色主题显示 */
  @media (prefers-color-scheme: light) {
    .light-theme {
      display: block;
    }
    .dark-theme {
      display: none;
    }
  }
  
  /* 深色主题显示 */
  @media (prefers-color-scheme: dark) {
    .light-theme {
      display: none;
    }
    .dark-theme {
      display: block;
    }
  }
  
  /* 默认显示浅色（兼容不支持媒体查询的环境） */
  .dark-theme {
    display: none;
  }
}

/* 方案二：Picture元素样式增强 */
picture {
  display: block;
  text-align: center;
  margin: 16px 0;
  
  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
  }
}

/* 方案三：GitHub风格主题切换支持 */
img[src$="#gh-light-mode-only"] {
  display: block;
}

img[src$="#gh-dark-mode-only"] {
  display: none;
}

@media (prefers-color-scheme: dark) {
  img[src$="#gh-light-mode-only"] {
    display: none;
  }
  
  img[src$="#gh-dark-mode-only"] {
    display: block;
  }
}

/* 深色主题下的样式调整 */
@media (prefers-color-scheme: dark) {
  .d2-ref {
    background: linear-gradient(135deg, #2c2c2c 0%, #1e1e1e 100%);
    border-color: #444;
    color: #e0e0e0;
  }
  
  a[href$=".d2"] {
    color: #66b3ff;
    background: rgba(102, 179, 255, 0.1);
    
    &:hover {
      background: rgba(102, 179, 255, 0.2);
    }
  }
}

/* 自定义主题切换按钮样式（可选） */
.theme-toggle-container {
  position: relative;
  display: inline-block;
  margin: 8px 0;
  
  .theme-toggle {
    background: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  @media (prefers-color-scheme: dark) {
    .theme-toggle {
      border-color: #666;
      color: #e0e0e0;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
} 