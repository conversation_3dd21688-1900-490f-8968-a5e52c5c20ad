2025-09-02 21:03:59,111 - __main__ - INFO - 原始数据读取测试器初始化完成
2025-09-02 21:03:59,111 - __main__ - INFO - 数据库配置: {'host': 'localhost', 'port': 9999, 'database': 'ecam_city', 'user': 'user', 'password': 'password'}
2025-09-02 21:03:59,111 - __main__ - INFO - 测试参数: 省份=山西, 城市=太原, 年份=2020
2025-09-02 21:03:59,111 - __main__ - INFO - 开始运行完整的原始数据读取功能测试
2025-09-02 21:03:59,111 - __main__ - INFO - 测试时间: 2025-09-02 21:03:59.111436
2025-09-02 21:03:59,111 - __main__ - INFO - === 测试数据库连接 ===
2025-09-02 21:03:59,111 - __main__ - INFO - 连接参数: {'host': 'localhost', 'port': 9999, 'database': 'ecam_city', 'user': 'user', 'password': 'password', 'charset': 'utf8mb4', 'collation': 'utf8mb4_general_ci', 'use_unicode': True, 'autocommit': True, 'connect_timeout': 30, 'read_timeout': 60}
2025-09-02 21:03:59,231 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,233 - __main__ - INFO - 数据库连接测试成功: (1,)
2025-09-02 21:03:59,234 - __main__ - INFO - 数据库版本: ('5.7.44',)
2025-09-02 21:03:59,235 - __main__ - INFO - 主要数据表存在: True
2025-09-02 21:03:59,235 - __main__ - INFO - === 测试能源排放因子获取 ===
2025-09-02 21:03:59,235 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 正在获取2020年山西的能源排放因子...
2025-09-02 21:03:59,255 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,271 - ecam_calculator.infrastructure.raw_data_repository_impl - WARNING - 表 ecam_in_energy_factor 返回空数据
2025-09-02 21:03:59,272 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 未找到省份'山西'的排放因子，将使用'全国'数据。
2025-09-02 21:03:59,291 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,294 - ecam_calculator.infrastructure.raw_data_repository_impl - WARNING - 表 ecam_in_energy_factor 返回空数据
2025-09-02 21:03:59,294 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 获取到0条能源因子记录
2025-09-02 21:03:59,296 - __main__ - INFO - 能源排放因子 验证结果:
2025-09-02 21:03:59,296 - __main__ - INFO -   - 表名: ecam_in_energy_factor
2025-09-02 21:03:59,296 - __main__ - INFO -   - 数据源: 能源因子
2025-09-02 21:03:59,296 - __main__ - INFO -   - 记录数: 0
2025-09-02 21:03:59,296 - __main__ - INFO -   - 列数: 0
2025-09-02 21:03:59,296 - __main__ - INFO - --- 测试参数验证 ---
2025-09-02 21:03:59,296 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 正在获取1800年山西的能源排放因子...
2025-09-02 21:03:59,296 - ecam_calculator.infrastructure.raw_data_repository_impl - ERROR - 无效的年份参数: 1800
2025-09-02 21:03:59,296 - __main__ - INFO - 无效年份测试结果: 记录数=0
2025-09-02 21:03:59,296 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 正在获取2020年的能源排放因子...
2025-09-02 21:03:59,296 - ecam_calculator.infrastructure.raw_data_repository_impl - ERROR - 无效的省份参数: 
2025-09-02 21:03:59,296 - __main__ - INFO - 无效省份测试结果: 记录数=0
2025-09-02 21:03:59,296 - __main__ - INFO - 能源排放因子 测试: 失败
2025-09-02 21:03:59,296 - __main__ - INFO - === 测试折标准煤系数获取 ===
2025-09-02 21:03:59,296 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 正在获取2020年山西的能源折标系数...
2025-09-02 21:03:59,316 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,318 - ecam_calculator.infrastructure.raw_data_repository_impl - WARNING - 表 ecam_in_energy_factor 返回空数据
2025-09-02 21:03:59,319 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 未找到省份 '山西' 的折标系数，将使用'全国'数据。
2025-09-02 21:03:59,337 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,341 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 表 ecam_in_energy_factor 数据验证通过，共 33 条记录
2025-09-02 21:03:59,344 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 获取到18条能源折标系数记录
2025-09-02 21:03:59,344 - __main__ - INFO - 折标准煤系数 验证结果:
2025-09-02 21:03:59,344 - __main__ - INFO -   - 表名: 折标系数
2025-09-02 21:03:59,344 - __main__ - INFO -   - 数据源: 折标系数
2025-09-02 21:03:59,344 - __main__ - INFO -   - 记录数: 18
2025-09-02 21:03:59,344 - __main__ - INFO -   - 列数: 2
2025-09-02 21:03:59,344 - __main__ - INFO -   - 列名: ['energy_type', 'value']
2025-09-02 21:03:59,344 - __main__ - INFO -   - 数据类型: {'energy_type': dtype('O'), 'value': dtype('float64')}
2025-09-02 21:03:59,344 - __main__ - INFO -   - 元数据: {'record_count': 18, 'columns': ['energy_type', 'value'], 'data_types': {'energy_type': dtype('O'), 'value': dtype('float64')}, 'timestamp': '2025-09-02T21:03:59.344297'}
2025-09-02 21:03:59,344 - __main__ - INFO - 折标系数数据分析:
2025-09-02 21:03:59,344 - __main__ - INFO - 能源类型数量: 18
2025-09-02 21:03:59,345 - __main__ - INFO - 能源类型列表: ['电力', '高炉煤气', '转炉煤气', '石蜡', '石脑油', '石油焦', '石油沥青', '燃料油', '煤油', '焦炭', '焦炉煤气', '炼厂干气', '液化石油气', '润滑油', '汽油', '其他能源', '其他煤气', '其他焦化产品']
2025-09-02 21:03:59,345 - __main__ - INFO - 折标系数范围: 0.9714 - 5.7140
2025-09-02 21:03:59,345 - __main__ - INFO - 折标准煤系数 测试: 成功
2025-09-02 21:03:59,345 - __main__ - INFO - === 测试月度用电量获取 ===
2025-09-02 21:03:59,345 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 正在获取山西从2020-01-01到2020-12-31的月度分行业用电量...
2025-09-02 21:03:59,365 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,635 - ecam_calculator.infrastructure.raw_data_repository_impl - WARNING - 表 ecam_in_m_pro_ind_ele_off 返回空数据
2025-09-02 21:03:59,635 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 获取到0条月度用电量记录
2025-09-02 21:03:59,635 - __main__ - INFO - 月度用电量 验证结果:
2025-09-02 21:03:59,635 - __main__ - INFO -   - 表名: ecam_in_m_pro_ind_ele_off
2025-09-02 21:03:59,635 - __main__ - INFO -   - 数据源: 月度分行业用电量
2025-09-02 21:03:59,635 - __main__ - INFO -   - 记录数: 0
2025-09-02 21:03:59,636 - __main__ - INFO -   - 列数: 0
2025-09-02 21:03:59,636 - __main__ - INFO - 月度用电量 测试: 失败
2025-09-02 21:03:59,636 - __main__ - INFO - === 测试年度省级能源消费量获取 ===
2025-09-02 21:03:59,636 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 正在获取 山西 从 2020 到 2020 的年度省级能源消费量...
2025-09-02 21:03:59,653 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,679 - ecam_calculator.infrastructure.raw_data_repository_impl - WARNING - 表 ecam_in_y_pro_ind_ene_off 存在 757 条无效数值数据
2025-09-02 21:03:59,679 - ecam_calculator.infrastructure.raw_data_repository_impl - WARNING - 表 ecam_in_y_pro_ind_ene_off 存在 73 条负值数据
2025-09-02 21:03:59,679 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 表 ecam_in_y_pro_ind_ene_off 数据验证通过，共 1120 条记录
2025-09-02 21:03:59,680 - ecam_calculator.infrastructure.raw_data_repository_impl - WARNING - 过滤掉 757 条无效数据
2025-09-02 21:03:59,680 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 获取到 363 条省级终端消费与加工转换记录
2025-09-02 21:03:59,680 - __main__ - INFO - 年度省级能源消费量 验证结果:
2025-09-02 21:03:59,680 - __main__ - INFO -   - 表名: ecam_in_y_pro_ind_ene_off
2025-09-02 21:03:59,680 - __main__ - INFO -   - 数据源: 年度省级分行业分品种能源消费量
2025-09-02 21:03:59,680 - __main__ - INFO -   - 记录数: 363
2025-09-02 21:03:59,680 - __main__ - INFO -   - 列数: 8
2025-09-02 21:03:59,680 - __main__ - INFO -   - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-02 21:03:59,681 - __main__ - INFO -   - 数据类型: {'year': dtype('int64'), 'province': dtype('O'), 'item': dtype('O'), 'convert': dtype('O'), 'method': dtype('O'), 'energy_type': dtype('O'), 'value': dtype('float64'), 'unit': dtype('O')}
2025-09-02 21:03:59,681 - __main__ - INFO -   - 元数据: {'record_count': 363, 'columns': ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit'], 'data_types': {'year': dtype('int64'), 'province': dtype('O'), 'item': dtype('O'), 'convert': dtype('O'), 'method': dtype('O'), 'energy_type': dtype('O'), 'value': dtype('float64'), 'unit': dtype('O')}, 'timestamp': '2025-09-02T21:03:59.680790'}
2025-09-02 21:03:59,681 - __main__ - INFO - 省级能源消费量数据分析:
2025-09-02 21:03:59,681 - __main__ - INFO - 年份范围: 2020 - 2020
2025-09-02 21:03:59,681 - __main__ - INFO - 行业数量: 28
2025-09-02 21:03:59,681 - __main__ - INFO - 能源类型数量: 29
2025-09-02 21:03:59,681 - __main__ - INFO - 总能源消费量: 701930.83
2025-09-02 21:03:59,682 - __main__ - INFO - 各能源类型消费量:
2025-09-02 21:03:59,682 - __main__ - INFO -   其他洗煤: 91400.93
2025-09-02 21:03:59,682 - __main__ - INFO -   其他焦化产品: 1878.47
2025-09-02 21:03:59,682 - __main__ - INFO -   其他煤气: 108.04
2025-09-02 21:03:59,682 - __main__ - INFO -   其他石油制品: 50.91
2025-09-02 21:03:59,683 - __main__ - INFO -   其他能源: 195.22
2025-09-02 21:03:59,683 - __main__ - INFO -   原煤: 138172.01
2025-09-02 21:03:59,683 - __main__ - INFO -   天然气: 366.44
2025-09-02 21:03:59,683 - __main__ - INFO -   柴油: 1998.07
2025-09-02 21:03:59,683 - __main__ - INFO -   汽油: 1178.20
2025-09-02 21:03:59,683 - __main__ - INFO -   油品合计: 3635.47
2025-09-02 21:03:59,683 - __main__ - INFO -   洗精煤: 36666.50
2025-09-02 21:03:59,683 - __main__ - INFO -   润滑油: 24.10
2025-09-02 21:03:59,683 - __main__ - INFO -   液化天然气: 351.32
2025-09-02 21:03:59,683 - __main__ - INFO -   液化石油气: 64.89
2025-09-02 21:03:59,683 - __main__ - INFO -   热力: 175345.94
2025-09-02 21:03:59,683 - __main__ - INFO -   焦炉煤气: 949.90
2025-09-02 21:03:59,683 - __main__ - INFO -   焦炭: 30110.62
2025-09-02 21:03:59,683 - __main__ - INFO -   煤制品: 509.72
2025-09-02 21:03:59,683 - __main__ - INFO -   煤合计: 198700.20
2025-09-02 21:03:59,683 - __main__ - INFO -   煤油: 201.57
2025-09-02 21:03:59,683 - __main__ - INFO -   煤矸石: 1278.90
2025-09-02 21:03:59,683 - __main__ - INFO -   燃料油: 4.37
2025-09-02 21:03:59,683 - __main__ - INFO -   电力: 15105.67
2025-09-02 21:03:59,683 - __main__ - INFO -   石油沥青: 146.85
2025-09-02 21:03:59,683 - __main__ - INFO -   石油焦: 62.68
2025-09-02 21:03:59,683 - __main__ - INFO -   石脑油: 22.68
2025-09-02 21:03:59,683 - __main__ - INFO -   石蜡: 10.43
2025-09-02 21:03:59,683 - __main__ - INFO -   转炉煤气: 229.13
2025-09-02 21:03:59,683 - __main__ - INFO -   高炉煤气: 3161.60
2025-09-02 21:03:59,683 - __main__ - INFO - 年度省级能源消费量 测试: 成功
2025-09-02 21:03:59,683 - __main__ - INFO - === 测试年度地市能源消费量获取 ===
2025-09-02 21:03:59,683 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 正在为城市 '太原' 获取 2020-2020 的工业能源消费量...
2025-09-02 21:03:59,703 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,703 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 步骤1: 正在 ene2 表中查询市级数据: 太原...
2025-09-02 21:03:59,722 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,898 - ecam_calculator.infrastructure.raw_data_repository_impl - ERROR - 表 ecam_in_y_pro_ind_ene2_off 缺少必需列: ['item']
2025-09-02 21:03:59,899 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 未找到市级数据，步骤2: 正在 ene 表中回退查询省级宏观行业数据: 山西...
2025-09-02 21:03:59,917 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,936 - ecam_calculator.infrastructure.raw_data_repository_impl - WARNING - 表 ecam_in_y_pro_ind_ene_off 存在 5 条无效数值数据
2025-09-02 21:03:59,936 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 表 ecam_in_y_pro_ind_ene_off 数据验证通过，共 32 条记录
2025-09-02 21:03:59,937 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 成功最终回退并获取到 32 条省级宏观工业能源消费量记录 (来自 ene)。
2025-09-02 21:03:59,937 - __main__ - INFO - 年度地市能源消费量 验证结果:
2025-09-02 21:03:59,937 - __main__ - INFO -   - 表名: 年度地市工业能源消费量_ene_fallback
2025-09-02 21:03:59,937 - __main__ - INFO -   - 数据源: 年度地市工业能源消费量_ene_fallback
2025-09-02 21:03:59,937 - __main__ - INFO -   - 记录数: 32
2025-09-02 21:03:59,937 - __main__ - INFO -   - 列数: 8
2025-09-02 21:03:59,937 - __main__ - INFO -   - 列名: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-02 21:03:59,938 - __main__ - INFO -   - 数据类型: {'year': dtype('int64'), 'province': dtype('O'), 'item': dtype('O'), 'convert': dtype('O'), 'method': dtype('O'), 'energy_type': dtype('O'), 'value': dtype('float64'), 'unit': dtype('O')}
2025-09-02 21:03:59,938 - __main__ - INFO -   - 元数据: {'record_count': 32, 'columns': ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit'], 'data_types': {'year': dtype('int64'), 'province': dtype('O'), 'item': dtype('O'), 'convert': dtype('O'), 'method': dtype('O'), 'energy_type': dtype('O'), 'value': dtype('float64'), 'unit': dtype('O')}, 'timestamp': '2025-09-02T21:03:59.937299'}
2025-09-02 21:03:59,938 - __main__ - INFO - 地市能源消费量数据分析:
2025-09-02 21:03:59,938 - __main__ - INFO - 数据来源: 年度地市工业能源消费量_ene_fallback
2025-09-02 21:03:59,938 - __main__ - INFO - 年份范围: 2020 - 2020
2025-09-02 21:03:59,938 - __main__ - INFO - 总能源消费量: 30442.86
2025-09-02 21:03:59,938 - __main__ - INFO - 年度地市能源消费量 测试: 成功
2025-09-02 21:03:59,938 - __main__ - INFO - === 测试年度GDP数据获取 ===
2025-09-02 21:03:59,938 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 正在获取 山西 从 2020 到 2020 的年度GDP数据...
2025-09-02 21:03:59,957 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,958 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 将为省份 '山西' 查询以下城市的数据: ['太原', '大同', '阳泉', '长治', '晋城', '朔州', '晋中', '运城', '忻州', '临汾', '吕梁']
2025-09-02 21:03:59,976 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:03:59,995 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 表 fct_y_gdp 数据验证通过，共 154 条记录
2025-09-02 21:03:59,996 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 成功获取到 154 条GDP记录
2025-09-02 21:03:59,999 - __main__ - INFO - 年度GDP数据 验证结果:
2025-09-02 21:04:00,000 - __main__ - INFO -   - 表名: fct_y_gdp
2025-09-02 21:04:00,000 - __main__ - INFO -   - 数据源: 年度GDP
2025-09-02 21:04:00,000 - __main__ - INFO -   - 记录数: 154
2025-09-02 21:04:00,000 - __main__ - INFO -   - 列数: 4
2025-09-02 21:04:00,000 - __main__ - INFO -   - 列名: ['year', 'area', 'indicator', 'record']
2025-09-02 21:04:00,000 - __main__ - INFO -   - 数据类型: {'year': dtype('int64'), 'area': dtype('O'), 'indicator': dtype('O'), 'record': dtype('O')}
2025-09-02 21:04:00,000 - __main__ - INFO -   - 元数据: {'record_count': 154, 'columns': ['year', 'area', 'indicator', 'record'], 'data_types': {'year': dtype('int64'), 'area': dtype('O'), 'indicator': dtype('O'), 'record': dtype('O')}, 'timestamp': '2025-09-02T21:03:59.999067'}
2025-09-02 21:04:00,000 - __main__ - INFO - GDP数据分析:
2025-09-02 21:04:00,000 - __main__ - INFO - 城市数量: 11
2025-09-02 21:04:00,000 - __main__ - INFO - 指标数量: 14
2025-09-02 21:04:00,000 - __main__ - ERROR - 年度GDP数据获取测试失败: can only concatenate str (not "int") to str
2025-09-02 21:04:00,000 - __main__ - INFO - 年度GDP数据 测试: 失败
2025-09-02 21:04:00,001 - __main__ - INFO - === 测试年度能耗强度数据获取 ===
2025-09-02 21:04:00,001 - __main__ - INFO - 测试城市: ['太原', '大同', '阳泉']
2025-09-02 21:04:00,001 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 正在为城市 ['太原', '大同', '阳泉'] 获取从 2020 到 2020 的年度能耗强度数据...
2025-09-02 21:04:00,022 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:04:00,026 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 表 fct_y_all_ene_intsty 数据验证通过，共 3 条记录
2025-09-02 21:04:00,027 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 成功获取到 3 条能耗强度记录
2025-09-02 21:04:00,027 - __main__ - INFO - 年度能耗强度数据 验证结果:
2025-09-02 21:04:00,027 - __main__ - INFO -   - 表名: fct_y_all_ene_intsty
2025-09-02 21:04:00,027 - __main__ - INFO -   - 数据源: 年度能耗强度
2025-09-02 21:04:00,027 - __main__ - INFO -   - 记录数: 3
2025-09-02 21:04:00,027 - __main__ - INFO -   - 列数: 4
2025-09-02 21:04:00,027 - __main__ - INFO -   - 列名: ['year', 'area', 'indicator', 'record']
2025-09-02 21:04:00,027 - __main__ - INFO -   - 数据类型: {'year': dtype('int64'), 'area': dtype('O'), 'indicator': dtype('O'), 'record': dtype('float64')}
2025-09-02 21:04:00,027 - __main__ - INFO -   - 元数据: {'record_count': 3, 'columns': ['year', 'area', 'indicator', 'record'], 'data_types': {'year': dtype('int64'), 'area': dtype('O'), 'indicator': dtype('O'), 'record': dtype('float64')}, 'timestamp': '2025-09-02T21:04:00.027205'}
2025-09-02 21:04:00,027 - __main__ - INFO - 能耗强度数据分析:
2025-09-02 21:04:00,027 - __main__ - INFO - 城市数量: 3
2025-09-02 21:04:00,027 - __main__ - INFO - 指标数量: 1
2025-09-02 21:04:00,027 - __main__ - INFO - 平均能耗强度: -4.5021
2025-09-02 21:04:00,027 - __main__ - INFO - 年度能耗强度数据 测试: 成功
2025-09-02 21:04:00,027 - __main__ - INFO - === 测试年度工业产品产量数据获取 ===
2025-09-02 21:04:00,027 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 正在获取山西从2020到2020的年度工业产品产量数据...
2025-09-02 21:04:00,046 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 数据库连接成功！
2025-09-02 21:04:00,055 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 表 fct_y_prd_output 数据验证通过，共 11 条记录
2025-09-02 21:04:00,057 - ecam_calculator.infrastructure.raw_data_repository_impl - INFO - 获取到11条工业产品产量记录
2025-09-02 21:04:00,058 - __main__ - INFO - 年度工业产品产量数据 验证结果:
2025-09-02 21:04:00,058 - __main__ - INFO -   - 表名: fct_y_prd_output
2025-09-02 21:04:00,058 - __main__ - INFO -   - 数据源: 年度工业产品产量
2025-09-02 21:04:00,058 - __main__ - INFO -   - 记录数: 11
2025-09-02 21:04:00,058 - __main__ - INFO -   - 列数: 8
2025-09-02 21:04:00,058 - __main__ - INFO -   - 列名: ['year', 'area', 'product_name', 'record', 'unit', 'source', 'value', 'product_type']
2025-09-02 21:04:00,058 - __main__ - INFO -   - 数据类型: {'year': dtype('int64'), 'area': dtype('O'), 'product_name': dtype('O'), 'record': dtype('float64'), 'unit': dtype('O'), 'source': dtype('O'), 'value': dtype('float64'), 'product_type': dtype('O')}
2025-09-02 21:04:00,058 - __main__ - INFO -   - 元数据: {'record_count': 11, 'columns': ['year', 'area', 'product_name', 'record', 'unit', 'source', 'value', 'product_type'], 'data_types': {'year': dtype('int64'), 'area': dtype('O'), 'product_name': dtype('O'), 'record': dtype('float64'), 'unit': dtype('O'), 'source': dtype('O'), 'value': dtype('float64'), 'product_type': dtype('O')}, 'timestamp': '2025-09-02T21:04:00.058118'}
2025-09-02 21:04:00,058 - __main__ - INFO - 工业产品产量数据分析:
2025-09-02 21:04:00,058 - __main__ - INFO - 产品类型数量: 11
2025-09-02 21:04:00,058 - __main__ - INFO - 总产品产量: 7131992.08
2025-09-02 21:04:00,058 - __main__ - INFO - 年度工业产品产量数据 测试: 成功
2025-09-02 21:04:00,058 - __main__ - INFO - === 测试总结 ===
2025-09-02 21:04:00,058 - __main__ - INFO - 总测试数: 9
2025-09-02 21:04:00,058 - __main__ - INFO - 成功数: 6
2025-09-02 21:04:00,058 - __main__ - INFO - 失败数: 3
2025-09-02 21:04:00,058 - __main__ - INFO - 成功率: 66.7%
2025-09-02 21:04:00,058 - __main__ - INFO - 详细测试结果:
2025-09-02 21:04:00,058 - __main__ - INFO -   ✓ 数据库连接
2025-09-02 21:04:00,059 - __main__ - INFO -   ✗ 能源排放因子
2025-09-02 21:04:00,059 - __main__ - INFO -   ✓ 折标准煤系数
2025-09-02 21:04:00,059 - __main__ - INFO -   ✗ 月度用电量
2025-09-02 21:04:00,059 - __main__ - INFO -   ✓ 年度省级能源消费量
2025-09-02 21:04:00,059 - __main__ - INFO -   ✓ 年度地市能源消费量
2025-09-02 21:04:00,059 - __main__ - INFO -   ✗ 年度GDP数据
2025-09-02 21:04:00,059 - __main__ - INFO -   ✓ 年度能耗强度数据
2025-09-02 21:04:00,059 - __main__ - INFO -   ✓ 年度工业产品产量数据
2025-09-02 21:04:00,059 - __main__ - INFO - 测试完成
2025-09-02 21:04:00,059 - __main__ - WARNING - 测试部分失败，请检查日志
