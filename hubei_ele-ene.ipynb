#%% md
# 能源消费总量计算方法
从《能源统计年鉴》能源平衡表（实物量）计算
能源消费总量=终端能源消费量+能源加工转换损失量+损失量
电热当量法：所有能源都是这三项的代数和乘以对应的折标系数，对应加总
发电煤耗法：电力计算为，电力的话按照电热当量和发电煤耗法
#%%
import numpy as np
import pandas as pd
from statsmodels.tsa.interp.denton import dentonm

#使用Denton方法拆分数据

# 假设的年度数据
annual_data = np.array([100, 110, 120, 130])
# 假设的季度指示器，这里简单使用随机数
quarterly_indicator = np.random.rand(16)  # 4年 * 4季度

# 使用Denton方法进行插值
interpolated_data = dentonm(quarterly_indicator, annual_data, freq="aq")

# 将结果转换为DataFrame以便查看
dates = pd.date_range(start='2010', periods=16, freq='Q')
result_df = pd.DataFrame(data=interpolated_data, index=dates, columns=['GDP'])
print(result_df)
#%%
%%sql
select * from shanxi.fact_energy_intensity

#%%
df_ene.set_index(['time', 'region', 'indicator']).
#%%
import pandas as pd
from typing import List, Callable

time_points = ['2010-01-01', '2015-01-01', '2020-01-01']

def calculate_static_index(df, rate_col, group_columns):
    """计算静态指数"""
    return (df[rate_col].fillna(0).add(100).div(100)
            .groupby([df[col] for col in group_columns])
            .transform(lambda x: x.cumprod() / x.iloc[0]))

def calculate_static_index(df: pd.DataFrame, rate_col: str, group_columns: List[str]) -> pd.Series:
    """计算静态指数"""
    return (df[rate_col].fillna(0).add(100).div(100)
            .groupby(group_columns)
            .cumprod()
            .div(df.groupby(group_columns)[rate_col].transform('first').add(100).div(100)))

def calculate_adjusted_value(df, index_col, value_col, group_columns):
    """计算调整后的值"""
    return (df[value_col]
            .groupby([df[col] for col in group_columns])
            .transform(lambda x: x.iloc[0] * df[index_col]))

def calculate_energy_intensity_index_static(df, group_columns):
    """计算静态能源强度指数，并添加到 DataFrame 中"""
    df['energy_intensity_index_static'] = (
        df['decline_rate'].fillna(0).add(100).div(100)
        .groupby([df[col] for col in group_columns])  # 按 region 分组
        .transform(lambda x: x.cumprod() / x.iloc[0])  # 计算累积乘积并除以第一个元素
    )
    return df

def fill_energy_intensity(df, source_col, target_col, group_columns):
    """如果存在历史能源强度值则向乘后添加，如果不存在则报错弹出"""
    df['fill_value'] = (
        df[target_col]
        .groupby([df[col] for col in group_columns])  # 按多列分组
        .transform(lambda x: x.iloc[0] * df[source_col])
    )
    df[target_col] = df[target_col].fillna(df['fill_value'])
    return df

def drop_columns(df, cols):
    """删除不再需要的列"""
    df.drop(columns=cols, inplace=True)
    return df

# 使用管道式编程整合处理流程
df_ene_filled = (df_ene
          .pipe(calculate_energy_intensity_index_static, group_columns=['region', 'indicator'])
          .pipe(fill_energy_intensity, source_col='energy_intensity_index_static', target_col='value', group_columns=['region', 'indicator','source'])
          .pipe(lambda df: df[['time','region', 'indicator', 'value', 'decline_rate', 'energy_intensity_index_static' ]])
          )

# 输出结果查看
df_ene_filled
#%%
import pandas as pd

def calculate_gdp_index_static(df, group_columns):
    """计算静态GDP指数，并添加到 DataFrame 中"""
    df['gdp_index_static'] = (
        df['growth_rate'].fillna(0).add(100).div(100)
        .groupby([df[col] for col in group_columns])  # 按 region 分组
        .transform(lambda x: x.cumprod() / x.iloc[0])  # 计算累积乘积并除以第一个元素
    )
    return df

def calculate_constant_price_gdp(df, source_col, target_col, group_columns):
    """计算不变价GDP"""
    df['gdp_constant_price'] = (
        df[target_col]
        .groupby([df[col] for col in group_columns])  # 按多列分组
        .transform(lambda x: x.iloc[0] * df[source_col])
    )
    return df

def drop_columns(df, cols):
    """删除不再需要的列"""
    df.drop(columns=cols, inplace=True)
    return df

# 使用管道式编程整合处理流程
df_gdp_filled = (df_gdp
                 .pipe(calculate_gdp_index_static, group_columns=['region', 'indicator'])
                 .pipe(calculate_constant_price_gdp, source_col='gdp_index_static', target_col='value', group_columns=['region', 'indicator', 'source'])
                 .pipe(lambda df: df[['time', 'region', 'indicator', 'value', 'growth_rate', 'gdp_index_static', 'gdp_constant_price']])
                 )

# 输出结果查看
print(df_gdp_filled)
#%%
%%sql
select * from shanxi.fact_gdp
#%%
import pandas as pd

def calculate_gdp_index_static(df, group_columns):
    """计算静态GDP指数，并添加到 DataFrame 中"""
    df['gdp_index_static'] = (
        df['growth_rate'].fillna(0).add(100).div(100)
        .groupby([df[col] for col in group_columns])  # 按 region 分组
        .transform(lambda x: x.cumprod() / x.iloc[0])  # 计算累积乘积并除以第一个元素
    )
    return df

def calculate_constant_price_gdp(df, source_col, target_col, group_columns):
    """计算不变价GDP"""
    df['gdp_constant_price'] = (
        df[target_col]
        .groupby([df[col] for col in group_columns])  # 按多列分组
        .transform(lambda x: x.iloc[0] * df[source_col])
    )
    return df

def drop_columns(df, cols):
    """删除不再需要的列"""
    df.drop(columns=cols, inplace=True)
    return df

# 使用管道式编程整合处理流程
df_gdp_filled = (df_gdp
                 .pipe(lambda df:, group_columns=group_columns + ['time'])
                 .pipe(calculate_gdp_index_static, group_columns=['region', 'indicator'])
                 .pipe(calculate_constant_price_gdp, source_col='gdp_index_static', target_col='value', group_columns=['region', 'indicator', 'source'])
                 .pipe(lambda df: df[['time', 'region', 'indicator', 'value', 'growth_rate', 'gdp_index_static', 'gdp_constant_price']])
                 )

# 输出结果查看
print(df_gdp_filled)
#%% md

#%%
import pandas as pd

def preprocess_data(df):
    """预处理数据"""
    # 确保时间列为datetime类型
    df['time'] = pd.to_datetime(df['time'], errors='coerce')

    # 删除时间列中的NaT值
    df = df.dropna(subset=['time'])

    # 确保其他关键列没有缺失值
    df = df.dropna(subset=['region', 'indicator', 'source', 'value'])

    # 将value列转换为float类型
    df['value'] = pd.to_numeric(df['value'], errors='coerce')

    # 排序
    df = df.sort_values(['region', 'indicator', 'source', 'time'])

    return df

def sort_and_group(df, group_columns):
    """对数据进行排序和分组"""
    return df.sort_values(group_columns + ['time'])

def calculate_static_index(df, rate_col, group_columns):
    """计算静态指数"""
    return (df[rate_col].fillna(0).add(100).div(100)
            .groupby([df[col] for col in group_columns])
            .transform(lambda x: x.cumprod() / x.iloc[0]))

def calculate_adjusted_value(df, index_col, value_col, group_columns):
    """计算调整后的值"""
    return (df[value_col]
            .groupby([df[col] for col in group_columns])
            .transform(lambda x: x.iloc[0] * df[index_col]))

def process_data(df, rate_col, value_col, group_columns, index_name, adjusted_name):
    """通用数据处理流程"""
    return (df
            .pipe(sort_and_group, group_columns=group_columns)
            .assign(**{
        index_name: lambda x: calculate_static_index(x, rate_col, group_columns),
        adjusted_name: lambda x: calculate_adjusted_value(x, index_name, value_col, group_columns)
    })
            .pipe(lambda x: x[[
        'time', 'region', 'indicator', value_col, rate_col, index_name, adjusted_name
    ]]))

def divide_and_process(df, time_points, process_func):
    """按时间点分割数据，处理每个部分，然后拼接结果"""
    # 预处理数据
    df = preprocess_data(df)

    time_points = pd.to_datetime(time_points)
    time_ranges = list(zip([df['time'].min()] + list(time_points), list(time_points) + [df['time'].max()]))

    processed_dfs = []
    for start, end in time_ranges:
        df_slice = df[(df['time'] >= start) & (df['time'] < end)]
        if not df_slice.empty:
            processed_dfs.append(process_func(df_slice))

    return pd.concat(processed_dfs).reset_index(drop=True)

# 能源强度计算
def process_energy_intensity(df):
    return process_data(
        df,
        rate_col='decline_rate',
        value_col='value',
        group_columns=['region', 'indicator', 'source'],
        index_name='energy_intensity_index_static',
        adjusted_name='adjusted_energy_intensity'
    )

# 不变价GDP计算
def process_constant_price_gdp(df):
    return process_data(
        df,
        rate_col='growth_rate',
        value_col='value',
        group_columns=['region', 'indicator', 'source'],
        index_name='gdp_index_static',
        adjusted_name='gdp_constant_price'
    )

# 使用示例
time_points = ['2010-01-01', '2015-01-01', '2020-01-01']

df_energy_processed = divide_and_process(df_ene, time_points, process_energy_intensity)
df_gdp_processed = divide_and_process(df_gdp, time_points, process_constant_price_gdp)

print(df_energy_processed)
print(df_gdp_processed)

#%%
class EconomicDataProcessor:
    def __init__(self, df):
        self.df = df
#%%
import pandas as pd

class EnergyDataProcessor:
    def __init__(self, df):
        self.df = df

    def impute_energy_intensity(self, source_col, target_col, group_column):
        """插补能源强度缺失值的逻辑：如果存在历史能源强度值则向乘后添加，如果不存在则以解矩阵方程的方式添加"""
        df = (self
              .calculate_energy_intensity_index_static(group_column='region')
              .calculate_energy_intensity(source_col='energy_intensity_index_static', 
                                          target_col='energy_intensity', 
                                          group_column='region')
              .fillna(self.df['fill_value'])
              .drop_columns(['fill_value'])
              .df)

    def divide_by_timestep(self, time_points):
        """
        按照给定的时间点分割df,返回一个列表存储分割后的df
        """
        time_slices = []
        for i in range(len(time_points)-1):
            mask = (self.df['time'] > time_points[i]) & (self.df['time'] < time_points[i+1])
            time_slices.append(self.df.loc[mask])
        # 添加最后一个时间段
        mask = self.df['time'] >= time_points[-1]
        time_slices.append(self.df.loc[mask])
        return time_slices

    def calculate_energy_intensity_index_static(self, group_column):
        """根据单位 GDP 能耗下降率，计算静态能源强度指数，并添加到 DataFrame 中"""
        self.df['energy_intensity_index_static'] = (
            self.df['decline_rate'].add(100).div(100)
            .groupby(self.df[group_column])  # 按 group_column 分组
            .transform(lambda x: x.cumprod() / x.iloc[0])  # 计算累积乘积并除以第一个元素
        )
        return self

    def calculate_energy_intensity(self, source_col, target_col, group_column):
        """计算能源强度，检验精度后使用计算值填补空缺值"""
        self.df['fill_value'] = (
            self.df['energy_intensity']
            .groupby(self.df[group_column])  # 按 group_column 分组
            .transform(lambda x: x.iloc[0] * self.df['energy_intensity_index_static'])
        )
        self.df[target_col] = self.df[target_col].fillna(self.df['fill_value'])
        return self


    def drop_columns(self, cols):
        """删除不再需要的列"""
        self.df.drop(columns=cols, inplace=True)
        return self

# 使用示例
# 假设 df_ene 已经正确加载，并且 df_ene 有 'region', 'date', 'decline_rate', 'energy_intensity' 列
time_points = ['2010-01-01', '2015-01-01', '2020-01-01']
imputer = EnergyDataProcessor(df_ene)
time_slices = imputer.divide_by_timestep(time_points)
df_ene_processed = (imputer
                    .calculate_energy_intensity_index_static(group_column='region')
                    .fill_energy_intensity(source_col='energy_intensity_index_static', target_col='energy_intensity', group_column='region')
                    .drop_columns(['fill_value'])
                    .df)

# 输出结果查看
print(df_ene_processed)
#%%
class EconomyDataProcessor:
    def __init__(self, df):
        self.df = df
        
    def calculate_gdp(self, source_col, target_col, group_column):
        

    def calculate_gdp(self, source_col, target_col, group_column):
#%%
# 合并数据计算能源消费总量
#%%
import statsmodels.api as sm
from statsmodels.tsa.stattools import adfuller, coint

# 假设df是包含时间序列数据的DataFrame，列名为var1, var2
# 单位根检验
adf_result_var1 = adfuller(df['var1'])
adf_result_var2 = adfuller(df['var2'])

print('ADF Statistic for var1: %f' % adf_result_var1[0])
print('p-value for var1: %f' % adf_result_var1[1])
print('ADF Statistic for var2: %f' % adf_result_var2[0])
print('p-value for var2: %f' % adf_result_var2[1])

# 如果两个变量都是非平稳的，进行协整检验
coint_result = coint(df['var1'], df['var2'])
print('Cointegration test statistic: %f' % coint_result[0])
print('p-value: %f' % coint_result[1])

# 如果协整检验的p值小于显著性水平（通常为0.05），则认为存在协整关系
#%%
from statsmodels.tsa.vector_ar.vecm import VECM
#%% md
# 数据处理
#%% md
# 自回归分布滞后模型
#%%
from typing import Literal, Sequence, Hashable
from sklearn.base import BaseEstimator, RegressorMixin
from statsmodels.tsa.ardl import ARDL
from statsmodels.tsa.vector_ar.vecm import VECM, select_order

# ARDL估计器
class ARDLRegressor(BaseEstimator, RegressorMixin):
    def __init__(
            self,
            lags: int | Sequence[int] | None,
            order: None | int | dict[Hashable, int | None] | Sequence[int] | dict[Hashable, int | Sequence[int] | None] = 0,
            trend: Literal["n", "c", "ct", "ctt"] = "c",
            **kwargs
    ) -> None:
        self.lags = lags
        self.order = order
        self.trend = trend
        self.kwargs = kwargs
        self.model = None

    def fit(self, X, y):
        try:
            self.model = ARDL(endog=y, 
                              lags=self.lags, 
                              exog=X, 
                              order=self.order, 
                              trend=self.trend, 
                              **self.kwargs).fit()
        except Exception as e:
            print(f"An error occurred while fitting the ARDL model: {e}")
            raise e
        return self

    def predict(self, X):
        if self.model is None:
            raise ValueError("Model is not fitted yet!")
        return self.model.predict(exog=X)

    def summary(self):
        """
        返回模型的统计摘要。
        :return: 模型摘要。
        """
        if self.model is None:
            raise ValueError("Model is not fitted yet!")
        return self.model.summary()

    def detect_structural_breaks(self, X, y, max_breaks=5):
        """
        检测结构断点。
        :param X: 自变量数据。
        :param y: 因变量数据。
        :param max_breaks: 最大断点数。
        :return: 断点位置列表。
        """
        best_score = float('inf')
        best_breaks = []
        for num_breaks in range(1, max_breaks + 1):
            # 假设断点均匀分布
            breakpoints = np.linspace(0, len(y), num_breaks + 2)[1:-1].astype(int)
            scores = []
            last_bp = 0
            for bp in breakpoints:
                self.fit(X[last_bp:bp], y[last_bp:bp])
                scores.append(self.score())
                last_bp = bp
            self.fit(X[last_bp:], y[last_bp:])
            scores.append(self.score())
            avg_score = np.mean(scores)
            if avg_score < best_score:
                best_score = avg_score
                best_breaks = breakpoints
        return best_breaks

    def score(self, X, y, criterion='AIC', weight_type='data_length'):
        total_score = 0
        total_weight = 0
        for model in self.models:
            if criterion == 'AIC':
                score = model.aic
            elif criterion == 'BIC':
                score = model.bic
            elif criterion == 'HQIC':
                score = model.hqic
            else:
                raise ValueError("Unsupported criterion")
    
            if weight_type == 'equal':
                weight = 1
            elif weight_type == 'data_length':
                weight = model.nobs
            elif weight_type == 'error_sensitive':
                weight = 1 / model.mse_resid
            elif weight_type == 'time_sensitive':
                weight = (model.nobs.index + 1) ** 2
            else:
                raise ValueError("Unsupported weight type")
    
            total_score += score * weight
            total_weight += weight
    
        weighted_score = total_score / total_weight
        return weighted_score        
#%%
# 创建一些模拟数据
np.random.seed(0)
X = pd.DataFrame({
    'x1': np.random.normal(size=100),
    'x2': np.random.normal(size=100)
})
y = 0.5 * X['x1'] - 0.3 * X['x2'] + np.random.normal(size=100)

# 实例化并拟合模型
model = ARDLRegressor(lags=1, order=1)
res = model.fit(X, y)

model.summary()

#%% md
# 向量误差修正模型
#%%
from typing import Literal, Sequence, Hashable
from sklearn.base import BaseEstimator, RegressorMixin
from statsmodels.tsa.ardl import ARDL
from statsmodels.tsa.vector_ar.vecm import VECM, select_order

# 估计器：VECM
class VECMEstimator(BaseEstimator, RegressorMixin):
    def __init__(self, maxlags=None, deterministic='n', seasons=0, exog=None, exog_coint=None, coint_rank=1):
        self.maxlags = maxlags
        self.deterministic = deterministic
        self.seasons = seasons
        self.exog = exog
        self.exog_coint = exog_coint
        self.coint_rank = coint_rank
        self.model_ = None

    def fit(self, X, y=None):
        selected_order = select_order(
            X, 
            maxlags=self.maxlags, 
            deterministic=self.deterministic,
            seasons=self.seasons, 
            exog=self.exog, 
            exog_coint=self.exog_coint
        )
        self.lags_ = selected_order
        self.model_ = VECM(X, k_ar_diff=self.lags_, coint_rank=self.coint_rank, deterministic=self.deterministic)
        self.model_ = self.model_.fit()
        return self

    def predict(self, X):
        return self.model_.predict(steps=len(X))

    def selected_score(self, X, y=None):
        if self.model_ is None:
            socre = self.model_.aic
        elif:
            self.model_.bic
            self.model_.hqic
        
        return score
#%%
# 超参优化：网格搜索
from sklearn.model_selection import GridSearchCV

param_grid = {
    'coint_rank': [1, 2, 3],
    'deterministic': ['n', 'co', 'ci', 'lo', 'li', 'coci', 'colo', 'coli', 'cilo', 'cili', 'loli']
}

vecm_estimator = VECMEstimator(maxlags=10)
grid_search = GridSearchCV(vecm_estimator, param_grid, cv=3)
grid_search.fit(X_train)  # X_train 是多变量时间序列数据

print("最佳参数：", grid_search.best_params_)
print("最佳分数：", grid_search.best_score_)
#%%
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, acf, pacf
from statsmodels.tsa.arima_model import ARIMA

# 数据准备
data = [20.4, -18.8, 10.6, 11.0, 11.0, 13.1, 16.2, 20.4, 25.8]
dates = pd.date_range(start='2023-01-01', periods=len(data), freq='M')
time_series = pd.Series(data, index=dates)

# 数据可视化
plt.figure(figsize=(10, 6))
plt.plot(time_series, label='Original Data')
plt.title('Time Series Data')
plt.xlabel('Date')
plt.ylabel('Value')
plt.legend()
plt.show()

# 趋势和季节性分析
decomposition = seasonal_decompose(time_series, model='additive')
trend = decomposition.trend
seasonal = decomposition.seasonal
residual = decomposition.resid

plt.figure(figsize=(12, 8))
plt.subplot(411)
plt.plot(time_series, label='Original')
plt.legend(loc='best')
plt.subplot(412)
plt.plot(trend, label='Trend')
plt.legend(loc='best')
plt.subplot(413)
plt.plot(seasonal, label='Seasonality')
plt.legend(loc='best')
plt.subplot(414)
plt.plot(residual, label='Residuals')
plt.legend(loc='best')
plt.tight_layout()
plt.show()

# 自相关分析
lag_acf = acf(time_series, nlags=20)
lag_pacf = pacf(time_series, nlags=20, method='ols')

plt.figure(figsize=(12, 6))
plt.subplot(121)
plt.plot(lag_acf)
plt.axhline(y=0, linestyle='--', color='gray')
plt.axhline(y=-1.96/np.sqrt(len(time_series)), linestyle='--', color='gray')
plt.axhline(y=1.96/np.sqrt(len(time_series)), linestyle='--', color='gray')
plt.title('Autocorrelation Function')

plt.subplot(122)
plt.plot(lag_pacf)
plt.axhline(y=0, linestyle='--', color='gray')
plt.axhline(y=-1.96/np.sqrt(len(time_series)), linestyle='--', color='gray')
plt.axhline(y=1.96/np.sqrt(len(time_series)), linestyle='--', color='gray')
plt.title('Partial Autocorrelation Function')
plt.tight_layout()
plt.show()

# 模型拟合
model = ARIMA(time_series, order=(1, 1, 1))
results_ARIMA = model.fit(disp=-1)

plt.figure(figsize=(10, 6))
plt.plot(time_series, label='Original')
plt.plot(results_ARIMA.fittedvalues, color='red', label='Fitted Values')
plt.title('ARIMA Model Fitting')
plt.legend()
plt.show()

# 预测
forecast, stderr, conf_int = results_ARIMA.forecast(steps=5)
forecast_dates = pd.date_range(start=dates[-1], periods=5, freq='M')
forecast_series = pd.Series(forecast, index=forecast_dates)

plt.figure(figsize=(10, 6))
plt.plot(time_series, label='Original')
plt.plot(forecast_series, color='red', label='Forecast')
plt.fill_between(forecast_series.index, conf_int[:, 0], conf_int[:, 1], color='pink', alpha=0.3)
plt.title('Forecast')
plt.legend()
plt.show()
#%%
%%sql
select * from hubei.fact_electric_consumption
where scope = '全社会用电量'
#%%
import matplotlib.pyplot as plt
import ruptures as rpt



series = df_ele.electricity_consumption

# detection
algo = rpt.Pelt(model="rbf").fit(series)
result = algo.predict(pen=10)


plt.show()
#%%
import numpy as np
import matplotlib.pyplot as plt
from Rbeast import beast

# 设置metadata参数
metadata = {
    'season': 'harmonic',  # 使用谐波曲线建模季节性成分
    'startTime': 1,        # 数据起始时间为第一个月
    'deltaTime': 1,        # 时间间隔为1个月
    'maxMissingRate': 0.75, # 如果数据缺失超过75%，BEAST将跳过
    'detrend': False       # 不移除全局趋势成分
}

# 检测断点
result = beast(df_ele.electricity_consumption, **metadata)

if 'scp' in result and len(result['scp']) > 0:
    print(f"检测到的断点位置: {result['scp']}")
    has_breakpoints = True
else:
    print("未检测到断点")
    has_breakpoints = False

# 绘制结果
plt.plot(df_ele.electricity_consumption, label='Original Signal')
plt.plot(result['scp'], label='Detected Change Points', linestyle='--', marker='o')
plt.legend()
plt.show()
#%% md
化石能源燃烧消费总量
各类化石能源的终端消费量以及火力发电的能源消费量
#%%
item_dict = {
    '一.可供本地区消费的能源量': {
        '1.一次能源生产量': None,
        '2.外省(区、市)调入量': None,
        '3.进口量': None,
        '4.境内飞机和轮船在境外的加油量':None,
        '5.本省(区、市)调出量(-)':None,
        '6.出口量(-)':None,
        '7.境外飞机和轮船在境内的加油量(-)':None,
        '8.库存增(-)、减(+)量':None
    },
    '二.加工转换投入(-)产出(+)量':{
        '1.火力发电': None,
        '2.供热': None,
        '3.煤炭洗选': None,
        '4.炼焦':None,
        '5.炼油及煤制油':None,
        '6.制气':None,
        '7.天然气液化':None,
        '8.煤制品加工':None,
        '9.回收能':None
    },
    '三.损失量': None,
    '四.终端消费量': {
        '1.农、林、牧、渔业': None,
        '2.工业': {
            '#用作原料、材料': None
        },
        '3.建筑业': None,
        '4.交通运输、仓储和邮政业': None,
        '5.批发和零售业、住宿和餐饮业': None,
        '6.其他': None,
        '7.居民生活': {
            '城镇': None,
            '乡村': None
        }
    },
    '五.平衡差额': None,
    '六.消费量合计': None
}


# 打印修正后的字典
import pprint
pprint.pprint(item_dict)

energy_type_dict = {
    '煤炭': {
        '煤合计': None,
        '洗精煤': None,
        '焦炭': None,
        '煤矸石': None,
        '原煤': None,
        '煤制品': None,
        '其他洗煤': None,
        '其他焦化产品': None,
        '其他煤气': None,
        '转炉煤气': None,
        '高炉煤气': None,
        '焦炉煤气': None
    },
    '石油': {
        '汽油': None,
        '原油': None,
        '液化石油气': None,
        '石油焦': None,
        '油品合计': None,
        '其他石油制品': None,
        '炼厂干气': None,
        '石油沥青': None,
        '溶剂油': None,
        '石蜡': None,
        '润滑油': None,
        '石脑油': None,
        '燃料油': None,
        '柴油': None,
        '煤油': None
    },
    '天然气': {
        '液化天然气': None,
        '天然气': None
    },
    '电力': {
        '电力': None
    },
    '其他能源': {
        '热力': None,
        '其他能源': None
    }
}

# 打印整理后的字典
import pprint
pprint.pprint(energy_type_dict)
#%%
energy_processing_dict = {
    '煤炭': {
        '原煤及其加工产品': {
            '原煤': None,
            '洗精煤': None,
            '煤矸石': None,
            '煤制品': None,
            '其他洗煤': None
        },
        '炼焦及其副产品': {
            '焦炭': None,
            '其他焦化产品': None,
            '煤气': {
                '焦炉煤气': None,
                '高炉煤气': None,
                '转炉煤气': None,
                '其他煤气': None
            }
        },
        '煤合计': None  # 煤合计可以视为总量统计
    }
}


#%%
sector_dict = {
    '农林牧渔业':'1.农、林、牧、渔业',
    '能源行业':{'1.火力发电','2.供热'},
    '工业':'2.工业',
    '建筑业':'3.建筑业',
    '交通运输业':'4.交通运输、仓储和邮政业',
    '服务业':'5.批发和零售业、住宿和餐饮业',
    '居民生活':'7.居民生活'
}

item_dict = {
    
}
#%% md

#%%
# 计算化石能源终端消费：从生产端核算能源消费量



#%%
import pandas as pd
import numpy as np
from statsmodels.tsa.ardl import ARDL
from statsmodels.tsa.ardl import ardl_select_order
import matplotlib.pyplot as plt

# 定义数据
dates = pd.date_range(start='2005-12-01', end='2022-12-01', freq='AS-DEC')
y = np.array([6.739781, 9.567947, 4.269932, 5.459056, 4.96003, 7.121666, 12.798666, 15.797326, 15.793022, 15.526106, 18.59119, 21.04893289, 26.362919, 41.68308828, 56.721972, 72.38434545, 105.588908, 137.6475352])
x = np.array([4.52, 3.44, 2.23, 3.17, 3.41, 4.16, 9.89, 11.81, 12.4, 12.25, 14.08, 15.8673, 20.79, 33.6372, 45.9251, 58.6319, 85.79, 111.8897])

# 创建DataFrame
data = pd.DataFrame({'y': y, 'x': x}, index=dates)

# 将数据分为训练集和验证集
train_data = data[:'2017-12-01']
test_data = data['2018-12-01':]

# 选择最佳的ARDL模型阶数
selected_order = ardl_select_order(train_data['y'], exog=train_data[['x']], maxlag=2, maxorder=2)


# 创建并拟合ARDL模型
model = ARDL(train_data['y'], lags=1, exog=train_data[['x']], order=2)
result = model.fit()
print(result.summary())

# 预测
forecast = result.predict(start=len(train_data), end=len(train_data) + len(test_data) - 1, exog_oos=test_data[['x']])

# 绘制结果
plt.figure(figsize=(10, 6))
plt.plot(train_data.index, train_data['y'], label='训练数据')
plt.plot(test_data.index, test_data['y'], label='验证数据')
plt.plot(test_data.index, forecast, label='预测数据', linestyle='--')
plt.legend()
plt.title('ARDL模型预测')
plt.show()
#%%
import pandas as pd
import ast

# 读取txt文件
with open('your_file.txt', 'r', encoding='utf-8') as file:
    data_string = file.read()

# 将字符串转换为Python列表
data_list = ast.literal_eval(data_string)

# 创建DataFrame
df = pd.DataFrame(data_list, columns=['日期', '地区', '用电类别', '用电量'])

# 将日期列转换为datetime类型
df['日期'] = pd.to_datetime(df['日期'])

# 将用电量列中的'\N'替换为空值（NaN）
df['用电量'] = df['用电量'].replace('\\N', pd.NA)

# 将用电量列转换为float类型，这会自动将NaN视为缺失值
df['用电量'] = df['用电量'].astype(float)

# 保存为xlsx文件
df.to_excel('output.xlsx', index=False)

print("文件已成功转换为xlsx格式并保存为'output.xlsx'，用电量中的'\\N'已被替换为空值")
#%%

# 将字符串转换为Python列表
data_list = ast.literal_eval(data_string)

# 创建DataFrame
df = pd.DataFrame(data_list, columns=['日期', '地区', '用电类别', '用电量'])

# 将日期列转换为datetime类型
df['日期'] = pd.to_datetime(df['日期'])

# 将用电量列转换为float类型
df['用电量'] = df['用电量'].astype(float)


#%%
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.tsa.stattools import adfuller
from statsmodels.stats.diagnostic import het_breuschpagan
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import acf
from statsmodels.graphics.tsaplots import plot_acf
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.tsa.ar_model import AutoReg
from statsmodels.stats.diagnostic import recursive_olsresiduals

# 准备数据
data = {
    2007: 702.02, 2008: 712.46, 2009: 883.20, 2010: 837.60, 2011: 990.07,
    2012: 1044.23, 2013: 1131.22, 2014: 1124.08, 2015: 1306.62, 2016: 1344.43,
    2017: 1317.76, 2018: 1408.34, 2019: 1664.55, 2020: 1803.80
}

df = pd.DataFrame(list(data.items()), columns=['Year', 'Consumption'])
df.set_index('Year', inplace=True)
#%%
def cusum_test(data):
    n = len(data)
    cs = np.cumsum(data - np.mean(data))
    cs = cs / (data.std() * np.sqrt(n))
    return cs

cs = cusum_test(df['Consumption'])

plt.figure(figsize=(12, 6))
plt.plot(df.index, cs)
plt.title('CUSUM Test')
plt.xlabel('年份')
plt.ylabel('CUSUM')
plt.axhline(y=0, color='r', linestyle='--')
plt.grid(True)
plt.show()
#%%
model = AutoReg(df['Consumption'], lags=1)
res = model.fit()

recresid = recursive_olsresiduals(res)

fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(211)
ax.plot(df.index[2:], recresid[0][3:])
ax.set_title('Recursive Residuals')
ax = fig.add_subplot(212)
ax.plot(df.index[2:], recresid[1][3:])
ax.set_title('Recursive Residuals (Standardized)')
plt.tight_layout()
plt.show()