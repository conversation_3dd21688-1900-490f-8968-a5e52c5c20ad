# 架构职责分离说明

## 问题分析

您指出的问题非常正确：我对整个项目结构缺乏认知，把方法写到了不该放的地方，违反了分层架构的原则。

## 正确的架构分层

### 1. Domain层（领域层）
**职责**: 定义业务接口和领域模型
**位置**: `ecam_calculator/domain/`
**内容**:
- 服务接口定义（抽象类）
- 领域模型（值对象、实体）
- 业务规则定义

**示例**:
```python
# ecam_calculator/domain/service/data_standardization_service.py
class DataStandardizationService(ABC):
    @abstractmethod
    def standardize(self, raw_data: RawData) -> List[StandardizedData]:
        pass
    
    @abstractmethod
    def standardize_all(self, raw_data_dict: Dict[str, RawData]) -> Dict[str, List[StandardizedData]]:
        pass
```

### 2. Infrastructure层（基础设施层）
**职责**: 实现具体的业务逻辑
**位置**: `ecam_calculator/infrastructure/`
**内容**:
- 服务实现类
- 数据访问实现
- 外部服务集成

**示例**:
```python
# ecam_calculator/infrastructure/services/data_standardization_service_impl.py
class DataStandardizationServiceImpl(DataStandardizationService):
    def standardize(self, raw_data: RawData) -> List[StandardizedData]:
        # 具体实现逻辑
        pass
```

### 3. Application层（应用层）
**职责**: 编排和协调各个服务
**位置**: `ecam_calculator/application/`
**内容**:
- 业务流程编排
- 服务协调
- 事务管理

## 能源转换流程的正确职责分离

### 1. 数据标准化阶段
**Domain层接口**:
```python
# ecam_calculator/domain/service/data_standardization_service.py
class DataStandardizationService(ABC):
    @abstractmethod
    def standardize(self, raw_data: RawData) -> List[StandardizedData]:
        """只负责数据映射和分类，不涉及能源转换"""
        pass
```

**Infrastructure层实现**:
```python
# ecam_calculator/infrastructure/services/data_standardization_service_impl.py
class DataStandardizationServiceImpl(DataStandardizationService):
    def standardize(self, raw_data: RawData) -> List[StandardizedData]:
        # 只进行行业和能源品种的标准化映射
        # 不进行能源转换计算
        pass
```

### 2. 能源转换阶段
**Domain层接口**:
```python
# ecam_calculator/domain/service/energy_conversion_service.py
class EnergyConversionService(ABC):
    @abstractmethod
    def convert_standardized_data(self, standardized_data: List[StandardizedData]) -> List[StandardizedData]:
        """专门负责能源转换计算"""
        pass
```

**Infrastructure层实现**:
```python
# ecam_calculator/infrastructure/energy_conversion_service.py
class EnergyConversionService(EnergyConversionService):
    def convert_standardized_data(self, standardized_data: List[StandardizedData]) -> List[StandardizedData]:
        # 具体的能源转换逻辑
        # 批量预加载转换因子
        # 向量化计算
        pass
```

### 3. 应用层编排
**Application层**:
```python
# ecam_calculator/application/calculation_job_service.py
class CalculationJobService:
    def _step_data_standardization(self, job, context):
        # 调用数据标准化服务
        pass
    
    def _step_energy_conversion(self, job, context):
        # 调用能源转换服务
        pass
```

## 修改总结

### 1. 修正的错误
- ❌ 将实现类写在Domain层接口文件中
- ❌ 在数据标准化服务中混入能源转换逻辑
- ❌ 违反单一职责原则

### 2. 正确的做法
- ✅ Domain层只定义接口
- ✅ Infrastructure层实现具体逻辑
- ✅ Application层负责编排
- ✅ 每个服务职责单一

### 3. 职责分离的好处
1. **可维护性**: 每个层都有明确的职责
2. **可测试性**: 可以独立测试每个层的逻辑
3. **可扩展性**: 可以轻松替换实现而不影响接口
4. **可理解性**: 代码结构清晰，易于理解

## 调用流程

```
Application层 (CalculationJobService)
    ↓ 调用接口
Domain层 (DataStandardizationService接口)
    ↓ 实现接口
Infrastructure层 (DataStandardizationServiceImpl)
    ↓ 返回结果
Application层继续处理
    ↓ 调用接口
Domain层 (EnergyConversionService接口)
    ↓ 实现接口
Infrastructure层 (EnergyConversionService实现)
    ↓ 返回结果
Application层继续处理
```

这样的架构确保了：
1. **依赖倒置**: 高层模块不依赖低层模块
2. **单一职责**: 每个类只有一个职责
3. **开闭原则**: 对扩展开放，对修改关闭
4. **接口隔离**: 客户端只依赖需要的接口


