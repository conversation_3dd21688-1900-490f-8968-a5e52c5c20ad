# 碳排放计算方案

## 1. 方案目标

本方案旨在设计并实现一个健壮、可扩展的碳排放计算模块。该模块将作为数据标准化流程的一部分，负责计算由 **能源活动** 和 **工业生产过程** 产生的二氧化碳（CO2）排放。

与能源单位转换类似，本方案遵循配置与数据分离的原则，所有碳排放因子都将从数据库中动态获取，确保计算的准确性和可维护性。

## 2. 核心设计

我们将创建一个新的领域服务 `CarbonEmissionService`，专门用于处理碳排放计算。该服务将被集成到现有的 `DataStandardizationServiceImpl` 中。

计算流程将分为两个独立的分支：

### 2.1. 能源活动碳排放

-   **输入**: `StandardizedData` 列表中 `data_type` 为 `ENERGY` 的数据。这些数据已经经过了行业和能源类型的标准化。
-   **核心逻辑**:
    1.  遍历所有能源消费数据。
    2.  根据每条记录的 `energy_type` 和 `year`，从数据库表 `ecam_in_energy_factor` 中查询对应的 **碳排放因子**。
    3.  **注意**: 优先使用 **基于实物量** 的排放因子（如 `吨CO2/吨原煤`），以保证最高精度。如果找不到，则回退使用 **基于标准煤当量** 的因子（如 `吨CO2/吨标准煤`）。
    4.  计算公式: `排放量 = 能源消费实物量 × 实物量排放因子` 或 `排放量 = 能源消费标准煤当量 × 标准煤排放因子`。
-   **输出**: 一系列新的 `StandardizedData` 对象，其 `data_type` 为 `EMISSION`。

### 2.2. 工业过程碳排放

-   **输入**: `StandardizedData` 列表中 `data_type` 为 `PRODUCT` 的数据。
-   **核心逻辑**:
    1.  遍历所有工业产品产量数据。
    2.  根据每条记录的 `product_name` (如 "水泥"、"粗钢") 和 `year`，从数据库表 `ecam_in_energy_factor` 中查询对应的 **工业过程排放因子**。
    3.  计算公式: `排放量 = 工业产品产量 × 过程排放因子`。
-   **输出**: 一系列新的 `StandardizedData` 对象，其 `data_type` 为 `EMISSION`。

### 2.3. 数据流集成点

**`CarbonEmissionService` 将在数据标准化流程的中间阶段被调用。**

具体的执行时机位于 `DataStandardizationServiceImpl` 内部：

1.  **输入阶段**: `DataStandardizationServiceImpl` 接收到所有原始数据 (`RawData`)。
2.  **初步标准化**: 服务对所有原始数据进行遍历，将其转换为 `StandardizedData` 对象列表。此阶段包括了**能源单位到标准煤当量的转换**。
3.  **--> 碳排放计算阶段 (当前服务调用点)**:
    -   将初步标准化的 `StandardizedData` 列表（包含能源、产品等所有类型）传递给 `CarbonEmissionService`。
    -   `CarbonEmissionService` 对列表中的 `ENERGY` 和 `PRODUCT` 数据进行处理，计算碳排放。
    -   服务返回一个新的 `StandardizedData` 列表，其中只包含 `data_type` 为 `EMISSION` 的新数据。
4.  **数据合并**: 将返回的排放数据追加到总的 `StandardizedData` 列表中。
5.  **最终分类**: `DataStandardizationServiceImpl` 根据 `data_type` 对合并后的列表进行分类，生成最终的字典（如 `categorized_data['emission']`）。

这样可以确保碳排放计算是基于已经过清洗、转换和标准化的可靠数据进行的。

---

## 3. 技术实现细节

### 3.1. 数据库查询 (`ecam_in_energy_factor`)

排放因子将通过 `factor` 字段值为 `'co2'` 来识别。

-   **能源活动因子查询**:
    ```sql
    SELECT value, unit, method FROM ecam_in_energy_factor
    WHERE energy_type = %s AND year <= %s AND factor = 'co2'
    ORDER BY year DESC, method ASC LIMIT 1;
    -- method 字段用于区分 '实物量折碳排放因子' 和 '等价值折碳排放因子'
    ```

-   **工业过程因子查询**: 工业产品的排放因子在表中也通过 `energy_type` 字段进行关联（例如，`energy_type` 为 "水泥产量"）。
    ```sql
    SELECT value, unit FROM ecam_in_energy_factor
    WHERE energy_type LIKE CONCAT('%', %s, '%') -- 使用产品名进行模糊匹配
    AND year <= %s AND factor = 'co2'
    ORDER BY year DESC LIMIT 1;
    ```

### 3.2. 新建服务: `CarbonEmissionService`

将在 `ecam_calculator/infrastructure/carbon_emission_service.py` 中创建此类。

```python
# (伪代码)
class CarbonEmissionService:
    def __init__(self, config_reader, database_connection):
        # 初始化数据库连接、配置读取器和缓存
        self._emission_factor_cache = {}

    def calculate_emissions(
        self,
        standardized_data_list: List[StandardizedData]
    ) -> List[StandardizedData]:
        """统一的排放计算入口"""
        emissions_data = []
        # 根据 data_type 分流
        energy_data = [d for d in standardized_data_list if d.data_type == StandardizedDataType.ENERGY]
        product_data = [d for d in standardized_data_list if d.data_type == StandardizedDataType.PRODUCT]

        emissions_data.extend(self._calculate_energy_emissions(energy_data))
        emissions_data.extend(self._calculate_process_emissions(product_data))
        return emissions_data

    def _calculate_energy_emissions(self, energy_data: List[StandardizedData]) -> List[StandardizedData]:
        # ... 实现能源活动排放计算逻辑 ...

    def _calculate_process_emissions(self, product_data: List[StandardizedData]) -> List[StandardizedData]:
        # ... 实现工业过程排放计算逻辑 ...

    def _get_emission_factor_from_db(self, factor_key: str, year: int, factor_type: str) -> Dict:
        # ... 实现数据库查询和缓存逻辑 ...
```

### 3.3. 服务集成: `DataStandardizationServiceImpl`

在 `_step_data_standardization` 方法的末尾，我们将调用 `CarbonEmissionService`。

```python
# (伪代码)
class DataStandardizationServiceImpl:
    def __init__(self, ..., database_connection):
        # ...
        if database_connection:
            self.energy_conversion_service = EnergyConversionService(...)
            self.carbon_emission_service = CarbonEmissionService(...) # 初始化新服务

    def standardize_data(self, raw_data_dict: Dict) -> Dict:
        # ... 已有的标准化流程 ...
        all_standardized_data = [...] # 所有标准化的能源、GDP、产品等数据

        # 新增：调用排放计算服务
        if self.carbon_emission_service:
            emission_data = self.carbon_emission_service.calculate_emissions(all_standardized_data)
            all_standardized_data.extend(emission_data)

        # ... 后续的数据分类 ...
        # 将排放数据也进行分类
        categorized_data['emission'] = [d for d in all_standardized_data if d.data_type == StandardizedDataType.EMISSION]
        return categorized_data
```

### 3.4. 新增 `StandardizedDataType`

为了标识排放数据，我们需要在 `StandardizedDataType` 枚举中增加一个新的类型。

**File**: `ecam_calculator/domain/model/value_objects.py`
```python
class StandardizedDataType(Enum):
    ENERGY = 1
    PRODUCT = 2
    ECONOMIC = 3
    EMISSION = 4 # 新增
```

## 4. 配置文件 (`parameters.yaml`)

暂时不需要对 `parameters.yaml` 进行大的修改，因为排放因子的查找逻辑将硬编码在 `CarbonEmissionService` 中，以确保与数据库结构的紧密耦合。未来可考虑增加配置项以提高灵活性。

## 5. 预期输出

计算完成后，将生成一系列 `data_type` 为 `EMISSION` 的 `StandardizedData` 对象。其 `attributes` 字典将包含详细的溯源信息：

```python
{
    'value': 1234.56,                  # CO2排放量 (单位: 万吨)
    'unit': '万吨',
    'emission_source_type': 'energy_activity' or 'industrial_process', # 排放源类型
    'original_activity_value': 5000.0, # 原始活动数据 (如原煤消耗量或水泥产量)
    'original_activity_unit': '吨',
    'emission_factor_used': 2.45,      # 使用的排放因子
    'emission_factor_unit': '吨CO2/吨',
    'emission_factor_source': 'IPCC 2006 Guidelines' # 因子来源
}
```

### 5.1. 阶段性 DataFrame 约定

为了提升计算效率，`CarbonEmissionService` 在其内部实现中，会将输入的 `List[StandardizedData]` 转换为 Pandas DataFrame 进行批处理。这个内部的 DataFrame 结构约定如下：

-   **输入 DataFrame (Input DF)**: 从 `List[StandardizedData]` 转换而来，至少包含以下列：
    -   `data_type`: `StandardizedDataType`
    -   `year`: 年份
    -   `province`: 省份
    -   `city`: 城市
    -   `source_table`: 原始表名
    -   `activity_key`: 活动标识 (能源类型或产品名称)
    -   `activity_value`: 活动数据 (能源消费量或产品产量)
    -   `activity_unit`: 活动数据单位

-   **处理过程**:
    1.  服务基于 `activity_key` 和 `year` 批量查询数据库获取排放因子。
    2.  将查询到的因子 `merge` 回 DataFrame。
    3.  进行单位换算和排放量计算，生成 `emission_value` 列。

-   **输出**: 服务在完成计算后，会将处理过的 DataFrame **转换回 `List[StandardizedData]`**，以符合领域服务接口的约定，并返回给调用方 (`DataStandardizationServiceImpl`)。外部调用者不直接与此阶段性 DataFrame 交互。

此方案确保了碳排放计算的逻辑清晰、数据可追溯，并与现有系统架构无缝集成。
