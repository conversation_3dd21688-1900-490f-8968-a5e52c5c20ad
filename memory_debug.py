#!/usr/bin/env python3
"""
内存调试脚本 - 用于诊断数据增强服务的内存使用问题
"""

import sys
import os
import pandas as pd
import logging
import gc
import tracemalloc

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ecam_calculator.infrastructure.database_reader import DatabaseReader
from ecam_calculator.domain.service.data_enhancement_service import DataEnhancementServiceImpl

def get_memory_usage():
    """获取当前内存使用情况"""
    import psutil
    process = psutil.Process()
    return process.memory_info().rss / 1024 / 1024  # MB

def log_memory_usage(stage_name):
    """记录内存使用情况"""
    try:
        memory_mb = get_memory_usage()
        print(f"[{stage_name}] 内存使用: {memory_mb:.2f} MB")
        return memory_mb
    except ImportError:
        print(f"[{stage_name}] 无法获取内存使用情况（psutil未安装）")
        return 0

def main():
    print("=== 内存调试脚本 ===")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 数据库配置
    db_config = {
        'host': 'db',
        'port': 3306,
        'database': 'ecam_city',
        'user': 'user',
        'password': 'password'
    }
    
    try:
        # 启动内存跟踪
        tracemalloc.start()
        
        # 1. 初始化阶段
        log_memory_usage("初始化前")
        
        enhancement_service = DataEnhancementServiceImpl()
        log_memory_usage("服务初始化后")
        
        # 2. 数据读取阶段
        db_reader = DatabaseReader(db_config)
        
        # 读取电力数据
        electricity_df, error = db_reader.read_electricity_consumption()
        if error:
            print(f"电力数据读取失败: {error}")
            return
        
        log_memory_usage("电力数据读取后")
        
        # 过滤2020年数据
        electricity_df['year'] = pd.to_datetime(electricity_df['month']).dt.year
        electricity_2020 = electricity_df[electricity_df['year'] == 2020]
        print(f"2020年电力数据: {len(electricity_2020)} 条记录")
        log_memory_usage("电力数据过滤后")
        
        # 读取省级数据
        energy_df, error = db_reader.read_energy_consumption()
        if error:
            print(f"省级数据读取失败: {error}")
            return
        
        energy_2020 = energy_df[energy_df['year'] == 2020]
        print(f"2020年省级数据: {len(energy_2020)} 条记录")
        log_memory_usage("省级数据读取后")
        
        # 3. 数据准备阶段
        raw_data = {
            'emission_factors': db_reader.read_emission_factors()[0],
            'energy_consumption': energy_2020,
            'energy_consumption2': db_reader.read_energy_consumption2()[0],
            'electricity': electricity_2020,
            'gdp_data': db_reader.read_gdp_data()[0],
            'energy_intensity': db_reader.read_energy_intensity()[0],
            'product_output': db_reader.read_industrial_products_output()[0]
        }
        
        log_memory_usage("数据准备完成")
        
        # 4. 数据增强阶段 - 逐步监控
        print("\n=== 开始数据增强 ===")
        
        # 准备电力数据
        prepared_electricity = enhancement_service._prepare_electricity_data(electricity_2020)
        log_memory_usage("电力数据准备后")
        
        # 空间降尺度
        spatial_result = enhancement_service.spatial_downscaling(
            provincial_data=energy_2020,
            city_data=raw_data['energy_consumption2'],
            city_electricity=prepared_electricity
        )
        log_memory_usage("空间降尺度完成")
        
        print(f"空间降尺度结果: {len(spatial_result)} 条记录")
        
        # 强制垃圾回收
        gc.collect()
        log_memory_usage("垃圾回收后")
        
        # 5. 内存跟踪结果
        current, peak = tracemalloc.get_traced_memory()
        print(f"\n内存跟踪结果:")
        print(f"当前内存: {current / 1024 / 1024:.2f} MB")
        print(f"峰值内存: {peak / 1024 / 1024:.2f} MB")
        
        tracemalloc.stop()
        
        print("\n=== 调试完成 ===")
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
