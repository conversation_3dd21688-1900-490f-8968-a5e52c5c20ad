--量费计算计划明细表
DROP TABLE IF EXISTS ads_cst_qty_charg_plan_del;

CREATE TABLE IF NOT EXISTS ads_cst_qty_charg_plan_del (
    qty_charg_calc_plan_id VARCHAR(16) NOT NULL COMMENT '量费计算计划标识',
    qty_charg_calc_bus_type VARCHAR(8) COMMENT '量费计算业务类型',
    calc_type VARCHAR(20) COMMENT '计算类型',
    exp_ym VARCHAR(8) COMMENT '费用年月',
    plan_no VARCHAR(32) COMMENT '计划编号',
    cur_plan_stat VARCHAR(8) COMMENT '当前计划状态',
    cur_stat_acmp_flag VARCHAR(8) COMMENT '当前状态完成标志',
    calc_id VARCHAR(16) COMMENT '计算标识',
    cust_no VARCHAR(16) COMMENT '客户编号',
    cust_name VARCHAR(256) COMMENT '客户名称',
    ec_addr VARCHAR(256) COMMENT '用能地址',
    ec_categ VARCHAR(8) COMMENT '用能类别',
    voltage VARCHAR(8) COMMENT '承压',
    t_settle_qty DECIMAL(15,0) COMMENT '总结算量',
    t_settle_exp DECIMAL(18,2) COMMENT '总结算费',
    PRIMARY KEY (qty_charg_calc_plan_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='量费计算计划明细';

-- 安装计费卡表
DROP TABLE IF EXISTS ads_cst_inst_bilg_card;

CREATE TABLE IF NOT EXISTS ads_cst_inst_bilg_card (
    bilg_card_id VARCHAR(16) NOT NULL COMMENT '计费卡标识',
    calc_id VARCHAR(16) COMMENT '计算标识',
    qty_charg_ym VARCHAR(6) COMMENT '量费年月',
    exp_attr_cls VARCHAR(8) COMMENT '费用属性分类',
    plan_no VARCHAR(16) COMMENT '计划编号',
    cust_no VARCHAR(16) COMMENT '客户编号',
    inst_id VARCHAR(16) COMMENT '安装点标识',
    prc_code VARCHAR(16) COMMENT '价格码',
    t_settle_qty DECIMAL(18,2) COMMENT '总结量量',
    t_settle_exp DECIMAL(18,2) COMMENT '总结费费',
    t_ctlg_exp DECIMAL(18,2) COMMENT '总目录费用',
    t_addl_charg DECIMAL(18,2) COMMENT '总加收费',
    ext_settle_qty DECIMAL(18,2) COMMENT '扩展结量量',
    inst_acct_id VARCHAR(16) COMMENT '安装点台账标识',
    exp_ymd VARCHAR(8) COMMENT '费用年月日',
    t_self_cons_elec_qty DECIMAL(18,2) COMMENT '总自发自用电量',
    t_self_cons_elec_charg DECIMAL(18,2) COMMENT '总自发自用加收电费',
    trnsm_dist_std_ver_no VARCHAR(16) COMMENT '输配计费标准版本号',
    inst_snap_id VARCHAR(16) COMMENT '安装点快照标识',
    write_off_type VARCHAR(8) COMMENT '冲减类型',
    as_ym VARCHAR(6) COMMENT '追补年月',
    PRIMARY KEY (bilg_card_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='安装计费卡';

-- 分段量费表
DROP TABLE IF EXISTS ads_cst_sgmt_qty_charg;

CREATE TABLE IF NOT EXISTS ads_cst_sgmt_qty_charg (
    sgmt_qty_charg_id VARCHAR(16) NOT NULL COMMENT '分段量费标识',
    bilg_card_id VARCHAR(16) COMMENT '计费卡标识',
    exp_attr_cls VARCHAR(8) COMMENT '费用属性分类',
    ctlg_cls VARCHAR(8) COMMENT '目录分类',
    settle_qty DECIMAL(15,0) COMMENT '结算量',
    exec_ctlg_up DECIMAL(14,10) COMMENT '执行目录单价',
    ctlg_exp DECIMAL(18,2) COMMENT '目录费用',
    deg_up DECIMAL(14,10) COMMENT '度数单价',
    deg_exp DECIMAL(18,2) COMMENT '度数费用',
    ext_settle_qty DECIMAL(15,0) COMMENT '扩展结算量',
    qty_charg_ym VARCHAR(6) COMMENT '量费年月',
    mgt_org_code VARCHAR(16) COMMENT '管理单位编码',
    cust_no VARCHAR(16) COMMENT '客户编号',
    inst_id VARCHAR(16) COMMENT '安装点标识',
    PRIMARY KEY (sgmt_qty_charg_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分段量费';

-- 专属费用/力调电费表
DROP TABLE IF EXISTS ads_cst_special_expense_lt;

CREATE TABLE IF NOT EXISTS ads_cst_special_expense_lt (
    cust_no VARCHAR(16) NOT NULL COMMENT '客户编号',
    qty_charg_ym VARCHAR(6) NOT NULL COMMENT '量费年月',
    settle_exp_qty_val DECIMAL(22,6) COMMENT '结算费用量值',
    settle_prc DECIMAL(14,10) COMMENT '结算价格',
    settle_exp DECIMAL(18,2) COMMENT '结算费用',
    calc_actl_pf_ap_q DECIMAL(15,0) COMMENT '计算实际功率因数有功电量',
    calc_actl_pf_rp_q DECIMAL(15,0) COMMENT '计算实际功率因数无功电量',
    actl_p_f DECIMAL(12,4) COMMENT '实际功率因数',
    PRIMARY KEY (cust_no, qty_charg_ym)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='专属费用/力调电费';

-- 专属费用/基本电费表
DROP TABLE IF EXISTS ads_cst_special_expense_base;

CREATE TABLE IF NOT EXISTS ads_cst_special_expense_base (
    cust_no VARCHAR(16) NOT NULL COMMENT '客户编号',
    qty_charg_ym VARCHAR(6) NOT NULL COMMENT '量费年月',
    spcl_fee_categ VARCHAR(8) NOT NULL COMMENT '专属费用类别',
    spcl_exp_scnd_lv_cls VARCHAR(8) COMMENT '专属费用二级分类',
    settle_exp_qty_val DECIMAL(22,6) COMMENT '结算费用量值',
    settle_prc DECIMAL(14,10) COMMENT '结算价格',
    settle_exp DECIMAL(18,2) COMMENT '结算费用',
    PRIMARY KEY (cust_no, qty_charg_ym, spcl_fee_categ)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='专属费用/基本电费';

-- 加收费表
DROP TABLE IF EXISTS ads_cst_addl_charg1;

CREATE TABLE IF NOT EXISTS ads_cst_addl_charg1 (
    cust_no VARCHAR(16) NOT NULL COMMENT '客户编号',
    qty_charg_ym VARCHAR(6) NOT NULL COMMENT '量费年月',
    addl_charg_ctlg VARCHAR(8) NOT NULL COMMENT '加收项',
    addl_num DECIMAL(15,0) COMMENT '加收量',
    addl_prc DECIMAL(14,10) COMMENT '加收单价',
    addl_amt DECIMAL(18,2) COMMENT '加收费用',
    PRIMARY KEY (cust_no, qty_charg_ym, addl_charg_ctlg)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='加收费';