#!/bin/bash

# 区域"电-能-碳"监测分析系统 Linux部署脚本
# 适用于 Python 3.7.2

echo "🚀 部署区域电-能-碳监测分析系统到Linux环境..."

# 检查Python版本
PYTHON_VERSION=$(python3 --version 2>&1 | grep -o '3\.7\.[0-9]')
if [[ -z "$PYTHON_VERSION" ]]; then
    echo "❌ 错误: 需要Python 3.7.x版本，当前版本: $(python3 --version)"
    echo "请安装Python 3.7.2:"
    echo "  Ubuntu/Debian: sudo apt-get install python3.7 python3.7-venv python3.7-pip"
    echo "  CentOS/RHEL: sudo yum install python37 python37-pip"
    exit 1
fi

echo "✅ Python版本检查通过: $PYTHON_VERSION"

# 创建虚拟环境
if [ ! -d ".venv" ]; then
    echo "🔧 创建Python虚拟环境..."
    python3.7 -m venv .venv
fi

# 激活虚拟环境
echo "🔌 激活虚拟环境..."
source .venv/bin/activate

# 升级pip
echo "📦 升级pip..."
pip install --upgrade pip

# 安装依赖
echo "📚 安装项目依赖..."
pip install -r requirements-py37.txt

# 安装项目本身
echo "🔧 安装项目..."
pip install -e .

echo "✅ Linux环境部署完成！"
echo ""
echo "使用方法："
echo "1. 激活虚拟环境: source .venv/bin/activate"
echo "2. 运行主程序: python main.py"
echo "3. 运行测试: pytest"
echo "4. 代码格式化: black ."
echo "5. 代码检查: flake8 ."
echo "6. 类型检查: mypy ."
echo ""
echo "当前Python版本: $(python --version)"
echo "虚拟环境路径: $(pwd)/.venv"
echo ""
echo "注意: 此环境已针对Python 3.7.2优化，确保兼容性！"
