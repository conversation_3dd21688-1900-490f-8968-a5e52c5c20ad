# 能源单位转换为标准煤当量方案

## 1. 方案概述

本方案旨在为"区域'电-能-碳'监测分析系统"提供完整的能源单位转换为标准煤当量的功能，确保不同能源品种、不同单位的数据能够统一转换为标准煤当量，便于后续的能源消费总量计算和碳排放核算。

## 2. 标准煤当量定义

**标准煤当量**：1千克标准煤的热值为29.3076兆焦耳（MJ），这是国际上通用的标准煤热值。

## 3. 能源品种分类与转换系数

### 3.1 煤炭类能源
| 能源品种 | 原始单位 | 标准煤当量系数 | 转换公式 | 备注 |
|---------|---------|---------------|----------|------|
| 原煤 | 万吨 | 0.7143 | 原煤量 × 0.7143 | 平均热值20.91 MJ/kg |
| 焦炭 | 万吨 | 0.9714 | 焦炭量 × 0.9714 | 平均热值28.47 MJ/kg |
| 洗精煤 | 万吨 | 0.9000 | 洗精煤量 × 0.9000 | 平均热值26.37 MJ/kg |
| 煤制品 | 万吨 | 0.7143 | 煤制品量 × 0.7143 | 按原煤热值计算 |
| 煤矸石 | 万吨 | 0.2857 | 煤矸石量 × 0.2857 | 平均热值8.37 MJ/kg |

### 3.2 石油类能源
| 能源品种 | 原始单位 | 标准煤当量系数 | 转换公式 | 备注 |
|---------|---------|---------------|----------|------|
| 原油 | 万吨 | 1.4286 | 原油量 × 1.4286 | 平均热值41.87 MJ/kg |
| 汽油 | 万吨 | 1.4714 | 汽油量 × 1.4714 | 平均热值43.12 MJ/kg |
| 柴油 | 万吨 | 1.4571 | 柴油量 × 1.4571 | 平均热值42.65 MJ/kg |
| 煤油 | 万吨 | 1.4714 | 煤油量 × 1.4714 | 平均热值43.12 MJ/kg |
| 燃料油 | 万吨 | 1.4286 | 燃料油量 × 1.4286 | 平均热值41.87 MJ/kg |

### 3.3 气体能源
| 能源品种 | 原始单位 | 标准煤当量系数 | 转换公式 | 备注 |
|---------|---------|---------------|----------|------|
| 天然气 | 亿立方米 | 13.3000 | 天然气量 × 13.3000 | 平均热值38.93 MJ/m³ |
| 焦炉煤气 | 亿立方米 | 5.7143 | 焦炉煤气量 × 5.7143 | 平均热值16.75 MJ/m³ |
| 高炉煤气 | 亿立方米 | 3.5714 | 高炉煤气量 × 3.5714 | 平均热值10.47 MJ/m³ |
| 转炉煤气 | 亿立方米 | 4.2857 | 转炉煤气量 × 4.2857 | 平均热值12.56 MJ/m³ |

### 3.4 电力与热力
| 能源品种 | 原始单位 | 标准煤当量系数 | 转换公式 | 备注 |
|---------|---------|---------------|----------|------|
| 电力 | 亿千瓦时 | 1.2290 | 电量 × 1.2290 | 按发电煤耗计算 |
| 热力 | 万吉焦 | 0.0341 | 热力量 × 0.0341 | 按供热煤耗计算 |

## 4. 技术实现方案

### 4.1 数据库表结构分析

基于实际的 `ecam_in_energy_factor` 表结构，该表存储了能源转换系数和碳排放因子：

**表结构：**
- `year`: 年份 (date)
- `source`: 数据来源 (varchar(50))
- `province`: 省份 (varchar(50))
- `factor`: 因子类型 (varchar(50)) - "折标系数" 或 "co2"
- `method`: 计算方法 (varchar(50))
- `industry`: 行业 (varchar(50))
- `energy_type`: 能源类型 (varchar(50))
- `unit`: 单位 (varchar(50))
- `value`: 系数值 (decimal(30,12))

**实际数据示例：**
- **折标系数**: 电力 - 吨标煤/万千瓦时 = 1.229 (2021年)
- **折标系数**: 原煤 - 吨标准煤/吨 = 0.7143 (2021年)
- **折标系数**: 焦炭 - 吨标准煤/吨 = 0.9714 (2020年)
- **折标系数**: 天然气 - 吨标准煤/万立方米 = 12.55 (2021年)
- **折标系数**: 热力 - 吨标准煤/百万千焦耳 = 0.03412 (2021年)

### 4.2 配置文件扩展

在现有的 `config/parameters.yaml` 中扩展能源品种配置，与数据库表结构保持一致：

```yaml
energy_types:
  fossil_fuels:
    - name: "原煤"
      standard_unit: "万吨标准煤"
      emission_category: "煤炭"
      conversion_method: "折标系数"
      database_lookup: true
      lookup_params:
        factor: "折标系数"
        energy_type: "原煤"
        unit: "吨标准煤/吨"
      
    - name: "焦炭"
      standard_unit: "万吨标准煤"
      emission_category: "煤炭"
      conversion_method: "折标系数"
      database_lookup: true
      lookup_params:
        factor: "折标系数"
        energy_type: "焦炭"
        unit: "吨标准煤/吨"
      
  petroleum_products:
    - name: "汽油"
      standard_unit: "万吨标准煤"
      emission_category: "石油"
      conversion_method: "折标系数"
      database_lookup: true
      lookup_params:
        factor: "折标系数"
        energy_type: "汽油"
        unit: "吨标准煤/吨"
      
  gas_energy:
    - name: "天然气"
      standard_unit: "万吨标准煤"
      emission_category: "天然气"
      conversion_method: "折标系数"
      database_lookup: true
      lookup_params:
        factor: "折标系数"
        energy_type: "天然气"
        unit: "吨标准煤/万立方米"
      
  electricity_heat:
    - name: "电力"
      standard_unit: "万吨标准煤"
      emission_category: "电力"
      conversion_method: "折标系数"
      database_lookup: true
      lookup_params:
        factor: "折标系数"
        energy_type: "电力"
        unit: "吨标煤/万千瓦时"
      
    - name: "热力"
      standard_unit: "万吨标准煤"
      emission_category: "热力"
      conversion_method: "折标系数"
      database_lookup: true
      lookup_params:
        factor: "折标系数"
        energy_type: "热力"
        unit: "吨标准煤/百万千焦耳"
```

### 4.3 核心转换服务类

创建 `EnergyConversionService` 类，基于数据库表进行转换系数查询：

```python
class EnergyConversionService:
    """能源单位转换服务 - 基于数据库表"""
    
    def __init__(self, config_reader, database_connection):
        self.config_reader = config_reader
        self.db_connection = database_connection
        self.conversion_cache = {}  # 缓存转换系数
        
    def convert_to_standard_coal(self, energy_data: pd.DataFrame, target_year: int = None) -> pd.DataFrame:
        """将能源数据转换为标准煤当量"""
        if 'energy_type' not in energy_data.columns:
            return energy_data
            
        result_df = energy_data.copy()
        result_df['standard_coal_equivalent'] = 0.0
        result_df['conversion_factor_used'] = 1.0
        result_df['conversion_method'] = '未转换'
        result_df['conversion_source'] = '未转换'
        
        for idx, row in result_df.iterrows():
            energy_type = row.get('energy_type')
            value = row.get('value', 0)
            unit = row.get('unit', '')
            year = row.get('year', target_year or 2021)  # 默认使用2021年系数
            
            if pd.isna(energy_type) or pd.isna(value):
                continue
                
            conversion_result = self._convert_single_energy(
                energy_type, value, unit, year
            )
            
            if conversion_result:
                result_df.at[idx, 'standard_coal_equivalent'] = conversion_result['standard_coal']
                result_df.at[idx, 'conversion_factor_used'] = conversion_result['factor']
                result_df.at[idx, 'conversion_method'] = conversion_result['method']
                result_df.at[idx, 'conversion_source'] = conversion_result['source']
                result_df.at[idx, 'standard_unit'] = '万吨标准煤'
        
        return result_df
    
    def _convert_single_energy(self, energy_type: str, value: float, unit: str, year: int) -> dict:
        """转换单个能源品种"""
        # 从数据库查询转换系数
        conversion_factor = self._get_conversion_factor_from_db(energy_type, year)
        
        if not conversion_factor:
            return None
            
        # 单位转换
        unit_conversion = self._convert_unit(unit, conversion_factor['unit'])
        if unit_conversion is None:
            return None
            
        # 计算标准煤当量
        standard_coal = value * conversion_factor['value'] * unit_conversion
        
        return {
            'standard_coal': standard_coal,
            'factor': conversion_factor['value'] * unit_conversion,
            'method': conversion_factor['method'],
            'source': conversion_factor['source']
        }
    
    def _get_conversion_factor_from_db(self, energy_type: str, year: int) -> dict:
        """从数据库查询转换系数"""
        cache_key = f"{energy_type}_{year}"
        
        if cache_key in self.conversion_cache:
            return self.conversion_cache[cache_key]
        
        try:
            cursor = self.db_connection.cursor()
            
            # 查询指定年份的转换系数，如果没有则查询最近的年份
            query = """
            SELECT factor, method, unit, value, source
            FROM ecam_in_energy_factor 
            WHERE factor = '折标系数' 
            AND energy_type = %s 
            AND year <= %s
            ORDER BY year DESC 
            LIMIT 1
            """
            
            cursor.execute(query, (energy_type, f"{year}-01-01"))
            result = cursor.fetchone()
            
            if result:
                conversion_factor = {
                    'factor': result[0],
                    'method': result[1],
                    'unit': result[2],
                    'value': float(result[3]),
                    'source': result[4]
                }
                
                # 缓存结果
                self.conversion_cache[cache_key] = conversion_factor
                return conversion_factor
            
            cursor.close()
            return None
            
        except Exception as e:
            print(f"查询转换系数失败: {e}")
            return None
    
    def _convert_unit(self, input_unit: str, target_unit: str) -> float:
        """单位转换 - 基于目标单位进行转换"""
        input_unit = input_unit.strip().lower()
        target_unit = target_unit.strip().lower()
        
        # 如果单位已经匹配，直接返回1.0
        if input_unit == target_unit:
            return 1.0
        
        # 重量单位转换
        weight_conversions = {
            '千克': 0.000001,  # 千克 -> 万吨
            '吨': 0.0001,      # 吨 -> 万吨
            '万吨': 1.0,       # 万吨 -> 万吨
        }
        
        # 体积单位转换
        volume_conversions = {
            '立方米': 0.00000001,    # 立方米 -> 亿立方米
            '万立方米': 0.0001,      # 万立方米 -> 亿立方米
            '亿立方米': 1.0,         # 亿立方米 -> 亿立方米
        }
        
        # 能量单位转换
        energy_conversions = {
            '千瓦时': 0.0000000001,   # 千瓦时 -> 亿千瓦时
            '万千瓦时': 0.0001,       # 万千瓦时 -> 亿千瓦时
            '亿千瓦时': 1.0,          # 亿千瓦时 -> 亿千瓦时
            '千焦耳': 0.0000000001,   # 千焦耳 -> 百万千焦耳
            '万千焦耳': 0.0001,       # 万千焦耳 -> 百万千焦耳
            '百万千焦耳': 1.0,        # 百万千焦耳 -> 百万千焦耳
        }
        
        # 检查重量单位
        for weight_unit, factor in weight_conversions.items():
            if weight_unit in input_unit:
                return factor
                
        # 检查体积单位
        for volume_unit, factor in volume_conversions.items():
            if volume_unit in input_unit:
                return factor
                
        # 检查能量单位
        for energy_unit, factor in energy_conversions.items():
            if energy_unit in input_unit:
                return factor
        
        # 默认返回1.0（假设已经是标准单位）
        return 1.0
    
    def get_carbon_emission_factor(self, energy_type: str, year: int) -> float:
        """获取碳排放因子"""
        try:
            cursor = self.db_connection.cursor()
            
            query = """
            SELECT value
            FROM ecam_in_energy_factor 
            WHERE factor = 'co2' 
            AND energy_type = %s 
            AND year <= %s
            ORDER BY year DESC 
            LIMIT 1
            """
            
            cursor.execute(query, (energy_type, f"{year}-01-01"))
            result = cursor.fetchone()
            
            cursor.close()
            
            if result:
                return float(result[0])
            return None
            
        except Exception as e:
            print(f"查询碳排放因子失败: {e}")
            return None
```

### 4.4 数据标准化服务集成

在现有的 `DataStandardizationServiceImpl` 中集成能源转换：

```python
def _standardize_energy(self, raw_data: RawData) -> List[StandardizedData]:
    """标准化能源数据（包含单位转换）"""
    standardized_list = []
    
    if raw_data.data.empty:
        self.logger.warning(f"数据源 '{raw_data.source_name}' 为空，跳过标准化。")
        return []
    
    # 行业名称标准化
    standardized_df = self._standardize_industry_names(raw_data.data, raw_data.source_name)
    
    # 能源品种标准化
    standardized_df = self._standardize_energy_types(standardized_df, raw_data.source_name)
    
    # 能源单位转换为标准煤当量
    if hasattr(self, 'energy_conversion_service'):
        standardized_df = self.energy_conversion_service.convert_to_standard_coal(standardized_df)
    
    # 转换为StandardizedData对象
    for _, row in standardized_df.iterrows():
        try:
            attributes = {
                'value': row.get('standard_coal_equivalent', row.get('value', 0)),
                'unit': '万吨标准煤',
                'indicator_category': 'energy_consumption',
                'industry': row.get('standard_industry', row.get('industry', '')),
                'energy_type': row.get('standard_energy_type', row.get('energy_type', '')),
                'original_value': row.get('value', 0),
                'original_unit': row.get('unit', ''),
                'conversion_factor': row.get('conversion_factor_used', 1.0),
                'conversion_method': row.get('conversion_method', '未转换'),
                'conversion_source': row.get('conversion_source', '未转换')
            }
            
            standardized_data = StandardizedData(
                data_type=StandardizedDataType.ENERGY,
                year=int(row['year']),
                province=self._get_province_from_city(row['area']),
                city=row['area'],
                source_table=raw_data.source_name,
                attributes=attributes
            )
            standardized_list.append(standardized_data)
            
        except Exception as e:
            self.logger.error(f"标准化行数据失败: {row}, 错误: {e}")
            continue
    
    self.logger.info(f"数据源 '{raw_data.source_name}' 标准化完成，产出 {len(standardized_list)} 条记录。")
    return standardized_list
```

### 4.5 服务初始化集成

在 `DataStandardizationServiceImpl` 的构造函数中初始化能源转换服务：

```python
def __init__(self, config_reader, database_connection):
    self.config_reader = config_reader
    self.logger = logging.getLogger(__name__)
    self._strategies = self._build_strategies()
    
    # 初始化能源转换服务
    self.energy_conversion_service = EnergyConversionService(
        config_reader, 
        database_connection
    )
```

## 5. 数据验证与质量控制

### 5.1 转换系数验证
- 检查转换系数是否在合理范围内
- 验证热值数据的科学性
- 确保单位转换的准确性

### 5.2 数据一致性检查
- 同一能源品种在不同表中的转换结果应一致
- 年度能源消费总量应与各品种汇总值匹配
- 地区能源消费应与省级数据协调

### 5.3 异常值检测
- 识别转换后数值异常的数据
- 检查原始数据与转换结果的合理性
- 标记需要人工审核的数据

## 6. 性能优化

### 6.1 批量处理
- 使用pandas向量化操作进行批量转换
- 避免逐行处理，提高转换效率

### 6.2 缓存机制
- 缓存转换系数配置
- 缓存常用单位转换结果

### 6.3 并行处理
- 对于大量数据，考虑并行转换
- 使用多进程或多线程提高处理速度

## 7. 测试方案

### 7.1 单元测试
- 测试各种能源品种的转换系数
- 验证单位转换的准确性
- 测试异常情况的处理

### 7.2 集成测试
- 测试与现有数据标准化流程的集成
- 验证转换后数据的完整性
- 测试性能表现

### 7.3 数据验证测试
- 使用已知的测试数据集验证转换结果
- 对比手动计算结果
- 验证转换后数据的业务逻辑正确性

## 8. 部署与维护

### 8.1 配置管理
- 转换系数配置集中管理
- 支持动态更新转换参数
- 版本控制和变更记录

### 8.2 监控与日志
- 记录转换过程的详细信息
- 监控转换成功率
- 记录异常转换情况

### 8.3 数据备份
- 转换前原始数据备份
- 转换后结果数据备份
- 转换过程日志备份

## 9. 后续扩展

### 9.1 支持更多能源品种
- 生物质能源
- 可再生能源
- 新型能源品种

### 9.2 动态转换系数
- 基于时间变化的转换系数
- 基于地区差异的转换系数
- 基于技术进步的转换系数

### 9.3 碳排放因子集成
- 将标准煤当量与碳排放因子结合
- 支持直接碳排放计算
- 提供碳排放核算功能

## 10. 总结

本方案提供了完整的能源单位转换为标准煤当量的技术实现方案，基于实际的数据库表结构设计，包括：

1. **基于数据库的转换系数体系**：直接从 `ecam_in_energy_factor` 表查询最新的转换系数，确保数据的时效性和准确性
2. **动态系数查询**：支持按年份查询转换系数，自动回退到最近的可用年份数据
3. **智能单位转换**：基于目标单位自动进行单位换算，支持重量、体积、能量等多种单位类型
4. **缓存机制**：实现转换系数缓存，提高查询性能
5. **碳排放因子集成**：同时支持标准煤当量转换和碳排放因子查询，为后续碳排放核算提供数据基础
6. **可追溯性**：记录转换过程的所有信息，包括转换系数来源、计算方法等

### 方案优势

- **数据权威性**：转换系数来自国家标准和IPCC指南，确保数据的科学性和权威性
- **时效性**：支持按年份查询，能够反映不同时期的技术进步和标准变化
- **灵活性**：支持多种能源品种和单位类型，适应不同的数据源
- **可维护性**：基于数据库存储，便于系数更新和维护
- **性能优化**：实现缓存机制，避免重复查询

### 实施效果

通过实施本方案，系统将能够：
- 统一不同能源品种的数据标准，实现标准煤当量的统一计算
- 提供准确、可追溯的能源转换结果
- 支持后续的能源消费总量计算和能效分析
- 为碳排放核算提供标准化的数据基础
- 提高数据处理的自动化程度和准确性
- 支持多年度数据的连续性和一致性分析
