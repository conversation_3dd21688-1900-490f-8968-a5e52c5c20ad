#!/bin/bash
# 构建whl包的脚本 - 生成平台无关的纯Python包

# 确保脚本在出错时停止
set -e

# 显示当前Python版本
echo "使用的Python版本:"
python3 --version

# 确保安装了最新的构建工具
echo "更新构建工具..."
pip3 install --upgrade pip setuptools wheel

# 清理之前的构建文件
echo "清理之前的构建文件..."
rm -rf build/ dist/ *.egg-info/

# 构建纯Python的whl包（平台无关）
echo "开始构建纯Python的whl包..."
python3 setup.py bdist_wheel --python-tag py3 --plat-name any

# 显示构建结果
echo "构建完成，生成的whl包:"
ls -lh dist/

# 重命名whl包，添加Linux标识
echo "重命名whl包为Linux版本..."
mkdir -p linux_dist
cp dist/*.whl linux_dist/
cd linux_dist
# 将文件名中的-py3-none-any.whl替换为-cp37-cp37m-linux_x86_64.whl
for file in *.whl; do
    new_name=$(echo $file | sed 's/-py3-none-any\.whl/-cp37-cp37m-linux_x86_64.whl/')
    mv "$file" "$new_name"
done
cd ..

echo "Linux版本whl包已生成:"
ls -lh linux_dist/

echo "构建过程完成！"