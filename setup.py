#!/usr/bin/env python3
"""
区域"电-能-碳"监测分析系统安装配置
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "区域电-能-碳监测分析系统"

# 读取requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="ecam-calculator",
    version="1.0.0",
    author="ECAM Team",
    author_email="<EMAIL>",
    description="区域电-能-碳监测分析系统核心计算包",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    packages=find_packages(),
    python_requires=">=3.7",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "ecam-calculator=ecam_calculator.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "ecam_calculator": ["config/*.yaml", "config/*.yml"],
    },
    zip_safe=False,
)


