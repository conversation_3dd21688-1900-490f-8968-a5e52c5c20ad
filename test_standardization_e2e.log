2025-09-03 07:59:53,785 - __main__ - INFO - 开始数据标准化端到端测试
2025-09-03 07:59:53,785 - __main__ - INFO - ============================================================
2025-09-03 07:59:53,785 - __main__ - INFO - 开始测试能源消费数据标准化完整流程
2025-09-03 07:59:53,785 - __main__ - INFO - ============================================================
2025-09-03 07:59:53,785 - __main__ - INFO - 步骤1: 读取配置
2025-09-03 07:59:53,828 - __main__ - INFO - ✓ 配置读取和验证成功
2025-09-03 07:59:53,829 - __main__ - INFO - 步骤2: 读取原始数据
2025-09-03 07:59:53,829 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有年度省级能源消费量数据...
2025-09-03 07:59:53,966 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 07:59:54,166 - ecam_calculator.infrastructure.database_reader - INFO - 获取到 25760 条省级终端消费与加工转换记录
2025-09-03 07:59:54,168 - __main__ - INFO - ✓ 原始数据读取成功: 25760 行
2025-09-03 07:59:54,168 - __main__ - INFO - 原始列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit']
2025-09-03 07:59:54,168 - __main__ - INFO - 原始数据样本:
2025-09-03 07:59:54,171 - __main__ - INFO -    year province item convert method energy_type  value unit
0  2000       山西   乡村     实物量                 原油    NaN   万吨
1  2000       山西   乡村     实物量                 原煤  442.0   万吨
2  2000       山西   乡村     实物量                 柴油    NaN   万吨
2025-09-03 07:59:54,171 - __main__ - INFO - 步骤3: 执行标准化
2025-09-03 07:59:54,171 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 07:59:54,171 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 07:59:54,171 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 07:59:54,173 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_y_pro_ind_ene_off
2025-09-03 07:59:54,649 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 25760 行数据
2025-09-03 07:59:54,650 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_y_pro_ind_ene_off' 标准化完成，输出 25760 行数据
2025-09-03 07:59:54,650 - __main__ - INFO - ✓ 标准化完成: 25760 行
2025-09-03 07:59:54,650 - __main__ - INFO - 标准化后列: ['year', 'province', 'item', 'convert', 'method', 'energy_type', 'value', 'unit', 'standard_province', 'macro_province', 'standard_item', 'macro_item', 'standard_energy_type', 'macro_energy_type']
2025-09-03 07:59:54,650 - __main__ - INFO - 步骤4: 验证标准化结果
2025-09-03 07:59:54,650 - __main__ - INFO - ✓ standard_province 列存在
2025-09-03 07:59:54,650 - __main__ - INFO - ✓ standard_item 列存在
2025-09-03 07:59:54,650 - __main__ - INFO - ✓ standard_energy_type 列存在
2025-09-03 07:59:54,650 - __main__ - INFO - ✓ macro_province 列存在
2025-09-03 07:59:54,650 - __main__ - INFO - ✓ macro_item 列存在
2025-09-03 07:59:54,650 - __main__ - INFO - ✓ macro_energy_type 列存在
2025-09-03 07:59:54,650 - __main__ - INFO - 标准化后数据样本:
2025-09-03 07:59:54,657 - __main__ - INFO -    year province item energy_type  value standard_province macro_province standard_item macro_item standard_energy_type macro_energy_type
0  2000       山西   乡村          原油    NaN                山西             山西           NaN        NaN                  NaN               NaN
1  2000       山西   乡村          原煤  442.0                山西             山西           NaN        NaN                   原煤                原煤
2  2000       山西   乡村          柴油    NaN                山西             山西           NaN        NaN                  NaN               NaN
2025-09-03 07:59:54,657 - __main__ - INFO - 步骤5: 统计结果
2025-09-03 07:59:54,657 - __main__ - INFO - 标准化列生成: 3/3
2025-09-03 07:59:54,657 - __main__ - INFO - 层次关系列生成: 3/3
2025-09-03 07:59:54,658 - __main__ - INFO - 标准化省份数量: 1
2025-09-03 07:59:54,659 - __main__ - INFO - 标准化行业数量: 3
2025-09-03 07:59:54,659 - __main__ - INFO - 标准化能源品种数量: 4
2025-09-03 07:59:54,659 - __main__ - INFO - ============================================================
2025-09-03 07:59:54,659 - __main__ - INFO - 测试完成
2025-09-03 07:59:54,659 - __main__ - INFO - ============================================================
2025-09-03 07:59:54,662 - __main__ - INFO - ============================================================
2025-09-03 07:59:54,662 - __main__ - INFO - 开始测试用电量数据标准化完整流程
2025-09-03 07:59:54,662 - __main__ - INFO - ============================================================
2025-09-03 07:59:54,662 - __main__ - INFO - 步骤1: 读取配置
2025-09-03 07:59:54,662 - __main__ - INFO - 步骤2: 读取原始数据
2025-09-03 07:59:54,663 - ecam_calculator.infrastructure.database_reader - INFO - 正在获取所有月度分行业用电量数据...
2025-09-03 07:59:54,688 - ecam_calculator.infrastructure.database_reader - INFO - 数据库连接成功！
2025-09-03 07:59:55,816 - ecam_calculator.infrastructure.database_reader - INFO - 获取到74496条月度用电量记录
2025-09-03 07:59:55,816 - __main__ - INFO - ✓ 原始数据读取成功: 74496 行
2025-09-03 07:59:55,816 - __main__ - INFO - 原始列: ['month', 'area', 'industry', 'electricity']
2025-09-03 07:59:55,817 - __main__ - INFO - 步骤3: 执行标准化
2025-09-03 07:59:55,817 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 30 个列标准化器
2025-09-03 07:59:55,817 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 加载了 1 个跨列映射器
2025-09-03 07:59:55,817 - ecam_calculator.domain.service.data_standardization_service - INFO - 简化数据标准化服务初始化完成
2025-09-03 07:59:55,819 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 开始标准化表: ecam_in_m_pro_ind_ele_off
2025-09-03 07:59:56,347 - ecam_calculator.domain.service.standardization_orchestrator - INFO - 标准化完成，输出 74496 行数据
2025-09-03 07:59:56,347 - ecam_calculator.domain.service.data_standardization_service - INFO - 表 'ecam_in_m_pro_ind_ele_off' 标准化完成，输出 74496 行数据
2025-09-03 07:59:56,347 - __main__ - INFO - ✓ 标准化完成: 74496 行
2025-09-03 07:59:56,347 - __main__ - INFO - 标准化后列: ['month', 'area', 'industry', 'electricity', 'standard_area', 'macro_area']
2025-09-03 07:59:56,347 - __main__ - INFO - 步骤4: 验证标准化结果
2025-09-03 07:59:56,347 - __main__ - INFO - ✓ standard_area 列存在
2025-09-03 07:59:56,347 - __main__ - WARNING - ✗ standard_item 列缺失
2025-09-03 07:59:56,347 - __main__ - INFO - ============================================================
2025-09-03 07:59:56,348 - __main__ - INFO - 用电量测试完成
2025-09-03 07:59:56,348 - __main__ - INFO - ============================================================
2025-09-03 07:59:56,352 - __main__ - INFO - ============================================================
2025-09-03 07:59:56,352 - __main__ - INFO - 测试总结
2025-09-03 07:59:56,352 - __main__ - INFO - ============================================================
2025-09-03 07:59:56,352 - __main__ - INFO - 能源消费数据标准化: ✓ 成功
2025-09-03 07:59:56,352 - __main__ - INFO - 用电量数据标准化: ✓ 成功
2025-09-03 07:59:56,352 - __main__ - INFO - 所有测试通过！
