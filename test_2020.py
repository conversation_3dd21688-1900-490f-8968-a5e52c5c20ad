#!/usr/bin/env python3
"""
测试2020年数据的简化版本
"""
import sys
import os
sys.path.append('/app')

import pandas as pd
from ecam_calculator.infrastructure.database_reader import DatabaseReader
from ecam_calculator.domain.service.data_standardization_service import DataStandardizationService
from ecam_calculator.domain.service.data_enhancement_service import DataEnhancementServiceImpl
from ecam_calculator.domain.service.energy_conversion_service import EnergyConversionService
from ecam_calculator.domain.service.modeling_prediction_service import ModelingPredictionService

def main():
    print("=== 测试2020年数据处理 ===")
    
    # 数据库配置
    db_config = {
        'host': 'db',
        'port': 3306,
        'database': 'ecam_city',
        'user': 'user',
        'password': 'password'
    }
    
    try:
        # 1. 初始化数据库读取器
        print("1. 初始化数据库读取器...")
        db_reader = DatabaseReader(db_config)
        
        # 2. 读取2020年数据
        print("2. 读取2020年数据...")
        
        # 读取电力数据并过滤2020年
        electricity_df, error = db_reader.read_electricity_consumption()
        if error:
            print(f"电力数据读取失败: {error}")
            return
        
        electricity_df['year'] = pd.to_datetime(electricity_df['month']).dt.year
        electricity_2020 = electricity_df[electricity_df['year'] == 2020]
        print(f"2020年电力数据: {len(electricity_2020)} 条记录")
        
        # 读取省级能源消费数据并过滤2020年
        energy_df, error = db_reader.read_energy_consumption()
        if error:
            print(f"省级能源消费数据读取失败: {error}")
            return
        
        energy_2020 = energy_df[energy_df['year'] == 2020]
        print(f"2020年省级能源消费数据: {len(energy_2020)} 条记录")
        
        # 读取地市工业能源消费数据
        energy2_df, error = db_reader.read_energy_consumption2()
        if error:
            print(f"地市工业能源消费数据读取失败: {error}")
            return
        
        print(f"地市工业能源消费数据: {len(energy2_df)} 条记录")
        
        # 3. 初始化服务
        print("3. 初始化服务...")
        standardization_service = DataStandardizationService()
        enhancement_service = DataEnhancementServiceImpl()
        conversion_service = EnergyConversionService()
        prediction_service = ModelingPredictionService()
        
        # 4. 数据标准化
        print("4. 执行数据标准化...")
        raw_data = {
            'emission_factors': db_reader.read_emission_factors()[0],
            'energy_consumption': energy_2020,
            'energy_consumption2': energy2_df,
            'electricity': electricity_2020,
            'gdp_data': db_reader.read_gdp_data()[0],
            'energy_intensity': db_reader.read_energy_intensity()[0],
            'product_output': db_reader.read_industrial_products_output()[0]
        }
        
        standardized_data = standardization_service.standardize_all(raw_data)
        print(f"标准化完成，数据源数量: {len(standardized_data)}")
        
        # 5. 数据增强
        print("5. 执行数据增强...")
        enhanced_data = enhancement_service.enhance_data(standardized_data, None)
        print(f"数据增强完成，输出数据源数量: {len(enhanced_data)}")
        
        # 6. 能源转换
        print("6. 执行能源转换...")
        converted_data = conversion_service.convert_energy_data(enhanced_data, None)
        print(f"能源转换完成，输出数据源数量: {len(converted_data)}")
        
        # 7. 建模预测
        print("7. 执行建模预测...")
        prediction_results = prediction_service.predict_energy_consumption(converted_data, None)
        print(f"建模预测完成，输出数据源数量: {len(prediction_results)}")
        
        print("=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
