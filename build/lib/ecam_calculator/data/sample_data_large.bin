#!/usr/bin/env python3
# 生成大型二进制数据文件
import os
import random
import struct
import zlib

# 生成随机的二进制数据
def generate_random_binary(size_mb=10):
    """生成指定大小的随机二进制数据"""
    size_bytes = size_mb * 1024 * 1024
    data = bytearray(os.urandom(size_bytes))
    
    # 添加一些结构化数据
    for i in range(0, len(data), 1024):
        if i + 16 <= len(data):
            # 添加一些浮点数
            for j in range(4):
                if i + j*4 + 4 <= len(data):
                    float_val = random.uniform(-1000.0, 1000.0)
                    float_bytes = struct.pack('f', float_val)
                    data[i+j*4:i+j*4+4] = float_bytes
    
    # 压缩一部分数据以增加熵
    chunk_size = size_bytes // 10
    for i in range(0, size_bytes, chunk_size):
        end = min(i + chunk_size, size_bytes)
        chunk = data[i:end]
        if random.random() < 0.3:  # 30%的几率压缩
            try:
                compressed = zlib.compress(chunk, level=random.randint(1, 9))
                if len(compressed) < len(chunk):
                    data[i:i+len(compressed)] = compressed
            except:
                pass
    
    return bytes(data)

# 生成10MB的随机数据
RANDOM_DATA = generate_random_binary(10)

# 这个文件本身就是数据，不需要执行
if __name__ == "__main__":
    print("这个文件包含了预生成的二进制数据，大小约为10MB")
    print(f"实际大小: {len(RANDOM_DATA) / (1024*1024):.2f} MB")
