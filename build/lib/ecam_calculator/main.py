#!/usr/bin/env python3
"""
主程序入口点 - 用于命令行调用
"""

import argparse
import logging
import sys
import os
from typing import Dict, Any

from ecam_calculator.application.calculation_job_service import CalculationJobService
from ecam_calculator.infrastructure.raw_data_repository_impl import RawDataRepositoryImpl
from ecam_calculator.infrastructure.persistence.job_repository_impl import JobRepositoryImpl


def setup_logging(log_level: str = "INFO") -> None:
    """设置日志级别"""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        numeric_level = logging.INFO
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def get_db_config(args: argparse.Namespace) -> Dict[str, Any]:
    """从命令行参数或环境变量获取数据库配置"""
    # 优先使用命令行参数
    if args.db_host:
        return {
            'host': args.db_host,
            'port': args.db_port,
            'user': args.db_user,
            'password': args.db_password,
            'database': args.db_name,
            'charset': 'utf8mb4'
        }
    
    # 其次使用环境变量
    elif os.environ.get('ECAM_DB_HOST'):
        return {
            'host': os.environ.get('ECAM_DB_HOST', '127.0.0.1'),
            'port': int(os.environ.get('ECAM_DB_PORT', '3306')),
            'user': os.environ.get('ECAM_DB_USER', 'root'),
            'password': os.environ.get('ECAM_DB_PASSWORD', ''),
            'database': os.environ.get('ECAM_DB_NAME', 'ecam_city'),
            'charset': 'utf8mb4'
        }
    
    # 最后使用默认配置
    else:
        return {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': 'rootpassword',
            'database': 'ecam_city',
            'charset': 'utf8mb4'
        }


def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="区域'电-能-碳'监测分析系统命令行工具"
    )
    
    # 数据库连接参数
    db_group = parser.add_argument_group('数据库连接参数')
    db_group.add_argument('--db-host', help='数据库主机地址')
    db_group.add_argument('--db-port', type=int, default=3306, help='数据库端口')
    db_group.add_argument('--db-user', help='数据库用户名')
    db_group.add_argument('--db-password', help='数据库密码')
    db_group.add_argument('--db-name', help='数据库名称')
    
    # 计算任务参数
    task_group = parser.add_argument_group('计算任务参数')
    task_group.add_argument('--start-year', type=int, required=True, help='起始年份')
    task_group.add_argument('--end-year', type=int, help='结束年份（默认与起始年份相同）')
    task_group.add_argument('--province', required=True, help='省份名称')
    
    # 其他参数
    parser.add_argument('--log-level', default='INFO', 
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='日志级别')
    
    args = parser.parse_args()
    
    # 如果未指定结束年份，则使用起始年份
    if not args.end_year:
        args.end_year = args.start_year
    
    return args


def main() -> int:
    """
    主函数 - 解析命令行参数并执行计算任务
    """
    try:
        # 解析命令行参数
        args = parse_args()
        
        # 设置日志级别
        setup_logging(args.log_level)
        logger = logging.getLogger(__name__)
        
        logger.info("=== 区域'电-能-碳'监测分析系统 ===")
        logger.info("正在启动系统...")
        
        # 获取数据库配置
        db_config = get_db_config(args)
        logger.info(f"数据库连接: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # 初始化基础设施
        job_repository = JobRepositoryImpl(db_config)
        raw_data_repository = RawDataRepositoryImpl(db_config)
        
        # 初始化应用服务
        job_service = CalculationJobService(
            job_repository=job_repository,
            raw_data_repo=raw_data_repository
        )
        
        logger.info("系统启动成功！")
        
        # 启动计算任务
        logger.info("\n--- 启动计算任务 ---")
        start_year = args.start_year
        end_year = args.end_year
        province = args.province
        
        logger.info(f"任务参数: 年份={start_year}-{end_year}, 省份={province}")
        
        job, context = job_service.start_new_job(
            start_year=start_year, 
            end_year=end_year, 
            province=province
        )
        
        logger.info(f"任务 {job.id} 执行完成！")
        logger.info(f"任务状态: {job.status.name}")
        
        # 输出结果摘要
        if hasattr(job_service, '_print_results_summary'):
            job_service._print_results_summary(context)
        
        logger.info("\n=== 系统运行完成 ===")
        return 0
        
    except Exception as e:
        logging.error(f"系统运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
