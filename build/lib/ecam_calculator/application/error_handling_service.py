import logging
import traceback
from typing import Any, Callable, Optional, Dict, List
from functools import wraps
from enum import Enum
import time
import json


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误类别"""
    DATA_VALIDATION = "data_validation"
    DATABASE_CONNECTION = "database_connection"
    CONFIGURATION = "configuration"
    BUSINESS_LOGIC = "business_logic"
    SYSTEM_ERROR = "system_error"
    EXTERNAL_SERVICE = "external_service"


class ErrorContext:
    """错误上下文信息"""
    
    def __init__(self, 
                 operation: str,
                 user_id: Optional[str] = None,
                 job_id: Optional[str] = None,
                 additional_data: Optional[Dict[str, Any]] = None):
        self.operation = operation
        self.user_id = user_id
        self.job_id = job_id
        self.additional_data = additional_data or {}
        self.timestamp = time.time()
        self.error_id = f"ERR_{int(self.timestamp * 1000)}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'error_id': self.error_id,
            'operation': self.operation,
            'user_id': self.user_id,
            'job_id': self.job_id,
            'timestamp': self.timestamp,
            'additional_data': self.additional_data
        }


class ErrorRecord:
    """错误记录"""
    
    def __init__(self,
                 error: Exception,
                 context: ErrorContext,
                 severity: ErrorSeverity,
                 category: ErrorCategory,
                 stack_trace: str):
        self.error = error
        self.context = context
        self.severity = severity
        self.category = category
        self.stack_trace = stack_trace
        self.resolved = False
        self.resolution_notes = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'error_type': type(self.error).__name__,
            'error_message': str(self.error),
            'context': self.context.to_dict(),
            'severity': self.severity.value,
            'category': self.category.value,
            'stack_trace': self.stack_trace,
            'resolved': self.resolved,
            'resolution_notes': self.resolution_notes
        }


class ErrorHandlingService:
    """统一错误处理服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_records: List[ErrorRecord] = []
        self.error_handlers: Dict[ErrorCategory, List[Callable]] = {
            category: [] for category in ErrorCategory
        }
        self.retry_configs: Dict[str, Dict[str, Any]] = {}
        
        # 设置默认重试配置
        self._setup_default_retry_configs()
    
    def _setup_default_retry_configs(self):
        """设置默认重试配置"""
        self.retry_configs = {
            'database_connection': {
                'max_retries': 3,
                'base_delay': 1.0,
                'max_delay': 10.0,
                'backoff_factor': 2.0
            },
            'external_service': {
                'max_retries': 5,
                'base_delay': 2.0,
                'max_delay': 30.0,
                'backoff_factor': 1.5
            },
            'data_processing': {
                'max_retries': 2,
                'base_delay': 0.5,
                'max_delay': 5.0,
                'backoff_factor': 1.0
            }
        }
    
    def register_error_handler(self, category: ErrorCategory, handler: Callable):
        """注册错误处理器"""
        if category not in self.error_handlers:
            self.error_handlers[category] = []
        self.error_handlers[category].append(handler)
        self.logger.info(f"注册错误处理器: {category.value} -> {handler.__name__}")
    
    def handle_error(self, 
                    error: Exception,
                    context: ErrorContext,
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    category: ErrorCategory = ErrorCategory.SYSTEM_ERROR) -> ErrorRecord:
        """处理错误"""
        
        # 创建错误记录
        stack_trace = traceback.format_exc()
        error_record = ErrorRecord(error, context, severity, category, stack_trace)
        
        # 记录错误
        self.error_records.append(error_record)
        
        # 记录到日志
        self._log_error(error_record)
        
        # 调用注册的错误处理器
        self._call_error_handlers(error_record)
        
        return error_record
    
    def _log_error(self, error_record: ErrorRecord):
        """记录错误到日志"""
        log_message = (
            f"错误 {error_record.context.error_id}: "
            f"{error_record.category.value} - {error_record.severity.value} - "
            f"{error_record.context.operation} - {str(error_record.error)}"
        )
        
        if error_record.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        elif error_record.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message)
        elif error_record.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
        
        # 记录详细堆栈信息
        if error_record.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self.logger.debug(f"详细堆栈: {error_record.stack_trace}")
    
    def _call_error_handlers(self, error_record: ErrorRecord):
        """调用注册的错误处理器"""
        handlers = self.error_handlers.get(error_record.category, [])
        
        for handler in handlers:
            try:
                handler(error_record)
            except Exception as e:
                self.logger.error(f"错误处理器执行失败: {e}")
    
    def retry_operation(self, 
                       operation: Callable,
                       operation_name: str,
                       retry_config: Optional[Dict[str, Any]] = None,
                       context: Optional[ErrorContext] = None) -> Any:
        """重试操作"""
        
        if retry_config is None:
            retry_config = self.retry_configs.get('data_processing', {})
        
        max_retries = retry_config.get('max_retries', 2)
        base_delay = retry_config.get('base_delay', 0.5)
        max_delay = retry_config.get('max_delay', 5.0)
        backoff_factor = retry_config.get('backoff_factor', 1.0)
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                result = operation()
                if attempt > 0:
                    self.logger.info(f"操作 {operation_name} 在第 {attempt + 1} 次重试后成功")
                return result
                
            except Exception as e:
                last_exception = e
                
                if attempt < max_retries:
                    # 计算延迟时间
                    delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                    
                    # 记录重试信息
                    retry_context = context or ErrorContext(operation_name)
                    self.logger.warning(
                        f"操作 {operation_name} 失败，将在 {delay:.2f} 秒后重试 "
                        f"(尝试 {attempt + 1}/{max_retries + 1}): {str(e)}"
                    )
                    
                    # 记录错误
                    self.handle_error(
                        e, retry_context,
                        severity=ErrorSeverity.MEDIUM,
                        category=ErrorCategory.BUSINESS_LOGIC
                    )
                    
                    time.sleep(delay)
                else:
                    # 最后一次尝试失败
                    self.logger.error(f"操作 {operation_name} 在 {max_retries + 1} 次尝试后最终失败")
                    if context:
                        self.handle_error(
                            e, context,
                            severity=ErrorSeverity.HIGH,
                            category=ErrorCategory.BUSINESS_LOGIC
                        )
                    raise
        
        # 这里不应该到达，但为了类型安全
        if last_exception:
            raise last_exception
        else:
            raise RuntimeError("重试操作失败，但未捕获到具体异常")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        total_errors = len(self.error_records)
        resolved_errors = sum(1 for record in self.error_records if record.resolved)
        unresolved_errors = total_errors - resolved_errors
        
        # 按严重程度统计
        severity_counts = {}
        for severity in ErrorSeverity:
            severity_counts[severity.value] = sum(
                1 for record in self.error_records if record.severity == severity
            )
        
        # 按类别统计
        category_counts = {}
        for category in ErrorCategory:
            category_counts[category.value] = sum(
                1 for record in self.error_records if record.category == category
            )
        
        return {
            'total_errors': total_errors,
            'resolved_errors': resolved_errors,
            'unresolved_errors': unresolved_errors,
            'severity_distribution': severity_counts,
            'category_distribution': category_counts,
            'recent_errors': [
                record.to_dict() for record in self.error_records[-10:]  # 最近10个错误
            ]
        }
    
    def resolve_error(self, error_id: str, resolution_notes: str):
        """标记错误为已解决"""
        for record in self.error_records:
            if record.context.error_id == error_id:
                record.resolved = True
                record.resolution_notes = resolution_notes
                self.logger.info(f"错误 {error_id} 已标记为解决: {resolution_notes}")
                break
        else:
            self.logger.warning(f"未找到错误 {error_id}")
    
    def export_error_report(self, file_path: str):
        """导出错误报告"""
        try:
            report_data = {
                'export_timestamp': time.time(),
                'error_summary': self.get_error_summary(),
                'all_errors': [record.to_dict() for record in self.error_records]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"错误报告已导出到: {file_path}")
            
        except Exception as e:
            self.logger.error(f"导出错误报告失败: {e}")
            raise


# 装饰器：自动错误处理
def handle_errors(severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                  category: ErrorCategory = ErrorCategory.BUSINESS_LOGIC,
                  retry: bool = False,
                  retry_config: Optional[Dict[str, Any]] = None):
    """错误处理装饰器"""
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            error_service = ErrorHandlingService()
            context = ErrorContext(
                operation=func.__name__,
                additional_data={
                    'args': str(args),
                    'kwargs': str(kwargs)
                }
            )
            
            try:
                if retry:
                    return error_service.retry_operation(
                        lambda: func(*args, **kwargs),
                        func.__name__,
                        retry_config,
                        context
                    )
                else:
                    return func(*args, **kwargs)
                    
            except Exception as e:
                error_service.handle_error(e, context, severity, category)
                raise
        
        return wrapper
    return decorator


# 全局错误处理服务实例
error_handling_service = ErrorHandlingService()


def get_error_handling_service() -> ErrorHandlingService:
    """获取错误处理服务实例"""
    return error_handling_service


