import time
import logging
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
from collections import defaultdict, deque
import json
import os


@dataclass
class PerformanceMetric:
    """性能指标"""
    operation_name: str
    start_time: float
    end_time: float
    duration: float
    memory_usage: float
    cpu_usage: float
    success: bool
    error_message: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration_ms(self) -> float:
        """持续时间（毫秒）"""
        return self.duration * 1000


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_available: float
    disk_usage_percent: float
    network_io: Dict[str, float]
    process_count: int


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        self.logger = logging.getLogger(__name__)
        
        # 性能指标历史
        self.operation_metrics: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=max_history_size)
        )
        
        # 系统指标历史
        self.system_metrics: deque = deque(maxlen=max_history_size)
        
        # 监控开关
        self.monitoring_enabled = True
        self.system_monitoring_enabled = True
        
        # 性能阈值
        self.performance_thresholds = {
            'slow_operation_threshold_ms': 1000.0,  # 1秒
            'memory_warning_threshold_percent': 80.0,  # 80%
            'cpu_warning_threshold_percent': 90.0,  # 90%
        }
        
        # 性能警告回调
        self.performance_warning_callbacks: List[Callable] = []
        
        # 系统监控线程
        self._monitoring_thread = None
        self._stop_monitoring = False
        
        # 启动系统监控
        self._start_system_monitoring()
    
    def _start_system_monitoring(self):
        """启动系统监控线程"""
        if self.system_monitoring_enabled:
            self._monitoring_thread = threading.Thread(
                target=self._monitor_system_metrics,
                daemon=True
            )
            self._monitoring_thread.start()
    
    def _monitor_system_metrics(self):
        """监控系统指标"""
        while not self._stop_monitoring:
            try:
                # 收集系统指标
                system_metrics = self._collect_system_metrics()
                self.system_metrics.append(system_metrics)
                
                # 检查性能警告
                self._check_performance_warnings(system_metrics)
                
                # 每5秒收集一次
                time.sleep(5)
                
            except Exception as e:
                self.logger.error(f"系统监控失败: {e}")
                time.sleep(10)  # 出错后等待更长时间
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available / (1024 ** 3)  # GB
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_usage_percent = disk.percent
            
            # 网络IO
            network_io = psutil.net_io_counters()
            network_data = {
                'bytes_sent': float(network_io.bytes_sent),
                'bytes_recv': float(network_io.bytes_recv),
                'packets_sent': float(network_io.packets_sent),
                'packets_recv': float(network_io.packets_recv)
            }
            
            # 进程数量
            process_count = len(psutil.pids())
            
            return SystemMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_available=memory_available,
                disk_usage_percent=disk_usage_percent,
                network_io=network_data,
                process_count=process_count
            )
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            # 返回默认值
            return SystemMetrics(
                timestamp=time.time(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_available=0.0,
                disk_usage_percent=0.0,
                network_io={},
                process_count=0
            )
    
    def _check_performance_warnings(self, system_metrics: SystemMetrics):
        """检查性能警告"""
        warnings = []
        
        # 内存警告
        if system_metrics.memory_percent > self.performance_thresholds['memory_warning_threshold_percent']:
            warnings.append(f"内存使用率过高: {system_metrics.memory_percent:.1f}%")
        
        # CPU警告
        if system_metrics.cpu_percent > self.performance_thresholds['cpu_warning_threshold_percent']:
            warnings.append(f"CPU使用率过高: {system_metrics.cpu_percent:.1f}%")
        
        # 触发警告回调
        if warnings:
            for callback in self.performance_warning_callbacks:
                try:
                    callback(warnings, system_metrics)
                except Exception as e:
                    self.logger.error(f"性能警告回调执行失败: {e}")
    
    def monitor_operation(self, operation_name: str):
        """操作监控装饰器"""
        def decorator(func: Callable) -> Callable:
            def wrapper(*args, **kwargs):
                if not self.monitoring_enabled:
                    return func(*args, **kwargs)
                
                # 记录开始时间
                start_time = time.time()
                start_memory = psutil.Process().memory_info().rss / (1024 ** 2)  # MB
                start_cpu = psutil.Process().cpu_percent()
                
                try:
                    # 执行操作
                    result = func(*args, **kwargs)
                    
                    # 记录成功指标
                    self._record_operation_metrics(
                        operation_name, start_time, start_memory, start_cpu,
                        success=True
                    )
                    
                    return result
                    
                except Exception as e:
                    # 记录失败指标
                    self._record_operation_metrics(
                        operation_name, start_time, start_memory, start_cpu,
                        success=False, error_message=str(e)
                    )
                    raise
            
            return wrapper
        return decorator
    
    def _record_operation_metrics(self, 
                                 operation_name: str,
                                 start_time: float,
                                 start_memory: float,
                                 start_cpu: float,
                                 success: bool,
                                 error_message: Optional[str] = None):
        """记录操作性能指标"""
        try:
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / (1024 ** 2)  # MB
            end_cpu = psutil.Process().cpu_percent()
            
            # 计算指标
            duration = end_time - start_time
            memory_usage = end_memory - start_memory
            cpu_usage = end_cpu - start_cpu
            
            # 创建性能指标
            metric = PerformanceMetric(
                operation_name=operation_name,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                memory_usage=memory_usage,
                cpu_usage=cpu_usage,
                success=success,
                error_message=error_message
            )
            
            # 添加到历史记录
            self.operation_metrics[operation_name].append(metric)
            
            # 检查慢操作警告
            if duration * 1000 > self.performance_thresholds['slow_operation_threshold_ms']:
                self.logger.warning(
                    f"慢操作检测: {operation_name} 耗时 {duration * 1000:.2f}ms"
                )
            
            # 记录性能指标
            if success:
                self.logger.debug(
                    f"操作 {operation_name} 完成: "
                    f"耗时 {duration * 1000:.2f}ms, "
                    f"内存变化 {memory_usage:.2f}MB, "
                    f"CPU变化 {cpu_usage:.2f}%"
                )
            else:
                self.logger.error(
                    f"操作 {operation_name} 失败: "
                    f"耗时 {duration * 1000:.2f}ms, "
                    f"错误: {error_message}"
                )
                
        except Exception as e:
            self.logger.error(f"记录性能指标失败: {e}")
    
    def get_operation_performance_summary(self, operation_name: str) -> Dict[str, Any]:
        """获取操作性能摘要"""
        if operation_name not in self.operation_metrics:
            return {}
        
        metrics = list(self.operation_metrics[operation_name])
        if not metrics:
            return {}
        
        # 计算统计信息
        durations = [m.duration for m in metrics]
        memory_changes = [m.memory_usage for m in metrics]
        cpu_changes = [m.cpu_usage for m in metrics]
        success_count = sum(1 for m in metrics if m.success)
        total_count = len(metrics)
        
        return {
            'operation_name': operation_name,
            'total_executions': total_count,
            'success_rate': success_count / total_count if total_count > 0 else 0,
            'duration_stats': {
                'min_ms': min(durations) * 1000,
                'max_ms': max(durations) * 1000,
                'avg_ms': sum(durations) / len(durations) * 1000,
                'p95_ms': sorted(durations)[int(len(durations) * 0.95)] * 1000
            },
            'memory_stats': {
                'min_mb': min(memory_changes),
                'max_mb': max(memory_changes),
                'avg_mb': sum(memory_changes) / len(memory_changes)
            },
            'cpu_stats': {
                'min_percent': min(cpu_changes),
                'max_percent': max(cpu_changes),
                'avg_percent': sum(cpu_changes) / len(cpu_changes)
            },
            'recent_executions': [
                {
                    'timestamp': m.start_time,
                    'duration_ms': m.duration_ms,
                    'success': m.success,
                    'error': m.error_message
                }
                for m in metrics[-10:]  # 最近10次执行
            ]
        }
    
    def get_system_performance_summary(self) -> Dict[str, Any]:
        """获取系统性能摘要"""
        if not self.system_metrics:
            return {}
        
        metrics = list(self.system_metrics)
        
        # 计算统计信息
        cpu_percents = [m.cpu_percent for m in metrics]
        memory_percents = [m.memory_percent for m in metrics]
        disk_percents = [m.disk_usage_percent for m in metrics]
        
        return {
            'monitoring_period': {
                'start': datetime.fromtimestamp(metrics[0].timestamp).isoformat(),
                'end': datetime.fromtimestamp(metrics[-1].timestamp).isoformat(),
                'duration_minutes': (metrics[-1].timestamp - metrics[0].timestamp) / 60
            },
            'cpu_stats': {
                'current_percent': cpu_percents[-1],
                'avg_percent': sum(cpu_percents) / len(cpu_percents),
                'max_percent': max(cpu_percents),
                'min_percent': min(cpu_percents)
            },
            'memory_stats': {
                'current_percent': memory_percents[-1],
                'avg_percent': sum(memory_percents) / len(memory_percents),
                'max_percent': max(memory_percents),
                'min_percent': min(memory_percents),
                'current_available_gb': metrics[-1].memory_available
            },
            'disk_stats': {
                'current_percent': disk_percents[-1],
                'avg_percent': sum(disk_percents) / len(disk_percents),
                'max_percent': max(disk_percents),
                'min_percent': min(disk_percents)
            },
            'process_count': metrics[-1].process_count,
            'network_io': metrics[-1].network_io
        }
    
    def add_performance_warning_callback(self, callback: Callable):
        """添加性能警告回调"""
        self.performance_warning_callbacks.append(callback)
    
    def export_performance_report(self, file_path: str):
        """导出性能报告"""
        try:
            report_data = {
                'export_timestamp': time.time(),
                'export_datetime': datetime.now().isoformat(),
                'system_performance': self.get_system_performance_summary(),
                'operation_performance': {
                    name: self.get_operation_performance_summary(name)
                    for name in self.operation_metrics.keys()
                },
                'performance_thresholds': self.performance_thresholds
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"性能报告已导出到: {file_path}")
            
        except Exception as e:
            self.logger.error(f"导出性能报告失败: {e}")
            raise
    
    def stop_monitoring(self):
        """停止监控"""
        self._stop_monitoring = True
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            self._monitoring_thread.join(timeout=5)
        
        self.logger.info("性能监控已停止")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器实例"""
    return performance_monitor


# 便捷的监控装饰器
def monitor_performance(operation_name: str):
    """性能监控装饰器"""
    return performance_monitor.monitor_operation(operation_name)


