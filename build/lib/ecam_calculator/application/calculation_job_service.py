"""
重构后的计算任务服务
负责协调和驱动计算任务的整个生命周期，使用新的领域服务体系
"""

import uuid
from typing import List, Optional, Dict, Any
import logging
from dataclasses import dataclass

from ecam_calculator.domain.model.calculation_job import CalculationJob, JobStatus
from ecam_calculator.domain.model.value_objects import (
    RawData, CityEnergyMatrix, EnergyConsumptionInventory, CarbonEmissionInventory
)
from ecam_calculator.domain.service.data_standardization_service import get_data_standardization_service
from ecam_calculator.domain.service.constraint_calculation_service import get_constraint_calculation_service
from ecam_calculator.domain.service.balancing_service import get_balancing_service
from ecam_calculator.domain.service.inventory_construction_service import get_inventory_construction_service
from ecam_calculator.domain.repository.job_repository import JobRepository
from ecam_calculator.domain.repository.raw_data_repository import RawDataRepository
from ecam_calculator.infrastructure.config_reader import get_config_reader
import pandas as pd


@dataclass
class CalculationContext:
    """计算任务上下文"""
    start_year: int
    end_year: int
    province: str
    cities: List[str]
    job_id: str
    raw_data: Dict[str, Any]
    standardized_data: Dict[str, List]
    constraints: Dict[str, Any]
    city_matrices: Dict[str, CityEnergyMatrix]
    energy_inventory: Optional[EnergyConsumptionInventory] = None
    carbon_inventory: Optional[CarbonEmissionInventory] = None


class CalculationJobService:
    """
    重构后的计算任务服务。
    负责协调和驱动计算任务的整个生命周期，使用新的领域服务体系。
    """

    def __init__(
        self,
        job_repository: JobRepository,
        raw_data_repo: RawDataRepository
    ):
        self.job_repository = job_repository
        self.raw_data_repo = raw_data_repo
        self.config_reader = get_config_reader()
        
        # 初始化领域服务
        self.data_standardization_service = get_data_standardization_service()
        self.constraint_calculation_service = get_constraint_calculation_service()
        self.balancing_service = get_balancing_service()
        self.inventory_construction_service = get_inventory_construction_service()
        
        # 设置日志
        self.logger = self._setup_logging()
        
        self.current_job_id = None

    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def start_new_job(self, start_year: int, end_year: int, province: str):
        """
        启动一个新的计算任务并执行所有步骤。
        """
        try:
            # 1. 创建并保存任务
            job = CalculationJob()
            self.current_job_id = job.id
            self.job_repository.save(job)
            self.logger.info(f"任务 {job.id} 已创建，状态为 {job.status.name}")

            # 2. 创建计算上下文
            context = CalculationContext(
                start_year=start_year,
                end_year=end_year,
                province=province,
                cities=self._get_cities_by_province(province),
                job_id=str(job.id),
                raw_data={},
                standardized_data={},
                constraints={},
                city_matrices={}
            )

            if not context.cities:
                raise ValueError(f"在配置文件中找不到省份 '{province}' 或其下没有城市。")

            # 3. 执行计算流程
            self._execute_calculation_pipeline(job, context)

            return job, context

        except Exception as e:
            self.logger.error(f"启动计算任务失败: {e}")
            if self.current_job_id:
                self._mark_job_failed(job, str(e))
            raise

    def _execute_calculation_pipeline(self, job: CalculationJob, context: CalculationContext):
        """执行计算流程"""
        try:
            # 步骤1: 获取原始数据
            self._step_raw_data_fetching(job, context)
            
            # 步骤2: 数据标准化
            self._step_data_standardization(job, context)
            
            # 步骤3: 约束计算
            self._step_constraint_calculation(job, context)
            
            # 步骤4: 数据平衡
            self._step_data_balancing(job, context)
            
            # 步骤5: 清单构建
            self._step_inventory_construction(job, context)
            
            # 步骤6: 完成
            self._step_completion(job, context)
            
        except Exception as e:
            self.logger.error(f"计算流程执行失败: {e}")
            self._mark_job_failed(job, str(e))
            raise

    def _step_raw_data_fetching(self, job: CalculationJob, context: CalculationContext):
        """步骤1: 获取原始数据"""
        self.logger.info("--- 步骤1: 正在获取原始数据 ---")
        
        try:
            # 获取GDP数据作为示例
            gdp_data = self.raw_data_repo.fetch_yearly_gdp(context.province, context.start_year, context.end_year)
            if gdp_data:
                # 将TabularData转换为RawData
                from ecam_calculator.domain.model.value_objects import RawData
                raw_gdp_data = RawData(
                    table_name='fct_y_gdp',
                    data=gdp_data.data,
                    metadata={'source': 'GDP数据'},
                    source_name='年度GDP'
                )
                context.raw_data['gdp'] = raw_gdp_data
            
            job.advance_state(JobStatus.RAW_DATA_FETCHED)
            self.job_repository.save(job)
            self.logger.info(f"任务 {job.id} 状态推进到 {job.status.name}")
            
        except Exception as e:
            self.logger.error(f"获取原始数据失败: {e}")
            raise

    def _step_data_standardization(self, job: CalculationJob, context: CalculationContext):
        """步骤2: 数据标准化"""
        self.logger.info("--- 步骤2: 正在执行数据标准化 ---")
        
        try:
            # 标准化GDP数据
            if 'gdp' in context.raw_data:
                standardized_gdp = self.data_standardization_service.standardize_economic_data(
                    context.raw_data['gdp']
                )
                context.standardized_data['gdp'] = standardized_gdp
            
            job.advance_state(JobStatus.STANDARDIZED)
            self.job_repository.save(job)
            self.logger.info(f"任务 {job.id} 状态推进到 {job.status.name}")
            
        except Exception as e:
            self.logger.error(f"数据标准化失败: {e}")
            raise

    def _step_constraint_calculation(self, job: CalculationJob, context: CalculationContext):
        """步骤3: 约束计算"""
        self.logger.info("--- 步骤3: 正在执行约束计算 ---")
        
        try:
            # 计算GDP约束
            if 'gdp' in context.standardized_data:
                constraints = self.constraint_calculation_service.calculate_constraints(
                    context.standardized_data['gdp'],
                    context.start_year,
                    context.province
                )
                context.constraints['gdp'] = constraints
            
            job.advance_state(JobStatus.STANDARDIZED)
            self.job_repository.save(job)
            self.logger.info(f"任务 {job.id} 状态推进到 {job.status.name}")
            
        except Exception as e:
            self.logger.error(f"约束计算失败: {e}")
            raise

    def _step_data_balancing(self, job: CalculationJob, context: CalculationContext):
        """步骤4: 数据平衡"""
        self.logger.info("--- 步骤4: 正在执行数据平衡 ---")
        
        try:
            # 这里应该实现IPF平衡算法
            # 暂时跳过，直接创建示例矩阵
            cities = context.cities
            industries = ['工业', '建筑业', '服务业']
            
            # 创建示例矩阵数据
            matrix_data = pd.DataFrame(
                index=cities,
                columns=industries,
                data=[[100, 50, 80] for _ in cities]  # 示例数据
            )
            
            city_matrix = CityEnergyMatrix(
                year=context.start_year,
                province=context.province,
                cities=cities,
                industries=industries,
                energy_types=['电力'],
                matrix_data=matrix_data,
                allocation_method='示例分配',
                source_data={'method': '示例'},
                validation_status='valid'
            )
            
            context.city_matrices[str(context.start_year)] = city_matrix
            
            job.advance_state(JobStatus.INVENTORY_CONSTRUCTING)
            self.job_repository.save(job)
            self.logger.info(f"任务 {job.id} 状态推进到 {job.status.name}")
            
        except Exception as e:
            self.logger.error(f"数据平衡失败: {e}")
            raise

    def _step_inventory_construction(self, job: CalculationJob, context: CalculationContext):
        """步骤5: 清单构建"""
        self.logger.info("--- 步骤5: 正在执行清单构建 ---")
        
        try:
            # 构建能源消费清单
            if context.city_matrices:
                city_matrix = list(context.city_matrices.values())[0]  # 使用第一年的矩阵
                energy_inventory = self.inventory_construction_service.construct_energy_consumption_inventory(
                    city_matrix,
                    []  # 暂时传入空列表
                )
                context.energy_inventory = energy_inventory
                
                # 构建碳排放清单
                carbon_inventory = self.inventory_construction_service.construct_carbon_emission_inventory(
                    energy_inventory,
                    []  # 暂时传入空列表
                )
                context.carbon_inventory = carbon_inventory
            
            job.advance_state(JobStatus.INVENTORY_CONSTRUCTED)
            self.job_repository.save(job)
            self.logger.info(f"任务 {job.id} 状态推进到 {job.status.name}")
            
        except Exception as e:
            self.logger.error(f"清单构建失败: {e}")
            raise

    def _step_completion(self, job: CalculationJob, context: CalculationContext):
        """步骤6: 完成"""
        self.logger.info("--- 步骤6: 计算任务完成 ---")
        
        try:
            job.advance_state(JobStatus.COMPLETED)
            self.job_repository.save(job)
            self.logger.info(f"任务 {job.id} 状态推进到 {job.status.name}")
            
            # 输出结果摘要
            self._print_results_summary(context)
            
        except Exception as e:
            self.logger.error(f"任务完成处理失败: {e}")
            raise

    def _get_cities_by_province(self, province: str) -> List[str]:
        """从配置中获取指定省份的城市列表"""
        province_city_mapping = self.config_reader.get_province_city_mapping()
        
        for mapping in province_city_mapping:
            if mapping.get('province') == province:
                return mapping.get('cities', [])
        
        # 如果没有找到，返回空列表
        return []

    def _mark_job_failed(self, job: CalculationJob, error_message: str):
        """标记任务为失败状态"""
        try:
            job.fail(error_message)
            self.job_repository.save(job)
            self.logger.error(f"任务 {job.id} 标记为失败: {error_message}")
        except Exception as e:
            self.logger.error(f"标记任务失败状态时出错: {e}")

    def _print_results_summary(self, context: CalculationContext):
        """打印结果摘要"""
        self.logger.info("=" * 60)
        self.logger.info("计算任务结果摘要")
        self.logger.info("=" * 60)
        self.logger.info(f"省份: {context.province}")
        self.logger.info(f"年份范围: {context.start_year} - {context.end_year}")
        self.logger.info(f"城市数量: {len(context.cities)}")
        self.logger.info(f"标准化数据:")
        for data_type, data_list in context.standardized_data.items():
            self.logger.info(f"  - {data_type}: {len(data_list)} 条")
        self.logger.info(f"约束条件: {len(context.constraints)} 年")
        self.logger.info(f"城市矩阵: {len(context.city_matrices)} 年")
        
        if context.energy_inventory:
            self.logger.info(f"能源消费清单: 已构建")
        if context.carbon_inventory:
            self.logger.info(f"碳排放清单: 已构建")
        
        self.logger.info("=" * 60)
