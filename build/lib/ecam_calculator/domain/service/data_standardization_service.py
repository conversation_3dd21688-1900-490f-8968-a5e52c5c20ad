"""
数据标准化服务重构实现
基于配置驱动的标准化策略，消除硬编码，实现类型安全
"""

from abc import ABC, abstractmethod
from typing import Union, List, Dict, Any, Callable, Optional
from ecam_calculator.domain.model.value_objects import (
    RawData, StandardizedEnergyData, StandardizedProductData, 
    StandardizedEconomicData, StandardizedMonthlyElectricityData
)
from ecam_calculator.infrastructure.config_reader import get_config_reader
import pandas as pd
import logging


class DataStandardizationService(ABC):
    """数据标准化服务接口"""
    
    @abstractmethod
    def standardize_data(self, raw_data: RawData) -> Union[
        List[StandardizedEnergyData], 
        List[StandardizedProductData], 
        List[StandardizedEconomicData],
        List[StandardizedMonthlyElectricityData]
    ]:
        """根据表类型选择对应的标准化策略"""
        pass
    
    @abstractmethod
    def standardize_energy_data(self, raw_data: RawData) -> List[StandardizedEnergyData]:
        """标准化能源数据"""
        pass
    
    @abstractmethod
    def standardize_product_data(self, raw_data: RawData) -> List[StandardizedProductData]:
        """标准化产品数据"""
        pass
    
    @abstractmethod
    def standardize_economic_data(self, raw_data: RawData) -> List[StandardizedEconomicData]:
        """标准化经济指标数据"""
        pass
    
    @abstractmethod
    def standardize_monthly_electricity_data(self, raw_data: RawData) -> List[StandardizedMonthlyElectricityData]:
        """标准化月度用电量数据"""
        pass


class DataStandardizationServiceImpl(DataStandardizationService):
    """数据标准化服务实现"""
    
    def __init__(self):
        self.config_reader = get_config_reader()
        self.logger = logging.getLogger(__name__)
        self._standardization_strategies = self._build_strategies()
    
    def _build_strategies(self) -> Dict[str, Callable]:
        """根据配置文件构建标准化策略"""
        strategies = {}
        
        # 能源消费数据标准化策略
        energy_tables = self.config_reader.get_table_configs_by_type('energy_consumption')
        for table_config in energy_tables:
            strategies[table_config['table_name']] = self.standardize_energy_data
        
        # 工业产品数据标准化策略
        product_tables = self.config_reader.get_table_configs_by_type('industrial_product')
        for table_config in product_tables:
            strategies[table_config['table_name']] = self.standardize_product_data
        
        # 经济指标数据标准化策略
        economic_tables = self.config_reader.get_table_configs_by_type('economic_indicator')
        for table_config in economic_tables:
            strategies[table_config['table_name']] = self.standardize_economic_data
            
        # 月度用电量数据标准化策略
        electricity_tables = self.config_reader.get_table_configs_by_type('electricity')
        for table_config in electricity_tables:
            strategies[table_config['table_name']] = self.standardize_monthly_electricity_data
            
        return strategies
    
    def standardize_data(self, raw_data: RawData) -> Union[
        List[StandardizedEnergyData], 
        List[StandardizedProductData], 
        List[StandardizedEconomicData],
        List[StandardizedMonthlyElectricityData]
    ]:
        """根据表类型选择对应的标准化策略"""
        table_name = raw_data.table_name
        if table_name not in self._standardization_strategies:
            raise ValueError(f"未找到表 {table_name} 的标准化策略")
        
        strategy = self._standardization_strategies[table_name]
        return strategy(raw_data)
    
    def standardize_energy_data(self, raw_data: RawData) -> List[StandardizedEnergyData]:
        """标准化能源数据"""
        try:
            self.logger.info(f"开始标准化能源数据: {raw_data.table_name}")
            
            table_config = self.config_reader.get_table_config(raw_data.table_name)
            if not table_config:
                raise ValueError(f"未找到表 {raw_data.table_name} 的配置")
            
            # 1. 数据验证
            self._validate_energy_data(raw_data, table_config)
            
            # 2. 行业名称标准化
            standardized_df = self._standardize_industry_names(raw_data.data, table_config)
            
            # 3. 能源品种标准化
            standardized_df = self._standardize_energy_types(standardized_df, table_config)
            
            # 4. 单位换算（折标）
            standardized_df = self._convert_to_standard_units(standardized_df, table_config)
            
            # 5. 转换为值对象
            standardized_data = []
            for _, row in standardized_df.iterrows():
                try:
                    standardized_item = StandardizedEnergyData(
                        year=self._extract_year(row.get('year', '')),
                        province=self._get_province_from_city(row.get('area', '')),
                        city=row.get('area', ''),
                        source_table=raw_data.table_name,
                        original_industry=row.get('industry', ''),
                        standard_industry=row.get('standard_industry', ''),
                        energy_type=row.get('energy_type', ''),
                        original_value=self._extract_numeric_value(row.get('energy_consumption', 0.0)),
                        original_unit=row.get('unit', ''),
                        standard_value=row.get('standard_value', 0.0),
                        standard_unit='万吨标准煤',
                        conversion_factor=row.get('conversion_factor', 1.0),
                        conversion_method=row.get('conversion_method', '折标煤')
                    )
                    standardized_data.append(standardized_item)
                except Exception as e:
                    self.logger.warning(f"跳过无效行: {e}")
                    continue
            
            self.logger.info(f"能源数据标准化完成，共 {len(standardized_data)} 条记录")
            return standardized_data
            
        except Exception as e:
            self.logger.error(f"能源数据标准化失败: {e}")
            raise
    
    def standardize_product_data(self, raw_data: RawData) -> List[StandardizedProductData]:
        """标准化产品数据"""
        try:
            self.logger.info(f"开始标准化产品数据: {raw_data.table_name}")
            
            table_config = self.config_reader.get_table_config(raw_data.table_name)
            if not table_config:
                raise ValueError(f"未找到表 {raw_data.table_name} 的配置")
            
            # 1. 数据验证
            self._validate_product_data(raw_data, table_config)
            
            # 2. 产品名称到行业映射
            standardized_df = self._map_product_to_industry(raw_data.data, table_config)
            
            # 3. 转换为值对象
            standardized_data = []
            for _, row in standardized_df.iterrows():
                try:
                    standardized_item = StandardizedProductData(
                        year=self._extract_year(row.get('year', '')),
                        province=self._get_province_from_city(row.get('area', '')),
                        city=row.get('area', ''),
                        source_table=raw_data.table_name,
                        original_product=row.get('product', ''),
                        standard_industry=row.get('standard_industry', ''),
                        original_value=self._extract_numeric_value(row.get('production', 0.0)),
                        original_unit=row.get('unit', ''),
                        emission_category=row.get('emission_category', 'industrial_process')
                    )
                    standardized_data.append(standardized_item)
                except Exception as e:
                    self.logger.warning(f"跳过无效行: {e}")
                    continue
            
            self.logger.info(f"产品数据标准化完成，共 {len(standardized_data)} 条记录")
            return standardized_data
            
        except Exception as e:
            self.logger.error(f"产品数据标准化失败: {e}")
            raise
    
    def standardize_economic_data(self, raw_data: RawData) -> List[StandardizedEconomicData]:
        """标准化经济指标数据"""
        try:
            self.logger.info(f"开始标准化经济指标数据: {raw_data.table_name}")
            
            table_config = self.config_reader.get_table_config(raw_data.table_name)
            if not table_config:
                raise ValueError(f"未找到表 {raw_data.table_name} 的配置")
            
            # 1. 数据验证
            self._validate_economic_data(raw_data, table_config)
            
            # 2. 指标分类
            standardized_df = self._classify_indicators(raw_data.data, table_config)
            
            # 3. 转换为值对象
            standardized_data = []
            for _, row in standardized_df.iterrows():
                try:
                    standardized_item = StandardizedEconomicData(
                        year=self._extract_year(row.get('year', '')),
                        province=self._get_province_from_city(row.get('area', '')),
                        city=row.get('area', ''),
                        source_table=raw_data.table_name,
                        indicator=row.get('indicator', ''),
                        indicator_category=row.get('indicator_category', ''),
                        original_value=self._extract_numeric_value(row.get('record', 0.0)),
                        original_unit=row.get('unit', ''),
                        business_usage=self._determine_business_usage(row.get('indicator_category', ''))
                    )
                    standardized_data.append(standardized_item)
                except Exception as e:
                    self.logger.warning(f"跳过无效行: {e}")
                    continue
            
            self.logger.info(f"经济指标数据标准化完成，共 {len(standardized_data)} 条记录")
            return standardized_data
            
        except Exception as e:
            self.logger.error(f"经济指标数据标准化失败: {e}")
            raise
    
    def standardize_monthly_electricity_data(self, raw_data: RawData) -> List[StandardizedMonthlyElectricityData]:
        """标准化月度用电量数据"""
        try:
            self.logger.info(f"开始标准化月度用电量数据: {raw_data.table_name}")
            
            table_config = self.config_reader.get_table_config(raw_data.table_name)
            if not table_config:
                raise ValueError(f"未找到表 {raw_data.table_name} 的配置")
            
            # 1. 数据验证
            self._validate_electricity_data(raw_data, table_config)
            
            # 2. 行业名称标准化
            standardized_df = self._standardize_industry_names(raw_data.data, table_config)
            
            # 3. 转换为值对象
            standardized_data = []
            for _, row in standardized_df.iterrows():
                try:
                    year, month = self._extract_year_month(row.get('month', ''))
                    if not year or not month:
                        continue
                        
                    standardized_item = StandardizedMonthlyElectricityData(
                        year=year,
                        month=month,
                        city=row.get('area', ''),
                        source_table=raw_data.table_name,
                        original_industry=row.get('industry', ''),
                        standard_industry=row.get('standard_industry', ''),
                        electricity_consumption=self._extract_numeric_value(row.get('electricity', 0.0)),
                        unit='万千瓦时'
                    )
                    standardized_data.append(standardized_item)
                except Exception as e:
                    self.logger.warning(f"跳过无效行: {e}")
                    continue
            
            self.logger.info(f"月度用电量数据标准化完成，共 {len(standardized_data)} 条记录")
            return standardized_data
            
        except Exception as e:
            self.logger.error(f"月度用电量数据标准化失败: {e}")
            raise
    
    # 私有方法实现
    def _validate_energy_data(self, raw_data: RawData, table_config: Dict):
        """验证能源数据的完整性"""
        required_fields = table_config.get('key_fields', [])
        missing_fields = [field for field in required_fields if field not in raw_data.data.columns]
        
        if missing_fields:
            raise ValueError(f"缺少必需字段: {missing_fields}")
    
    def _validate_product_data(self, raw_data: RawData, table_config: Dict):
        """验证产品数据的完整性"""
        required_fields = table_config.get('key_fields', [])
        missing_fields = [field for field in required_fields if field not in raw_data.data.columns]
        
        if missing_fields:
            raise ValueError(f"缺少必需字段: {missing_fields}")
    
    def _validate_economic_data(self, raw_data: RawData, table_config: Dict):
        """验证经济指标数据的完整性"""
        required_fields = table_config.get('key_fields', [])
        missing_fields = [field for field in required_fields if field not in raw_data.data.columns]
        
        if missing_fields:
            raise ValueError(f"缺少必需字段: {missing_fields}")
    
    def _validate_electricity_data(self, raw_data: RawData, table_config: Dict):
        """验证用电量数据的完整性"""
        required_fields = table_config.get('key_fields', [])
        missing_fields = [field for field in required_fields if field not in raw_data.data.columns]
        
        if missing_fields:
            raise ValueError(f"缺少必需字段: {missing_fields}")
    
    def _standardize_industry_names(self, df: pd.DataFrame, table_config: Dict) -> pd.DataFrame:
        """标准化行业名称"""
        mapping_config = self.config_reader.get_raw_name_to_standard_map()
        industry_mapping = table_config.get('industry_mapping', {})
        
        # 合并配置映射和表级映射
        all_mappings = {**industry_mapping}
        for mapping in mapping_config:
            all_mappings[mapping.get('raw_name', '')] = mapping.get('standard_name', '')
        
        # 应用映射
        df_copy = df.copy()
        df_copy['standard_industry'] = df_copy['industry'].map(all_mappings).fillna(df_copy['industry'])
        
        return df_copy
    
    def _standardize_energy_types(self, df: pd.DataFrame, table_config: Dict) -> pd.DataFrame:
        """标准化能源品种"""
        energy_types = self.config_reader.get_energy_types()
        energy_mapping = {}
        
        # 构建能源品种映射
        for category, types in energy_types.items():
            for energy_type in types:
                if isinstance(energy_type, dict):
                    energy_mapping[energy_type.get('raw_name', '')] = energy_type.get('standard_name', '')
                else:
                    energy_mapping[energy_type] = energy_type
        
        # 应用映射
        df_copy = df.copy()
        if 'energy_type' in df_copy.columns:
            df_copy['energy_type'] = df_copy['energy_type'].map(energy_mapping).fillna(df_copy['energy_type'])
        
        return df_copy
    
    def _convert_to_standard_units(self, df: pd.DataFrame, table_config: Dict) -> pd.DataFrame:
        """单位换算（折标）"""
        method = table_config.get('standardization_method', '折标煤')
        df_copy = df.copy()
        
        if method == '折标煤':
            # 获取折标系数 - 暂时使用默认值，后续从配置中读取
            df_copy['conversion_factor'] = 1.0
            df_copy['standard_value'] = df_copy['original_value'] * df_copy['conversion_factor']
            df_copy['conversion_method'] = '折标煤'
        
        return df_copy
    
    def _map_product_to_industry(self, df: pd.DataFrame, table_config: Dict) -> pd.DataFrame:
        """产品名称到行业映射"""
        product_mapping = self.config_reader.get_product_to_industry_map()
        df_copy = df.copy()
        
        # 应用产品到行业映射
        df_copy['standard_industry'] = df_copy['product'].map(product_mapping).fillna('其他')
        
        # 添加排放类别
        df_copy['emission_category'] = 'industrial_process'
        
        return df_copy
    
    def _classify_indicators(self, df: pd.DataFrame, table_config: Dict) -> pd.DataFrame:
        """指标分类"""
        indicator_categories = self.config_reader.get_indicator_categories()
        df_copy = df.copy()
        
        # 根据指标名称确定分类
        def classify_indicator(indicator_name: str) -> str:
            for category, config in indicator_categories.items():
                keywords = config.get('keywords', [])
                if any(keyword in indicator_name for keyword in keywords):
                    return category
            return 'unknown'
        
        df_copy['indicator_category'] = df_copy['indicator'].apply(classify_indicator)
        
        return df_copy
    
    def _get_province_from_city(self, city: str) -> str:
        """根据城市名称从配置文件中获取对应的省份"""
        province_city_mapping = self.config_reader.get_province_city_mapping()
        
        for mapping in province_city_mapping:
            province = mapping.get('province', '')
            cities = mapping.get('cities', [])
            if city in cities:
                return str(province)
        
        # 如果没有找到映射，返回空字符串
        return ''
    
    def _extract_year(self, year_value) -> int:
        """提取年份"""
        if pd.isna(year_value):
            return 0
        
        try:
            if isinstance(year_value, str):
                # 处理 "2005" 格式
                if year_value.isdigit():
                    return int(year_value)
                # 处理 "2000-01-01" 格式
                elif '-' in year_value:
                    return int(year_value.split('-')[0])
            elif hasattr(year_value, 'year'):
                # 处理日期对象
                return year_value.year
            else:
                return int(year_value)
        except (ValueError, TypeError):
            return 0
        
        return 0
    
    def _extract_year_month(self, month_value) -> tuple:
        """提取年月"""
        if pd.isna(month_value):
            return 0, 0
        
        try:
            if isinstance(month_value, str):
                if '-' in month_value:
                    parts = month_value.split('-')
                    if len(parts) >= 2:
                        return int(parts[0]), int(parts[1])
                elif month_value.isdigit() and len(month_value) == 6:
                    # 处理 "202201" 格式
                    return int(month_value[:4]), int(month_value[4:6])
            elif hasattr(month_value, 'year') and hasattr(month_value, 'month'):
                # 处理日期对象
                return month_value.year, month_value.month
        except (ValueError, TypeError):
            pass
        
        return 0, 0
    
    def _extract_numeric_value(self, value) -> float:
        """提取数值"""
        if pd.isna(value):
            return 0.0
        
        try:
            if isinstance(value, str):
                # 移除可能的单位和其他非数字字符
                cleaned = ''.join(c for c in value if c.isdigit() or c in '.-')
                return float(cleaned) if cleaned else 0.0
            else:
                return float(value)
        except (ValueError, TypeError):
            return 0.0
    
    def _determine_business_usage(self, indicator_category: str) -> str:
        """确定业务用途"""
        if indicator_category.lower() == 'gdp':
            return '约束计算'
        elif indicator_category.lower() == 'energy_intensity':
            return '总量约束'
        else:
            return '其他用途'


# 工厂函数
def get_data_standardization_service() -> DataStandardizationService:
    """获取数据标准化服务实例"""
    return DataStandardizationServiceImpl()
