"""
清单构建服务
负责构建能源消费清单和碳排放清单
"""

from typing import List, Optional
from ecam_calculator.domain.model.value_objects import (
    StandardizedMonthlyElectricityData,
    StandardizedProductData,
    EnergyConsumptionInventory,
    CarbonEmissionInventory,
    CityEnergyMatrix
)
from ecam_calculator.infrastructure.config_reader import get_config_reader
import pandas as pd
import logging


class InventoryConstructionServiceImpl:
    """清单构建服务实现"""
    
    def __init__(self):
        self.config_reader = get_config_reader()
        self.logger = logging.getLogger(__name__)
    
    def construct_energy_consumption_inventory(
        self, 
        city_matrix: CityEnergyMatrix,
        energy_data: List[StandardizedMonthlyElectricityData]
    ) -> EnergyConsumptionInventory:
        """构建能源消费清单"""
        try:
            self.logger.info("开始构建能源消费清单...")
            
            # 从城市矩阵中提取能源消费数据
            energy_consumption_data = []
            
            # 遍历矩阵中的每个城市和行业
            for city in city_matrix.cities:
                for industry in city_matrix.industries:
                    # 获取该城市该行业的能源消费量
                    consumption = city_matrix.get_energy_consumption(city, industry)
                    if consumption > 0:
                        energy_consumption_data.append({
                            'city': city,
                            'industry': industry,
                            'energy_consumption': consumption,
                            'unit': '万千瓦时'
                        })
            
            # 创建能源消费清单
            inventory = EnergyConsumptionInventory(
                year=city_matrix.year,
                province=city_matrix.province,
                energy_consumption_data=energy_consumption_data
            )
            
            self.logger.info(f"能源消费清单构建完成，包含 {len(energy_consumption_data)} 条记录")
            return inventory
            
        except Exception as e:
            self.logger.error(f"构建能源消费清单失败: {e}")
            raise
    
    def construct_carbon_emission_inventory(
        self,
        energy_inventory: EnergyConsumptionInventory,
        product_data: List[StandardizedProductData]
    ) -> CarbonEmissionInventory:
        """构建碳排放清单"""
        try:
            self.logger.info("开始构建碳排放清单...")
            
            # 获取碳排放因子配置
            emission_factors = self.config_reader.get_industrial_process_emission_factors()
            
            carbon_emission_data = []
            
            # 处理工业过程碳排放（基于产品数据）
            for product in product_data:
                if product.original_product in emission_factors:
                    emission_factor = emission_factors[product.original_product]
                    carbon_emission = product.original_value * emission_factor
                    
                    carbon_emission_data.append({
                        'city': product.city,
                        'industry': product.standard_industry,
                        'product': product.original_product,
                        'production_volume': product.original_value,
                        'emission_factor': emission_factor,
                        'carbon_emission': carbon_emission,
                        'emission_type': 'industrial_process',
                        'unit': '吨CO2'
                    })
            
            # 处理能源燃烧碳排放（基于能源消费数据）
            # 这里需要能源的碳排放因子，暂时使用默认值
            energy_emission_factor = 0.0006  # 万千瓦时转换为吨CO2的默认因子
            
            for energy_item in energy_inventory.energy_consumption_data:
                carbon_emission = energy_item['energy_consumption'] * energy_emission_factor
                
                carbon_emission_data.append({
                    'city': energy_item['city'],
                    'industry': energy_item['industry'],
                    'product': '电力',
                    'production_volume': energy_item['energy_consumption'],
                    'emission_factor': energy_emission_factor,
                    'carbon_emission': carbon_emission,
                    'emission_type': 'energy_combustion',
                    'unit': '吨CO2'
                })
            
            # 创建碳排放清单
            inventory = CarbonEmissionInventory(
                year=energy_inventory.year,
                province=energy_inventory.province,
                carbon_emission_data=carbon_emission_data
            )
            
            self.logger.info(f"碳排放清单构建完成，包含 {len(carbon_emission_data)} 条记录")
            return inventory
            
        except Exception as e:
            self.logger.error(f"构建碳排放清单失败: {e}")
            raise


# 工厂函数
def get_inventory_construction_service() -> InventoryConstructionServiceImpl:
    """获取清单构建服务实例"""
    return InventoryConstructionServiceImpl()
