from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import pandas as pd

from ecam_calculator.domain.model.value_objects import (
    StandardizedEconomicData,
    ConstraintData
)
from ecam_calculator.infrastructure.config_reader import get_config_reader


class ConstraintCalculationService(ABC):
    """
    约束计算服务接口。
    主要职责是根据标准化后的经济指标数据，计算出用于IPF平衡算法的行约束和列约束。
    """
    
    @abstractmethod
    def calculate_constraints(
        self, 
        economic_data: List[StandardizedEconomicData],
        year: int,
        province: str
    ) -> ConstraintData:
        """
        计算指定年份和省份的约束条件。
        
        Args:
            economic_data: 标准化后的经济指标数据列表
            year: 统计年份
            province: 省份名称
            
        Returns:
            包含行约束和列约束的ConstraintData对象
        """
        pass
    
    @abstractmethod
    def calculate_row_constraints(
        self, 
        economic_data: List[StandardizedEconomicData],
        year: int,
        province: str
    ) -> pd.Series:
        """
        计算行约束（省级行业总量）。
        
        Args:
            economic_data: 标准化后的经济指标数据列表
            year: 统计年份
            province: 省份名称
            
        Returns:
            包含各行业约束值的Series
        """
        pass
    
    @abstractmethod
    def calculate_column_constraints(
        self, 
        economic_data: List[StandardizedEconomicData],
        year: int,
        province: str
    ) -> pd.Series:
        """
        计算列约束（城市总能耗）。
        
        Args:
            economic_data: 标准化后的经济指标数据列表
            year: 统计年份
            province: 省份名称
            
        Returns:
            包含各城市约束值的Series
        """
        pass


class ConstraintCalculationServiceImpl(ConstraintCalculationService):
    """约束计算服务实现"""
    
    def __init__(self):
        self.config_reader = get_config_reader()
    
    def calculate_constraints(
        self, 
        economic_data: List[StandardizedEconomicData],
        year: int,
        province: str
    ) -> ConstraintData:
        """计算指定年份和省份的约束条件"""
        
        # 计算行约束（省级行业总量）
        row_constraints = self.calculate_row_constraints(economic_data, year, province)
        
        # 计算列约束（城市总能耗）
        column_constraints = self.calculate_column_constraints(economic_data, year, province)
        
        # 创建约束条件值对象
        constraint_data = ConstraintData(
            year=year,
            row_constraints=row_constraints,
            column_constraints=column_constraints,
            constraint_source='GDP和能耗强度',
            calculation_method='IPF约束计算'
        )
        
        return constraint_data
    
    def calculate_row_constraints(
        self, 
        economic_data: List[StandardizedEconomicData],
        year: int,
        province: str
    ) -> pd.Series:
        """计算行约束（省级行业总量）"""
        
        # 过滤指定年份和省份的数据
        filtered_data = [
            data for data in economic_data 
            if data.year == year and data.province == province
        ]
        
        # 按指标类别分组
        gdp_data = {}
        for data in filtered_data:
            if data.indicator_category.lower() == 'gdp':
                # 提取行业信息（从指标名称中）
                industry = self._extract_industry_from_indicator(data.indicator)
                if industry:
                    gdp_data[industry] = data.original_value
        
        # 转换为Series
        row_constraints = pd.Series(gdp_data)
        
        return row_constraints
    
    def calculate_column_constraints(
        self, 
        economic_data: List[StandardizedEconomicData],
        year: int,
        province: str
    ) -> pd.Series:
        """计算列约束（城市总能耗）"""
        
        # 过滤指定年份和省份的数据
        filtered_data = [
            data for data in economic_data 
            if data.year == year and data.province == province
        ]
        
        # 按城市分组，计算能耗强度约束
        city_constraints = {}
        for data in filtered_data:
            if data.indicator_category.lower() == 'energy_intensity':
                city = data.city
                if city:
                    # 基于能耗强度计算城市总能耗约束
                    # 这里需要结合GDP数据来计算
                    gdp_value = self._get_city_gdp(economic_data, year, city)
                    if gdp_value > 0:
                        # 能耗强度 × GDP = 总能耗
                        total_energy = data.original_value * gdp_value
                        city_constraints[city] = total_energy
        
        # 转换为Series
        column_constraints = pd.Series(city_constraints)
        
        return column_constraints
    
    def _extract_industry_from_indicator(self, indicator: str) -> Optional[str]:
        """从指标名称中提取行业信息"""
        
        # 定义指标到行业的映射规则
        indicator_industry_map = {
            '地区生产总值（亿元）': '总计',
            '第一产业增加值（亿元）': '农林牧漁业',
            '第二产业增加值（亿元）': '工业',
            '第三产业增加值（亿元）': '服务业',
            '工业增加值（亿元）': '工业',
            '建筑业增加值（亿元）': '建筑业'
        }
        
        return indicator_industry_map.get(indicator)
    
    def _get_city_gdp(self, economic_data: List[StandardizedEconomicData], year: int, city: str) -> float:
        """获取指定城市和年份的GDP值"""
        
        for data in economic_data:
            if (data.year == year and 
                data.city == city and 
                data.indicator == '地区生产总值（亿元）'):
                return data.original_value
        
        return 0.0
    
    def calculate_city_total_consumption(
        self, 
        gdp_series: pd.DataFrame,
        intensity_series: pd.DataFrame
    ) -> pd.DataFrame:
        """
        根据GDP序列和单位GDP能耗序列，计算出各个城市在某时间段内的能源消费总量。
        这是后续宏观调平中的一个关键列约束。

        Args:
            gdp_series: 包含GDP基准数据的DataFrame。
            intensity_series: 包含单位GDP能耗数据的DataFrame。

        Returns:
            一个包含年份、地区和计算出的总能耗的DataFrame。
        """
        # 保持向后兼容的方法
        # 这个方法将在后续重构中逐步替换
        
        # 合并GDP和能耗强度数据
        merged_data = pd.merge(
            gdp_series, 
            intensity_series, 
            on=['year', 'area'], 
            how='inner'
        )
        
        # 计算总能耗：GDP × 能耗强度
        if 'gdp_value' in merged_data.columns and 'intensity_value' in merged_data.columns:
            merged_data['total_consumption'] = (
                merged_data['gdp_value'] * merged_data['intensity_value']
            )
        elif 'record' in merged_data.columns:
            # 使用record字段作为GDP值
            merged_data['total_consumption'] = (
                pd.to_numeric(merged_data['record'], errors='coerce') * 
                pd.to_numeric(merged_data.get('intensity_value', 1), errors='coerce')
            )
        
        # 选择需要的列
        result_columns = ['year', 'area', 'total_consumption']
        available_columns = [col for col in result_columns if col in merged_data.columns]
        
        return merged_data[available_columns]


# 工厂函数
def get_constraint_calculation_service() -> ConstraintCalculationService:
    """获取约束计算服务实例"""
    return ConstraintCalculationServiceImpl()