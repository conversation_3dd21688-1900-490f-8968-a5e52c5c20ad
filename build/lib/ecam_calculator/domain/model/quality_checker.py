from ecam_calculator.domain.model.value_objects import TabularData, QualityCheckedData

class QualityChecker:
    """
    一个无状态的流程步骤聚合，负责检查原始数据的质量。
    """

    def check(self, raw_data: TabularData) -> QualityCheckedData:
        """
        对输入数据执行数据质量检查。
        
        在真实的实现中，这里会包含一组可配置的规则。
        目前，我们只执行一个简单的检查。
        """
        print(f"正在对来自源 '{raw_data.source_name}' 的数据进行质量检查...")

        if raw_data.is_empty():
            # 在真实场景中，这里可能会抛出一个业务异常
            print("警告：原始数据为空，直接通过。")

        # 在这里，你可以实现更具体的规则，例如：
        # - 检查关键列中的空值
        # - 检查数值是否在有效范围内
        # - 检查数据的一致性

        print("质量检查通过。")
        return QualityCheckedData(
            source_name=raw_data.source_name,
            data=raw_data.data
        )
