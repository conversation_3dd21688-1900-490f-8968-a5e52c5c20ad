from typing import Dict, Any, List
import pandas as pd
import yaml

from ecam_calculator.domain.model.value_objects import QualityCheckedData, StandardizedData


class DataStandardizer:
    """
    一个无状态的流程步骤聚合，负责将多个来源的、质量检查过的数据，
    进行清洗、映射、合并，最终输出一份统一的、标准化的数据集。
    """

    def __init__(self, config_path: str = "config/parameters.yaml"):
        """
        初始化DataStandardizer，加载配置文件并创建映射字典。
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            print(f"成功加载配置文件: {config_path}")
            # 创建一个从关键字到标准行业名称的快速查找字典
            self.keyword_to_standard_map = self._create_keyword_map()
            # 加载省市映射关系
            self.province_city_map = self._create_province_city_map()
            # 从映射配置中提取能源行业的特定关键字，用于特殊处理（例如取绝对值）
            self.energy_sector_keywords = self._get_energy_sector_keywords()
            # 新增：加载子行业到宏观行业的映射
            self.sub_to_macro_map = self.config.get('sub_to_macro_industry_map', {})
        except FileNotFoundError:
            print(f"警告: 配置文件 {config_path} 未找到，将使用空映射。")
            self.config = {}
            self.keyword_to_standard_map = {}
            self.province_city_map = {}
            self.energy_sector_keywords = []
            self.sub_to_macro_map = {}

    def _normalize_macro_dataframe(self, df: pd.DataFrame, source_name: str) -> pd.DataFrame:
        """
        一个简化的标准化流程，专门用于处理没有复杂行业维度的宏观经济数据（如GDP、能耗强度）。
        """
        print(f"正在为宏观数据 '{source_name}' 执行简化标准化...")
        
        # 1. 确保核心列存在
        if 'year' not in df.columns or 'area' not in df.columns or 'value' not in df.columns:
            print(f"警告: 宏观数据源 '{source_name}' 缺少核心列 (year, area, value)，无法处理。")
            return pd.DataFrame()

        # 2. 确保数据类型正确
        df['year'] = df['year'].apply(self._to_year_period)
        df['value'] = pd.to_numeric(df['value'], errors='coerce')
        df.dropna(subset=['year', 'value'], inplace=True)
        
        # 3. 只保留必要的列
        columns_to_keep = ['year', 'area', 'value']
        if 'indicator' in df.columns:
            columns_to_keep.append('indicator')
            
        final_df = df[columns_to_keep].copy()
        
        # 修正：为地市用电量数据手动添加'macro_industry'，以满足后续矩阵构建的需求
        if source_name == '年度地市用电量':
            final_df['macro_industry'] = '总计'
            final_df['energy_type'] = '电力' # 用电量数据必然是电力
            
        final_df['source_table'] = source_name
        
        print(f"宏观数据 '{source_name}' 处理完成，生成 {len(final_df)} 条记录。")
        return final_df


    def _get_energy_sector_keywords(self) -> List[str]:
        """从配置中提取“能源行业”对应的所有关键字。"""
        if 'raw_name_to_standard_map' in self.config:
            for rule in self.config['raw_name_to_standard_map']:
                if rule.get('standard_name') == '能源行业':
                    return rule.get('keywords', [])
        return []

    def _create_province_city_map(self) -> Dict[str, List[str]]:
        """
        将配置文件中的省市映射关系，转换为一个用于快速查找的字典: 
        {'省名': ['市名1', '市名2', ...], ...}
        """
        mapping = {}
        if 'province_city_mapping' in self.config:
            for item in self.config['province_city_mapping']:
                province = item.get('province')
                cities = item.get('cities', [])
                if province:
                    mapping[province] = cities
        return mapping

    def get_cities_by_province(self, province: str) -> List[str]:
        """
        根据省份名称，获取其包含的所有城市列表。
        """
        return self.province_city_map.get(province, [])

    def _create_keyword_map(self) -> Dict[str, str]:
        """
        将配置文件中的列表式、关键字映射规则，转换为一个扁平的、
        用于快速查找的字典: {'关键字': '标准行业名', ...}
        """
        mapping = {}
        if 'raw_name_to_standard_map' in self.config:
            for rule in self.config['raw_name_to_standard_map']:
                standard_name = rule.get('standard_name')
                for keyword in rule.get('keywords', []):
                    mapping[keyword] = standard_name
        print("创建的关键字到标准行业映射字典已生成。")
        return mapping

    def _map_industry_name(self, raw_name: str) -> str:
        """
        根据关键字映射字典，将一个原始的行业名称映射到标准名称。
        采用首次匹配原则。
        """
        if not isinstance(raw_name, str):
            return "未分类"
            
        # 优化：优先检查是否包含“工业”或“建筑业”等大类，快速返回
        if raw_name in self.config.get('standard_industry_structure', []):
             return raw_name

        for keyword, standard_name in self.keyword_to_standard_map.items():
            if keyword in raw_name:
                return standard_name
        
        return None # 修正：对于任何不匹配项，返回None而不是"未分类"

    def _normalize_and_aggregate_dataframe(self, df: pd.DataFrame, source_name: str, conversion_factors: pd.DataFrame = None) -> pd.DataFrame:
        """
        核心的标准化与聚合方法，处理具有复杂行业维度的数据。
        """
        if df.empty:
            return pd.DataFrame()

        initial_rows = len(df)

        # 1. 精确创建 'standard_industry' 列
        # Bug Fix: 对于“能源因子”，它与能源品种相关，不应进行行业映射。
        if source_name != '能源因子':
            # 核心逻辑：根据数据源的不同，采用不同的列进行标准化映射
            if source_name == '年度工业产品产量' and 'product_name' in df.columns:
                df['standard_industry'] = df['product_name'].apply(self._map_industry_name)
            elif 'item' in df.columns:
                df['standard_industry'] = df['item'].apply(self._map_industry_name)
            elif 'industry' in df.columns:
                df['standard_industry'] = df['industry'].apply(self._map_industry_name)
            
            # 2. 对“能源行业”的原始条目（加工转换投入）的数值取其相反数
            if 'item' in df.columns and self.energy_sector_keywords:
                energy_mask = df['item'].isin(self.energy_sector_keywords)
                # 修正：不是取绝对值，而是取其相反数（负值），以正确反映扣减
                df.loc[energy_mask, 'value'] = -df.loc[energy_mask, 'value']

        # 3. 关键步骤：上卷到宏观行业 (sub-industry -> macro-industry)
        if 'standard_industry' in df.columns:
            df['macro_industry'] = df['standard_industry'].map(self.sub_to_macro_map)

        # 4. 清理：仅对需要行业映射的数据源执行此操作
        if source_name != '能源因子':
            df.dropna(subset=['standard_industry', 'macro_industry'], inplace=True)
            print(f"标准化和映射后，保留了 {len(df)}/{initial_rows} 行数据。")
        
        if df.empty:
            print("警告：在映射和上卷聚合后，所有行都被删除，没有剩余数据。")
            return pd.DataFrame()

        # 5. 应用折标煤系数（如果提供了）
        #    注意：只对包含'energy_type'列的数据进行此操作
        if conversion_factors is not None and not conversion_factors.empty and 'energy_type' in df.columns:
            df = pd.merge(df, conversion_factors, on='energy_type', how='left', suffixes=('', '_factor'))
            
            if 'factor' in df.columns:
                df['factor'].fillna(1.0, inplace=True)
                df['value'] = pd.to_numeric(df['value'], errors='coerce')
                df['factor'] = pd.to_numeric(df['factor'], errors='coerce')
                df['standard_value'] = df['value'] * df['factor']
            else:
                # 如果合并后没有因子列，说明没有匹配上，直接使用原值
                df['standard_value'] = pd.to_numeric(df['value'], errors='coerce')
        else:
            # 如果没有提供折标系数或数据没有能源类型，则认为其本身就是标准值
            df['standard_value'] = pd.to_numeric(df['value'], errors='coerce')


        # 6. 按标准维度进行最终聚合
        #    修正：同时保留 standard_industry 和 macro_industry，以满足下游不同模块的需求
        group_by_cols = ['year', 'area', 'standard_industry', 'macro_industry']
        
        # 能源因子数据没有行业维度，且可能没有energy_type，需要特殊处理
        if source_name == '能源因子':
            # 关键修正：将因子数据列名 ('factor', 'factor_value') 与消费数据列名 ('item', 'value') 对齐
            df.rename(columns={'factor': 'item'}, inplace=True)
            
            # 确保只有一个value列，并处理可能的重复情况
            if 'factor_value' in df.columns and 'value' not in df.columns:
                df.rename(columns={'factor_value': 'value'}, inplace=True)
            elif 'factor_value' in df.columns and 'value' in df.columns:
                # 如果两者都存在，优先使用 factor_value，并删除原始 value
                df['value'] = df['factor_value']
                df.drop(columns=['factor_value'], inplace=True)

            df['year'] = df['year'].apply(self._to_year_period)
            
            # 仅保留后续计算所需的列
            cols_to_keep = ['year', 'area', 'item', 'value', 'source_table']
            # 在选择列之前，确保不存在重复的列
            df = df.loc[:,~df.columns.duplicated()]
            df = df[cols_to_keep]

            print(f"数据源 '{source_name}' 处理完成。")
            print("聚合后的数据预览 (已标准化):")
            print(df.head())
            print()
            return df

        # 对于能源数据，我们需要按能源类型进行聚合
        if 'energy_type' in df.columns:
            group_by_cols.append('energy_type')
        agg_df = df.groupby(group_by_cols, as_index=False)['standard_value'].sum()


        agg_df['source_table'] = source_name
        
        # 统一出口前再次确保year类型正确
        if 'year' in agg_df.columns:
            agg_df['year'] = agg_df['year'].apply(self._to_year_period)
            
        return agg_df

    def standardize(self, checked_data_list: List[QualityCheckedData], conversion_factors: pd.DataFrame = None) -> StandardizedData:
        """
        将经过质量检查的原始数据列表转换为标准化格式。
        它能识别不同类型的数据源，并应用不同的标准化流程。
        """
        print("--- 开始数据标准化与聚合流程 ---")
        all_processed_dfs = []
        macro_economic_sources = [
            '年度GDP', 
            '年度能耗强度'
        ] # 定义哪些数据源走简化流程

        for checked_data in checked_data_list:
            source_name = checked_data.source_name
            df = checked_data.data
            
            print(f"\n正在处理数据源: '{source_name}'...")

            if source_name in macro_economic_sources:
                # 走简化的宏观数据处理流程
                processed_df = self._normalize_macro_dataframe(df, source_name)
            else:
                # 走复杂的能源行业数据处理流程
                processed_df = self._normalize_and_aggregate_dataframe(df, source_name, conversion_factors)
            
            if processed_df is not None and not processed_df.empty:
                print(f"数据源 '{source_name}' 处理完成。")
                print("聚合后的数据预览:")
                print(processed_df.head())
                all_processed_dfs.append(processed_df)
            else:
                print(f"警告：数据源 '{source_name}' 处理后为空，已被丢弃。")

        print("\n正在合并所有已标准化的数据...")
        if not all_processed_dfs:
            print("警告: 没有可合并的数据。")
            final_df = pd.DataFrame()
        else:
            final_df = pd.concat(all_processed_dfs, ignore_index=True)
        
        print("所有数据源合并完成。")
        print("--- 数据标准化与聚合流程结束 ---")

        return StandardizedData(source_name='merged_standardized_data', data=final_df)

    def _to_year_period(self, year_value):
        """
        一个健壮的函数，将不同格式的年份值转换为一个代表全年的 pandas.Period 对象。
        - 纯数字（2021, 2021.0）直接转为年度 Period。
        - 日期时间对象或字符串（'2021-01-01'）提取年份并转为年度 Period。
        """
        if pd.isna(year_value):
            return pd.NaT
        try:
            # 优先尝试作为数字处理
            year_int = int(float(year_value))
            # 'A-DEC' 表示年度，且年度结束于12月
            return pd.Period(year=year_int, freq='A-DEC')
        except (ValueError, TypeError):
            # 如果失败，则尝试作为日期时间格式处理
            try:
                dt = pd.to_datetime(year_value)
                return pd.Period(year=dt.year, freq='A-DEC')
            except (ValueError, TypeError):
                return pd.NaT

    def _to_integer_year(self, year_value):
        """
        一个健壮的函数，将不同格式的年份值转换为整数。
        - 纯数字（2021, 2021.0）直接转为整数。
        - 日期时间对象或字符串（'2021-01-01'）提取年份。
        """
        if pd.isna(year_value):
            return None
        # 如果是数字，直接转换为整数
        if isinstance(year_value, (int, float)):
            return int(year_value)
        # 尝试作为数字字符串处理
        try:
            return int(float(year_value))
        except (ValueError, TypeError):
            # 如果失败，则尝试作为日期时间格式处理
            try:
                return pd.to_datetime(year_value).year
            except (ValueError, TypeError):
                return None
