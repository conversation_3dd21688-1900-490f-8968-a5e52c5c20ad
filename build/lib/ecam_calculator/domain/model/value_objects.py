from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional
import pandas as pd

@dataclass(frozen=True)
class RawData:
    """原始数据值对象 - 直接从数据库读取"""
    table_name: str                    # 数据库表名
    data: pd.DataFrame                 # 原始数据
    metadata: Dict[str, Any]          # 表结构元数据
    source_name: str                   # 数据源名称

@dataclass(frozen=True)
class StandardizedEnergyData:
    """标准化能源数据值对象 - 能源消费专用"""
    year: int                          # 年份
    province: str                      # 省份
    city: str                          # 城市
    source_table: str                  # 来源表名
    original_industry: str             # 原始行业名称
    standard_industry: str             # 标准化后的行业名称
    energy_type: str                   # 能源品种
    original_value: float              # 原始数值
    original_unit: str                 # 原始单位
    standard_value: float              # 标准值（折标后）
    standard_unit: str                 # 标准单位（万吨标准煤）
    conversion_factor: float           # 折标系数
    conversion_method: str             # 折标方法

@dataclass(frozen=True)
class StandardizedProductData:
    """标准化工业产品数据值对象 - 工业产品专用"""
    year: int                          # 年份
    province: str                      # 省份
    city: str                          # 城市
    source_table: str                  # 来源表名
    original_product: str              # 原始产品名称
    standard_industry: str             # 标准化后的行业名称
    original_value: float              # 原始产量
    original_unit: str                 # 原始单位
    emission_category: str             # 排放类别

@dataclass(frozen=True)
class StandardizedEconomicData:
    """标准化经济指标数据值对象 - GDP和能耗强度专用"""
    year: int                          # 年份
    province: str                      # 省份
    city: str                          # 城市
    source_table: str                  # 来源表名
    indicator: str                     # 指标名称
    indicator_category: str            # 指标类别（GDP/能耗强度）
    original_value: float              # 原始数值
    original_unit: str                 # 原始单位
    business_usage: str                # 业务用途

@dataclass(frozen=True)
class StandardizedMonthlyElectricityData:
    """标准化月度用电量数据值对象 - 地市用电量专用"""
    year: int                          # 年份
    month: int                         # 月份
    city: str                          # 城市
    source_table: str                  # 来源表名
    original_industry: str             # 原始行业名称
    standard_industry: str             # 标准化后的行业名称
    electricity_consumption: float     # 用电量
    unit: str                          # 计量单位

@dataclass
class CityEnergyMatrix:
    """城市能源矩阵值对象 - 数据下沉后的中间结果"""
    year: int                          # 年份
    province: str                      # 省份
    cities: List[str]                  # 城市列表
    industries: List[str]              # 行业列表
    energy_types: List[str]            # 能源品种列表
    matrix_data: pd.DataFrame          # 城市-行业-能源品种矩阵
    allocation_method: str             # 分配方法（如用电比例）
    source_data: Dict[str, Any]       # 源数据信息
    validation_status: str             # 验证状态
    
    def get_energy_consumption(self, city: str, industry: str) -> float:
        """获取指定城市和行业的能源消费量"""
        try:
            value = self.matrix_data.loc[city, industry]
            if pd.isna(value):
                return 0.0
            # 安全地转换为float
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                return float(value) if value.replace('.', '').replace('-', '').isdigit() else 0.0
            else:
                return 0.0
        except (KeyError, IndexError, ValueError):
            return 0.0

@dataclass(frozen=True)
class ConstraintData:
    """约束条件值对象 - 用于IPF平衡的约束数据"""
    year: int                          # 年份
    row_constraints: pd.Series         # 行约束（省级行业总量）
    column_constraints: pd.Series      # 列约束（城市总能耗）
    constraint_source: str             # 约束来源
    calculation_method: str            # 计算方法

@dataclass(frozen=True)
class DataQualityReport:
    """数据质量报告值对象 - 记录数据处理过程中的质量信息"""
    processing_stage: str              # 处理阶段
    completeness_score: float          # 完整性得分
    consistency_score: float           # 一致性得分
    accuracy_score: float              # 准确性得分
    issues_found: List[str]            # 发现的问题
    recommendations: List[str]         # 改进建议

@dataclass
class EnergyConsumptionInventory:
    """能源消费清单值对象 - 最终输出"""
    year: int                          # 年份
    province: str                      # 省份
    energy_consumption_data: List[Dict[str, Any]]  # 能源消费数据

@dataclass
class CarbonEmissionInventory:
    """碳排放清单值对象 - 最终输出"""
    year: int                          # 年份
    province: str                      # 省份
    carbon_emission_data: List[Dict[str, Any]]  # 碳排放数据

# 保留原有的基础值对象，用于向后兼容
@dataclass(frozen=True)
class TabularData:
    """
    代表从数据源获取的原始数据，通常是类似表格的格式。
    内部使用 pandas DataFrame，因为它功能强大且灵活。
    """
    source_name: str
    data: pd.DataFrame

    def is_empty(self) -> bool:
        return self.data.empty

@dataclass(frozen=True)
class QualityCheckedData:
    """
    代表已通过初步质量检查的数据。
    它是一个不可变的数据包，为下一个流程步骤做好了准备。
    """
    source_name: str
    data: pd.DataFrame
    # 这里可以添加关于执行了哪些检查的元数据等。
    checked_at: pd.Timestamp = field(default_factory=pd.Timestamp.utcnow)
