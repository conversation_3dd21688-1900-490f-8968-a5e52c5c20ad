from typing import Tuple, Dict, List, Optional
import pandas as pd
import numpy as np
from ecam_calculator.domain.model.value_objects import (
    StandardizedData, 
    EnergyConsumptionInventory, 
    CarbonEmissionInventory
)
from ecam_calculator.domain.service.balancing_service import BalancingService
from ecam_calculator.domain.service.constraint_calculation_service import ConstraintCalculationService
import yaml
from ipfn import ipfn


class InventoryConstructor:
    """
    一个无状态的流程步骤聚合，负责编排整个清单的构造流程，包括数据准备、计算和最终的平衡对齐。
    """
    def __init__(
        self,
        balancing_service: BalancingService,
        constraint_service: ConstraintCalculationService,
    ):
        self.balancing_service = balancing_service
        self.constraint_service = constraint_service
        
        # 紧急修复: 从配置文件加载sub_to_macro_map
        try:
            with open("config/parameters.yaml", 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            self.sub_to_macro_map = config.get('sub_to_macro_industry_map', {})
            self.process_emission_factors = config.get('industrial_process_emission_factors', {})
        except FileNotFoundError:
            print("警告: 配置文件 config/parameters.yaml 未找到，sub_to_macro_map 将为空。")
            self.sub_to_macro_map = {}
            self.process_emission_factors = {}
        
    def construct(self, standardized_data: StandardizedData, province: str) -> Tuple[EnergyConsumptionInventory, CarbonEmissionInventory]:
        """
        按年份循环，执行"约束-分配-对齐"的流程。
        """
        print("\n--- 步骤4: 正在构造清单 (按年循环) ---")
        
        # 提前计算所有年份的城市总能耗，作为列约束
        gdp_df = standardized_data.data[standardized_data.data['source_table'] == '年度GDP']
        intensity_df = standardized_data.data[standardized_data.data['source_table'] == '年度能耗强度']
        all_years_city_consumption = self.constraint_service.calculate_city_total_consumption(gdp_df, intensity_df)

        # 获取所有需要处理的年份
        years_to_process = sorted(all_years_city_consumption['year'].unique())
        all_inventories = []

        for year_period in years_to_process:
            year = year_period.year # 提取整数年份用于筛选和计算
            print(f"\n--- 开始处理年份: {year} ---")
            
            # 3. 筛选当年度数据
            df_year = standardized_data.data[standardized_data.data['year'] == year_period]
            
            # 4. 拆分当年度数据子集
            provincial_energy_df = df_year[df_year['source_table'] == '年度省级分行业分品种能源消费量'].copy()
            city_electricity_df = df_year[df_year['source_table'] == '年度地市用电量'].copy()

            # 5. 从预计算结果中获取当年度的列约束
            city_total_consumption = all_years_city_consumption[all_years_city_consumption['year'] == year_period].copy()

            # 6. 构建当年度初步清单矩阵 M1 (数据下沉)
            preliminary_matrix_m1 = self.balancing_service.build_preliminary_macro_matrix(provincial_energy_df, city_electricity_df)
            if preliminary_matrix_m1.empty:
                print(f"警告: 年份 {year} 无法构建初步矩阵，跳过该年份。")
                continue

            # 根据业务逻辑，对能源行业的产出值（负值）进行二次取负，使其变为正值参与平衡计算
            # 增加判断，仅对负值进行操作，以兼容源数据中可能存在的“正产出值”的脏数据
            if '能源行业' in preliminary_matrix_m1.index:
                print("正在根据业务规则处理'能源行业'的产出值为正... ")
                # 选择 '能源行业' 行中所有小于0的值，然后将它们乘以 -1
                energy_sector = preliminary_matrix_m1.loc['能源行业']
                preliminary_matrix_m1.loc['能源行业'] = energy_sector.where(energy_sector >= 0, -energy_sector)


            # 确保矩阵中没有0或负值，这会导致IPF算法失败
            preliminary_matrix_m1[preliminary_matrix_m1 <= 0] = 1e-6


            # 7. 计算当年度行约束 (省级行业总量)
            provincial_industry_consumption = preliminary_matrix_m1.sum(axis=1)
        
            # 关键对齐步骤：确保列约束的地区与M1矩阵的列完全对齐
            m1_cities = preliminary_matrix_m1.columns.tolist()
            city_total_consumption = city_total_consumption.set_index('area')
            aligned_column_constraints = city_total_consumption.reindex(m1_cities)['total_energy_consumption'].fillna(0)
        
            # --- 总量校准 (Raking) ---
            row_sum = provincial_industry_consumption.sum()
            col_sum = aligned_column_constraints.sum()
        
            if col_sum > 0: # 避免除以零
                scaling_factor = row_sum / col_sum
                print(f"行约束总和: {row_sum}, 列约束总和: {col_sum}, 计算出的缩放因子: {scaling_factor}")
                calibrated_column_constraints = aligned_column_constraints * scaling_factor
                print(f"校准后的列约束总和: {calibrated_column_constraints.sum()}")
            else:
                print("警告：列约束总和为0，无法进行校准。")
                calibrated_column_constraints = aligned_column_constraints


            # 8. 执行第一次宏观调平
            print(f"--- 正在为年份 {year} 执行第一次宏观调平 ---")
            balanced_matrix_m1 = self.balancing_service.balance_matrix(
                matrix_to_balance=preliminary_matrix_m1,
                row_constraints=provincial_industry_consumption,
                column_constraints=calibrated_column_constraints
            )

            if balanced_matrix_m1 is None:
                print(f"警告: 年份 {year} 调平失败，跳过该年份。")
                continue
            
            # 9. 格式化并保存当年度结果
            balanced_df = pd.DataFrame(balanced_matrix_m1, index=preliminary_matrix_m1.index, columns=preliminary_matrix_m1.columns)
            balanced_df = balanced_df.stack().reset_index()
            balanced_df.columns = ['standard_industry', 'area', 'total_standard_value']
            balanced_df['year'] = year

            # --- 最终业务逻辑：清单应同时包含行业总量和分品种明细 ---
            
            # 1. 整理行业总量数据
            total_inventory_df = balanced_df.rename(columns={'total_standard_value': 'standard_value'})
            total_inventory_df['energy_type'] = '总计'
            total_inventory_df = total_inventory_df[['year', 'area', 'standard_industry', 'energy_type', 'standard_value']]

            print(f"正在为年份 {year} 按能源品种拆分已平衡的行业能耗...")
            
            # 2. 获取当年的省级能耗数据，并计算各行业的能源结构（mix_ratio）
            provincial_energy_df = standardized_data.get_data_by_year_and_area(year, province)
            provincial_energy_df = provincial_energy_df[
                provincial_energy_df['energy_type'].notna() & 
                (provincial_energy_df['source_table'] != '能源因子')
            ].copy()

            if not provincial_energy_df.empty:
                industry_totals = provincial_energy_df.groupby('standard_industry')['standard_value'].sum().replace(0, 1) # 避免除以零
                industry_energy_type_totals = provincial_energy_df.groupby(['standard_industry', 'energy_type'])['standard_value'].sum()
                energy_mix_df = (industry_energy_type_totals / industry_totals).reset_index(name='mix_ratio')
                
                # 3. 将能源结构应用到已平衡的地市行业数据上，生成分品种明细
                detailed_df = pd.merge(balanced_df, energy_mix_df, on='standard_industry', how='left')
                detailed_df['mix_ratio'].fillna(0, inplace=True)
                detailed_df['standard_value'] = detailed_df['total_standard_value'] * detailed_df['mix_ratio']
                detailed_inventory_df = detailed_df[['year', 'area', 'standard_industry', 'energy_type', 'standard_value']]
                
                # 4. 合并总量和明细
                year_inventory_df = pd.concat([total_inventory_df, detailed_inventory_df], ignore_index=True)
                all_inventories.append(year_inventory_df)
            else:
                print(f"警告：年份 {year} 缺少省级能源数据，无法进行能源品种拆分。仅保留总量数据。")
                all_inventories.append(total_inventory_df)

        if not all_inventories:
            print("警告: 未能为任何年份生成能源清单。")
            empty_inventory = EnergyConsumptionInventory(pd.DataFrame(columns=['year', 'area', 'standard_industry', 'macro_industry', 'energy_type', 'standard_value']))
            empty_carbon_inventory = self._construct_carbon_inventory(empty_inventory, standardized_data)
            return empty_inventory, empty_carbon_inventory

        # 将所有年份的清单数据合并成一个DataFrame
        inventory_df = pd.concat(all_inventories, ignore_index=True)
        inventory_df['macro_industry'] = inventory_df['standard_industry'].map(self.sub_to_macro_map)
        
        # 最终清单对象
        energy_inventory = EnergyConsumptionInventory(inventory_data=inventory_df)
        
        # --- 新增步骤：构造碳排放清单 ---
        print("\n--- 步骤5: 正在构造碳排放清单 ---")
        carbon_inventory = self._construct_carbon_inventory(energy_inventory, standardized_data)
        
        return (
            energy_inventory, 
            carbon_inventory
        )

    def _construct_carbon_inventory(
        self, 
        energy_inventory: EnergyConsumptionInventory, 
        standardized_data: StandardizedData
    ) -> CarbonEmissionInventory:
        """
        基于能源消费清单和原始数据，构造碳排放清单。
        """
        all_carbon_dfs = []
        std_df = standardized_data.data
        inventory_df = energy_inventory.inventory_data

        # 确保 'year' 列是可合并的整数类型
        std_df['year'] = std_df['year'].apply(lambda p: p.year if isinstance(p, pd.Period) else p)

        # 1. 计算化石能源燃烧产生的碳排放
        
        # 关键修正：碳排放计算必须基于最终平衡后的能源清单，且只使用分品种数据
        energy_consumption_df = inventory_df[inventory_df['energy_type'] != '总计'].copy()
        
        factors_df = std_df[std_df['source_table'] == '能源因子'].copy()
        
        # 关键修正：确保用于合并的因子表的年份也是整数
        if not factors_df.empty:
            factors_df['year'] = factors_df['year'].apply(lambda p: p.year if isinstance(p, pd.Period) else p)

        energy_emission_df = pd.DataFrame() # 初始化为空
        if not energy_consumption_df.empty and not factors_df.empty:
            
            # --- 全新逻辑：根据业务规则，区分处理化石能源因子和电力因子 ---
            
            # 1. 分离因子
            electricity_factors_df = factors_df[factors_df['energy_type'] == '电力'].copy()
            fossil_factors_df = factors_df[factors_df['energy_type'] != '电力'].copy()
            
            # 2. 处理电力因子（年特定）：应用向前填充
            if not electricity_factors_df.empty:
                all_years = energy_consumption_df['year'].unique()
                latest_factor_year = electricity_factors_df['year'].max()
                
                if latest_factor_year < all_years.max():
                    print(f"警告：电力因子数据只到 {latest_factor_year} 年，将使用最新因子填充未来年份。")
                    latest_factors = electricity_factors_df[electricity_factors_df['year'] == latest_factor_year]
                    
                    new_factors = []
                    for year_to_fill in all_years:
                        if year_to_fill > latest_factor_year:
                            fill_df = latest_factors.copy()
                            fill_df['year'] = year_to_fill
                            new_factors.append(fill_df)
                    
                    if new_factors:
                        electricity_factors_df = pd.concat([electricity_factors_df] + new_factors, ignore_index=True)

            # 3. 处理化石能源因子（恒定）：移除年份，保留唯一值
            if not fossil_factors_df.empty:
                print("信息：正在为化石能源使用不区分年份的恒定排放因子。")
                fossil_factors_df = fossil_factors_df.drop(columns=['year'])
                fossil_factors_df.drop_duplicates(subset=['energy_type'], keep='first', inplace=True)

            # 4. 分离能源消费数据
            electricity_consumption_df = energy_consumption_df[energy_consumption_df['energy_type'] == '电力'].copy()
            fossil_consumption_df = energy_consumption_df[energy_consumption_df['energy_type'] != '电力'].copy()

            # 5. 分别合并
            merged_dfs = []
            
            if not electricity_consumption_df.empty and not electricity_factors_df.empty:
                electricity_factors_df.rename(columns={'value': 'co2_factor'}, inplace=True)
                merged_electricity = pd.merge(
                    electricity_consumption_df,
                    electricity_factors_df[['year', 'energy_type', 'co2_factor']],
                    on=['year', 'energy_type'],
                    how='left'
                )
                merged_dfs.append(merged_electricity)

            if not fossil_consumption_df.empty and not fossil_factors_df.empty:
                fossil_factors_df.rename(columns={'value': 'co2_factor'}, inplace=True)
                merged_fossil = pd.merge(
                    fossil_consumption_df,
                    fossil_factors_df[['energy_type', 'co2_factor']],
                    on='energy_type',
                    how='left'
                )
                merged_dfs.append(merged_fossil)

            if merged_dfs:
                # 6. 合并结果并计算
                merged_energy_df = pd.concat(merged_dfs, ignore_index=True)
                merged_energy_df['co2_factor'].fillna(0, inplace=True)
                
                # 关键业务逻辑修正: 确保使用 'standard_value' (折标量) 而非 'value' (原始量)
                # 'standard_value' 是在标准化流程中计算得出的
                merged_energy_df['carbon_emission'] = merged_energy_df['standard_value'] * merged_energy_df['co2_factor']
                
                energy_emission_df = merged_energy_df.dropna(subset=['carbon_emission'])
                energy_emission_df = energy_emission_df[energy_emission_df['carbon_emission'] > 0]
                if not energy_emission_df.empty:
                    energy_emission_df = energy_emission_df[[
                        'year', 'area', 'standard_industry', 'macro_industry', 'carbon_emission'
                    ]]
                    energy_emission_df['emission_source'] = '能源燃烧'

        print(f"成功计算出 {len(energy_emission_df)} 条能源燃烧碳排放记录。")

        # 2. 计算工业生产过程产生的碳排放
        # 修正：工业过程的排放计算也应该基于平衡后的数据，但此处我们简化为使用原始标准化数据
        industrial_production_df = std_df[std_df['source_table'] == '年度工业产品产量'].copy()
        if not industrial_production_df.empty and self.process_emission_factors:
            industrial_production_df['process_factor'] = industrial_production_df['standard_industry'].map(self.process_emission_factors)
            industrial_production_df['process_factor'].fillna(0, inplace=True)
            
            # 计算排放量 (value * process_factor)
            industrial_production_df['carbon_emission'] = industrial_production_df['value'] * industrial_production_df['process_factor']
            
            # 整理结果
            process_emission_df = industrial_production_df[[
                'year', 'area', 'standard_industry', 'macro_industry', 'carbon_emission'
            ]].copy()
            process_emission_df.dropna(subset=['carbon_emission'], inplace=True)
            process_emission_df['source'] = '工业过程'
            all_carbon_dfs.append(process_emission_df)
            print(f"成功计算出 {len(process_emission_df)} 条工业过程碳排放记录。")

        # 3. 合并所有碳排放数据
        if not all_carbon_dfs:
            print("警告：未能计算出任何碳排放数据。")
            return CarbonEmissionInventory(inventory_data=pd.DataFrame())
            
        final_carbon_df = pd.concat(all_carbon_dfs, ignore_index=True)
        
        # 按所有维度进行聚合，加总来自不同源的排放
        final_carbon_df = final_carbon_df.groupby(
            ['year', 'area', 'standard_industry', 'macro_industry', 'source'], 
            as_index=False
        )['carbon_emission'].sum()
        
        print(f"碳排放清单构造完成，共计 {len(final_carbon_df)} 条记录。")
        return CarbonEmissionInventory(inventory_data=final_carbon_df)
