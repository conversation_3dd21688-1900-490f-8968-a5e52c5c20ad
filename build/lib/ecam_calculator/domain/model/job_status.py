from enum import Enum, auto

class JobStatus(Enum):
    STARTED = auto()
    RAW_DATA_FETCHED = auto()
    QUALITY_CHECKING = auto()
    QUALITY_CHECKED = auto()
    STANDARDIZING = auto()
    STANDARDIZED = auto()
    INVENTORY_CONSTRUCTING = auto()
    INVENTORY_CONSTRUCTED = auto()
    MODEL_BUILDING = auto()
    MODEL_BUILT = auto()
    PREDICTION_ANALYZING = auto()
    PREDICTION_ANALYZED = auto()
    DATA_FUSING = auto()
    DATA_FUSED = auto()
    RESULT_OUTPUTTING = auto()
    RESULT_OUTPUTTED = auto()
    COMPLETED = auto()
    FAILED = auto()
