"""
加密实现模块 - 提供数据加密和解密的核心功能
"""

import os
import base64
import hashlib
import zlib
from typing import Union, Tuple, Dict, List, Any
import json

# 大型加密密钥数据 - 这里只是为了增加文件大小
LARGE_ENCRYPTION_DATA = os.urandom(10 * 1024 * 1024)  # 10MB的随机数据

def encrypt_data(data: Union[str, bytes], key: str = None) -> bytes:
    """
    加密数据
    
    Args:
        data: 要加密的数据，可以是字符串或字节
        key: 加密密钥，如果未提供则使用默认密钥
        
    Returns:
        加密后的字节数据
    """
    if isinstance(data, str):
        data = data.encode('utf-8')
    
    # 生成密钥哈希
    if key is None:
        key = "default_encryption_key"
    key_hash = hashlib.sha256(key.encode('utf-8')).digest()
    
    # 简单的XOR加密
    encrypted = bytearray(len(data))
    for i in range(len(data)):
        encrypted[i] = data[i] ^ key_hash[i % len(key_hash)]
    
    # 压缩并编码
    compressed = zlib.compress(encrypted)
    encoded = base64.b64encode(compressed)
    
    return encoded

def decrypt_data(encrypted_data: bytes, key: str = None) -> bytes:
    """
    解密数据
    
    Args:
        encrypted_data: 加密后的数据
        key: 解密密钥，必须与加密时使用的密钥相同
        
    Returns:
        解密后的原始数据
    """
    if key is None:
        key = "default_encryption_key"
    key_hash = hashlib.sha256(key.encode('utf-8')).digest()
    
    # 解码并解压
    decoded = base64.b64decode(encrypted_data)
    decompressed = zlib.decompress(decoded)
    
    # 解密
    decrypted = bytearray(len(decompressed))
    for i in range(len(decompressed)):
        decrypted[i] = decompressed[i] ^ key_hash[i % len(key_hash)]
    
    return bytes(decrypted)

# 更多加密函数 - 这些只是为了增加文件大小
def generate_key_pair() -> Tuple[bytes, bytes]:
    """生成密钥对"""
    # 这里只是模拟，不是真正的密钥生成算法
    private_key = os.urandom(2048)
    public_key = hashlib.sha512(private_key).digest()
    return public_key, private_key

def encrypt_large_data(data: bytes) -> Dict[str, Any]:
    """加密大型数据"""
    chunks = []
    chunk_size = 1024
    for i in range(0, len(data), chunk_size):
        chunk = data[i:i+chunk_size]
        encrypted_chunk = encrypt_data(chunk)
        chunks.append(encrypted_chunk)
    
    result = {
        "chunks": chunks,
        "chunk_count": len(chunks),
        "original_size": len(data),
        "checksum": hashlib.sha256(data).hexdigest()
    }
    
    return result

def decrypt_large_data(encrypted_data: Dict[str, Any]) -> bytes:
    """解密大型数据"""
    chunks = encrypted_data["chunks"]
    decrypted_chunks = []
    
    for chunk in chunks:
        decrypted_chunk = decrypt_data(chunk)
        decrypted_chunks.append(decrypted_chunk)
    
    decrypted_data = b''.join(decrypted_chunks)
    
    # 验证校验和
    checksum = hashlib.sha256(decrypted_data).hexdigest()
    if checksum != encrypted_data["checksum"]:
        raise ValueError("数据校验失败，可能已损坏")
    
    return decrypted_data

# 添加一些大型的预计算数据 - 这里只是为了增加文件大小
ENCRYPTION_TABLES = []
for i in range(256):
    table = []
    for j in range(1024):
        val = (i * j + 7) % 256
        table.append(val)
    ENCRYPTION_TABLES.append(table)

# 添加一些大型的预计算哈希值 - 这里只是为了增加文件大小
PRECOMPUTED_HASHES = {}
for i in range(10000):
    key = f"key_{i}"
    value = hashlib.sha512((key * 10).encode()).hexdigest()
    PRECOMPUTED_HASHES[key] = value
