"""
配置文件读取器
用于读取和解析业务参数配置文件
"""

import yaml
import os
from typing import Dict, List, Any, Optional
from pathlib import Path


class ConfigReader:
    """配置文件读取器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置读取器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        if config_path is None:
            # 获取当前文件所在目录的上级目录中的config目录
            current_dir = Path(__file__).parent
            config_path = current_dir.parent.parent / "config" / "parameters.yaml"
        
        self.config_path = Path(config_path)
        self._config: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
            print(f"成功加载配置文件: {self.config_path}")
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._config = {}
    
    def reload_config(self):
        """重新加载配置文件"""
        self._load_config()
    
    def get_table_config(self, table_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定表的配置
        
        Args:
            table_name: 表名
            
        Returns:
            表配置字典，如果不存在则返回None
        """
        table_configs = self._config.get('table_configs', {})
        return table_configs.get(table_name)
    
    def get_energy_types(self) -> Dict[str, List[Dict[str, str]]]:
        """
        获取能源品种配置
        
        Returns:
            能源品种配置字典
        """
        return self._config.get('energy_types', {})
    
    def get_indicator_categories(self) -> Dict[str, Dict[str, Any]]:
        """
        获取指标分类配置
        
        Returns:
            指标分类配置字典
        """
        return self._config.get('indicator_categories', {})
    
    def get_raw_name_to_standard_map(self) -> List[Dict[str, str]]:
        """
        获取原始名称到标准名称的映射
        
        Returns:
            映射配置列表
        """
        return self._config.get('raw_name_to_standard_map', [])
    
    def get_sub_to_macro_industry_map(self) -> Dict[str, str]:
        """
        获取子行业到宏观行业的映射
        
        Returns:
            映射配置字典
        """
        return self._config.get('sub_to_macro_industry_map', {})
    
    def get_product_to_industry_map(self) -> Dict[str, str]:
        """
        获取产品到行业的映射
        
        Returns:
            映射配置字典
        """
        return self._config.get('product_to_industry_map', {})
    
    def get_province_city_mapping(self) -> List[Dict[str, List[str]]]:
        """
        获取省份城市映射
        
        Returns:
            省份城市映射配置列表
        """
        return self._config.get('province_city_mapping', [])
    
    def get_industrial_process_emission_factors(self) -> Dict[str, float]:
        """
        获取工业过程排放因子
        
        Returns:
            排放因子配置字典
        """
        return self._config.get('industrial_process_emission_factors', {})
    
    def get_standard_industry_structure(self) -> List[str]:
        """
        获取标准行业结构
        
        Returns:
            标准行业结构列表
        """
        return self._config.get('standard_industry_structure', [])
    
    def get_energy_types_by_category(self, category: str) -> List[str]:
        """
        根据类别获取能源品种名称列表
        
        Args:
            category: 能源类别（如 'fossil_fuels', 'petroleum_products' 等）
            
        Returns:
            能源品种名称列表
        """
        energy_types = self.get_energy_types()
        category_data = energy_types.get(category, [])
        return [item['name'] for item in category_data]
    
    def get_all_energy_type_names(self) -> List[str]:
        """
        获取所有能源品种名称
        
        Returns:
            所有能源品种名称列表
        """
        energy_types = self.get_energy_types()
        all_names = []
        for category_data in energy_types.values():
            all_names.extend([item['name'] for item in category_data])
        return all_names
    
    def get_indicators_by_category(self, category: str) -> List[str]:
        """
        根据类别获取指标名称列表
        
        Args:
            category: 指标类别（如 'GDP', '能耗强度'）
            
        Returns:
            指标名称列表
        """
        indicator_categories = self.get_indicator_categories()
        category_data = indicator_categories.get(category, {})
        return category_data.get('indicators_to_extract', [])
    
    def get_table_configs_by_type(self, table_type: str) -> List[Dict[str, Any]]:
        """
        根据表类型获取表配置列表
        
        Args:
            table_type: 表类型（如 'energy_consumption', 'industrial_product' 等）
            
        Returns:
            表配置列表
        """
        table_configs = self._config.get('table_configs', {})
        return [
            {'table_name': name, **config}
            for name, config in table_configs.items()
            if config.get('table_type') == table_type
        ]
    
    def get_standardization_method(self, table_name: str) -> Optional[str]:
        """
        获取指定表的标准化方法
        
        Args:
            table_name: 表名
            
        Returns:
            标准化方法，如果不存在则返回None
        """
        table_config = self.get_table_config(table_name)
        return table_config.get('standardization_method') if table_config else None
    
    def requires_standardization(self, table_name: str) -> bool:
        """
        检查指定表是否需要标准化
        
        Args:
            table_name: 表名
            
        Returns:
            是否需要标准化
        """
        table_config = self.get_table_config(table_name)
        return table_config.get('requires_standardization', False) if table_config else False
    
    def get_key_fields(self, table_name: str) -> List[str]:
        """
        获取指定表的关键字段
        
        Args:
            table_name: 表名
            
        Returns:
            关键字段列表
        """
        table_config = self.get_table_config(table_name)
        return table_config.get('key_fields', []) if table_config else []
    
    def get_source_name(self, table_name: str) -> Optional[str]:
        """
        获取指定表的源名称
        
        Args:
            table_name: 表名
            
        Returns:
            源名称，如果不存在则返回None
        """
        table_config = self.get_table_config(table_name)
        return table_config.get('source_name') if table_config else None


# 全局配置读取器实例
config_reader = ConfigReader()


def get_config_reader() -> ConfigReader:
    """
    获取全局配置读取器实例
    
    Returns:
        配置读取器实例
    """
    return config_reader
