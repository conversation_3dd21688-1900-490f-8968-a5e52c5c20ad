import uuid
from typing import Dict, List, Optional

from ecam_calculator.domain.model.calculation_job import CalculationJob
from ecam_calculator.domain.repository.job_repository import JobRepository


class MemoryCalculationJobRepository(JobRepository):
    """
    JobRepository 的内存实现，用于测试和开发目的。
    """
    _jobs: Dict[uuid.UUID, CalculationJob] = {}

    def __init__(self):
        self._jobs: Dict[uuid.UUID, CalculationJob] = {}

    def save(self, job: CalculationJob) -> None:
        print(f"任务 {job.id} 已保存")
        self._jobs[job.id] = job

    def find_by_id(self, job_id: uuid.UUID) -> Optional[CalculationJob]:
        print(f"查找任务 {job_id}")
        return self._jobs.get(job_id)

    def find_all(self) -> List[CalculationJob]:
        print("查询所有任务")
        return list(self._jobs.values())
