from datetime import date
import pandas as pd
import mysql.connector
from typing import Optional

from ecam_calculator.domain.repository.raw_data_repository import RawDataRepository
from ecam_calculator.domain.model.value_objects import TabularData

class RawDataRepositoryImpl(RawDataRepository):
    """
    RawDataRepository的实现。
    从MySQL数据库获取数据。
    """
    
    def __init__(self, db_config: dict):
        self.connection_params = {
            'host': db_config['host'],
            'port': int(db_config['port']),
            'database': db_config['database'],
            'user': db_config['user'],
            'password': db_config['password']
        }
    
    def _get_connection(self):
        """获取数据库连接"""
        try:
            # 添加字符集配置以确保中文字符正确显示
            connection_params = self.connection_params.copy()
            connection_params.update({
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_general_ci',
                'use_unicode': True
            })
            return mysql.connector.connect(**connection_params)
        except mysql.connector.Error as e:
            print(f"数据库连接失败: {e}")
            return None
    
    def fetch_emission_factors(self, year: int, province: str) -> TabularData:
        """获取能源排放因子"""
        print(f"正在获取{year}年{province}的能源排放因子...")
        
        conn = self._get_connection()
        if not conn:
            return TabularData(source_name="能源因子", data=pd.DataFrame())
        
        try:
            # 修正：根据用户最新的业务逻辑输入，我们必须精确地只选择
            # “标煤折碳排放因子”，因为我们的计算是基于已经折算为标准煤的能源清单。
            query = """
            SELECT 
                year, source, province as area, factor, method, 
                industry, energy_type, unit, value 
            FROM ecam_in_energy_factor 
            WHERE year <= %s AND province = %s
            AND method = '标煤折碳排放因子'
            ORDER BY year DESC
            """
            df = pd.read_sql(query, conn, params=[f"{year}-12-31", province])
            
            # 如果没有找到特定省份的数据，则使用全国数据
            if df.empty:
                # 并将 province 列别名为 area
                query = """
                SELECT 
                    year, source, province as area, factor, method, 
                    industry, energy_type, unit, value 
                FROM ecam_in_energy_factor 
                WHERE year <= %s AND province = '全国'
                AND method = '标煤折碳排放因子'
                ORDER BY year DESC
                """
                df = pd.read_sql(query, conn, params=[f"{year}-12-31"])
            
            # 确保数据类型正确
            if not df.empty:
                df['source_table'] = '能源因子'
                # 转换value列为数值类型
                if 'value' in df.columns:
                    df['value'] = pd.to_numeric(df['value'], errors='coerce')
                
                # 添加factor_value列
                if 'value' in df.columns and 'factor_value' not in df.columns:
                    df['factor_value'] = df['value']
            
            print(f"获取到{len(df)}条能源因子记录")
            return TabularData(source_name="能源因子", data=df)
        except Exception as e:
            print(f"获取能源因子失败: {e}")
            return TabularData(source_name="能源因子", data=pd.DataFrame())
        finally:
            conn.close()
    
    def fetch_standard_coal_conversion_factors(self, year: int, province: str) -> TabularData:
        """获取所有能源品种的折标准煤系数"""
        print(f"正在获取{year}年{province}的能源折标系数...")
        
        conn = self._get_connection()
        if not conn:
            return TabularData(source_name="折标系数", data=pd.DataFrame())
        
        try:
            # 优先获取省份的，如果没有则获取全国的
            query = """
            SELECT energy_type, value 
            FROM ecam_in_energy_factor 
            WHERE year <= %s AND province = %s AND factor = '折标系数'
            ORDER BY year DESC
            """
            df = pd.read_sql(query, conn, params=[f"{year}-12-31", province])
            
            if df.empty:
                print(f"未找到省份 '{province}' 的折标系数，将使用'全国'数据。")
                df = pd.read_sql(query, conn, params=[f"{year}-12-31", '全国'])
            
            # 去重，确保每个能源类型只有一个最新的系数值
            df = df.drop_duplicates(subset=['energy_type'], keep='first')
            
            print(f"获取到{len(df)}条能源折标系数记录")
            return TabularData(source_name="折标系数", data=df)
        except Exception as e:
            print(f"获取能源折标系数失败: {e}")
            return TabularData(source_name="折标系数", data=pd.DataFrame())
        finally:
            conn.close()
            
    def fetch_monthly_industry_electricity(self, province: str, start_date: date, end_date: date) -> TabularData:
        """获取月度分行业用电量"""
        print(f"正在获取{province}从{start_date}到{end_date}的月度分行业用电量...")
        
        conn = self._get_connection()
        if not conn:
            return TabularData(source_name="月度分行业用电量", data=pd.DataFrame())
        
        try:
            query = """
            SELECT * FROM ecam_in_m_pro_ind_ele_off 
            WHERE area = %s AND year BETWEEN %s AND %s
            ORDER BY year, month
            """
            df = pd.read_sql(query, conn, params=[province, start_date.year, end_date.year])
            print(f"获取到{len(df)}条月度用电量记录")
            return TabularData(source_name="月度分行业用电量", data=df)
        except Exception as e:
            print(f"获取月度用电量失败: {e}")
            return TabularData(source_name="月度分行业用电量", data=pd.DataFrame())
        finally:
            conn.close()
    
    def fetch_yearly_provincial_energy_consumption(self, province: str, start_year: int, end_year: int) -> TabularData:
        """获取年度省级分行业分品种能源消费量 (ecam_in_y_pro_ind_ene_off)"""
        print(f"正在获取 {province} 从 {start_year} 到 {end_year} 的年度省级能源消费量...")
        
        conn = self._get_connection()
        if not conn:
            return TabularData(source_name="年度省级分行业分品种能源消费量", data=pd.DataFrame())
        
        try:
            # 修正: 查询正确的省级能源消费表 ecam_in_y_pro_ind_ene_off
            # 并将 province 列别名为 area，以统一数据结构
            query = """
            SELECT 
                YEAR(year) as year, 
                province as area, 
                item, 
                `convert`, 
                method, 
                energy_type, 
                value, 
                unit 
            FROM ecam_in_y_pro_ind_ene_off 
            WHERE province = %s AND YEAR(year) BETWEEN %s AND %s
            AND item IN (
                -- 终端消费
                '1.农、林、牧、渔业', '2.工业', '3.建筑业', 
                '4.交通运输、仓储和邮政业', '5.批发和零售业、住宿和餐饮业', 
                '6.其他', '7.居民生活',
                -- 加工转换投入 (作为能源行业自身消耗)
                '1.火力发电', '2.供热', '3.煤炭洗选', '4.炼焦', '5.炼油及煤制油',
                '6.制气', '7.天然气液化', '8.煤制品加工', '9.回收能'
            )
            ORDER BY year
            """
            df = pd.read_sql(query, conn, params=[province, start_year, end_year])
            print(f"获取到 {len(df)} 条省级终端消费与加工转换记录")
            return TabularData(source_name="年度省级分行业分品种能源消费量", data=df)
        except Exception as e:
            print(f"获取省级能源消费量失败: {e}")
            return TabularData(source_name="年度省级分行业分品种能源消费量", data=pd.DataFrame())
        finally:
            conn.close()

    def fetch_yearly_electricity_by_city(self, province: str, start_year: int, end_year: int) -> TabularData:
        """
        获取指定省份下所有地市的年度总用电量。
        此方法会从月度数据聚合得到年度数据。
        """
        print(f"正在聚合计算 {province} 省各地市从 {start_year} 到 {end_year} 的年度用电量...")
        
        conn = self._get_connection()
        if not conn:
            return TabularData(source_name="年度地市用电量", data=pd.DataFrame())

        try:
            # month 字段是 text，需要做一些处理来提取年份
            # 假设 month 格式为 YYYY-MM-DD
            query = """
            SELECT 
                SUBSTRING_INDEX(month, '-', 1) AS year,
                area,
                industry,
                SUM(electricity) as value
            FROM ecam_in_m_pro_ind_ele_off
            WHERE area LIKE %s 
              AND SUBSTRING_INDEX(month, '-', 1) BETWEEN %s AND %s
            GROUP BY year, area, industry
            ORDER BY year, area, industry;
            """
            # 使用 LIKE '%市' 或类似模式来匹配一个省的所有城市，这里简化处理
            # 实际可能需要一个地区层级表来判断归属
            # 为了演示，我们假设所有 area 都属于该 province
            df = pd.read_sql(query, conn, params=[f'%', start_year, end_year])

            if not df.empty:
                df['year'] = pd.to_numeric(df['year'])
            
            print(f"成功聚合计算出 {len(df)} 条年度地市用电量记录。")
            return TabularData(source_name="年度地市用电量", data=df)
        except Exception as e:
            print(f"聚合年度地市用电量失败: {e}")
            return TabularData(source_name="年度地市用电量", data=pd.DataFrame())
        finally:
            conn.close()

    def fetch_yearly_city_industry_energy_consumption(self, city: str, start_year: int, end_year: int) -> TabularData:
        """
        获取年度地市工业能源消费量。
        实现分层回退逻辑：
        1. 优先在 ene2 表中查询精确的市级数据 (去掉'市'字后缀)。
        2. 如果市级数据为空，则回退到 ene 表中查询省级数据。
        """
        print(f"正在为 {city} (及所属省份) 从 {start_year} 到 {end_year} 获取年度工业能源消费量...")
        
        conn = self._get_connection()
        if not conn:
            return TabularData(source_name="年度地市工业能源消费量", data=pd.DataFrame())

        try:
            # 准备市级和省级名称
            city_name = city.replace('市', '')
            province_name = "山西" # 简化处理

            # 1. 优先查询市级数据 (ene2 表)
            print(f"步骤1: 正在 ene2 表中查询市级数据: {city_name}...")
            query_city = """
            SELECT year, area, industry, energy_type, value, unit, `convert`, method 
            FROM ecam_in_y_pro_ind_ene2_off 
            WHERE area = %s AND year BETWEEN %s AND %s
            ORDER BY year
            """
            df = pd.read_sql(query_city, conn, params=[city_name, start_year, end_year])

            if not df.empty:
                print(f"成功获取到 {len(df)} 条市级工业能源消费量记录 (来自 ene2)。")
                return TabularData(source_name="年度地市工业能源消费量_ene2", data=df)

            # 2. 如果市级数据为空，回退查询省级宏观行业数据 (ene 表)
            print(f"未找到市级数据，步骤2: 正在 ene 表中回退查询省级宏观行业数据: {province_name}...")
            query_province_ene = """
            SELECT YEAR(year) as year, province as area, item as industry, `convert`, method, energy_type, value, unit 
            FROM ecam_in_y_pro_ind_ene_off 
            WHERE province = %s AND YEAR(year) BETWEEN %s AND %s
            AND item = '2.工业'
            ORDER BY year
            """
            df = pd.read_sql(query_province_ene, conn, params=[province_name, start_year, end_year])
            
            if not df.empty:
                print(f"成功最终回退并获取到 {len(df)} 条省级宏观工业能源消费量记录 (来自 ene)。")
                return TabularData(source_name="年度地市工业能源消费量_ene_fallback", data=df)
            
            print("所有查询层级（市级ene2, 省级ene）均未找到相关工业能源消费数据。")
            return TabularData(source_name="年度地市工业能源消费量", data=pd.DataFrame())
        except Exception as e:
            print(f"获取地市工业能源消费量失败: {e}")
            return TabularData(source_name="年度地市工业能源消费量", data=pd.DataFrame())
        finally:
            conn.close()
    
    def fetch_yearly_gdp(self, province: str, start_year: int, end_year: int) -> TabularData:
        """获取年度GDP数据"""
        print(f"正在获取{province}从{start_year}到{end_year}的年度GDP数据...")
        
        try:
            conn = self._get_connection()
            # 获取该省份下的所有城市
            from ecam_calculator.infrastructure.config_reader import get_config_reader
            config_reader = get_config_reader()
            province_city_mapping = config_reader.get_province_city_mapping()
            
            cities = []
            for mapping in province_city_mapping:
                if mapping.get('province') == province:
                    cities = mapping.get('cities', [])
                    break
            
            if not cities:
                print(f"在配置文件中找不到省份 '{province}' 的城市列表")
                return TabularData(source_name="年度GDP", data=pd.DataFrame())
            
            # 查询该省份下所有城市的GDP数据
            placeholders = ','.join(['%s'] * len(cities))
            query = f"""
            SELECT 
                year, 
                area, 
                indicator,
                record
            FROM fct_y_gdp
            WHERE area IN ({placeholders}) AND year BETWEEN %s AND %s
            ORDER BY year, area
            """
            
            params = cities + [start_year, end_year]
            df = pd.read_sql(query, params=params, con=conn)
            print(f"获取到{len(df)}条GDP记录")
            return TabularData(source_name="年度GDP", data=df)
        except Exception as e:
            print(f"获取GDP数据失败: {e}")
            return TabularData(source_name="年度GDP", data=pd.DataFrame())

    def fetch_yearly_energy_intensity(self, city: str, start_year: int, end_year: int) -> TabularData:
        """获取年度能耗强度数据，并保持为长格式"""
        print(f"正在获取{city}从{start_year}到{end_year}的年度能耗强度数据...")
        
        try:
            conn = self._get_connection()
            query = """
            SELECT 
                year, 
                area, 
                indicator,
                record as value
            FROM fct_y_all_ene_intsty
            WHERE area LIKE %s AND year BETWEEN %s AND %s
            AND indicator IN ('单位地区生产总值能源消耗(等价值)', '万元地区生产总值能耗降低率')
            ORDER BY year, area
            """
            city_pattern = f"{city}%"
            df_long = pd.read_sql(query, conn, params=[city_pattern, start_year, end_year])
            
            if df_long.empty:
                print("获取到0条能耗强度记录")
            else:
                print(f"获取到{len(df_long)}条能耗强度记录 (长格式)")

            return TabularData(source_name="年度能耗强度", data=df_long)
        except Exception as e:
            print(f"获取能耗强度数据失败: {e}")
            return TabularData(source_name="年度能耗强度", data=pd.DataFrame())

    def fetch_yearly_industrial_products_output(self, area: str, start_year: int, end_year: int) -> TabularData:
        """获取年度工业产品产量数据"""
        print(f"正在获取{area}从{start_year}到{end_year}的年度工业产品产量数据...")
        
        conn = self._get_connection()
        if not conn:
            return TabularData(source_name="年度工业产品产量", data=pd.DataFrame())
        
        try:
            # 根据实际数据库表结构查询
            query = """
            SELECT * FROM fct_y_prd_output 
            WHERE area = %s AND year BETWEEN %s AND %s
            ORDER BY year
            """
            df = pd.read_sql(query, conn, params=[area, start_year, end_year])
            
            # 确保数据类型正确
            if not df.empty:
                # 转换year列为数值类型
                df['year'] = pd.to_numeric(df['year'], errors='coerce')
                # 转换record列为数值类型
                if 'record' in df.columns:
                    df['record'] = pd.to_numeric(df['record'], errors='coerce')
                # 转换value列为数值类型
                if 'value' in df.columns:
                    df['value'] = pd.to_numeric(df['value'], errors='coerce')
                
                # 根据实际数据结构，确保有value列
                if 'record' in df.columns and 'value' not in df.columns:
                    df['value'] = df['record']
                
                # 添加产品类型列
                if 'product_name' in df.columns and 'product_type' not in df.columns:
                    df['product_type'] = df['product_name']
            
            print(f"获取到{len(df)}条工业产品产量记录")
            return TabularData(source_name="年度工业产品产量", data=df)
        except Exception as e:
            print(f"获取工业产品产量数据失败: {e}")
            return TabularData(source_name="年度工业产品产量", data=pd.DataFrame())
        finally:
            conn.close()
