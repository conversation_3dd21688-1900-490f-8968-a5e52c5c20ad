============================================================
区域电-能-碳监测分析系统兼容性检查报告
============================================================

📋 系统信息:
  平台: macOS-26.0-arm64-arm-64bit
  架构: 64bit
  机器: arm64
  处理器: arm

🐍 Python兼容性:
  当前版本: 3.8.20
  ✅ 兼容Python 3.8，但建议使用Python 3.7.2

📦 依赖包检查:
  ✅ pandas: 2.0.3 (要求: >=1.3.5,<2.0.0) ✅
  ✅ numpy: 1.24.4 (要求: >=1.21.6,<1.24.0) ✅
  ✅ pydantic: 2.10.6 (要求: >=1.10.0,<2.0.0) ✅
  ❌ PyYAML: not installed (要求: >=6.0.0) ✅
  ❌ mysql-connector-python: not installed (要求: >=8.0.27,<9.0.0) ✅
  ✅ ipfn: unknown (要求: >=1.4.0) ✅
  ✅ openpyxl: 3.1.5 (要求: >=3.0.0,<3.2.0) ✅
  ✅ xlsxwriter: 3.2.5 (要求: >=3.0.0,<3.3.0) ✅

📊 总体评估:
  ✅ 当前环境与Python 3.7.2兼容
  ⚠️ 建议使用Python 3.7.2以获得最佳兼容性

💡 建议:
  - 检测到ARM64架构，建议使用Python 3.8+
  - 使用 requirements.txt 安装依赖
  - 运行 ./deploy_linux.sh 进行Linux环境部署

============================================================