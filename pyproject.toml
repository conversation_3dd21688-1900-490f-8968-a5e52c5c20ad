[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ecam-calculator"
version = "1.0.0"
description = "区域电-能-碳监测分析系统核心计算包"
authors = [
    {name = "ECAM Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8,<3.9"
dependencies = [
    "pandas>=1.3.5,<2.0.0",
    "numpy>=1.21.6,<1.24.0",
    "mysql-connector-python>=8.0.27,<9.0.0",
    "ipfn>=1.4.0",
    "PyYAML>=6.0.0",
    "pydantic>=1.10.0,<2.0.0",
    "openpyxl>=3.0.0,<3.2.0",
    "xlsxwriter>=3.0.0,<3.3.0",
    "statsmodels>=0.13.0,<0.15.0",
    "scipy>=1.9.0,<1.11.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0,<7.0.0",
    "black>=22.0.0,<23.0.0",
    "flake8>=4.0.0,<6.0.0",
    "mypy>=0.950,<1.0.0",
]

[project.scripts]
ecam-calculator = "ecam_calculator.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["ecam_calculator*"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
