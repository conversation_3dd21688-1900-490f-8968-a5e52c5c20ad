# 区域"电-能-碳"监测分析系统 Linux安装说明

## 一、系统要求

- Linux操作系统（推荐CentOS 7或Ubuntu 18.04+）
- Python 3.7
- MySQL 5.7或更高版本

## 二、安装步骤

### 1. 安装MySQL

```bash
# CentOS系统
sudo yum install -y mysql-server mysql-devel

# Ubuntu系统
sudo apt-get update
sudo apt-get install -y mysql-server libmysqlclient-dev
```

启动MySQL服务：

```bash
# CentOS系统
sudo systemctl start mysqld
sudo systemctl enable mysqld

# Ubuntu系统
sudo systemctl start mysql
sudo systemctl enable mysql
```

### 2. 安装Python 3.7

```bash
# CentOS系统
sudo yum install -y python3 python3-devel

# Ubuntu系统
sudo apt-get install -y python3 python3-dev
```

### 3. 安装pip和依赖

```bash
# 安装pip
sudo yum install -y python3-pip   # CentOS
# 或
sudo apt-get install -y python3-pip   # Ubuntu

# 升级pip
pip3 install --upgrade pip

# 安装依赖
pip3 install mysql-connector-python==8.0.32
pip3 install ipfn==1.4.4
pip3 install pandas==1.3.5
pip3 install numpy==1.21.6
pip3 install PyYAML==5.4.1
pip3 install dependency-injector==4.41.0
pip3 install python-dateutil==2.8.2
pip3 install SQLAlchemy==1.4.46
pip3 install tqdm==4.65.0
```

### 4. 安装计算器包

```bash
# 安装whl包
pip3 install ecam-calculator-1.0.0-cp37-cp37m-linux_x86_64.whl
```

### 5. 导入数据库

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE ecam_city CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入SQL脚本
mysql -u root -p ecam_city < dlkst_prd_mysql57-ecam_city.sql
```

## 三、配置数据库连接

### 方法1：使用环境变量

将以下内容添加到`~/.bashrc`文件中：

```bash
export ECAM_DB_HOST=localhost
export ECAM_DB_PORT=3306
export ECAM_DB_USER=root
export ECAM_DB_PASSWORD=your_password
export ECAM_DB_NAME=ecam_city
```

应用更改：

```bash
source ~/.bashrc
```

### 方法2：使用命令行参数

在运行程序时直接指定数据库连接参数：

```bash
ecam-calculator --db-host localhost --db-port 3306 --db-user root --db-password your_password --db-name ecam_city --start-year 2022 --province 山西
```

## 四、使用方法

### 基本用法

```bash
# 计算单一年份数据
ecam-calculator --start-year 2022 --province 山西

# 计算多年数据
ecam-calculator --start-year 2020 --end-year 2022 --province 山西
```

### 调整日志级别

```bash
# 显示详细日志
ecam-calculator --start-year 2022 --province 山西 --log-level DEBUG

# 只显示警告和错误
ecam-calculator --start-year 2022 --province 山西 --log-level WARNING
```

## 五、常见问题排查

### 1. 数据库连接问题

如果遇到数据库连接错误：

```bash
# 检查MySQL服务是否运行
sudo systemctl status mysql  # 或 mysqld

# 检查数据库用户权限
mysql -u root -p -e "SHOW GRANTS;"

# 检查数据库是否存在
mysql -u root -p -e "SHOW DATABASES;"
```

### 2. 依赖问题

如果遇到依赖相关错误：

```bash
# 检查Python版本
python3 --version

# 检查pip版本
pip3 --version

# 检查已安装的包
pip3 list | grep -E "mysql|pandas|numpy|yaml"
```

### 3. 权限问题

如果遇到权限错误：

```bash
# 使用虚拟环境安装
python3 -m venv ecam_env
source ecam_env/bin/activate
pip install ecam-calculator-1.0.0-cp37-cp37m-linux_x86_64.whl
```