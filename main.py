#!/usr/bin/env python3
"""
主程序入口点 - 重构后的简化版本
"""

from ecam_calculator.application.calculation_job_service import CalculationJobService
from ecam_calculator.infrastructure.persistence.job_repository_impl import JobRepositoryImpl
from ecam_calculator.infrastructure.config_reader import get_config_reader


def main():
    """
    主函数 - 直接使用重构后的服务
    """
    print("=== 区域'电-能-碳'监测分析系统 ===")
    print("正在启动系统...")
    
    try:
        # 初始化配置读取器
        config_reader = get_config_reader()
        
        # 从配置文件读取数据库配置
        db_config = config_reader.get_database_settings()
        if not db_config:
            raise ValueError("配置文件中缺少数据库配置 (database_settings)")
        
        # 从配置文件读取作业配置
        job_settings = config_reader.get_job_settings()
        if not job_settings:
            raise ValueError("配置文件中缺少作业配置 (job_settings)")
        
        print(f"数据库配置 (来自配置文件): {db_config}")
        print(f"作业配置 (来自配置文件): {job_settings}")

        # 初始化基础设施
        job_repository = JobRepositoryImpl(db_config)
        raw_data_repository = RawDataRepositoryImpl(config_reader)
        
        # 初始化领域服务
        from ecam_calculator.infrastructure.services import (
            DataStandardizationServiceImpl, 
            ConstraintCalculationServiceImpl,
            QualityCheckServiceImpl,
            InventoryConstructionServiceImpl,
            ResultOutputServiceImpl
        )
        
        quality_check_service = QualityCheckServiceImpl()
        data_standardization_service = DataStandardizationServiceImpl(
            config_reader=config_reader,
            database_connection=db_config
        )
        constraint_calculation_service = ConstraintCalculationServiceImpl()
        inventory_construction_service = InventoryConstructionServiceImpl()
        result_output_service = ResultOutputServiceImpl()
        
        # 初始化应用服务
        job_service = CalculationJobService(
            job_repository=job_repository,
            raw_data_repo=raw_data_repository,
            quality_check_service=quality_check_service,
            data_standardization_service=data_standardization_service,
            constraint_calculation_service=constraint_calculation_service,
            inventory_construction_service=inventory_construction_service,
            result_output_service=result_output_service
        )
        
        print("系统启动成功！")
        
        # 从配置中获取任务参数
        start_year = job_settings.get('start_year', 2022)
        end_year = job_settings.get('end_year', 2022)
        province = job_settings.get('province', '山西省')
        
        # 启动一个计算任务
        print("\n--- 启动计算任务 ---")
        print(f"任务参数 (来自配置文件): 年份={start_year}-{end_year}, 省份={province}")
        
        job, context = job_service.start_new_job(
            start_year=start_year, 
            end_year=end_year, 
            province_name=province
        )
        
        print(f"任务 {job.id} 执行完成！")
        print(f"任务状态: {job.status.name}")
        
        # 输出结果摘要
        if hasattr(job_service, '_print_results_summary'):
            job_service._print_results_summary(context)
        
        print("\n=== 系统运行完成 ===")
        
    except Exception as e:
        print(f"系统运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
