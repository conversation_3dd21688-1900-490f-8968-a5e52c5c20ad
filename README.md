# 区域"电-能-碳"监测分析系统

## 项目简介

区域"电-能-碳"监测分析系统（ECAM）是一个基于领域驱动设计（DDD）的区域能源消费和碳排放监测分析计算工具。系统采用配置驱动的标准化架构，提供从原始数据处理、数据标准化、清单构建到约束计算的完整功能链条。

## 系统架构特点

### 1. **配置驱动的标准化架构**
- **统一标准化规则**：地区、行业、能源品种的标准化逻辑统一管理
- **表级配置简化**：表配置专注于元数据，业务逻辑通过配置描述
- **透明化设计**：数据库真实结构与标准化过程完全透明

### 2. **领域驱动设计**
- **清晰的分层架构**：应用层、领域层、基础设施层职责明确
- **值对象设计**：`RawData`、`StandardizedData`等值对象保证数据一致性
- **领域服务**：`DataStandardizationService`等核心业务逻辑封装

### 3. **数据流设计**
```
原始数据 → 通用标准化 → 业务逻辑处理 → 清单构建 → 结果输出
   │           │              │              │           │
   ▼           ▼              ▼              ▼           ▼
RawData → 列名映射/行业映射 → 能源转换/单位换算 → Inventory → CSV/Excel
```

## 核心功能模块

### 1. **数据标准化模块**
- **通用标准化**：地区、行业、能源品种的统一标准化处理
- **配置驱动**：通过YAML配置定义标准化规则，避免硬编码
- **业务逻辑分离**：通用标准化与表特定业务逻辑解耦

### 2. **清单构建模块**
- **能源消费清单**：基于标准化数据构建完整的能源消费清单
- **碳排放清单**：基于能源清单和排放因子计算碳排放清单
- **多层级融合**：支持省级到地市级的数据融合

### 3. **约束计算模块**
- **经济指标约束**：基于GDP等经济指标进行数据约束
- **能源强度约束**：基于能耗强度指标进行数据验证
- **数据平衡**：确保不同层级数据的一致性

## 配置架构

### 1. **全局标准化配置**
```yaml
# 全局标准化规则
standardization_rules:
  region:
    source_columns: ["province", "area"]
    target_column: "province"
    mapping: {...}
  
  industry:
    source_columns: ["item", "industry"]
    target_column: "industry"
    mapping: {...}
  
  energy:
    source_columns: ["energy_type"]
    target_column: "energy_type"
    mapping: {...}
```

### 2. **表级配置**
```yaml
tables:
  ecam_in_y_pro_ind_ene_off:
    description: "年度省级分行业分品种能源消费量"
    data_type: "energy_consumption"
    columns: ["year", "province", "item", "energy_type", "value", "unit"]
    business_logic:
      - energy_conversion: true
      - standard_coal_conversion: true
```

## 安装方法

### 使用Docker（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd 区域"电-能-碳"监测分析系统

# 启动服务
docker-compose up -d

# 运行数据标准化测试
docker-compose exec app python export_standardized_data.py
```

### 使用pip安装

```bash
pip install ecam-calculator-1.0.0-cp37-cp37m-linux_x86_64.whl
```

## 数据库配置

系统需要连接MySQL数据库，请确保已正确安装MySQL并创建了相应的数据库和表：

```bash
mysql -u root -p < dlkst_prd_mysql57-ecam_city.sql
```

## 使用方法

### 命令行使用

```bash
# 基本用法
ecam-calculator --start-year 2022 --province 山西

# 指定数据库连接
ecam-calculator --db-host localhost --db-port 3306 --db-user root --db-password password --db-name ecam_city --start-year 2022 --province 山西

# 指定年份范围
ecam-calculator --start-year 2020 --end-year 2022 --province 山西
```

### 环境变量配置

```bash
export ECAM_DB_HOST=localhost
export ECAM_DB_PORT=3306
export ECAM_DB_USER=root
export ECAM_DB_PASSWORD=password
export ECAM_DB_NAME=ecam_city

ecam-calculator --start-year 2022 --province 山西
```

### 作为Python库使用

```python
from ecam_calculator.application.calculation_job_service import CalculationJobService
from ecam_calculator.infrastructure.raw_data_repository_impl import RawDataRepositoryImpl
from ecam_calculator.infrastructure.persistence.job_repository_impl import JobRepositoryImpl

# 配置数据库连接
db_config = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'password',
    'database': 'ecam_city',
    'charset': 'utf8mb4'
}

# 初始化服务
job_repository = JobRepositoryImpl(db_config)
raw_data_repository = RawDataRepositoryImpl(db_config)
job_service = CalculationJobService(
    job_repository=job_repository,
    raw_data_repo=raw_data_repository
)

# 执行计算任务
job, context = job_service.start_new_job(
    start_year=2022, 
    end_year=2022, 
    province="山西"
)

# 获取结果
print(f"任务状态: {job.status.name}")
```

## 配置文件说明

系统使用YAML格式的配置文件，主要配置文件包括：

- `ecam_calculator/config/parameters.yaml`：参数配置文件，包含标准化规则和映射关系
- `ecam_deploy/config/parameters.yaml`：部署环境配置文件

## 系统要求

- Python 3.7 或更高版本
- MySQL 5.7 或更高版本
- Docker（推荐）或 Linux 操作系统

## 开发指南

### 项目结构

```
ecam_calculator/
├── application/          # 应用层
├── domain/              # 领域层
│   ├── model/          # 领域模型
│   └── service/        # 领域服务
├── infrastructure/      # 基础设施层
│   ├── config_reader.py
│   ├── raw_data_repository_impl.py
│   └── domain_services_impl.py
└── config/              # 配置文件
    └── parameters.yaml
```

### 核心设计原则

1. **配置驱动**：通过配置文件定义标准化规则，避免硬编码
2. **透明化设计**：数据库真实结构在配置中明确反映
3. **职责分离**：通用标准化与业务逻辑解耦
4. **统一接口**：所有数据标准化使用统一的接口和流程

## 文档结构

- `doc/1. 业务与领域分析/`：业务领域分析和统一语言
- `doc/2. 战略设计/`：系统架构和上下文映射
- `doc/3. 战术设计/`：详细的技术实现设计
- `doc/补充领域知识.md`：数据表结构和业务知识